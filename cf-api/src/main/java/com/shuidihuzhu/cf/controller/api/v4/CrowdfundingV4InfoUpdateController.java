package com.shuidihuzhu.cf.controller.api.v4;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.account.verify.client.model.VerifyIdcardVO;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.biz.crowdfunding.material.IMaterialCenterService;
import com.shuidihuzhu.cf.biz.crowdfunding.material.credit.ICrowdfundingAuthorPropertyBiz;
import com.shuidihuzhu.cf.biz.deposit.CfPayeeParamRecordBiz;
import com.shuidihuzhu.cf.biz.task.CfInfoTaskBiz;
import com.shuidihuzhu.cf.client.material.feign.CfMaterialWriteClient;
import com.shuidihuzhu.cf.client.material.model.CfMaterialAddOrUpdateVo;
import com.shuidihuzhu.cf.client.material.model.materialField.MaterialExtKeyConst;
import com.shuidihuzhu.cf.client.ugc.model.enums.HitMomentEnum;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.ModuleKeyCons;
import com.shuidihuzhu.cf.controller.api.base.BaseController;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.dto.UserSatisfactionDTO;
import com.shuidihuzhu.cf.enhancer.utils.HelpResponseUtils;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.*;
import com.shuidihuzhu.cf.facade.CfCaseEndFacade;
import com.shuidihuzhu.cf.facade.ICfPatientInfoFacade;
import com.shuidihuzhu.cf.facade.ICrowdfundingInfoFacade;
import com.shuidihuzhu.cf.finance.client.feign.deposit.CfDepositAccountFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.model.CfReminderWord;
import com.shuidihuzhu.cf.finance.model.vo.ServiceChargeVo;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.vo.*;
import com.shuidihuzhu.cf.model.deposit.CfPayeeParamRecord;
import com.shuidihuzhu.cf.mq.payload.DishonestPeoplePayload;
import com.shuidihuzhu.cf.param.raise.RaiseCaseParam;
import com.shuidihuzhu.cf.param.raise.RaiseControlParam;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.response.crowdfunding.CrowdfundingInfoResponse;
import com.shuidihuzhu.cf.service.caseinfo.CaseInfoApproveStageService;
import com.shuidihuzhu.cf.service.caseinfo.RaiseDraftService;
import com.shuidihuzhu.cf.service.crowdfunding.*;
import com.shuidihuzhu.cf.service.crowdfunding.casematerial.CfMaterialStatusService;
import com.shuidihuzhu.cf.service.deposit.CfBankCardVerifyService;
import com.shuidihuzhu.cf.service.deposit.CfPaDepositPayeeAccountService;
import com.shuidihuzhu.cf.service.risk.limit.ICfRiskService;
import com.shuidihuzhu.cf.util.BackCardUtil;
import com.shuidihuzhu.cf.util.BeenCopyUtil;
import com.shuidihuzhu.cf.util.VersionUtils;
import com.shuidihuzhu.cf.util.crowdfunding.CfIdCardUtil;
import com.shuidihuzhu.cf.util.crowdfunding.CrowdfundingUtil;
import com.shuidihuzhu.cf.util.crowdfunding.EmojiUtil;
import com.shuidihuzhu.cf.util.raise.RaiseParamConvert;
import com.shuidihuzhu.cf.vo.AuditStatusVO;
import com.shuidihuzhu.cf.vo.CfFirsApproveMaterialVO;
import com.shuidihuzhu.cf.vo.LastCasePreAuditStatusVO;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.baseservice.pay.model.CardBinParam;
import com.shuidihuzhu.client.baseservice.pay.model.CardBinResult;
import com.shuidihuzhu.client.baseservice.pay.model.PayRpcResponse;
import com.shuidihuzhu.client.baseservice.pay.v1.BankCardBinInfoClient;
import com.shuidihuzhu.client.cf.risk.model.enums.UserOperationEnum;
import com.shuidihuzhu.client.model.enums.CaseEndReasonEnum;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.annotation.SessionKeyValidateRequired;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.enums.LoginResult;
import com.shuidihuzhu.common.web.enums.Platform;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.*;
import com.shuidihuzhu.frame.client.api.platform.NewUserClient;
import com.shuidihuzhu.frame.client.model.platform.ModuleNewUser;
import com.shuidihuzhu.infra.starter.rocketmq.core.DelayLevel;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Controller;
import org.springframework.util.Base64Utils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by lixuan on 2016/12/06.
 */
@Slf4j
@RefreshScope
@Controller
@RequestMapping(path = "api/cf/v4", produces = "application/json;charset=UTF-8")
public class CrowdfundingV4InfoUpdateController extends BaseController {

    private static final String REDIS_LOCK_KEY = "CrowdFunding_V4_InfoUpdate_";
    @Autowired
    private CrowdfundingInfoService crowdfundingInfoService;
    @Autowired
    private UserInfoDelegate userInfoDelegate;
    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private BankCardBinInfoClient bankCardBinInfoClient;
    @Autowired
    private CfHospitalExtService cfHospitalExtService;
    @Autowired
    private CfInfoExtBiz cfInfoExtBiz;
    @Autowired
    private CfInfoMirrorService cfInfoMirrorService;

    @Autowired
    private CfInfoHospitalPayeeService cfInfoHospitalPayeeService;
    @Autowired
    private CfInfoTaskBiz cfInfoTaskBiz;
    @Autowired
    private CfFirstApproveBiz cfFirstApproveBiz;

    @Resource
    private CrowdfundingIdCaseService crowdfundingIdCaseService;

    @Resource
    private IdCardVerifyService idCardVerifyService;

    @Autowired
    private CrowdfundingInfoStatusBiz crowdfundingInfoStatusBiz;

    @Autowired
    private ICfPatientInfoFacade cfPatientInfoFacade;

    @Autowired
    private ICfRiskService cfRiskService;

    @Autowired
    private IMaterialCenterService centerService;

    @Resource(name = "cfRedissonHandler")
    private RedissonHandler redissonHandler;

    @Autowired(required = false)
    private Producer producer;

    @Resource
    private ICrowdfundingInfoFacade crowdfundingInfoFacade;

    @Autowired
    private CfCharityPayeeBiz cfCharityPayeeBiz;

    @Autowired
    private CfPaDepositPayeeAccountService cfPaDepositPayeeAccountService;

    @Autowired
    private CfCaseEndFacade cfCaseEndFacade;
    @Autowired
    private CfBankCardVerifyService cfBankCardVerifyService;
    @Autowired
    private CaseInfoApproveStageService caseInfoApproveStageService;

    @Autowired
    private ICrowdfundingAuthorPropertyBiz authorPropertyBiz;

    @Autowired
    private RaiseDraftService raiseDraftService;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;

    @Autowired
    private CfCommonStoreService cfCommonStoreService;

    @Autowired
    private CrowdfundingInfoHospitalPayeeBiz crowdfundingInfoHospitalPayeeBiz;

    @Autowired
    private ShuidiCipher shuidiCipher;

    @Autowired
    private CrowdfundingInfoPayeeBiz crowdfundingInfoPayeeBiz;

    @Autowired
    private CfMaterialStatusService cfMaterialStatusService;

    @Autowired
    private CfPayeeParamRecordBiz cfPayeeParamRecordBiz;

    @Autowired
    private CrowdfundingInfoSimpleBiz crowdfundingInfoSimpleBiz;

    @Resource
    private NewUserClient newUserClient;
    @Autowired
    private CfDepositAccountFeignClient cfDepositAccountFeignClient;
    @Autowired
    private CfMaterialWriteClient materialWriteClient;
    /**
     * 1，校验各种参数
     * 1.1，用户id是否在黑名单
     * 1.2，UserInfoModel是否存在
     * 1.3，cfInfoBaseVo和infoFragmentVo不为空
     * 1.4，手机号不为空并且可以根据手机号找到对应用户
     * 2，过风控的各种逻辑
     * 2.1，短时间内不允许用户发起三个以上的案例
     * 2.2，标题过敏感词
     * 2.3，内容过敏感词
     * 3，校验各种参数
     * 3.1，title不能为空且不能大于60个字
     * 3.2，content不能为空
     * 3.3，筹款金额不能小于0或者大于9999999
     * 3.4，照片附件数量不得大于8
     * 4，设置是否为志愿者扫描过二维码
     * 5，存储各种信息，
     * 5.1，存储各种信息
     * 5.2，发送mq
     * 6，存储自动发起的模板信息
     *
     * @param param
     * @param needMobile
     * @param paramv2
     * @return
     */
    @RequestMapping(path = "/add-or-update-base-info", method = RequestMethod.POST)
    @ResponseBody
    @SessionKeyValidateRequired
    @Deprecated
    public Response<Void> addOrUpdateBaseInfo(String param, @RequestParam(defaultValue = "1") Integer needMobile,
                                              @RequestParam(value = "paramv2", required = false, defaultValue = "") String paramv2) {
        log.info("CrowdfundingV4InfoUpdateController addOrUpdateBaseInfo param:{}\tparamV2:{}", param, paramv2);
        long userId = ContextUtil.getUserId();

        //拒绝使用接口发起
        // 自动生成消息传递工单
        log.info("add-or-update-base-info接口发起案例。param:{}", param);
        crowdfundingInfoBiz.visitOldRaiseCase(userId);
        return NewResponseUtil.makeResponse(CfErrorCode.CF_V3_FORBIDEN_ERROR.getCode(),
                CfErrorCode.CF_V3_FORBIDEN_ERROR.getMsg(), null);

    }

    // 前置审核资料驳回后，用户在重新提交资料
    @RequestMapping(path = "/update-first-approve-info", method = RequestMethod.POST)
    @ResponseBody
    @SessionKeyValidateRequired
    public Response<Void> updateFirstApproveInfo(String param) {
        long userId = ContextUtil.getUserId();

        if (userId <= 0) {
            return NewResponseUtil.makeError(CfErrorCode.CF_USER_ACCOUNT_NO_LOGIN);
        }

        log.info("前置审核资料驳回后保存。userId:{}, param:{}", userId, param);
        if (StringUtils.isEmpty(param)) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }

        CfFirsApproveMaterialVO material = null;
        HttpServletRequest servletRequest = (HttpServletRequest) ContextUtil.getRequest();
        String appVersion = servletRequest.getParameter("appVersion");

        try {
            material = JSON.parseObject(param, CfFirsApproveMaterialVO.class);
        } catch (Exception e) {
            log.error("前置审核资料驳回后，用户在重新提交资料，json解析异常", e);
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_JSON_PARSE_ERROR);
        }
        if (material != null) {
            if (!Strings.isNullOrEmpty(material.getPatientBornCard()) && material.getPatientBornCard().length() > 64) {
                return NewResponseUtil.makeResponse(CfErrorCode.SYSTEM_PARAM_ERROR_TOO_LONG.getCode(), "患者出生证编码过长", null);
            }
        }
        return cfFirstApproveBiz.updateAfterRejectForReSubmit(material, userId, appVersion);
    }

    @RequestMapping(path = "/get-first-approve-show", method = RequestMethod.POST)
    @ResponseBody
    @SessionKeyValidateRequired
    public Response<CfFirsApproveMaterialVO> showFirstApproveInfo(String infoUuid) {
        long userId = ContextUtil.getUserId();
        if (userId <= 0) {
            return NewResponseUtil.makeError(CfErrorCode.CF_USER_ACCOUNT_NO_LOGIN);
        }

        CrowdfundingInfo cfInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        if(cfInfo == null){
            return NewResponseUtil.makeError(CfErrorCode.CF_NOT_FOUND);
        }
        if (userId != cfInfo.getUserId()) {
            return NewResponseUtil.makeError(CfErrorCode.CF_INFO_PARAM_ERROR_IDENTITY_INVALID);
        }

        CfFirsApproveMaterial material = cfFirstApproveBiz.getByInfoUuid(infoUuid);
        CfFirsApproveMaterialVO vo = cfFirstApproveBiz.buildShowWithMaterial(material);
        UserInfoModel userInfoModel = userInfoDelegate.getUserInfoByUserId(cfInfo.getUserId());
        vo.setPhone(shuidiCipher.decrypt(userInfoModel.getCryptoMobile()));

        return NewResponseUtil.makeSuccess(vo);
    }

    @RequestMapping(path = "/get-last-reject-case", method = RequestMethod.POST)
    @ResponseBody
    @SessionKeyValidateRequired
    public Response<CfFirstApproveBiz.CaseRejectObject> getLastRejectCase() {
        long userId = ContextUtil.getUserId();
        if (userId <= 0) {
            return NewResponseUtil.makeError(CfErrorCode.CF_USER_ACCOUNT_NO_LOGIN);
        }

        return NewResponseUtil.makeSuccess(cfFirstApproveBiz.getLastRejectCaseByUserId(userId));
    }

    @RequestMapping(path = "/get-first-approve-info", method = RequestMethod.POST)
    @ResponseBody
    @SessionKeyValidateRequired
    public Response<CfFirsApproveMaterialVO> getFirstApproveInfo(String infoUuid) {
        long userId = ContextUtil.getUserId();
        if (userId <= 0) {
            return NewResponseUtil.makeError(CfErrorCode.CF_USER_ACCOUNT_NO_LOGIN);
        }
        HttpServletRequest servletRequest = (HttpServletRequest) ContextUtil.getRequest();
        String appVersion = servletRequest.getParameter("appVersion");

        return cfFirstApproveBiz.getCanEditMaterialVo(infoUuid, userId, appVersion);
    }

    /**
     * 获取对应案例 首次审核状态
     *
     * @param infoUuid
     * @return
     */
    @RequestMapping(path = "/get-first-approve-status", method = {RequestMethod.GET, RequestMethod.POST})
    @SessionKeyValidateRequired
    @ResponseBody
    public Response<AuditStatusVO> getPreAuditStatus(String infoUuid) {
        AuditStatusVO result = new AuditStatusVO();
        CfInfoExt infoExt = cfInfoExtBiz.getByInfoUuid(infoUuid);
        if (infoExt == null) {
            log.warn("getPreAuditStatus null uuid={}", infoUuid);
            return NewResponseUtil.makeSuccess(null);
        }
        FirstApproveStatusEnum statusEnum = FirstApproveStatusEnum.parse(infoExt.getFirstApproveStatus());
        result.setStatus(statusEnum);
        return NewResponseUtil.makeSuccess(result);
    }

    /**
     * 如果最近一次的草稿创建时间晚于最近案例创建时间，说明用户需要发起新案例，进入提交成功待审核页；否则获取最近一次发起的筹款 首次审核状态
     *
     * @return
     */
    @ApiOperation("获取最近一次发起的筹款 首次审核状态")
    @PostMapping(path = "/get-last-case-first-approve-status")
    @SessionKeyValidateRequired
    @ResponseBody
    public Response<LastCasePreAuditStatusVO> getLastCasePreAuditStatus(@RequestParam(value = "appVersion", required = false)
                                                                                    String appVersion) {
        long userId = ContextUtil.getUserId();
        if (userId <= 0) {
            log.warn("{}", userId);
            return NewResponseUtil.makeError(CfErrorCode.CF_USER_ACCOUNT_NO_LOGIN);
        }

        CrowdfundingBaseInfoBackup baseInfoBackup = crowdfundingInfoService.selectRecentlyCfByUserId(userId, CrowdfundingBaseInfoBackup.DraftTypeEnum.ORIGIN.getCode());
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getLastByUserId(userId);
        if (baseInfoBackup != null && (null == crowdfundingInfo || baseInfoBackup.getCreateTime().after(crowdfundingInfo.getCreateTime()))) {
            return NewResponseUtil.makeSuccess(null, "有最近草稿");
        }
        if (null == crowdfundingInfo) {
            return NewResponseUtil.makeError(ErrorCode.CF_NOT_FOUND);
        }
        String infoUuid = crowdfundingInfo.getInfoId();

        CfInfoExt infoExt = cfInfoExtBiz.getByInfoUuid(infoUuid);

        CfErrorCode checkCode = cfInfoExtBiz.checkSupportInitialProperty(ContextUtil.getAppVersion(), infoExt);
        if (checkCode != CfErrorCode.SUCCESS) {
            return NewResponseUtil.makeError(checkCode);
        }

        FirstApproveStatusEnum statusEnum = FirstApproveStatusEnum.parse(infoExt.getFirstApproveStatus());
        LastCasePreAuditStatusVO vo = new LastCasePreAuditStatusVO();
        vo.setInfoUuid(infoUuid);
        vo.setStatus(statusEnum);

        return NewResponseUtil.makeSuccess(vo);
    }

    @RequestMapping(path = "/add-or-update-base-info-v2", method = RequestMethod.POST)
    @ResponseBody
    @SessionKeyValidateRequired
    public Response<CrowdfundingInfoResponse.Data> addOrUpdateBaseInfov2(String param, @RequestParam(defaultValue = "1") Integer needMobile, @RequestParam(value = "appVersion", required = false) String appVersion) {
        log.info("CrowdfundingV4InfoUpdateController addOrUpdateBaseInfo param:{}", param);
        // 全量 3100了 这个接口不作为发起案例的接口
        return NewResponseUtil.makeError(CfErrorCode.APP_VERSION_TOO_LOW_TIPS);

//        long userId = ContextUtil.getUserId();
//        String currAppVersion = ContextUtil.getAppVersion();
//        if (StringUtils.isNotBlank(currAppVersion) && !AppVersionUitl.greaterEqual(currAppVersion, AppVersionUitl.version_2)) {
//            return NewResponseUtil.makeError(CfErrorCode.APP_VERSION_TOO_LOW_TIPS);
//        }
//
//        CrowdfundingInfoBaseVo baseVo = RaiseParamUtils.getBaseVo(param, appVersion, userId);
//        if (baseVo != null) {
//            if (!Strings.isNullOrEmpty(baseVo.getPatientBornCard()) && baseVo.getPatientBornCard().length() > 64) {
//                return NewResponseUtil.makeResponse(CfErrorCode.SYSTEM_PARAM_ERROR_TOO_LONG.getCode(), "患者出生证编码过长", null);
//            }
//            if (baseVo.getCfVersion() == null) {
//                log.info("发起案例的version字段为空.param:{}", param);
//            }
//        }
//        return crowdfundingInfoFacade.addOrUpdateBaseInfoV2(baseVo);
    }

    /**
     * 发起接口
     */
    @PostMapping("/add-or-update-base-info-v4")
    @ResponseBody
    @SessionKeyValidateRequired
    public Response<CrowdfundingInfoResponse.Data> addOrUpdateBaseInfov4(
            String raiseControlParam,
            @RequestParam(value = "appVersion", required = false) String appVersion) {
        log.info("CrowdfundingV4InfoUpdateController addOrUpdateBaseInfov4 appVersion:{}", appVersion);
        String clientIp = ContextUtil.getClientIp();
        RaiseControlParam controlParam = JSON.parseObject(raiseControlParam, RaiseControlParam.class);
        long userId = ContextUtil.getUserId();
        Response<RaiseCaseParam> draftResponse = raiseDraftService.getDraft(userId);
        if (draftResponse.notOk()) {
            return HelpResponseUtils.makeRelayError(draftResponse);
        }
        RaiseCaseParam data = draftResponse.getData();
        CrowdfundingInfoBaseVo baseVO = RaiseParamConvert.convertRaise(data, controlParam);

        CrowdfundingInfoBaseVo baseVo = buildBaseVO(baseVO, appVersion, userId);
        if (baseVo != null) {
            if (!Strings.isNullOrEmpty(baseVo.getPatientBornCard()) && baseVo.getPatientBornCard().length() > 64) {
                return NewResponseUtil.makeResponse(CfErrorCode.SYSTEM_PARAM_ERROR_TOO_LONG.getCode(), "患者出生证编码过长", null);
            }
            if (baseVo.getCfVersion() == null) {
                log.info("发起案例的version字段为空.param:{}", baseVO);
            }

            // 防止案例重复发起校验
            log.info("防止案例重复发起校验开始，raiseBasicInfoParam: {}", baseVo.getRaiseBasicInfoParam());
            OpResult<CrowdfundingInfo> verifyRaiseBasicInfoOPResult = crowdfundingInfoFacade.preventCasesFromRecurring(baseVo, userId);
            if (verifyRaiseBasicInfoOPResult.isFail()) {
                log.info("防止案例重复发起校验不通过 userId:{}, baseVo:{}, verifyRaiseBasicInfoOPResult:{}", userId, baseVo, verifyRaiseBasicInfoOPResult);
                return NewResponseUtil.makeError(verifyRaiseBasicInfoOPResult.getErrorCode());
            }
            log.info("防止案例重复发起校验通过 userId:{}", userId);

        }

        if (Objects.nonNull(baseVO)){
            fillDeviceId(baseVO);
        }
        return crowdfundingInfoFacade.addOrUpdateBaseInfoV2(baseVo,clientIp, HitMomentEnum.CASE_RAISE);
    }

    @RequestMapping(path = "/add-or-update-base-info-v3", method = RequestMethod.POST)
    @ResponseBody
    @SessionKeyValidateRequired
    @Deprecated
    public Response<CrowdfundingInfoResponse.Data> addOrUpdateBaseInfov3(String param, @RequestParam(defaultValue = "1") Integer needMobile, @RequestParam(value = "appVersion", required = false) String appVersion) {
        long userId = ContextUtil.getUserId();
        CrowdfundingInfoBaseVo cfInfoBaseVo = getBaseVo(param, appVersion, userId);

        if (cfInfoBaseVo == null) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR, null);
        }

        //拒绝使用v3接口发起
        if (StringUtils.isBlank(cfInfoBaseVo.getInfoUuid())) {
            // 自动生成消息传递工单
            log.info("add-or-update-base-info-v3接口发起案例。param:{}", param);
            crowdfundingInfoBiz.visitOldRaiseCase(userId);
            return NewResponseUtil.makeResponse(CfErrorCode.CF_V3_FORBIDEN_ERROR.getCode(),
                    CfErrorCode.CF_V3_FORBIDEN_ERROR.getMsg(), null);
        }

        //infoUuid不是空，表示是修改
        boolean isUpdate = StringUtils.isNotBlank(cfInfoBaseVo.getInfoUuid());

        //参数校验&风控环节
        OpResult result = crowdfundingInfoFacade.onValidate(cfInfoBaseVo, userId, HitMomentEnum.CASE_RAISE);
        if (!result.isSuccess()) {
            return NewResponseUtil.makeError(result.getErrorCode());
        }
        fillDeviceId(cfInfoBaseVo);
        CrowdfundingInfoResponse response = addOrUpdate(cfInfoBaseVo, ContextUtil.getClientIp());
        if (!isUpdate && response.getErrorCode().getCode() == CfErrorCode.SUCCESS.getCode()) {
            //更新cf_info_ext，需要审核6项材料
            cfInfoExtBiz.updateNeedCaseList(response.getInfoUuid(), CrowdfundingInfoDataStatusTypeEnum.getAllCaseList());
        }

        saveBaseInfoTemplateRecord(cfInfoBaseVo, response);

        return NewResponseUtil.makeResponse(response.getErrorCode().getCode(), response.getErrorCode().getMsg(),
                response.getData());
    }


    /**
     * 存储自动发起模板记录
     *
     * @param cfInfoBaseVo
     * @param response
     */
    private void saveBaseInfoTemplateRecord(CrowdfundingInfoBaseVo cfInfoBaseVo, CrowdfundingInfoResponse response) {
        crowdfundingInfoFacade.saveBaseInfoTemplateRecord(cfInfoBaseVo, response);
    }

    /**
     * 患者身份验证添加与更新
     *
     * @param param
     * @param channel
     * @param appVersion
     * @return
     */
    @RequestMapping(path = "/add-or-update-patient-info", method = RequestMethod.POST)
    @ResponseBody
    @SessionKeyValidateRequired
    @ApiOperation(value = "患者身份验证添加与更新", httpMethod = "POST")
    public Response<CrowdfundingInfoResponse.Data> addOrUpdatePatientInfo(String param,
                                                                          @RequestParam(value = "channel", required = false) String channel,
                                                                          @RequestParam(value = "channelType", required = false) String channelType,
                                                                          @RequestParam(value = "appVersion", required = false) String appVersion) {
        long userId = ContextUtil.getUserId();
        String clientIp = ContextUtil.getClientIp();
        if (userId <= 0) {
            log.warn("CrowdFundingV4InfoUpdateController addOrUpdatePatientInfo userId:{} is  illegality{}", userId);
            return NewResponseUtil.makeError(CfErrorCode.CF_USER_ACCOUNT_NO_LOGIN);
        }
        try {
            log.info("CrowdFundingV4InfoUpdateController addOrUpdatePatientInfo param:{}, channel:{}, appVersion=:{}",
                    param, channel, appVersion);
            CrowdfundingInfoPatientVo crowdfundingInfoPatientVo =
                    JSON.parseObject(param, CrowdfundingInfoPatientVo.class);
            if (crowdfundingInfoPatientVo == null || crowdfundingInfoPatientVo.getInfoUuid() == null) {
                return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
            }

            if (crowdfundingInfoPatientVo.getIdType() != null &&
                    crowdfundingInfoPatientVo.getIdType() == UserIdentityType.identity &&
                    !CfIdCardUtil.isValidIdCard(crowdfundingInfoPatientVo.getIdCard())) {

                return NewResponseUtil.makeError(CfErrorCode.USER_INFO_ID_CARD_ERROR);
            }

            log.info("CrowdFundingV4InfoUpdateController addOrUpdatePatientInfo crowdFundingInfoPatientVo:{}",
                    crowdfundingInfoPatientVo);

            //兼容1.11.0版本之前的照片上传，H5直接用新版本
            boolean processIdCardPhoto = VersionUtils.processIdCardPhoto(appVersion, channelType);
            log.info("userId:{}，当前设备：{}，当前版本：{}，是否走新版本：{}", userId, channelType, appVersion, processIdCardPhoto);
            CrowdfundingInfoResponse response = this.cfPatientInfoFacade.addOrUpdatePatientInfo(channel,
                    appVersion,
                    crowdfundingInfoPatientVo,
                    processIdCardPhoto,
                    true,clientIp,userId);
            return NewResponseUtil.makeResponse(response.getErrorCode().getCode(), response.getErrorCode().getMsg(),
                    response.getData());
        } catch (Exception e) {
            log.error("CrowdfundingV4InfoUpdateController addOrUpdatePatientInfo error!", e);
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_ERROR);
        }
    }


    /**
     * 房产与车产添加与更新
     * 前端没有调用了
     *
     * @param param
     * @param channel
     * @param appVersion
     * @return
     */
    @RequestMapping(path = "/add-or-update-credit-supplement-info", method = RequestMethod.POST)
    @ResponseBody
    @SessionKeyValidateRequired
    @ApiOperation(value = "增信房产、车产添加和更新", httpMethod = "POST")
    public Response<CrowdfundingInfoResponse.Data> addOrUpdateCreditSupplementInfo(@ApiParam("增信数据参数") String param,
                                                                                   @ApiParam("案例infoUuid") String infoUuid,
                                                                                   @ApiParam("channel") @RequestParam(value = "channel", required = false) String channel,
                                                                                   @ApiParam("app版本信息") @RequestParam(value = "appVersion", required = false) String appVersion) {
        long userId = ContextUtil.getUserId();
        if (userId <= 0) {
            log.warn("CrowdFundingV4InfoUpdateController addOrUpdateCreditSupplementInfo userId:{} is  illegality{}",
                    userId);
            return NewResponseUtil.makeError(CfErrorCode.CF_USER_ACCOUNT_NO_LOGIN);
        }

        CfErrorCode canSubmitCredit = authorPropertyBiz.verifyCanSubmitCredit(infoUuid);
        if (canSubmitCredit != CfErrorCode.SUCCESS) {
            return NewResponseUtil.makeError(canSubmitCredit);
        }
        String tryLock = "";
        try {
            tryLock = redissonHandler.tryLock(this.getRedisLockKey(infoUuid, userId), 0, 2000L);
            if (StringUtils.isEmpty(tryLock)) {
                return NewResponseUtil.makeSuccess(null);
            }
        } catch (Exception e) {
            log.error("infoUuid:{},userId:{}, get redis lock failed", infoUuid, userId);
        }
        try {
            log.info(
                    "CrowdFundingV4InfoUpdateController addOrUpdateCreditSupplementInfo param:{}, channel:{}, appVersion=:{}",
                    param, channel, appVersion);

            List<CfCreditSupplement> creditSupplementList = JSONArray.parseArray(param, CfCreditSupplement.class);

            if (CollectionUtils.isNotEmpty(creditSupplementList)) {
                String infoId = creditSupplementList.get(0).getInfoUuid();
                CfInfoExt cfInfoExt = cfInfoExtBiz.getByInfoUuid(infoId);
                if (cfInfoExt.getCfVersion() == CfVersion.definition_20181131.getCode()) {
                    return NewResponseUtil.makeError(CfErrorCode.CF_INFO_VERSION_ERROR_CREDIT_SUPPLEMENT);
                }
            }

            CrowdfundingInfoResponse response =
                    this.crowdfundingInfoService.addOrUpdateCreditSupplementInfo(userId, creditSupplementList, infoUuid);

            return ResponseUtil.makeResponse(response.getErrorCode().getCode(), response.getErrorCode().getMsg(),
                    response.getData());

        } catch (Exception e) {
            log.error("CrowdfundingV4InfoUpdateController addOrUpdateCreditSupplementInfo error!", e);
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_ERROR);
        } finally {
            try {
                if (StringUtils.isNotBlank(tryLock)) {
                    redissonHandler.unLock(this.getRedisLockKey(infoUuid, userId), tryLock);
                }
            } catch (Exception e) {
                log.error("", e);
            }
        }
    }


    @RequestMapping(path = "/check-payee-info", method = RequestMethod.POST)
    @ResponseBody
    @SessionKeyValidateRequired
    public Response<CrowdfundingInfoResponse.Data> checkPayeeInfo(String param) {
        log.info("CrowdfundingV4InfoUpdateController checkPayeeInfo param:{}", param);
        CrowdfundingInfoPayeeVo crowdfundingInfoPayeeVo = JSON.parseObject(param, CrowdfundingInfoPayeeVo.class);
        if (crowdfundingInfoPayeeVo == null || crowdfundingInfoPayeeVo.getInfoUuid() == null) {
            return NewResponseUtil.makeResponse(CfErrorCode.SYSTEM_PARAM_ERROR.getCode(),
                    CfErrorCode.SYSTEM_PARAM_ERROR.getMsg(), null);
        }

        long userId = ContextUtil.getUserId();
        if (userId <= 0) {
            log.warn("CrowdfundingV4InfoUpdateController addOrUpdatePayeeInfo userId is {}", userId);
            return NewResponseUtil.makeResponse(LoginResult.LOGIN.code, LoginResult.LOGIN.msg, null);
        }

        CrowdfundingInfo crowdfundingInfo = this.crowdfundingInfoBiz.getFundingInfo(crowdfundingInfoPayeeVo.getInfoUuid());

        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_NOT_EXIST_RECORD_ERROR);
        }

        String payeeName = StringUtils.trimToEmpty(crowdfundingInfoPayeeVo.getPayeeName());
        crowdfundingInfoPayeeVo.setPayeeName(payeeName);
        String bankCard = StringUtils.trimToEmpty(crowdfundingInfoPayeeVo.getBankCard());
        String bankName = StringUtils.trimToEmpty(crowdfundingInfoPayeeVo.getBankName());
        String idCard = StringUtils.trimToEmpty(crowdfundingInfoPayeeVo.getIdCard());

        UserIdentityType idType = crowdfundingInfoPayeeVo.getIdType();
        if (StringUtils.isBlank(bankName) || StringUtils.isBlank(payeeName)) {
            return NewResponseUtil.makeError(CfErrorCode.CF_INFO_PARAM_ERROR_PAYEE);
        }
        String mobile = StringUtils.trimToEmpty(crowdfundingInfoPayeeVo.getMobile());
        if (illegal(mobile)) {
            return NewResponseUtil.makeError(CfErrorCode.USER_ACCOUNT_MOBILE_ERROR);
        }

        if (StringUtils.isBlank(bankCard) || !BackCardUtil.checkBankCard(bankCard)) {
            return NewResponseUtil.makeError(CfErrorCode.CF_BANK_CARD_VERIFY_FAILED);
        }
        if (StringUtils.isEmpty(idCard) || idType == null) {
            return NewResponseUtil.makeError(CfErrorCode.CF_INFO_PARAM_ERROR_PAYEE);
        }
        if ((idType == UserIdentityType.identity && IdCardUtil.illegal(idCard))
                || (idType != UserIdentityType.identity && !idCard.matches("[a-zA-Z0-9]{1,30}"))) {
            return NewResponseUtil.makeError(CfErrorCode.CF_PARAM_ERROR_PAYEE_ID_CARD_ERROR);
        }

        //身份证不足18位不让提交（过滤掉15位的身份证）
        if (idType == UserIdentityType.identity && !CfIdCardUtil.isValidIdCard(idCard)) {
            return NewResponseUtil.makeError(CfErrorCode.USER_INFO_ID_CARD_ERROR);
        }

        if (idType == UserIdentityType.identity && CfIdCardUtil.getCurrentAge(idCard) < 18) {
            return NewResponseUtil.makeError(CfErrorCode.PAYEE_NOT_ALLOW_OF_AGE);
        }
        //收款人 银行卡 要素校验
        CrowdfundingInfoResponse response = cfBankCardVerifyService.backCardVerify(crowdfundingInfoPayeeVo,
                crowdfundingInfo);
        log.info("response:{}", JSON.toJSONString(response));
        if (response == null) {
            return NewResponseUtil.makeError(CfErrorCode.CF_BANK_CARD_VERIFICATION_FAILED);
        }
        return NewResponseUtil.makeResponse(response.getErrorCode().getCode(), response.getErrorCode().getMsg(),
                response.getData());
    }

    @PostMapping("/pre-check-payee-account-status")
    @ResponseBody
    @SessionKeyValidateRequired
    @ApiOperation(value = "校验收款账户是否已经鉴权成功")
    public Response<Boolean> preCheckPayeeAccountStatus(@RequestParam @ApiParam(value = "案例infoUuid") String infoUuid,
                                                        @RequestParam @ApiParam(value = "持卡人姓名") String payeeName,
                                                        @RequestParam @ApiParam(value = "持卡人卡号") String bankCard,
                                                        @RequestParam @ApiParam(value = "发卡行名称") String bankName,
                                                        @RequestParam @ApiParam(value = "持卡人身份证号") String idCard) {

        long userId = ContextUtil.getUserId();

        if (userId <= 0) {
            log.warn("CrowdfundingV4InfoUpdateController addOrUpdatePayeeInfo userId is {}", userId);
            return NewResponseUtil.makeResponse(LoginResult.LOGIN.code, LoginResult.LOGIN.msg, null);
        }

        CrowdfundingInfo crowdfundingInfo = this.crowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (crowdfundingInfo == null || userId != crowdfundingInfo.getUserId()) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_NOT_EXIST_RECORD_ERROR);
        }

        payeeName = StringUtils.trimToEmpty(payeeName);
        bankCard = StringUtils.trimToEmpty(bankCard);
        bankName = StringUtils.trimToEmpty(bankName);
        idCard = StringUtils.trimToEmpty(idCard);

        if (StringUtils.isBlank(bankName) || StringUtils.isBlank(payeeName)) {
            return NewResponseUtil.makeError(CfErrorCode.CF_INFO_PARAM_ERROR_PAYEE);
        }
        if (StringUtils.isBlank(bankCard) || !BackCardUtil.checkBankCard(bankCard)) {
            return NewResponseUtil.makeError(CfErrorCode.CF_BANK_CARD_VERIFY_FAILED);
        }
        if (IdCardUtil.illegal(idCard)) {
            return NewResponseUtil.makeError(CfErrorCode.CF_PARAM_ERROR_PAYEE_ID_CARD_ERROR);
        }
        //身份证不足18位不让提交（过滤掉15位的身份证）
        if (!CfIdCardUtil.isValidIdCard(idCard)) {
            return NewResponseUtil.makeError(CfErrorCode.USER_INFO_ID_CARD_ERROR);
        }

        // 年龄小于18
        if (CfIdCardUtil.getCurrentAge(idCard) < 18) {
            return NewResponseUtil.makeError(CfErrorCode.PAYEE_NOT_ALLOW_OF_AGE);
        }

        String identity = StringUtils.EMPTY;
        String key = "" + crowdfundingInfo.getId();
        try {
            identity = redissonHandler.tryLock(key, 0L, -1);
            CfReminderWord<Boolean> result = cfPaDepositPayeeAccountService.preCheckPayeeAccountStatus(payeeName, bankCard, bankName, idCard, crowdfundingInfo);
            return result.buildResponse();
        } catch (Exception e) {
            log.warn("加锁失败,key:{}", key);
        } finally {
            if (StringUtils.isNotBlank(identity)) {
                try {
                    redissonHandler.unLock(key, identity);
                } catch (Exception e) {
                    log.warn("解锁失败,key:{}", key);
                }
            }
        }
        return NewResponseUtil.makeSuccess(false);
    }

    /**
     * 通过卡bin获取对应的银行卡信息
     * clientId 描述 https://wiki.shuiditech.com/pages/viewpage.action?pageId=*********
     * getBankInfoByCardBin wiki https://wiki.shuiditech.com/pages/viewpage.action?pageId=*********
     *
     * @param bankCardId
     * @return
     */
    @SessionKeyValidateRequired
    @RequestMapping(path = "/get-card-bin", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "通过卡bin获取对应的银行卡信息")
    public Response<CardBinResult> getCardBin(@ApiParam(value = "bankCardId") String bankCardId) {
        if (StringUtils.isEmpty(bankCardId)) {
            return NewResponseUtil.makeFail("银行卡信息不可以为空");
        }
        if (bankCardId.length() < 9) {
            return NewResponseUtil.makeFail("银行卡位数不足，请检查");
        }
        if (!StringUtils.isNumeric(bankCardId)) {
            return NewResponseUtil.makeFail("银行卡号只能是阿拉伯数字，请检查");
        }
        CardBinParam cardBinParam = new CardBinParam();
        cardBinParam.setBankCardNum(Base64Utils.encodeToString(bankCardId.getBytes()));
        cardBinParam.setClientId(16);
        PayRpcResponse<CardBinResult> payRpcResponse = bankCardBinInfoClient.getBankInfoByCardBin(cardBinParam);
        log.info("payRpcResponse:{}\tbankCardId:{}", JSON.toJSONString(payRpcResponse), bankCardId);

        if (payRpcResponse.isSuccess() && payRpcResponse.getResult() != null) {
            return NewResponseUtil.makeSuccess(payRpcResponse.getResult());
        } else {
            return NewResponseUtil.makeFail("未获取到银行卡信息，请手动填写");
        }
    }

    /**
     * 诊断证明添加与更新
     *
     * @param param
     * @return
     */
    @RequestMapping(path = "/add-or-update-treatment-info", method = RequestMethod.POST)
    @ResponseBody
    @SessionKeyValidateRequired
    @ApiOperation(value = "诊断证明添加与更新", httpMethod = "POST")
    public Response<CrowdfundingInfoResponse.Data> addOrUpdateTreatmentInfo(String param,
                                                                            @ApiParam("app版本信息") @RequestParam(value = "appVersion", required = false) String appVersion) {

        return NewResponseUtil.makeError(CfErrorCode.INITIAL_CASE_CANT_SUBMIT_CREDIT);

//        try {
//            log.info("CrowdfundingV4InfoUpdateController addOrUpdateTreatmentInfo param:{}", param);
//            long userId = ContextUtil.getUserId();
//            if (userId <= 0) {
//                log.warn("CrowdfundingV4InfoUpdateController addOrUpdateTreatmentInfo userId is {}", userId);
//                return NewResponseUtil.makeError(CfErrorCode.CF_INFO_PARAM_ERROR_IDENTITY_INVALID);
//            }
//            CrowdfundingInfoTreatmentVo crowdfundingInfoTreatmentVo =
//                    JSON.parseObject(param, CrowdfundingInfoTreatmentVo.class);
//
//            if (crowdfundingInfoTreatmentVo == null) {
//                return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
//            }
//            CrowdfundingInfoResponse response =
//                    this.crowdfundingInfoService.addOrUpdateTreatmentInfo(userId, appVersion,
//                            crowdfundingInfoTreatmentVo);
//            return NewResponseUtil.makeResponse(response.getErrorCode().getCode(), response.getErrorCode().getMsg(),
//                    response.getData());
//        } catch (Exception e) {
//            log.error("add-or-update-treatment-info", e);
//            return NewResponseUtil.makeError(CfErrorCode.CF_COMMENT_ERROR_IN_BLACK_LIST);
//        }
    }

    @RequestMapping(path = "/add-or-update-id-verify", method = RequestMethod.POST)
    @ResponseBody
    @SessionKeyValidateRequired
    @ApiOperation(value = "补充或者修改材料中的发起人身份证", httpMethod = "POST", response = CrowdfundingIdCaseStatusEnum.class)
    public Response<Integer> addOrUpdateIdVerify(@RequestParam("name") String name,
                                                 @RequestParam("identify") String identify,
                                                 @RequestParam(value = "infoUuid") String infoUuid) {

        if (StringUtils.isBlank(name) || StringUtils.isBlank(identify) || StringUtils.isBlank(infoUuid)) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }

        //前端传入的身份证号进行隐藏字符过滤
        identify = CfIdCardUtil.filiterHideCode(identify);

        //身份证不足18位不让提交（过滤掉15位的身份证）
        if (!CfIdCardUtil.isValidIdCard(identify)) {
            return NewResponseUtil.makeError(CfErrorCode.USER_INFO_ID_CARD_ERROR);
        }

        long userId = ContextUtil.getUserId();

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }

        CfInfoExt cfInfoExt = cfInfoExtBiz.getByInfoUuid(crowdfundingInfo.getInfoId());

        List<CrowdfundingInfoDataStatusTypeEnum> requiedList = CrowdfundingUtil.getRequiredCaseList(cfInfoExt);
        if (!requiedList.contains(CrowdfundingInfoDataStatusTypeEnum.ID_VERIFY)) {
            //根本不需要此项材料
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }


        //不管验证成功与否，只要填写过了，就添加一个材料，为未审核状态
        CfFirsApproveMaterial cfFirsApproveMaterial = cfFirstApproveBiz.getByInfoUuid(infoUuid);
        UserRelTypeEnum relType = null;
        if (null != cfFirsApproveMaterial) {
            relType = UserRelTypeEnum.getUserRelTypeEnum(cfFirsApproveMaterial.getUserRelationType());
        }
        if (null == relType) {
            relType = UserRelTypeEnum.SELF;
            log.error("案例:{}，没有初审材料", infoUuid);
        }
        VerifyIdcardVO verifyIdcardVO = idCardVerifyService.verfiy(name, identify, relType, userId);
        CrowdfundingIdCaseStatusEnum statusEnum = CrowdfundingIdCaseStatusEnum.convert(verifyIdcardVO.getCode());
        if (statusEnum == CrowdfundingIdCaseStatusEnum.VERIFY_FAIL) {
            return NewResponseUtil.makeSuccess(statusEnum.getCode());
        }

        CrowdfundingIdCase existIdCase = crowdfundingIdCaseService.getByInfoId(crowdfundingInfo.getId());

        if (existIdCase == null) {
            String encryptIdentify = oldShuidiCipher.aesEncrypt(identify);
            CrowdfundingIdCase idCase = CrowdfundingIdCase.build(crowdfundingInfo.getId(), name, identify, encryptIdentify);
            crowdfundingIdCaseService.insert(idCase);
        } else {
            crowdfundingIdCaseService.updateIdCase(crowdfundingInfo.getId(), name, identify);
        }

        // 发起人信息变更时 发送消息
        producer.send(new Message<>(MQTopicCons.CF,
                MQTagCons.CF_BASE_INFO_CHANGE_TAG, MQTagCons.CF_BASE_INFO_CHANGE_TAG + "-" +
                crowdfundingInfo.getId()
                + "-" + System.currentTimeMillis(), crowdfundingInfo.getId(), DelayLevel.S5));
        //变更身份验证的状态
        crowdfundingIdCaseService.updateStatus(crowdfundingInfo.getId(), statusEnum);

        //对应这个材料的状态
        CrowdfundingInfoStatusEnum infoStatusEnum = CrowdfundingIdCaseStatusEnum.convert(statusEnum);

        //目前库中有没有这个状态的记录？
        CrowdfundingInfoStatus statusRecord = crowdfundingInfoStatusBiz.getByInfoUuidAndType(infoUuid, CrowdfundingInfoDataStatusTypeEnum.ID_VERIFY.getCode());

        if (statusRecord == null) {

            CrowdfundingInfoStatus crowdfundingInfoStatus = new CrowdfundingInfoStatus();
            crowdfundingInfoStatus.setInfoUuid(crowdfundingInfo.getInfoId());
            CrowdfundingInfoDataStatusTypeEnum crowdfundingInfoDataStatusType = CrowdfundingInfoDataStatusTypeEnum.ID_VERIFY;
            crowdfundingInfoStatus.setType(crowdfundingInfoDataStatusType.getCode());
            crowdfundingInfoStatus.setStatus(infoStatusEnum.getCode());
            // 新增案例id
            crowdfundingInfoStatus.setCaseId(crowdfundingInfo.getId());
            int result = crowdfundingInfoStatusBiz.add(crowdfundingInfoStatus);
            if (result <= 0) {
                return NewResponseUtil.makeError(CfErrorCode.SYSTEM_ERROR);
            }
        } else {
            crowdfundingInfoStatusBiz.updateByInfoId(infoUuid, CrowdfundingInfoDataStatusTypeEnum.ID_VERIFY.getCode(), infoStatusEnum);
        }

        return NewResponseUtil.makeSuccess(statusEnum.getCode());
    }


    /**
     * 提交审核
     *
     * @param infoUuid 项目ID
     * @param linkKey 代录入标识
     * @return
     */
    @RequestMapping(path = "/submit-for-review", method = RequestMethod.POST)
    @ResponseBody
    @SessionKeyValidateRequired
    public Response<Void> submitForReview(String infoUuid,
                                          @RequestParam(value = "linkKey",required = false) String linkKey) {
        log.info("CrowdfundingV4InfoUpdateController submitForReview param:{}", infoUuid);
        long userId = ContextUtil.getUserId();
        if (userId <= 0) {
            log.warn("CrowdfundingV4InfoUpdateController submitForReview contextUserId: {}", userId);
            return NewResponseUtil.makeResponse(LoginResult.LOGIN.code, LoginResult.LOGIN.msg, null);
        }

        CrowdfundingInfo crowdfundingInfo = this.crowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeError(CfErrorCode.CF_NOT_FOUND);
        }
        if (userId != crowdfundingInfo.getUserId()) {
            return NewResponseUtil.makeError(CfErrorCode.CF_INFO_PARAM_ERROR_IDENTITY_INVALID);
        }

        CfOperatingRecord cfOperatingRecord = this.cfInfoMirrorService.before(crowdfundingInfo,
                crowdfundingInfo.getUserId(),
                crowdfundingInfo.getPayeeName(),
                CfOperatingRecordEnum.Type.SUBMIT_AUDIT,
                CfOperatingRecordEnum.Role.USER);
        Response<Void> response = crowdfundingInfoService.submitForReview(crowdfundingInfo);
        if (StringUtils.isNotEmpty(linkKey)) {
            CfMaterialAddOrUpdateVo materialVo = CfMaterialAddOrUpdateVo.builder()
                    .caseId(crowdfundingInfo.getId())
                    .materialName(MaterialExtKeyConst.APPROVE_LINK_KEY)
                    .materialValue(linkKey)
                    .materialLabel("")
                    .materialExt("")
                    .build();
            materialWriteClient.addOrUpdateByFields(crowdfundingInfo.getId(), Lists.newArrayList(materialVo));
        }
        this.cfInfoMirrorService.after(cfOperatingRecord);
        log.info("CrowdfundingV4InfoUpdateController submitForReview response:{}", JSON.toJSONString(response));

        try {
            // 获取案例ID
            int caseId = crowdfundingInfo.getId();
            // 查询该案例的收款人参数记录列表
            List<CfPayeeParamRecord> recordList = cfPayeeParamRecordBiz.getCfPayeeParamRecord(caseId);

            // 如果收款人记录数量小于等于1，直接发送失信人员检查消息
            if (recordList.size() <=1 ){
                // 构建消息唯一标识key：用户ID + 案例ID
                String key = new StringBuilder().append(userId).append(caseId).toString();
                // 创建失信人员检查载荷对象
                DishonestPeoplePayload dishonestPeoplePayload = new DishonestPeoplePayload(userId, caseId, " "," ",
                        crowdfundingInfo.getPayeeName(),crowdfundingInfo.getPayeeIdCard(),crowdfundingInfo.getCreateTime(), 0);
                // 发送失信人员检查MQ消息
                Message message = new Message(MQTopicCons.CF, MQTagCons.DISHONEST_PEOPLE_HANDLE_MSG, key, JSON.toJSONString(dishonestPeoplePayload));
                producer.send(message);
            }else{
                // 如果有多条记录，比较前两条记录的收款人信息是否一致
                CfPayeeParamRecord recordFirst = recordList.get(0);
                CfPayeeParamRecord recordSecond = recordList.get(1);
                // 判断前两条记录的姓名和身份证是否相同
                boolean judge = StringUtils.equals(recordFirst.getName(), recordSecond.getName()) && StringUtils.equals(recordFirst.getEncryptIdCard(), recordSecond.getEncryptIdCard());
                // 如果收款人信息不一致，说明可能存在风险，发送失信人员检查消息
                if (!judge){
                    // 构建消息唯一标识key：用户ID + 案例ID
                    String key = new StringBuilder().append(userId).append(caseId).toString();
                    // 创建失信人员检查载荷对象
                    DishonestPeoplePayload dishonestPeoplePayload = new DishonestPeoplePayload(userId, caseId, " "," ",
                            crowdfundingInfo.getPayeeName(),crowdfundingInfo.getPayeeIdCard(),crowdfundingInfo.getCreateTime(), 0);
                    // 发送失信人员检查MQ消息
                    Message message = new Message(MQTopicCons.CF, MQTagCons.DISHONEST_PEOPLE_HANDLE_MSG, key, JSON.toJSONString(dishonestPeoplePayload));
                    producer.send(message);
                }
            }
        }catch (Exception e){
            // 捕获异常并记录错误日志，避免影响主流程
            log.error("收款人失信信息发送失败, ", e);
        }

        return response;

    }


    private boolean illegal(String mobile) {
        return StringUtils.isEmpty(mobile) || !Consts.pMobile.matcher(mobile).matches();
    }

    @RequestMapping(path = "/case-end-reason", method = RequestMethod.POST)
    @ResponseBody
    @SessionKeyValidateRequired
    public Response<Map<Integer, String>> getCaseEndReasonMap() {
        return NewResponseUtil.makeSuccess(CaseEndReasonEnum.enumMap);
    }

    @RequestMapping(path = "/save-hospital-info", method = RequestMethod.POST)
    @ResponseBody
    @SessionKeyValidateRequired
    public Response<Void> saveHospitalInfo(String param) {
        try {
            log.info("CrowdfundingV4InfoUpdateController saveHospitalInfo param:{}", param);
            CfHospitalExt cfHospitalExt = JSON.parseObject(param, CfHospitalExt.class);
            if (cfHospitalExt == null) {
                return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
            }
            String hospitalName = cfHospitalExt.getHospitalName();
            if (EmojiUtil.containsEmoji(hospitalName)) {
                return NewResponseUtil.makeSuccess(null);
            }
            cfHospitalExtService.insertIntoHospitalExt(cfHospitalExt);
        } catch (Exception e) {
            log.error("CrowdfundingV4InfoUpdateController saveHospitalInfo error!", e);
            return NewResponseUtil.makeSuccess(null);
        }
        return NewResponseUtil.makeSuccess(null);
    }

    @RequestMapping(path = "/add-base-info-from-h5", method = RequestMethod.POST)
    @ResponseBody
    @SessionKeyValidateRequired
    @Deprecated
    public Response<CrowdfundingInfoResponse> addBaseInfoFromH5(String param,
                                                                @ApiParam("app版本信息") @RequestParam(value = "appVersion", required = false) String appVersion) {

        return NewResponseUtil.makeError(CfErrorCode.INITIAL_CASE_CANT_SUBMIT_CREDIT);
//        log.info("addBaseInfoFromH5 param:{}", param);
//        long userId = ContextUtil.getUserId();
//        String clientIp = ContextUtil.getClientIp();
//        CrowdfundingBaseInfoH5Vo crowdfundingBaseInfoH5Vo = JSON.parseObject(param, CrowdfundingBaseInfoH5Vo.class);
//        if (StringUtils.isBlank(crowdfundingBaseInfoH5Vo.getTitle()) ||
//                StringUtils.isBlank(crowdfundingBaseInfoH5Vo.getContent())) {
//            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
//        }
//
//        CrowdfundingInfoBaseVo crowdfundingInfoBaseVo = new CrowdfundingInfoBaseVo();
//        crowdfundingInfoBaseVo.setUserId(userId);
//        crowdfundingInfoBaseVo.setTargetAmount(crowdfundingBaseInfoH5Vo.getTargetAmount());
//        crowdfundingInfoBaseVo.setTitle(crowdfundingBaseInfoH5Vo.getTitle());
//        crowdfundingInfoBaseVo.setContent(crowdfundingBaseInfoH5Vo.getContent());
//        crowdfundingInfoBaseVo.setRelationType(crowdfundingBaseInfoH5Vo.getRelationType());
//        crowdfundingInfoBaseVo.setChannel(crowdfundingBaseInfoH5Vo.getChannel());
//        crowdfundingInfoBaseVo.setContentType(CfContentTypeEnum.DEFAULT);
//
//        OpResult validateResult = crowdfundingInfoFacade.onValidate(crowdfundingInfoBaseVo, userId);
//        if (!validateResult.isSuccess()) {
//            return NewResponseUtil.makeError(validateResult.getErrorCode());
//        }
//
//        fillDeviceId(crowdfundingInfoBaseVo);
//        CrowdfundingInfoResponse crowdfundingInfoResponse = addOrUpdate(crowdfundingInfoBaseVo, ContextUtil.getClientIp());
//        String infoUuid = crowdfundingInfoResponse.getInfoUuid();
//        try {
//            CrowdfundingInfoPatientVo crowdfundingInfoPatientVo = new CrowdfundingInfoPatientVo();
//            crowdfundingInfoPatientVo.setInfoUuid(infoUuid);
//            crowdfundingInfoPatientVo.setName(crowdfundingBaseInfoH5Vo.getPatientName());
//            crowdfundingInfoPatientVo.setIdCard("");
//            crowdfundingInfoPatientVo.setIdCardPhoto("");
//            crowdfundingInfoPatientVo.setIdType(UserIdentityType.identity);
//            this.cfPatientInfoFacade.addOrUpdatePatientInfo("", "", crowdfundingInfoPatientVo, false, false, clientIp, userId);
//        } catch (Exception e) {
//            log.error("", e);
//        }
//
//        try {
//            CrowdfundingInfoTreatmentVo crowdfundingInfoTreatmentVo = new CrowdfundingInfoTreatmentVo();
//            crowdfundingInfoTreatmentVo.setInfoUuid(infoUuid);
//            crowdfundingInfoTreatmentVo.setDiseaseName(crowdfundingBaseInfoH5Vo.getDiseaseName());
//            crowdfundingInfoTreatmentVo.setSubAttachmentType(SubTreatmentTypeEnum.none.value());
//            this.crowdfundingInfoService.addOrUpdateTreatmentInfo(userId, appVersion, crowdfundingInfoTreatmentVo);
//        } catch (Exception e) {
//            log.error("", e);
//        }
//
//        return NewResponseUtil.makeSuccess(crowdfundingInfoResponse);
    }

    @RequestMapping(path = "/add-or-update-base-info-attachment", method = RequestMethod.POST)
    @ResponseBody
    @SessionKeyValidateRequired
    public Response<Void> addOrUpdateBaseInfoAttachment(String infoUuid, String attachments) {
        if (StringUtils.isBlank(infoUuid) || StringUtils.isBlank(attachments)) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        CrowdfundingInfo crowdfundingInfo = this.crowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeError(CfErrorCode.CF_NOT_FOUND);
        }
        List<String> attachmentList = Splitter.on(",").splitToList(attachments);
        // 保存待审核图文
        caseInfoApproveStageService.update(crowdfundingInfo.getId(), null, null, attachmentList);
        long currentUserId = ContextUtil.getUserId();
        this.crowdfundingInfoService.addOrUpdateBaseInfoAttachment(crowdfundingInfo, attachmentList, currentUserId);
        this.cfInfoTaskBiz.updateAttachCfNum(crowdfundingInfo, CollectionUtils.size(attachmentList));
        return NewResponseUtil.makeSuccess(null);
    }

    private String getRedisLockKey(String infoUuid, long userId) {
        return REDIS_LOCK_KEY + infoUuid + "_" + userId;
    }

    //新增或者需改筹款案例
    private CrowdfundingInfoResponse addOrUpdate(CrowdfundingInfoBaseVo cfInfoBaseVo, String clientIP) {
        return crowdfundingInfoFacade.addOrUpdate(cfInfoBaseVo, clientIP);
    }


    @ApiOperation("增加用户满意度")
    @SessionKeyValidateRequired
    @RequestMapping(path = "/add-user-satisfaction", method = {RequestMethod.POST})
    @ResponseBody
    public Response addUserSatisfaction(@RequestParam("infoUuid") String infoUuid,
                                        @RequestParam("informationName") String informationName,
                                        @RequestParam("result") Integer result,
                                        @RequestParam("testType") Integer testType){
        if(Objects.isNull(result) || result < 0 || Objects.isNull(testType)|| testType < 0){
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        UserSatisfactionDTO userSatisfactionDTO = new UserSatisfactionDTO(infoUuid,informationName,result,testType);
        CfInfoSimpleModel crowdfundingInfo = crowdfundingInfoSimpleBiz.getFundingInfo(infoUuid);
        if(Objects.isNull(crowdfundingInfo)){
            return NewResponseUtil.makeError(CfErrorCode.CF_NOT_FOUND);
        }
        userSatisfactionDTO.setCaseId(crowdfundingInfo.getId());
        long userId = ContextUtil.getUserId();
        if(userId <= 0){
            return NewResponseUtil.makeError(CfErrorCode.CF_USER_ACCOUNT_NO_LOGIN);
        }
        userSatisfactionDTO.setUserId(userId);
        return crowdfundingInfoBiz.addUserSatisfaction(userSatisfactionDTO);

    }

    @ApiOperation("用户停止筹款，发送验证码")
    @SessionKeyValidateRequired
    @PostMapping("/send-verify-code")
    @ResponseBody
    public Response<Void> sendVerifyCode(){
        long userId = ContextUtil.getUserId();
        if(userId <= 0){
            return NewResponseUtil.makeError(CfErrorCode.CF_USER_ACCOUNT_NO_LOGIN);
        }
        return cfCaseEndFacade.sendVerifyCode(userId,ContextUtil.getClientIp());
    }




    @ApiOperation("保存一键复制状态")
    @SessionKeyValidateRequired
    @PostMapping("save-or-update-copy-status")
    @ResponseBody
    public Response<Void> saveOrUpdateCopyStatus(@RequestParam(value = "infoUuid",required = false,defaultValue = "")String infoUuid){
        long userId = ContextUtil.getUserId();
        Response<ModuleNewUser> response = newUserClient.get(ModuleKeyCons.FIRST_COPY,userId);
        if(Objects.isNull(response)){
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_ERROR);
        }
        ModuleNewUser moduleNewUser = response.getData();
        if(Objects.isNull(moduleNewUser)){
            newUserClient.create(ModuleKeyCons.FIRST_COPY+infoUuid,userId,0,infoUuid);
        }else{
            newUserClient.updateModuleKey(ModuleKeyCons.FIRST_COPY+infoUuid,userId,moduleNewUser.getId());
        }
        return NewResponseUtil.makeSuccess();
    }

    @ApiOperation("获取一键保存使用状态")
    @SessionKeyValidateRequired
    @PostMapping("get-copy-status")
    @ResponseBody
    public Response<Boolean> getCopyStatus(@RequestParam(value = "infoUuid",required = false,defaultValue = "")String infoUuid){
        long userId = ContextUtil.getUserId();
        Response<ModuleNewUser> response = newUserClient.get(ModuleKeyCons.FIRST_COPY+infoUuid,userId);
        if(Objects.isNull(response)||
                response.notOk() || Objects.nonNull(response.getData())) {
            return NewResponseUtil.makeSuccess(true);
        }
        return NewResponseUtil.makeSuccess(false);
    }

    @SessionKeyValidateRequired
    @ResponseBody
    @PostMapping("/pre-check-service-charge-v1")
    public Response<ServiceChargeVo> preCheckServiceChargeV1(@RequestParam(value = "info", required = false, defaultValue = "") String info,
                                                             @RequestParam(value = "channel", required = false, defaultValue = "") String channel,
                                                             @RequestParam(value = "infoUuid", required = false, defaultValue = "") String infoUuid) {

        long userId = ContextUtil.getUserId();
        ServiceChargeVo resultVo = crowdfundingInfoFacade.preCheckServiceCharge(channel, infoUuid, userId, info);
        return NewResponseUtil.makeSuccess(resultVo);
    }
}

