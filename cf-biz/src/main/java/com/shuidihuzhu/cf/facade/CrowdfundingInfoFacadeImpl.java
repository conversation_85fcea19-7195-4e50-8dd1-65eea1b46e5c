package com.shuidihuzhu.cf.facade;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ctrip.framework.apollo.ConfigService;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.account.model.UserThirdModel;
import com.shuidihuzhu.account.model.service.MobileUserIdModel;
import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.biz.crowdfunding.material.IMaterialCenterService;
import com.shuidihuzhu.cf.biz.crowdfunding.material.common.MaterialCenterConvert;
import com.shuidihuzhu.cf.biz.crowdfunding.toufang.ICfToufangInviteService;
import com.shuidihuzhu.cf.client.material.model.CfCaseRaiseMaterialModel;
import com.shuidihuzhu.cf.client.material.model.CfPropertyInsuranceInfoModel;
import com.shuidihuzhu.cf.client.material.model.materialField.CfInitialPropertyField;
import com.shuidihuzhu.cf.client.material.model.medication.MedicineInfoConstant;
import com.shuidihuzhu.cf.client.material.model.medication.UserMedicineView;
import com.shuidihuzhu.cf.client.ugc.model.domain.risk.RiskControlWordCategoryDO;
import com.shuidihuzhu.cf.client.ugc.model.enums.HitLocationEnum;
import com.shuidihuzhu.cf.client.ugc.model.enums.HitMomentEnum;
import com.shuidihuzhu.cf.client.ugc.model.view.RiskWordResultV2;
import com.shuidihuzhu.cf.constants.AsyncPoolConstants;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.WxConstants;
import com.shuidihuzhu.cf.constants.crowdfunding.CrowdfundingCons;
import com.shuidihuzhu.cf.delegate.EncryptDelegate;
import com.shuidihuzhu.cf.delegate.ICfGrowthtoolDelegate;
import com.shuidihuzhu.cf.delegate.IRegisterClewFacade;
import com.shuidihuzhu.cf.delegate.SimpleUserAccountDelegate;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.delegate.UserThirdDelegate;
import com.shuidihuzhu.cf.domain.dedicated.CfToufangInviteCaseRelationDO;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.*;
import com.shuidihuzhu.cf.facade.caseinfo.InitialAuditSubmitService;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceCapitalAccountFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.enums.CapitalServiceEnum;
import com.shuidihuzhu.cf.finance.model.CfReminderWord;
import com.shuidihuzhu.cf.finance.model.vo.ServiceChargeVo;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.param.SendHitNoticeToAlarmParam;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoBaseVo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingSeekHelpInfoFragmentVo;
import com.shuidihuzhu.cf.mq.payload.CaseLabelPayload;
import com.shuidihuzhu.cf.mq.producer.CommonMessageHelperService;
import com.shuidihuzhu.cf.param.raise.RaiseBasicInfoParam;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.response.crowdfunding.CreateInfoResultWrap;
import com.shuidihuzhu.cf.response.crowdfunding.CrowdfundingInfoResponse;
import com.shuidihuzhu.cf.service.TracerService;
import com.shuidihuzhu.cf.service.crowdfunding.*;
import com.shuidihuzhu.cf.service.crowdfunding.casematerial.CfLivingGuardService;
import com.shuidihuzhu.cf.service.crowdfunding.event.CrowdFundingAddPublisher;
import com.shuidihuzhu.cf.service.crowdfunding.event.CrowdFundingUpdatePublisher;
import com.shuidihuzhu.cf.service.impl.ApplicationServiceImpl;
import com.shuidihuzhu.cf.service.msg.MsgClientService;
import com.shuidihuzhu.cf.service.notice.RaiseSuccessNoticeService;
import com.shuidihuzhu.cf.service.risk.dark.DarkListService;
import com.shuidihuzhu.cf.service.risk.limit.ICfRiskService;
import com.shuidihuzhu.cf.service.risk.word.RiskControlWordHelpService;
import com.shuidihuzhu.cf.util.crowdfunding.CfIdCardUtil;
import com.shuidihuzhu.cf.vo.initialaudit.CfPropertyInsuranceVO;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.account.v1.accountservice.MobileUserIdResponse;
import com.shuidihuzhu.client.account.wecom.feign.FeiShuTaskClient;
import com.shuidihuzhu.client.account.wecom.model.FeiShuTaskRequestDto;
import com.shuidihuzhu.client.account.wecom.model.FeiShuTaskUpdateResponse;
import com.shuidihuzhu.client.baseservice.alarm.v1.AlarmClient;
import com.shuidihuzhu.client.cf.growthtool.model.lovepartner.CfPartnerCaseRelationDo;
import com.shuidihuzhu.client.cf.growthtool.model.lovepartner.CfPartnerInfoDo;
import com.shuidihuzhu.client.cf.risk.client.CfUgcWhileListClient;
import com.shuidihuzhu.client.cf.risk.model.enums.UserOperationEnum;
import com.shuidihuzhu.client.cf.search.client.CfSearchClient;
import com.shuidihuzhu.client.cf.search.model.CfCaseIndexSearchParam;
import com.shuidihuzhu.client.cf.search.model.CfCaseIndexSearchResult;
import com.shuidihuzhu.client.cf.search.model.CfCaseModel;
import com.shuidihuzhu.client.cf.search.model.SearchRpcResult;
import com.shuidihuzhu.client.model.CheckUserScanOrInvitedResultModel;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.data.analytics.javasdk.core.Analytics;
import com.shuidihuzhu.data.analytics.javasdk.core.UserTagTypeEnum;
import com.shuidihuzhu.data.servicelog.meta.cf.CaseSensitiveWords;
import com.shuidihuzhu.cf.enhancer.utils.AlarmBotService;
import com.shuidihuzhu.infra.starter.rocketmq.core.DelayLevel;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.wx.enums.AccountThirdTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.RegExUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.*;

/**
 * Created by sven on 18/8/14.
 *
 * <AUTHOR>
 */
@Slf4j
@RefreshScope
@Service
public class CrowdfundingInfoFacadeImpl implements ICrowdfundingInfoFacade {

    @Autowired
    private CfBlackListBiz cfBlackListBiz;

    @Autowired
    private BlackListService blackListService;

    @Autowired
    private ApplicationServiceImpl applicationService;

    @Autowired
    private DarkListService darkListService;

    @Autowired
    private UserInfoDelegate userInfoDelegate;
    @Autowired
    private SimpleUserAccountDelegate simpleUserAccountDelegate;

    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;

    @Autowired
    private ICfRiskService cfRiskService;
    @Autowired
    private UserThirdDelegate userThirdDelegate;
    @Autowired
    private CrowdFundingAddPublisher crowdFundingAddPublisher;

    @Autowired
    private CrowdfundingCreateService crowdfundingCreateService;
    @Autowired
    private CrowdfundingUpdateService crowdfundingUpdateService;
    @Autowired
    private CrowdFundingUpdatePublisher crowdFundingUpdatePublisher;

    @Autowired
    private ICfToufangInviteService cfToufangInviteService;

    @Autowired
    private CfStatus4MQBiz cfStatus4MQBiz;
    @Autowired
    private AlarmClient alarmClient;
    @Autowired
    private CfInfoMirrorService cfInfoMirrorService;

    @Autowired
    private OldShuidiCipher oldShuidiCipher;
    @Resource
    private CfUgcWhileListClient cfUgcWhileListClient;

    @Value("${baseinfo.raise.block.timescope:6}")
    private int raiseBlockTimeScope = 6; //
    @Value("${baseinfo.raise.block.timethreshold:3}")
    private int raiseBlockTimesThreshold = 3;

    @Value("${apollo.raise-check-age:true}")
    private boolean checkAge;

    @Value("${apollo.need-submit-medicine:12}")
    private String needSubmitMedicine;

    private static final int MIX_RAISE_AGE = 18;

    @Autowired
    protected CfRedisKvBiz cfRedisKvBiz;

    @Value("${filter.raise.case.mobile:15214499008}")
    private String FILTER_RAISE_CASE_MOBILE;

    @Autowired
    private RaiseSuccessNoticeService raiseSuccessNoticeService;

    @Autowired
    private CrowdfundingInfoService crowdfundingInfoService;

    @Autowired
    private CfFirstApproveBiz cfFirstApproveBiz;

    @Resource
    private InitialAuditSubmitService initialAuditSubmitService;
    @Autowired
    private Analytics analytics;

    @Autowired
    private IRegisterClewFacade registerClewFacadeImpl;

    @Autowired
    private RiskControlWordHelpService riskControlWordHelpService;
    @Autowired
    private ICfGrowthtoolDelegate cfGrowthtoolDelegate;
    @Autowired
    private MsgClientService msgClientService;
    @Autowired
    private SensitiveProcessorService sensitiveProcessorService;

    @Autowired
    private CfInfoExtBiz cfInfoExtBiz;
    @Autowired
    private ShuidiCipher shuidiCipher;

    @Autowired
    private IMaterialCenterService materialService;

    @Resource(name = "cfRedissonHandler")
    private RedissonHandler cfRedissonHandler;

    @Autowired
    private CfNoHandlingFeeBiz cfNoHandlingFeeBiz;

    @Resource
    private CrowdfundingInfoSimpleBiz simpleBiz;

    @Resource
    private CfCaseUpdateAmountRecordService updateAmountRecordService;

    @Value("${apollo.funding.black.idcard:}")
    private List<String> idCardList = Lists.newArrayList();

    @Autowired
    private CommonMessageHelperService commonMessageHelperService;

    @Autowired
    private TracerService tracerService;
    @Autowired
    private CfFinanceCapitalAccountFeignClient cfFinanceCapitalAccountFeignClient;
    @Resource
    private CfSearchClient cfSearchClient;
    @Autowired
    private EncryptDelegate encryptDelegate;
    @Value("${verify.raise.basic.info.switch:false}")
    private boolean verifyRaiseBasicInfoSwitch;

    @Autowired
    private FeiShuTaskClient feiShuTaskClient;

    @Resource(name = AsyncPoolConstants.FEI_SHU_TASK)
    private Executor feishuExecutor;

    public boolean allowRaiseCase(long userId) {
        if (StringUtils.isBlank(FILTER_RAISE_CASE_MOBILE)) {
            return true;
        }

        String[] lists = FILTER_RAISE_CASE_MOBILE.split(",");
        List<String> mobiles = Lists.newArrayList();
        for (String mobile : lists) {
            if (StringUtils.isNotBlank(mobile)) {
                mobiles.add(mobile.trim());
            }
        }

        if (CollectionUtils.isEmpty(mobiles)) {
            return true;
        }

        List<MobileUserIdModel> mobileUserIdResponses = null;
        try {
            mobileUserIdResponses = simpleUserAccountDelegate.getUserIdsByMobiles(mobiles);
        } catch (Throwable e) {
            log.error("simpleUserAccountGrpcClient.getUserIdsByMobiles userId:{} 异常", userId, e);
            return true;
        }

        if (mobileUserIdResponses == null) {
            return true;
        }

        for (MobileUserIdModel mobileUserIdResponse : mobileUserIdResponses) {
            if (mobileUserIdResponse != null && mobileUserIdResponse.getUserId() == userId) {
                return false;
            }
        }
        return true;
    }

    /**
     * 1，校验各种参数
     * 1.1，用户id是否在黑名单
     * 1.2，调用风控服务判断用户是否可以发起
     * 1.3，UserInfoModel是否存在
     * 1.4，cfInfoBaseVo和infoFragmentVo不为空
     * 1.5，手机号不为空并且可以根据手机号找到对应用户
     * 2，过风控的各种逻辑
     * 2.1，短时间内不允许用户发起三个以上的案例
     * 2.2，标题过敏感词
     * 2.3，内容过敏感词
     * 3，校验各种参数
     * 3.1，title不能为空且不能大于60个字
     * 3.2，content不能为空
     * 3.3，筹款金额不能小于0或者大于9999999
     * 3.4，照片附件数量不得大于8
     * <p>
     * 这是之前在CrowdfundingV4InfoUpdateController中的逻辑，目前不做优化，先迁移到这里，以去除CrowdfundingV4InfoUpdateController中的重复代码
     * <p>
     * 4,初审材料校验部分
     * 4.1 后端校验发起人姓名中是否有数字、字母（后端新增），有的话提示：请正确填写发起人姓名，不能带有数字或字母
     * 4.2 后端校验患者姓名中是否有数字、字母（后端新增），有的话提示：请正确填写患者姓名，不能带有数字或字母
     * 4.2
     *
     * @return
     */
    @Override
    public OpResult onValidate(CrowdfundingInfoBaseVo cfInfoBaseVo, long userId, HitMomentEnum hitMomentEnum) {
        String diseaseName = Optional.ofNullable(cfInfoBaseVo)
                .map(CrowdfundingInfoBaseVo::getContentV2)
                .map(CrowdfundingSeekHelpInfoFragmentVo::getDiseaseName)
                .orElse("");
        OpResult baseInfoResult = verifyBaseInfo(cfInfoBaseVo, userId, hitMomentEnum, diseaseName);
        if (baseInfoResult.isFail()) {
            return baseInfoResult;
        }

        OpResult firstApproveResult = verifySubFirstApprove(cfInfoBaseVo, userId);
        if (firstApproveResult.isFail()) {
            return firstApproveResult;
        }

        return OpResult.createSucResult();
    }

    @Override
    public OpResult verifyBaseInfo(CrowdfundingInfoBaseVo cfInfoBaseVo, long userId, HitMomentEnum hitMomentEnum, String diseaseName) {
        if (userId <= 0) {
            log.warn("CrowdfundingV4InfoUpdateController addOrUpdateBaseInfo userId is {}", userId);
            return OpResult.createFailResult(CfErrorCode.CF_USER_ACCOUNT_NO_LOGIN);
        }

        if (!allowRaiseCase(userId)) {
            log.info("当前用户无法发起案例 userId:{}", userId);
            return OpResult.createFailResult(CfErrorCode.RISK_RAISE_PERMITTED);
        }

        boolean canRaise = darkListService.checkCanRaise(cfInfoBaseVo, userId);
        if (!canRaise) {
            log.info("verify dark list 用户某信息已被拉黑名单 userId:{}", userId);
            return OpResult.createFailResult(CfErrorCode.BLACKLIST_FUNDRAISING_FILTER);
        }

        //调用风控服务判断用户是否可以发起
        if (!cfRiskService.operatorValid(userId, 0, UserOperationEnum.RAISE)) {
            return OpResult.createFailResult(CfErrorCode.RISK_RAISE_PERMITTED);
        }

        UserInfoModel userInfoModel = this.userInfoDelegate.getUserInfoByUserId(userId);
        if (userInfoModel == null) {
            return OpResult.createFailResult(CfErrorCode.CF_USER_ACCOUNT_NO_LOGIN);
        }

        if (StringUtils.isBlank(userInfoModel.getCryptoMobile())) {
            return OpResult.createFailResult(CfErrorCode.CF_MOBILE_NEEDED);
        }

        boolean newCase = false;
        if (StringUtils.isBlank(cfInfoBaseVo.getInfoUuid())) {
            newCase = true;
            try {
                boolean inMobileBlack = this.cfBlackListBiz
                        .isInMobileBlack(shuidiCipher.decrypt(userInfoModel.getCryptoMobile()));
                if (inMobileBlack) {
                    return OpResult.createFailResult(CfErrorCode.CF_ERROR_IN_BLACK_LIST);
                }
            } catch (Exception e) {
                log.error("", e);
            }
        }

        /////////////////////////////////////////////
        //风控需求: 过去某段时间发起超过几次，则不允许再发起
        /////////////////////////////////////////////
        if (newCase && this.raiseBlockTimesThreshold > 0) {
            List<CrowdfundingInfoView> cases = this.crowdfundingInfoBiz.getByUserId(userId);
            if (!org.apache.commons.collections.CollectionUtils.isEmpty(cases) &&
                    cases.size() >= this.raiseBlockTimesThreshold) {
                CrowdfundingInfoView crowdfundingInfoView = cases.get(cases.size() - this.raiseBlockTimesThreshold);
                Timestamp timeThresHold =
                        DateUtil.addHours(new Timestamp(System.currentTimeMillis()), -this.raiseBlockTimeScope);
                if (crowdfundingInfoView.getCreateTime().after(new Date(timeThresHold.getTime()))) {

                    log.info("CF_RAISE_TOO_MUCH {} of {} at {}", this.raiseBlockTimesThreshold,
                            crowdfundingInfoView.getId(), crowdfundingInfoView.getCreateTime());

                    return OpResult.createFailResult(CfErrorCode.CF_RAISE_TOO_MUCH);
                }
            }
        }

        boolean userInWhileList = userInWhileList(userId);

        if (!userInWhileList) {
            //过滤标题
            String title = cfInfoBaseVo.getTitle();
            RiskWordResultV2 titleResult = riskControlWordHelpService.isHitRaiseBanneredV1(title, RiskControlWordCategoryDO.RiskWordUseScene.CASE_RAISE_TITLE,
                    hitMomentEnum.getCode(), HitLocationEnum.TITLE.getCode(), tracerService.getSpanId(), userId, title, cfInfoBaseVo.getContent(), diseaseName);

            if (!titleResult.isPassed()) {
                sendHitNotice(userInfoModel, title, titleResult, cfInfoBaseVo, hitMomentEnum, HitLocationEnum.TITLE, diseaseName);
                return OpResult.createFailResult(CfErrorCode.CF_RAISE_TITLE_NOT_ALLOWED);
            }

            //过滤内容
            String content = cfInfoBaseVo.getContent();
            RiskWordResultV2 contentResult = riskControlWordHelpService.isHitRaiseBanneredV1(content, RiskControlWordCategoryDO.RiskWordUseScene.CASE_RAISE_ARTICLE,
                    hitMomentEnum.getCode(), HitLocationEnum.COMMENT.getCode(), tracerService.getSpanId(), userId, title, cfInfoBaseVo.getContent(), diseaseName);

            if (!contentResult.isPassed()) {
                sendHitNotice(userInfoModel, content, contentResult, cfInfoBaseVo, hitMomentEnum, HitLocationEnum.COMMENT, diseaseName);
                return OpResult.createFailResult(CfErrorCode.CF_RAISE_CONTENT_NOT_ALLOWED);
            }
        }

        Integer targetAmount = cfInfoBaseVo.getTargetAmount();
        String title = cfInfoBaseVo.getTitle();
        String content = cfInfoBaseVo.getContent();
        List<String> attachments = cfInfoBaseVo.getAttachments();
        if (StringUtils.isBlank(title) || StringUtils.isBlank(content) || targetAmount == null
                || targetAmount <= 0) {
            return OpResult.createFailResult(CfErrorCode.CF_INFO_PARAM_ERROR_BASE);
        }
        if (StringUtils.length(title) > 60) {
            return OpResult.createFailResult(CfErrorCode.CF_TITLE_TOO_LONG);
        }
        if (StringUtils.isEmpty(cfInfoBaseVo.getInfoUuid())) {
            if (targetAmount > CrowdfundingCons.MAX_TARGET_AMOUNT) {
                return OpResult.createFailResult(CfErrorCode.CF_PARAM_ERROR_TARGET_AMOUNT);
            }
        } else {
            CfInfoSimpleModel simpleModel = simpleBiz.getFundingInfo(cfInfoBaseVo.getInfoUuid());
            int caseId = simpleModel.getId();
            Optional<CfCaseUpdateAmountRecord> optional = updateAmountRecordService.getByCaseId(caseId);
            if (optional.isPresent()) {
                if (targetAmount > optional.get().getTargetAmount() / 100) {
                    return OpResult.createFailResult(CfErrorCode.CF_TARGET_AMOUNT_MORE_THAN_ZERO);
                }
            } else {
                if (targetAmount > CrowdfundingCons.MAX_TARGET_AMOUNT) {
                    return OpResult.createFailResult(CfErrorCode.CF_PARAM_ERROR_TARGET_AMOUNT);
                }
            }
        }

        if (CollectionUtils.size(attachments) > 16) {
            return OpResult.createFailResult(CfErrorCode.CF_INFO_PARAM_ERROR_PICS_OVERMUCH);
        }
        return OpResult.createSucResult();
    }

    @Override
    public OpResult verifySubFirstApprove(CrowdfundingInfoBaseVo cfInfoBaseVo, long userId) {

        /** 4,初审材料校验部分
         * 4.1 后端校验发起人姓名中是否有数字、字母（后端新增），有的话提示：请正确填写发起人姓名，不能带有数字或字母
         * 4.2 后端校验患者姓名中是否有数字、字母（后端新增），有的话提示：请正确填写患者姓名，不能带有数字或字母
         */
        if (StringUtils.isNotEmpty(cfInfoBaseVo.getSelfRealName()) && !CfIdCardUtil.containSpaceNumberCharacter(cfInfoBaseVo.getSelfRealName())) {
            return OpResult.createFailResult(CfErrorCode.CF_RAISER_NAME_ERROR);
        }

        if (StringUtils.isNotEmpty(cfInfoBaseVo.getPatientRealName())) {
            if (cfInfoBaseVo.getPatientIdType() == UserIdentityType.identity.getCode()) {
                //有身份证,验数字+字母
                if (!CfIdCardUtil.containSpaceNumberCharacter(cfInfoBaseVo.getPatientRealName())) {
                    return OpResult.createFailResult(CfErrorCode.CF_PAITENT_NAME_ERROR);
                }
            } else if (cfInfoBaseVo.getPatientIdType() == UserIdentityType.birth.getCode()) {
                //只有出生卡，只验数字
                if (!CfIdCardUtil.containSpaceNumber(cfInfoBaseVo.getPatientRealName())) {
                    return OpResult.createFailResult(CfErrorCode.CF_PAITENT_NAME_NUMBER_ERROR);
                }
            }
        }

        return OpResult.createSucResult();
    }

    @org.jetbrains.annotations.NotNull
    private Set<String> getWhiteList() {
        Set<String> userids;
        userids = new HashSet<>();
        String[] ids = new String[0];
        String kv = cfRedisKvBiz.queryValueByKey("filteruserids");
        if (!Strings.isNullOrEmpty(kv)) {
            ids = kv.split(",");
        }
        for (int i = 0; i < ids.length; i++) {
            userids.add(ids[i]);
        }
        return userids;
    }

    private boolean userInWhileList(long userId) {
        boolean userInWhileList = false;
        Response<Boolean> inWhileList = cfUgcWhileListClient.inWhileList(userId);
        if (inWhileList.ok()) {
            userInWhileList = Optional.ofNullable(inWhileList.getData()).orElse(false);
        }
        return userInWhileList;
    }


    @Override
    public OpResult checkCharacter(String title, String content, long userId, HitMomentEnum hitMomentEnum) {

        UserInfoModel userInfoModel = this.userInfoDelegate.getUserInfoByUserId(userId);
        log.warn("CrowdfundingV4InfoUpdateController addOrUpdateBaseInfo userInfoModel is {}", userInfoModel);
        if (userInfoModel == null) {
            return OpResult.createFailResult(CfErrorCode.CF_USER_ACCOUNT_NO_LOGIN);
        }

        boolean userInWhileList = userInWhileList(userId);


        if (userInWhileList) {
            return OpResult.createSucResult();
        }

        if (StringUtils.isNotEmpty(title)) {

            //过滤标题
            RiskWordResultV2 titleResult = riskControlWordHelpService.isHitRaiseBanneredV1(title, RiskControlWordCategoryDO.RiskWordUseScene.CASE_RAISE_TITLE,
                    hitMomentEnum.getCode(), HitLocationEnum.TITLE.getCode(), tracerService.getSpanId(), userId, title, content, "");
            if (!titleResult.isPassed()) {
                sendHitNotice(userInfoModel, title, titleResult, title, content, hitMomentEnum, HitLocationEnum.TITLE, "");
                return OpResult.createFailResult(CfErrorCode.CF_RAISE_TITLE_NOT_ALLOWED);
            }
        }


        if (StringUtils.isNotEmpty(content)) {

            //过滤内容
            RiskWordResultV2 contentResult = riskControlWordHelpService.isHitRaiseBanneredV1(content, RiskControlWordCategoryDO.RiskWordUseScene.CASE_RAISE_ARTICLE,
                    hitMomentEnum.getCode(), HitLocationEnum.COMMENT.getCode(), tracerService.getSpanId(), userId, title, content, "");
            if (!contentResult.isPassed()) {
                sendHitNotice(userInfoModel, content, contentResult, title, content, hitMomentEnum, HitLocationEnum.COMMENT, "");
                return OpResult.createFailResult(CfErrorCode.CF_RAISE_CONTENT_NOT_ALLOWED);
            }
        }

        return OpResult.createSucResult();
    }

    private void sendHitNotice(@NotNull UserInfoModel userInfoModel, String content, RiskWordResultV2 hitResult, CrowdfundingInfoBaseVo cfInfoBaseVo,
                               HitMomentEnum hitMomentEnum, HitLocationEnum hitLocationEnum, String diseaseName) {
        sendHitNotice(userInfoModel, content, hitResult, cfInfoBaseVo.getTitle(), cfInfoBaseVo.getContent(), hitMomentEnum, hitLocationEnum, diseaseName);
    }


    private void sendHitNotice(@NotNull UserInfoModel userInfoModel, String content, RiskWordResultV2 hitResult, String caseTitle, String caseContent, HitMomentEnum hitMomentEnum, HitLocationEnum hitLocationEnum, String diseaseName) {
        //发企业微信报警
        SendHitNoticeToAlarmParam sendHitNoticeToAlarmParam = SendHitNoticeToAlarmParam.builder()
                .userInfoModel(userInfoModel)
                .hitResult(hitResult)
                .caseTitle(caseTitle)
                .caseContent(caseContent)
                .desc(hitMomentEnum.getEnumDesc(hitMomentEnum.getCode()))
                .location(hitLocationEnum.getEnumDesc(hitLocationEnum.getCode()))
                .build();
        if (StringUtils.isNotEmpty(diseaseName)) {
            sendHitNoticeToAlarmParam.setDiseaseName(diseaseName);
        }
        sendHitNoticeToAlarm(sendHitNoticeToAlarmParam);
    }

    private String tagKeyWord(RiskWordResultV2 hitResult, String content) {
        String title = null;
        for (String hitWords : hitResult.getHitWords()) {
            title = content.replaceAll(hitWords, "<font color='red'>" + hitWords + "</font>");
        }
        return title;
    }


    @Override
    public OpResult<Void> onValidateV2(CrowdfundingInfoBaseVo cfInfoBaseVo, long userId) {

        Integer targetAmount = cfInfoBaseVo.getTargetAmount();
        String title = cfInfoBaseVo.getTitle();
        String content = cfInfoBaseVo.getContent();
        List<String> attachments = cfInfoBaseVo.getAttachments();
        if (StringUtils.isBlank(title) || StringUtils.isBlank(content) || targetAmount == null
                || targetAmount <= 0) {
            return OpResult.createFailResult(CfErrorCode.CF_INFO_PARAM_ERROR_BASE);
        }
        if (StringUtils.length(title) > 60) {
            return OpResult.createFailResult(CfErrorCode.CF_TITLE_TOO_LONG);
        }

        Optional<CfCaseUpdateAmountRecord> optional = Optional.empty();
        if (StringUtils.isNotEmpty(cfInfoBaseVo.getInfoUuid())) {
            CfInfoSimpleModel simpleModel = simpleBiz.getFundingInfo(cfInfoBaseVo.getInfoUuid());
            int caseId = simpleModel.getId();
            optional = updateAmountRecordService.getByCaseId(caseId);
        }
        if (optional.isPresent()) {
            if (targetAmount > optional.get().getTargetAmount() / 100) {
                return OpResult.createFailResult(CfErrorCode.CF_TARGET_AMOUNT_MORE_THAN_ZERO);
            }
        } else {
            if (targetAmount > CrowdfundingCons.MAX_TARGET_AMOUNT) {
                return OpResult.createFailResult(CfErrorCode.CF_PARAM_ERROR_TARGET_AMOUNT);
            }
        }

        if (CollectionUtils.size(attachments) > 8) {
            return OpResult.createFailResult(CfErrorCode.CF_INFO_PARAM_ERROR_PICS_OVERMUCH);
        }

        return OpResult.createSucResult();
    }

    /**
     * 添加筹款信息
     *
     * @param infoBaseVo
     * @param clientIp
     * @return
     */
    @Override
    public CrowdfundingInfoResponse addBaseInfo(CrowdfundingInfoBaseVo infoBaseVo, String clientIp) {
        //检查用户是否扫描过志愿者二维码
        infoBaseVo.setVolunteerUniqueCode("");
        CheckUserScanOrInvitedResultModel checkUserScanResultModel = cfGrowthtoolDelegate.checkUserScan(infoBaseVo.getUserId(), infoBaseVo.getInfoUuid());
        int checkUserScan = checkUserScanResultModel.getResultCode();
        if (StringUtils.isNotBlank(checkUserScanResultModel.getVolunteerUniqueCode())) {
            infoBaseVo.setChannel(checkUserScanResultModel.getChannel());
            infoBaseVo.setVolunteerUniqueCode(checkUserScanResultModel.getVolunteerUniqueCode());
        }
        // 检查用户是否被志愿者链接邀请过
        CheckUserScanOrInvitedResultModel checkUserIsInviteResultModel = cfGrowthtoolDelegate.checkUserIsInvite(infoBaseVo.getUserId(), checkUserScan);
        checkUserScan = checkUserIsInviteResultModel.getResultCode();
        if (StringUtils.isNotBlank(checkUserIsInviteResultModel.getVolunteerUniqueCode())) {
            infoBaseVo.setChannel(checkUserIsInviteResultModel.getChannel());
            infoBaseVo.setVolunteerUniqueCode(checkUserIsInviteResultModel.getVolunteerUniqueCode());
        }
        // 检查用户是否被筹款人邀请过
        CheckUserScanOrInvitedResultModel checkUserIsInviteByCrowdfundUserResultModel = cfGrowthtoolDelegate.checkUserIsInviteByCrowdfundUser(infoBaseVo.getUserId(), checkUserScan);
        checkUserScan = checkUserIsInviteByCrowdfundUserResultModel.getResultCode();
        if (StringUtils.isNotBlank(checkUserIsInviteByCrowdfundUserResultModel.getVolunteerUniqueCode())) {
            infoBaseVo.setChannel(checkUserIsInviteByCrowdfundUserResultModel.getChannel());
            infoBaseVo.setVolunteerUniqueCode(checkUserIsInviteByCrowdfundUserResultModel.getVolunteerUniqueCode());
        }
        //校验下是否兼职
        CfPartnerInfoDo cfPartnerInfoDo = null;
        if (StringUtils.isNotBlank(infoBaseVo.getVolunteerUniqueCode())) {
            cfPartnerInfoDo = cfGrowthtoolDelegate.getCfPartnerInfoByUniqueCode(infoBaseVo.getVolunteerUniqueCode());
            //是线下兼职
            if (Objects.nonNull(cfPartnerInfoDo)) {
                infoBaseVo.setVolunteerUniqueCode(cfPartnerInfoDo.getLeaderUniqueCode());
            }
        }

        //筹款基本信息
        CrowdfundingInfo crowdfundingInfo = build(infoBaseVo);
        String infoUuid = crowdfundingInfo.getInfoId();

        UserThirdModel userThirdModel = userThirdDelegate.getThirdModelWithUserId(infoBaseVo.getUserId(),
                WxConstants.FUNDRAISER_THIRD_TYPE);

        UserInfoModel userInfoModel = userInfoDelegate.getUserInfoByUserId(infoBaseVo.getUserId());

        if (userInfoModel == null) {
            CrowdfundingInfoResponse response = new CrowdfundingInfoResponse();
            response.setErrorCode(CfErrorCode.CF_ADD_BASE_INFO_ERROR);
            response.setInfoUuid(crowdfundingInfo.getInfoId());
            return response;
        }

        //1, 添加基本信息
        CreateInfoResultWrap resultWrap = crowdfundingCreateService.createInfo(crowdfundingInfo, infoBaseVo, clientIp);

        //地区免手续费
        cfNoHandlingFeeBiz.getAreaNoHandlingFeeFree(resultWrap.getInfoExt());
        // 用户是否需要填写药物情况
        fillUserDrugFlag(crowdfundingInfo, infoBaseVo);
        createSyncMaterialCenter(resultWrap, infoBaseVo);

        CrowdfundingInfoResponse response = CreateInfoResultWrap.convertToInfoResp(resultWrap);
        if (response != null && response.getErrorCode() != CfErrorCode.SUCCESS) {
            return response;
        }

        //记录兼职案例绑定信息
        if (Objects.nonNull(cfPartnerInfoDo)) {
            CfPartnerCaseRelationDo cfPartnerCaseRelationDo = new CfPartnerCaseRelationDo();
            cfPartnerCaseRelationDo.setUniqueCode(cfPartnerInfoDo.getUniqueCode());
            cfPartnerCaseRelationDo.setCaseId(crowdfundingInfo.getId());
            cfPartnerCaseRelationDo.setLeaderUniqueCode(cfPartnerInfoDo.getLeaderUniqueCode());
            cfGrowthtoolDelegate.createPartnerCaseRelation(cfPartnerCaseRelationDo);
        }

        if (checkUserScan == 3) {
            try {
                //判断是否是在筹人员
                CrowdfundingInfo sourceCf = crowdfundingInfoBiz.getLastNoFinished(Long.valueOf(infoBaseVo.getVolunteerUniqueCode()));
                List<CrowdfundingInfo> views = crowdfundingInfoBiz.getCrowdfundingInfoListByUserId(Long.parseLong(infoBaseVo.getVolunteerUniqueCode()));
                Optional<Integer> lastAvailableCase = views.stream()
                        .map(CrowdfundingInfo::getId)
                        .distinct()
                        .sorted(Integer::compareTo)
                        .filter(item -> cfRiskService.operatorValid(0, item, UserOperationEnum.ORDER))
                        .findFirst();
                log.debug("判断是否是在筹人员raiser:{},InvitorTypeEnum.raiser:{}", lastAvailableCase.isPresent(), CfToufangInviteCaseRelationDO.InvitorTypeEnum.raiser.getCode());
                //对于老带新来源的记录单独的邀请记录
                CfToufangInviteCaseRelationDO caseRelationDO = new CfToufangInviteCaseRelationDO();
                caseRelationDO.setInfoUuid(crowdfundingInfo.getInfoId());
                caseRelationDO.setCreateTime(new Date());
                caseRelationDO.setUpdateTime(caseRelationDO.getCreateTime());
                caseRelationDO.setInvitorUniqueCode(infoBaseVo.getVolunteerUniqueCode());
                caseRelationDO.setInvitorType(lastAvailableCase.isPresent() ? CfToufangInviteCaseRelationDO.InvitorTypeEnum.raiser.getCode() : CfToufangInviteCaseRelationDO.InvitorTypeEnum.normal.getCode());
                caseRelationDO.setInvitorCaseId(lastAvailableCase.orElse(0));
                cfToufangInviteService.addInviteRelation(caseRelationDO);
            } catch (Exception e) {
                log.error("老带新活动记录插入异常", e);
            }
        }
        //  用户扫描志愿者二维码发起后  通知cf-clewtrack 建立bdcrm线索
        registerClewFacadeImpl.sendMq2CfClewtrack(crowdfundingInfo, infoBaseVo.getVolunteerUniqueCode(), clientIp, cfPartnerInfoDo);
        response.setErrorCode(CfErrorCode.SUCCESS);
        response.setInfoUuid(infoUuid);

        // onApplicationEvent 事件触发的方法 ，可以失败
        //0）CaseCreateTraceListener  创建筹款案例上报数据
        //1）CreateRiskListener  创建风险对应记录
        //2) UserTagClientListener 存储userTagHistory
        //3) CfInfoStatListener 扩展信息:添加筹款信息统计
        //4) CfInfoTaskListener 扩展信息:创建筹款任务

        //5) TouchSetCannotShareOrDonateListener 禁止案例转发、捐款
        //6)  pin
        //7) CaseCouldShareAndDonateListener 案例可以转发的时候
        //8) sendAddTagMQ
        //9) CaseBeginFundingTraceListener 筹款开始案例上报
        //10) PromoteRiskListener  发起AI检测内容风险
        //12) SaveOriginInfoForCaseListener  异步推断来源
        //13) CaseSendMQListener  新建案例 发送消息 用于重复二次案例的判断
        //14) AddBaseAfterListener
        //15) ServiceLogListener
        //16) UserLabelListener
        //17) CaseDepositAccountCreateListener  案例创建成功后注册托管账户
        try {
            //1.5保存筹款基本信息后需要发的MQ
            cfStatus4MQBiz.sendStatusMQ(crowdfundingInfo, CfBaseStatus.cf_doing);

            Message message = new Message<>(MQTopicCons.CF,
                    MQTagCons.CF_BASE_INFO_CHANGE_TAG, MQTagCons.CF_BASE_INFO_CHANGE_TAG + "-" +
                    crowdfundingInfo.getId()
                    + "-" + System.currentTimeMillis(), crowdfundingInfo.getId(), DelayLevel.S5);

            crowdFundingAddPublisher.addCrowdFunding(infoBaseVo, crowdfundingInfo,
                    clientIp, userThirdModel, message, userInfoModel);

            msgClientService.sendCasePatientChangeMsg(crowdfundingInfo.getId());
        } catch (Exception e) {
            log.error("onApplicationEvent error:", e);
        }
        //如果是先审后发，则在此处不发消息
        if (StringUtils.isEmpty(infoBaseVo.getSelfRealName())
                && StringUtils.isEmpty(infoBaseVo.getPatientRealName())
        ) {
            raiseSuccessNoticeService.sendMessage(crowdfundingInfo);
        }
        // 获取当前环境，并发送mq
        String activeProfile = applicationService.getActiveProfile();

        CaseLabelPayload caseLabelPayload = new CaseLabelPayload();
        caseLabelPayload.setCaseId(crowdfundingInfo.getId());
        caseLabelPayload.setActiveProfile(activeProfile);
        String jsonString = JSON.toJSONString(caseLabelPayload);
        String key = "case_label" + crowdfundingInfo.getId();

        Message message = new Message(MQTopicCons.CF, MQTagCons.CF_CASE_LABEL, key, jsonString);
        commonMessageHelperService.send(message);
        log.info("send profile message,key:profile,activeProfile:{}", activeProfile);
        return response;
    }


    private CrowdfundingInfo build(CrowdfundingInfoBaseVo infoBaseVo) {
        String infoId = UUID.randomUUID().toString();
        //筹款基本信息
        CrowdfundingInfo info = new CrowdfundingInfo();
        info.setInfoId(infoId);
        info.setUserId(infoBaseVo.getUserId());//发起筹款人用户ID
        info.setContentType(infoBaseVo.getContentType().getValue());//求助说明的内容类型，0：整个文章 1: 分段填写的

        CrowdfundingChannelTypeEnum channelTypeEnum = CrowdfundingChannelTypeEnum.individual;
        String channelTypeName = infoBaseVo.getChannelTypeName();
        if (StringUtils.isNotBlank(channelTypeName)) {
            channelTypeEnum = CrowdfundingChannelTypeEnum.fromTypeName(channelTypeName);
        }
        info.setChannelType(channelTypeEnum);//收款委托渠道: organization 基金会渠道, individual  个人求助
        info.setTitle(infoBaseVo.getTitle());//筹款标题
        info.setContent(sensitiveProcessorService.process(infoBaseVo.getContent()));//筹款文字内容
        info.setEncryptContent(oldShuidiCipher.aesEncrypt(infoBaseVo.getContent()));
        info.setTargetAmount(infoBaseVo.getTargetAmount() * 100);//筹款金额,单位:分
        info.setChannel(infoBaseVo.getChannel());//渠道
        String titleImg = "";//首图地址
        List<String> attachments = infoBaseVo.getAttachments();
        if (attachments != null && attachments.size() > 0) {
            titleImg = attachments.get(0);
        }
        info.setTitleImg(titleImg);
        info.setBeginTime(new Date());
        info.setEndTime(DateUtils.addDays(new Date(), 30));
        CrowdfundingRelationType relationType = CrowdfundingRelationType.empty;//发起人和受助人的关系
        if (infoBaseVo.getRelationType() != null) {
            relationType = infoBaseVo.getRelationType();
        }
        info.setRelationType(relationType);//发起人和受助人的关系枚举
        info.setRelation(relationType.getDescription());
        info.setType(CrowdfundingType.SERIOUS_ILLNESS.value());//缺省为大病
        info.setCreateTime(new Date());
        info.setPayeeMobile("");//收款人联系电话
        info.setPayeeName(infoBaseVo.getPayeeName());//收款人真实姓名

        return info;
    }


    @Override
    public CrowdfundingInfoResponse updateBaseInfo(CrowdfundingInfoBaseVo infoBaseVo) {
        infoBaseVo.setVolunteerUniqueCode("");
        CheckUserScanOrInvitedResultModel checkUserScanResultModel = cfGrowthtoolDelegate.checkUserScan(infoBaseVo.getUserId(), infoBaseVo.getInfoUuid());
        if (StringUtils.isNotBlank(checkUserScanResultModel.getVolunteerUniqueCode())) {
            infoBaseVo.setChannel(checkUserScanResultModel.getChannel());
            infoBaseVo.setVolunteerUniqueCode(checkUserScanResultModel.getVolunteerUniqueCode());
        }
        long userId = infoBaseVo.getUserId();
        String infoUuid = infoBaseVo.getInfoUuid();
        CrowdfundingInfoResponse response = new CrowdfundingInfoResponse();
        response.setInfoUuid(infoUuid);
        CrowdfundingInfo crowdfundingInfo = this.crowdfundingInfoBiz.getFundingInfo(infoUuid);
        int caseId = crowdfundingInfo.getId();
        //若当前案例处于已提交或者是审核通过状态(2,4),不让用户更新
        if (crowdfundingInfo.getStatus() == CrowdfundingStatus.SUBMITTED ||
                crowdfundingInfo.getStatus() == CrowdfundingStatus.CROWDFUNDING_STATED) {
            log.info("addOrUpdateBaseInfo info status is:{} infoUuid:{}", crowdfundingInfo.getStatus().value(), infoUuid);
            response.setErrorCode(CfErrorCode.CF_INFO_SUBMITTED);
            response.setInfoUuid(crowdfundingInfo.getInfoId());
            return response;
        }
        CfErrorCode verifyResult = verifyInfoId(crowdfundingInfo, userId);
        if (!verifyResult.equals(CfErrorCode.SUCCESS)) {
            response.setErrorCode(verifyResult);
            return response;
        }

        // 1,镜像信息 before
        CfOperatingRecord cfOperatingRecord = this.cfInfoMirrorService.beforeNew(crowdfundingInfo,
                crowdfundingInfo.getUserId(), crowdfundingInfo.getPayeeName(),
                CfOperatingRecordEnum.Type.SUBMIT_BASE, CfOperatingRecordEnum.Role.USER);
        cfOperatingRecord.setTwModifyChannel(infoBaseVo.getTwModifyChannel());
        if (cfOperatingRecord == null) {
            log.warn("镜像信息beforeNew return null");
            response.setErrorCode(CfErrorCode.DEFAULT);
            return response;
        }

        // 2，更新
        crowdfundingUpdateService.updateBaseInfo(infoBaseVo, crowdfundingInfo);

        // 3，镜像信息after
        cfInfoMirrorService.afterNew(cfOperatingRecord);
        try {
            Message message = new Message(MQTopicCons.CF, MQTagCons.CF_UPDATE_BASE_INFO_MSG, MQTagCons.CF_UPDATE_BASE_INFO_MSG + "_" + cfOperatingRecord.getId(), cfOperatingRecord);
            // 1, SubmitForReviewListener 提交审核（全部三项都保存之后才可以提交审核）
            // 2, PromoteRiskListener 更新基本信息风险
            // 3, CaseSendMQListener 发送mq
            crowdFundingUpdatePublisher.updateCrowdFunding(infoBaseVo, crowdfundingInfo, message);

        } catch (Exception e) {
            log.error(" updateCrowdFunding onApplicationEvent error:", e);
        } finally {
            response.setErrorCode(CfErrorCode.SUCCESS);
            response.setExtra(null);
        }
        // 初审图文信息修改
        initialAuditSubmitService.onBaseInfoSubmit(caseId);

        /*
        根据初审状态返回不同msg
        1.初审通过前：填写完图文信息点击提交出的弹窗文案：筹款图文已经修改完成（现状，保持不变）
        2.初审通过后用户再修改图文：填写完图文信息点击提交出的弹窗文案：修改信息预计15分钟后，在案例详情页公示，请耐心等待
         */
        CfInfoExt ext = cfInfoExtBiz.getByCaseId(caseId);
        boolean isSuccess = response.getErrorCode() == CfErrorCode.SUCCESS;
        if (isSuccess) {
            boolean firstApprovePassed = FirstApproveStatusEnum.isPassed(FirstApproveStatusEnum.parse(ext.getFirstApproveStatus()));
            response.setMsg(firstApprovePassed ? "修改信息预计15分钟后，在案例详情页公示，请耐心等待。" : "筹款图文已经修改完成");
        }

        return response;
    }

    @Override
    public CrowdfundingInfoResponse addOrUpdate(CrowdfundingInfoBaseVo cfInfoBaseVo, String clientIP) {

        // 指定身份账号的不允许发起
        CrowdfundingInfoResponse response = this.checkIdCard(cfInfoBaseVo);
        if (null != response && !CfErrorCode.SUCCESS.equals(response.getErrorCode())) {
            log.info("身份证检查不过");
            return response;
        }
        if (StringUtils.isBlank(cfInfoBaseVo.getInfoUuid())) {
            response = addBaseInfo(cfInfoBaseVo, clientIP);
        } else {
            response = updateBaseInfo(cfInfoBaseVo);
        }
        return response;
    }

    @Override
    public void saveBaseInfoTemplateRecord(CrowdfundingInfoBaseVo cfInfoBaseVo, CrowdfundingInfoResponse response) {
        try {
            CfBaseInfoTemplateRecord cfBaseInfoTemplateRecord = cfInfoBaseVo.getCfBaseInfoTemplateRecord();
            if (cfBaseInfoTemplateRecord != null) {
                crowdfundingInfoService.saveBaseInfoTemplateRecord(cfBaseInfoTemplateRecord, response.getInfoUuid());
            }
        } catch (Exception e) {
            log.error("CrowdfundingV4InfoUpdateController addOrUpdateBaseInfo save base info record error!", e);
        }
    }

    /**
     * 校验前置材料
     *
     * @param userId
     * @param cfInfoBaseVo
     * @return
     */
    @Override
    public Response<Void> validateMaterial(long userId, CrowdfundingInfoBaseVo cfInfoBaseVo) {
        // 检查数据正确性
        boolean patientHasIdCard = cfInfoBaseVo.isPatientHasIdCard();
        int patientIdType = cfInfoBaseVo.getPatientIdType();
        // 若患者证件类型类出生证 则 不允许 patientHasIdCard为1
        if (UserIdentityType.getCode(UserIdentityType.birth) == patientIdType
                && patientHasIdCard) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }

        // 发起人年龄校验 需要成年
        Response<Void> checkRaiseAgeResp = checkRaiseAge(cfInfoBaseVo);
        if (checkRaiseAgeResp.notOk()) {
            return checkRaiseAgeResp;
        }

        // 考虑 type为0的旧数据 只使用 ==
        if (UserIdentityType.getCode(UserIdentityType.birth) == patientIdType) {
            cfInfoBaseVo.setPatientIdCard("");
        }

        if (UserIdentityType.getCode(UserIdentityType.identity) == patientIdType) {
            cfInfoBaseVo.setPatientBornCard("");
        }

        //身份证不足18位不让提交（过滤掉15位的身份证）
        if (StringUtils.isNotBlank(cfInfoBaseVo.getPatientIdCard()) && !CfIdCardUtil.isValidIdCard(cfInfoBaseVo.getPatientIdCard())) {
            return NewResponseUtil.makeError(CfErrorCode.ADD_CROWDFUNDING_IDCARD_ERROR_OTHER);
        }

        if (StringUtils.isNotBlank(cfInfoBaseVo.getSelfIdCard()) && !CfIdCardUtil.isValidIdCard(cfInfoBaseVo.getSelfIdCard())) {
            return NewResponseUtil.makeError(CfErrorCode.ADD_CROWDFUNDING_IDCARD_ERROR_SELF);
        }

        // 选择是贫困 必须上传贫困证明
        Integer poverty = cfInfoBaseVo.getPoverty();
        if (poverty != null && poverty == 1 && StringUtils.isEmpty(cfInfoBaseVo.getPovertyImageUrl())) {
            return NewResponseUtil.makeError(CfErrorCode.ADD_CROWDFUNDING_FIRST_APPROVE_POVERTY_IMAGE_EMPTY);
        }

        // 这里前端上完后在加上等号
        if (cfInfoBaseVo.getTargetAmount() != null) {

            if (cfInfoBaseVo.getTargetAmount() <= 0) {
                return NewResponseUtil.makeError(CfErrorCode.CF_TARGET_AMOUNT_MORE_THAN_ZERO);
            }

            if (cfInfoBaseVo.getTargetAmount() < CfFirstApproveBiz.TARGET_AMOUT_NEED_DESC
                    && StringUtils.isNotEmpty(cfInfoBaseVo.getTargetAmountDesc())) {
                return NewResponseUtil.makeError(CfErrorCode.USER_NO_NEED_INPUT_TARGET_AMOUNT_DESC);
            }
        }

        crowdfundingInfoService.trimEmptyChar(cfInfoBaseVo);

        // 首次提交才会走身份校验
        if (StringUtils.isEmpty(cfInfoBaseVo.getInfoUuid())) {
            if (StringUtils.isEmpty(cfInfoBaseVo.getPreAuditImageUrl())) {
                return NewResponseUtil.makeError(CfErrorCode.ADD_CROWDFUNDING_FIRST_APPROVE_IMAGE_EMPTY);
            }
            CfErrorCode firstApproveResult = cfFirstApproveBiz.firstApprove(cfInfoBaseVo, userId);
            if (firstApproveResult != CfErrorCode.SUCCESS) {
                return NewResponseUtil.makeError(firstApproveResult);
            }
        }
        return null;
    }


    private Response<Void> checkRaiseAge(CrowdfundingInfoBaseVo cfInfoBaseVo) {
        if (!checkAge) {
            return NewResponseUtil.makeSuccess(null);
        }

        UserRelTypeEnum relType = cfInfoBaseVo.getRelType();
        if (relType == null) {
            log.info("relType null");
            return NewResponseUtil.makeSuccess(null);
        }
        String idCard;
        if (relType == UserRelTypeEnum.SELF) {
            idCard = cfInfoBaseVo.getPatientIdCard();
        } else {
            idCard = cfInfoBaseVo.getSelfIdCard();
        }
        // 发起人年龄校验 需要成年

        if (StringUtils.isBlank(idCard)) {
            return NewResponseUtil.makeSuccess(null);
        }

        if (!CfIdCardUtil.isValidIdCard(idCard)) {
            return NewResponseUtil.makeError(CfErrorCode.CF_VERIFY_IDCARD__ERROR);
        }

        int age = CfIdCardUtil.getAge(idCard);
        if (age < MIX_RAISE_AGE) {
            return NewResponseUtil.makeError(CfErrorCode.USER_NOT_ALLOW_RAISE_OF_AGE);
        }
        return NewResponseUtil.makeSuccess(null);
    }

    @Override
    public Response<CrowdfundingInfoResponse.Data> addOrUpdateBaseInfoV2(CrowdfundingInfoBaseVo cfInfoBaseVo, String clientIp, HitMomentEnum hitMomentEnum) {
        if (cfInfoBaseVo == null) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR, null);
        }
        long userId = cfInfoBaseVo.getUserId();

        OpResult validateResult = onValidate(cfInfoBaseVo, userId, hitMomentEnum);
        if (validateResult.isFail()) {
            return NewResponseUtil.makeError(validateResult.getErrorCode());
        }

        Response<Void> errorResponse = validateMaterial(userId, cfInfoBaseVo);
        if (errorResponse != null) {
            crowdfundingInfoService.saveIdCardErrorMsg(errorResponse.getCode(), cfInfoBaseVo.getCfVersion(), userId);
            return NewResponseUtil.makeResponse(errorResponse.getCode(), errorResponse.getMsg(), null);
        }

        Integer cfVersion = cfInfoBaseVo.getCfVersion();
        if (cfVersion != null && CfVersion.isInitialProperty(cfVersion)) {

            if (cfInfoBaseVo.getPropertyInsuranceParam() == null) {
                return NewResponseUtil.makeError(CfErrorCode.CREDIT_INFO_NULL_WITH_3000);
            }

            // 增信材料、贫困材料 需要校验
            CfErrorCode insuranceCode = cfInfoBaseVo.getPropertyInsuranceParam().validate();
            if (insuranceCode != CfErrorCode.SUCCESS) {
                return NewResponseUtil.makeError(insuranceCode);
            }
        }

        CrowdfundingInfoResponse response = addOrUpdate(cfInfoBaseVo, clientIp);
        //新案例提工单
        saveBaseInfoTemplateRecord(cfInfoBaseVo, response);

        return NewResponseUtil.makeResponse(response.getErrorCode().getCode(), response.getMsg(),
                response.getData());
    }

    private CfErrorCode verifyInfoId(CrowdfundingInfo crowdfundingInfo, long requestUserId) {
        if (crowdfundingInfo == null) {
            return CfErrorCode.CF_NOT_FOUND;
        }
        if (crowdfundingInfo.getStatus() != CrowdfundingStatus.APPROVE_PENDING
                && crowdfundingInfo.getStatus() != CrowdfundingStatus.APPROVE_DENIED) {
            return CfErrorCode.CF_CAN_NOT_EDIT;
        }

        if (crowdfundingInfo.getUserId() != requestUserId) {
            return CfErrorCode.CF_INFO_PARAM_ERROR_IDENTITY_INVALID;
        }
        return CfErrorCode.SUCCESS;
    }

    public void fillUserDrugFlag(CrowdfundingInfo caseInfo, CrowdfundingInfoBaseVo infoBaseVo) {
        if (caseInfo == null || infoBaseVo == null) {
            return;
        }

        if (infoBaseVo.getMedicineView() == null || infoBaseVo.getMedicineView().getWhetherFillInMedicine() == null) {
            // userId 调用鹰眼
            UserMedicineView drugView = new UserMedicineView();

            drugView.setWhetherFillInMedicine(queryWhetherFillInDrug(caseInfo.getUserId(), caseInfo.getId()));
            drugView.setEagleSource(MedicineInfoConstant.EagleSource.RAISE_CASE.getCode());
            infoBaseVo.setMedicineView(drugView);
        } else {
            infoBaseVo.getMedicineView().setEagleSource(MedicineInfoConstant.EagleSource.MEDICAL_PAGE.getCode());
        }

        log.info("案例的药物是否填写的标记 caseId:{} userId:{} fillInFlag:{}", caseInfo.getId(),
                caseInfo.getUserId(), infoBaseVo.getMedicineView().getWhetherFillInMedicine());

        infoBaseVo.getMedicineView().setCaseId(caseInfo.getId());
    }

    public int queryWhetherFillInDrug(long userId, int caseId) {

        if (StringUtils.isNotBlank(needSubmitMedicine) && needSubmitMedicine.contains(userId + "")) {
            log.info("userId在白名单中,全部需要填写用药信息.userId:{}", userId);
            return 1;
        }

        Integer version = 1;
        return version;
    }

    /**
     * 搜索文章中的禁止发起词，如果有，就不让发起
     *
     * @param sendHitNoticeToAlarmParam
     */
    @Override
    public void sendHitNoticeToAlarm(SendHitNoticeToAlarmParam sendHitNoticeToAlarmParam) {
        UserInfoModel userInfoModel = sendHitNoticeToAlarmParam.getUserInfoModel();
        RiskWordResultV2 hitResult = sendHitNoticeToAlarmParam.getHitResult();

        String alarmText = convertAlarmTextWithProcess(sendHitNoticeToAlarmParam);

        try {
            String redisKey = "cf_hit_notice_" + userInfoModel.getUserId() + "_" + StringUtils.join(hitResult.getHitWords(), ",");
            if (cfRedissonHandler.incrAndSetTimeWhenNotExists(redisKey, TimeUnit.MINUTES.toMillis(5)) != 1) {
                log.info("UGC审核不发送报警. userId:{}, hitWords:{}", userInfoModel.getUserId(), StringUtils.join(hitResult.getHitWords(), ","));
                return;
            }

            handleNoticeUgcRaise(alarmText);

        } catch (Exception e) {
            log.error("UGC审核报警发送失败. alarmText:{}", alarmText, e);
        }

        CaseSensitiveWords csw = new CaseSensitiveWords();
        try {

            // https://wiki.shuiditech.com/pages/viewpage.action?pageId=380862503
            String cryptoMobile = Optional.of(userInfoModel)
                    .map(UserInfoModel::getCryptoMobile)
                    .orElse("");
            csw.setCase_content(sendHitNoticeToAlarmParam.getCaseContent());
            csw.setUser_encrypt_mobile(cryptoMobile);
            csw.setCase_title(sendHitNoticeToAlarmParam.getCaseTitle());
            csw.setDisease_name(sendHitNoticeToAlarmParam.getDiseaseName());
            csw.setSensitive_words_list(Joiner.on(",").join(hitResult.getHitWords()));
            csw.setUser_tag(String.valueOf(userInfoModel.getUserId()));
            csw.setUser_tag_type(UserTagTypeEnum.userid);
            analytics.track(csw);
            log.info("大数据打点上报,敏感词:{}", JSONObject.toJSONString(csw));
        } catch (Exception e) {
            log.error("大数据打点上报异常,敏感词:{}", JSONObject.toJSONString(csw), e);
        }
    }

    private void handleNoticeUgcRaise(String alarmText) {
        CompletableFuture.runAsync(() -> handleNoticeUgcRaiseAsync(alarmText), feishuExecutor);
    }

    private void handleNoticeUgcRaiseAsync(String alarmText) {
        createTaskAsync(alarmText);
        final String taskPrefix = "";
//        final String taskPrefix = taskOk ? "" : "【此条对应飞书任务创建失败】\n";
        if (applicationService.isProduction()) {
            alarmClient.sendMarkdownByGroup("cf-ugc-audit", taskPrefix + alarmText);
        } else {
            AlarmBotService.sentMarkDown("3ceaac53-97f6-4562-aadd-62a2d0a56de7", taskPrefix + alarmText);
        }
    }

    private boolean createTaskAsync(String alarmText) {
        log.debug("createTask alarmText {}", alarmText);
        final String[] collaboratorArr = ConfigService.getAppConfig().getArrayProperty("apollo.ugc-raise-collaborator", ",", null);
        final String[] followerArr = ConfigService.getAppConfig().getArrayProperty("apollo.ugc-raise-follower", ",", null);
        final boolean hasCollaborator = ArrayUtils.getLength(collaboratorArr) > 0;
        final boolean hasFollower = ArrayUtils.getLength(followerArr) > 0;
        if (!hasCollaborator && !hasFollower) {
            log.info("任务 处理人也没有 关注人也没有 就不创建任务了");
            return false;
        }

        final FeiShuTaskRequestDto.FeiShuTaskRequestDtoBuilder builder = FeiShuTaskRequestDto.builder();
        // 水滴应用助手thirdType 786
        builder.thirdType(786)
                .summary("UGC审核-发起被拦截校验任务")
                .richDescription(alarmText)
                .origin(FeiShuTaskRequestDto.Origin.builder().platformI18nName("{\"zh_cn\": \"筹UGC\"}").build());
        if (hasCollaborator) {
            builder.collaboratorIds(Lists.newArrayList(collaboratorArr));
        }
        if (hasFollower) {
            builder.followerIds(Lists.newArrayList(followerArr));
        }

        final FeiShuTaskRequestDto taskRequest = builder.build();
        final Response<FeiShuTaskUpdateResponse> taskResp = feiShuTaskClient.create(taskRequest);
        log.debug("createTask taskRequest {}, taskResp {}", taskRequest, taskResp);
        final boolean ok = taskResp != null && taskResp.ok();
        if (!ok) {
            log.warn("飞书任务创建接口失败或超时 createTask taskRequest {}, taskResp {}", taskRequest, taskResp);
        }
        return ok;
    }

    @Override
    public ServiceChargeVo preCheckServiceCharge(String channel, String infoUuid, long userId, String info) {
        channel = Optional.ofNullable(channel).orElse(StringUtils.EMPTY);
        infoUuid = Optional.ofNullable(infoUuid).orElse(StringUtils.EMPTY);
        info = Optional.ofNullable(info).orElse(StringUtils.EMPTY);
        FeignResponse<ServiceChargeVo> chargeVoResp = cfFinanceCapitalAccountFeignClient.preCheckServiceChargeV1(info, channel, infoUuid, userId);
        log.info("v1 infoUuid:{}, userId:{}, info:{}, resp:{}", infoUuid, userId, info, JSON.toJSONString(chargeVoResp));
        ServiceChargeVo result = chargeVoResp.getData();
        if (result == null) {
            result = new ServiceChargeVo();
            result.setServiceCharge(CapitalServiceEnum.NO_SERVICE_CHARGE.getCode());
            result.setRaiseShowService(false);
        }
        return result;
    }

    // 第一页的原始校验逻辑
    @Override
    public OpResult<CrowdfundingInfo> verifyRaiseBasicInfo(CrowdfundingInfoBaseVo cfInfoBaseVo, long userId) {
        if (verifyRaiseBasicInfoSwitch) {
            return OpResult.createSucResult();
        }
        RaiseBasicInfoParam raiseBasicInfoParam = cfInfoBaseVo.getRaiseBasicInfoParam();
        if (Objects.isNull(raiseBasicInfoParam)) {
            return OpResult.createSucResult();
        }
        String raisePatientName = raiseBasicInfoParam.getRaisePatientName();
        if (StringUtils.isEmpty(raisePatientName)) {
            return OpResult.createSucResult();
        }
        String raisePatientIdCard = raiseBasicInfoParam.getRaisePatientIdCard();
        if (StringUtils.isBlank(raisePatientIdCard)) {
            return OpResult.createSucResult();
        }
        int raisePatientIdType = raiseBasicInfoParam.getRaisePatientIdType();

        if (!CfIdCardUtil.isValidIdCard(raisePatientIdCard)) {
            return OpResult.createFailResult(CfErrorCode.CF_VERIFY_IDCARD__ERROR);
        }
        int userRelationTypeForC = raiseBasicInfoParam.getUserRelationTypeForC();
        int age = CfIdCardUtil.getAge(raisePatientIdCard);
        if (age < MIX_RAISE_AGE && BaseInfoTemplateConst.CfBaseInfoRelationshipEnum.SELF.getRaiseRealType().getValue() == userRelationTypeForC) {
            return OpResult.createFailResult(CfErrorCode.USER_NOT_ALLOW_RAISE_OF_AGE);
        }
        //身份证不足18位不让提交（过滤掉15位的身份证）
        if (StringUtils.isNotBlank(raiseBasicInfoParam.getRaisePatientIdCard()) && !CfIdCardUtil.isValidIdCard(raiseBasicInfoParam.getRaisePatientIdCard())) {
            return OpResult.createFailResult(CfErrorCode.ADD_CROWDFUNDING_IDCARD_ERROR_OTHER);
        }
        if (raisePatientIdType == UserIdentityType.identity.getCode()) {
            // 这个地方，是发起的第一页，没有发起人身份证号，但是为了方法通用型且校验患者身份证，关系默认传本人
            CfErrorCode cfErrorCode = cfFirstApproveBiz.verifyIdcard(raiseBasicInfoParam.getSelfRealName(), "", raisePatientName, raisePatientIdCard, raisePatientIdType, UserRelTypeEnum.SELF, userId);
            if (!cfFirstApproveBiz.isSuccess(cfErrorCode)) {
                return OpResult.createFailResult(cfErrorCode);
            }
        }
        return getCrowdfundingInfoOpResult(userId, raisePatientIdCard);
    }

    private OpResult<CrowdfundingInfo> getCrowdfundingInfoOpResult(long userId, String raisePatientIdCard) {
        // 新增身份证判空
        if (StringUtils.isBlank(raisePatientIdCard)) {
            return OpResult.createSucResult();
        }
        CfCaseIndexSearchParam p = new CfCaseIndexSearchParam();
        p.setPatientIdCard(raisePatientIdCard);
        p.setEndTimeStart(System.currentTimeMillis());
        p.setSize(1000);
        // 根据患者身份证号去查询该患者目前还在流程中的案例
        SearchRpcResult<CfCaseIndexSearchResult> searchResult = cfSearchClient.cfCaseIndexSearch(p);
        List<CfCaseModel> cfCaseModels = Optional.ofNullable(searchResult)
                .map(SearchRpcResult::getData)
                .map(CfCaseIndexSearchResult::getModels)
                .orElse(new ArrayList<>());
        log.info("从cfSearch查询的cfCaseModels: {}", cfCaseModels);
        if (CollectionUtils.isEmpty(cfCaseModels)) {
            log.info("verifyRaiseBasicInfo 重复案例为空 dp getRaiseInfo");
            return OpResult.createSucResult();
        }
        int caseId = cfCaseModels.get(0).getId();
        CrowdfundingInfo crowdfundingInfoRepeat = crowdfundingInfoBiz.getFundingInfoById(caseId);
        CfInfoExt cfInfoExtRepeat = cfInfoExtBiz.getByCaseId(caseId);
        if (Objects.isNull(crowdfundingInfoRepeat) || Objects.isNull(cfInfoExtRepeat)) {
            log.info("重复案例为空，返回SUCCESS");
            return OpResult.createSucResult();
        }
        if (crowdfundingInfoRepeat.getUserId() != userId) {
            if (cfInfoExtRepeat.getFirstApproveStatus() == FirstApproveStatusEnum.APPLY_SUCCESS.getCode()) {
                return OpResult.createFailResult(CfErrorCode.REPEAT_CASE_FIRST_APPROVE_NO_PASS, crowdfundingInfoRepeat);
            }
            return OpResult.createFailResult(CfErrorCode.REPEAT_CASE_FIRST_APPROVE_NO_PASS);
        }
        if (cfInfoExtRepeat.getFirstApproveStatus() == FirstApproveStatusEnum.APPLY_SUCCESS.getCode()) {
            return OpResult.createFailResult(CfErrorCode.REPEAT_CASE_SELF_FIRST_APPROVE_PASS, crowdfundingInfoRepeat);
        }
        if (cfInfoExtRepeat.getFirstApproveStatus() == FirstApproveStatusEnum.APPLYING.getCode()) {
            return OpResult.createFailResult(CfErrorCode.REPEAT_CASE_SELF_FIRST_APPROVE_APPLYING);
        }
        if (cfInfoExtRepeat.getFirstApproveStatus() == FirstApproveStatusEnum.APPLY_FAIL.getCode()) {
            return OpResult.createFailResult(CfErrorCode.REPEAT_CASE_SELF_FIRST_APPROVE_REJECT, crowdfundingInfoRepeat);
        }
        return OpResult.createSucResult();
    }

    /**
     * 校验第一页信息
     *
     * @param cfInfoBaseVo
     * @param userId
     * @return
     */
    @Override
    public OpResult<CrowdfundingInfo> preventCasesFromRecurring(CrowdfundingInfoBaseVo cfInfoBaseVo,long userId) {
        RaiseBasicInfoParam raiseBasicInfoParam = cfInfoBaseVo.getRaiseBasicInfoParam();
        if (Objects.isNull(raiseBasicInfoParam)) {
            return OpResult.createSucResult();
        }
        String raisePatientIdCard = raiseBasicInfoParam.getRaisePatientIdCard();
        return getCrowdfundingInfoOpResult(userId, raisePatientIdCard);
    }

    private String convertAlarmTextWithProcess(SendHitNoticeToAlarmParam sendHitNoticeToAlarmParam) {
        String key = "apollo.risk-word-ugc.notice-html-enable";
        Boolean enableHtml = ConfigService.getAppConfig().getBooleanProperty(key, false);
        String s = convertAlarmText(sendHitNoticeToAlarmParam);
        if (!enableHtml) {
            s = RegExUtils.removeAll(s, "<[^>]+>");
        }
        return s;
    }

    private String convertAlarmText(SendHitNoticeToAlarmParam sendHitNoticeToAlarmParam) {
        UserInfoModel userInfoModel = sendHitNoticeToAlarmParam.getUserInfoModel();
        RiskWordResultV2 hitResult = sendHitNoticeToAlarmParam.getHitResult();
        String desc = sendHitNoticeToAlarmParam.getDesc();
        String location = sendHitNoticeToAlarmParam.getLocation();
        String mobile = Optional.of(userInfoModel).map(UserInfoModel::getCryptoMobile)
                .map(shuidiCipher::decrypt).orElse("");

        String caseTitle = sendHitNoticeToAlarmParam.getCaseTitle();
        String caseContent = sendHitNoticeToAlarmParam.getCaseContent();
        String diseaseName = sendHitNoticeToAlarmParam.getDiseaseName();

        if (StringUtils.isNotEmpty(caseContent) && StringUtils.isNotEmpty(caseTitle) && StringUtils.isNotEmpty(diseaseName)) {
            String title = tagKeyWord(hitResult, caseTitle);
            String content = tagKeyWord(hitResult, caseContent);
            return String.format(
                    "%s\n触发时机:[" + desc + "]\n拦截位置:" + location + "\nuserId:<font color='red'>%d </font>\nmobile:<font color='blue'>%s</font> \n禁止发起词:<font color='red'>%s</font> \n标题:%s \n文本:%s\n疾病名称:%s",
                    DateUtil.getCurrentDateTimeStr(),
                    userInfoModel.getUserId(),
                    mobile,
                    StringUtils.join(hitResult.getHitWords(), ","),
                    title,
                    content,
                    diseaseName
            );
        }

        if (StringUtils.isNotEmpty(caseContent) && StringUtils.isNotEmpty(caseTitle)) {
            String title = tagKeyWord(hitResult, caseTitle);
            String content = tagKeyWord(hitResult, caseContent);
            return String.format(
                    "%s\n触发时机:[" + desc + "]\n拦截位置:" + location + "\nuserId:<font color='red'>%d </font>\nmobile:<font color='blue'>%s</font> \n禁止发起词:<font color='red'>%s</font> \n标题:%s \n文本:%s",
                    DateUtil.getCurrentDateTimeStr(),
                    userInfoModel.getUserId(),
                    mobile,
                    StringUtils.join(hitResult.getHitWords(), ","),
                    title,
                    content
            );
        }


        if (StringUtils.isNotEmpty(diseaseName)) {
            String caseDiseaseName = tagKeyWord(hitResult, diseaseName);
            return String.format(
                    "%s\n触发时机:[" + desc + "]\n拦截位置:" + location + "\nuserId:<font color='red'>%d </font>\nmobile:<font color='blue'>%s</font> \n禁止发起词:<font color='red'>%s</font> \n所患疾病名称:%s ",
                    DateUtil.getCurrentDateTimeStr(),
                    userInfoModel.getUserId(),
                    mobile,
                    StringUtils.join(hitResult.getHitWords(), ","),
                    caseDiseaseName
            );
        }


        return "";
    }

    public void createSyncMaterialCenter(CreateInfoResultWrap resultWrap, CrowdfundingInfoBaseVo infoBaseVo) {

        if (resultWrap == null || resultWrap.getErrorCode() != CfErrorCode.SUCCESS || resultWrap.getCaseInfo() == null
                || resultWrap.getFirstApproveMaterial() == null || resultWrap.getInfoExt() == null) {
            log.info("创建案例失败,不用同步材料。infoBaseVo:{} result:{}", infoBaseVo, resultWrap);
            return;
        }
        CfCaseRaiseMaterialModel raiseModel = new CfCaseRaiseMaterialModel();
        raiseModel.setCaseId(resultWrap.getCaseInfo().getId());
        raiseModel.setFirstApproveMaterial(resultWrap.getFirstApproveMaterial());

        // 增信信息
        CfPropertyInsuranceInfoModel insuranceModel = CfPropertyInsuranceVO.buildInsuranceVo(CfVersion
                .isInitialProperty(resultWrap.getInfoExt().getCfVersion()) ? infoBaseVo.getPropertyInsuranceParam() : null);
        if (insuranceModel != null) {
            insuranceModel.setCaseId(resultWrap.getCaseInfo().getId());
        }

        raiseModel.setInsuranceInfoModel(insuranceModel);
        raiseModel.setLivingGuard(infoBaseVo.getLivingGuardParam());
        raiseModel.setBasicInfoModel(MaterialCenterConvert.convertFromParam(infoBaseVo.getRaiseBasicInfoParam()));
        raiseModel.setContentInfoModel(MaterialCenterConvert.convertFromInfoBaseVo(infoBaseVo));
        raiseModel.setUserMedicineView(infoBaseVo.getMedicineView());

        Map<String, String> extInfo = Maps.newHashMap();
        extInfo.put(CfInitialPropertyField.insurance_modify_user_id, "" + resultWrap.getCaseInfo().getUserId());
        raiseModel.setExtInfo(extInfo);
        log.info("创建案例成功,同步材料。insuranceModel:{} infoBaseVo:{}", JSONObject.toJSONString(insuranceModel), JSONObject.toJSONString(infoBaseVo));
        materialService.createCaseSyncMaterialCenter(raiseModel);
    }

    private CrowdfundingInfoResponse checkIdCard(CrowdfundingInfoBaseVo cfInfoBaseVo) {
        CrowdfundingInfoResponse crowdfundingInfoResponse = new CrowdfundingInfoResponse();
        try {
            if (null == cfInfoBaseVo) {
                crowdfundingInfoResponse.setErrorCode(CfErrorCode.RISK_RAISE_PERMITTED);
                return crowdfundingInfoResponse;
            }
            String selfIdCard = cfInfoBaseVo.getSelfIdCard();
            if (StringUtils.isEmpty(selfIdCard)) {
                log.info("selfIdCard 身份证为空,改为患者身份证");
                selfIdCard = cfInfoBaseVo.getPatientIdCard();
            }
            if (CollectionUtils.isEmpty(idCardList) || StringUtils.isEmpty(selfIdCard)) {
                log.info("idCardList 为空 或者  身份证为空");
                crowdfundingInfoResponse.setErrorCode(CfErrorCode.SUCCESS);
                return crowdfundingInfoResponse;
            }
            String selfIdCardX = selfIdCard.replace("x", "X");
            String selfIdCardx = selfIdCard.replace("X", "x");
            if (idCardList.contains(selfIdCard) || idCardList.contains(selfIdCardx) || idCardList.contains(selfIdCardX)) {
                log.info("身份在禁止发起名单内");
                crowdfundingInfoResponse.setErrorCode(CfErrorCode.RISK_RAISE_PERMITTED);
                return crowdfundingInfoResponse;
            }

            crowdfundingInfoResponse.setErrorCode(CfErrorCode.SUCCESS);
            return crowdfundingInfoResponse;
        } catch (Exception e) {
            log.error("", e);
            crowdfundingInfoResponse.setErrorCode(CfErrorCode.SUCCESS);
            return crowdfundingInfoResponse;
        }

    }
}
