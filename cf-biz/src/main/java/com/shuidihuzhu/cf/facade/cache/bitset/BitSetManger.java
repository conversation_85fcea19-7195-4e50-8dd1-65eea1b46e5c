package com.shuidihuzhu.cf.facade.cache.bitset;

import com.google.common.collect.Maps;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.util.DateUtil;
import org.redisson.api.RBitSet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @description:
 * @author: zheng<PERSON>u
 * @date: 2021-04-09 11:25
 **/
@Service
public class BitSetManger {

    @Resource(name = "cfRedissonHandler")
    private RedissonHandler redissonHandler;

    // Redis bitmap 是 【数字位索引】 → 【布尔值（0 或 1）】 的映射
    /**
     * 获取到redis key cf_info_visitor的key以及size放到map中
     */
    public Map<String, Object> getInfo() {
        String bitSetKey = "cf_info_visitor" + DateUtil.getCurrentDateStr();
        RBitSet bitSet = redissonHandler.getRedissonClient().getBitSet(bitSetKey);
        HashMap<String, Object> info = Maps.newHashMap();
        info.put("bitSetKey", bitSetKey);
        info.put("size", bitSet.size());
        return info;
    }

    /**
     * 获取redis key cf_info_visitor的指定下标的值
     */
    public boolean getIndexInfo(long index){
        String bitSetKey = "cf_info_visitor" + DateUtil.getCurrentDateStr();
        RBitSet bitSet = redissonHandler.getRedissonClient().getBitSet(bitSetKey);
        return bitSet.get(index);
    }
}
