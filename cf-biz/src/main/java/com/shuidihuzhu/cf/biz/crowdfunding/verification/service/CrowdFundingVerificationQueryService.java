package com.shuidihuzhu.cf.biz.crowdfunding.verification.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCaseDetailsMsgBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.verification.CrowdFundingVerificationBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.verification.service.cache.QueryVerifyUserService;
import com.shuidihuzhu.cf.constants.crowdfunding.status.RelationShip;
import com.shuidihuzhu.cf.domain.risk.RiskUgcVerifyDO;
import com.shuidihuzhu.cf.enums.risk.UgcTypeEnum;
import com.shuidihuzhu.cf.facade.risk.verify.RiskUgcFacade;
import com.shuidihuzhu.cf.model.CaseLabelsManagement;
import com.shuidihuzhu.cf.model.FoundationLabelShowConfig;
import com.shuidihuzhu.cf.model.ShowFundationLabel;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSimpleModel;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdFundingVerificationVo;
import com.shuidihuzhu.cf.service.crowdfunding.BlackListService;
import com.shuidihuzhu.cf.service.crowdfunding.comment.CfCommentHandlerService;
import com.shuidihuzhu.cf.service.label.CaseLabelsManagementService;
import com.shuidihuzhu.cf.service.risk.verify.RiskUgcVerifyService;
import com.shuidihuzhu.cf.vo.CrowdFundingVerificationListVo;
import com.shuidihuzhu.client.cf.admin.model.AdminCaseDetailsMsg;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by zhangyiqian on 16/10/18.
 */
@Service
@RefreshScope
public class CrowdFundingVerificationQueryService {

    @Value("#{'${apollo.water.verify.government.cases:}'.split(',')}")
    private List<Integer> governmentCaseIds;

    @Value("#{'${apollo.water.verify.foundation.cases:}'.split(',')}")
    private List<Integer> foundationCaseIds;
    private static Integer isShowLabel = 1;


    private static Comparator<CrowdFundingVerification> compByLength = (v1, v2) -> StringUtils.length(v2.getDescription()) - StringUtils.length(v1.getDescription()) ;

    @Autowired
    private CrowdFundingVerificationBiz crowdFundingVerificationBiz;

    @Autowired
    private RiskUgcVerifyService riskUgcVerifyService;

    @Autowired
    private QueryVerifyUserService queryVerifyUserService;

    @Autowired
    private RiskUgcFacade riskUgcFacade;

    @Autowired
    private CfCommentHandlerService cfCommentHandlerService;

    @Autowired
    private AdminCaseDetailsMsgBiz adminCaseDetailsMsgBiz;

    @Resource
    private BlackListService blackListService;

    @Autowired
    private CaseLabelsManagementService labelsManagementService;

    public int countVerifyUser (@NotNull final String crowdFundingInfoId) {
        Integer count = crowdFundingVerificationBiz.countCrowdFundingVerificationByInfoUuid(crowdFundingInfoId);
        if (null == count || count <= 0) {
            return 0;
        }

        return count;
    }

    public int countVerifyUserByRelationShips(final String crowdfundingInfoId,
                                              List<Integer> relationships) {
        if(CollectionUtils.isEmpty(relationships)) {
            return 0;
        }

        Integer count = this.crowdFundingVerificationBiz.countVerificationByInfoUuidAndRelationships(crowdfundingInfoId, relationships);
        if(count == null || count <= 0) {
            return 0;
        }
        return count;
    }

    public List<CrowdFundingVerification> queryCrowdFundingVerification (@NotNull final String crowdFundingInfoId, int limit) {
        if (limit <= 0 || limit > 5) {
            limit = 5;
        }

        List<CrowdFundingVerification> crowdFundingVerificationList = crowdFundingVerificationBiz
        		.queryCrowdFundingVerificationByInfoUuid(crowdFundingInfoId, limit);
        if (CollectionUtils.isEmpty(crowdFundingVerificationList)) {
            return Collections.emptyList();
        }

        return crowdFundingVerificationList;
    }

    public List<CrowdFundingVerification> queryAllCrowdFundingVerification(@NotNull final String crowdFundingInfoId,
    		@NotNull final boolean isAll, int limit) {
        if (isAll) {
            return crowdFundingVerificationBiz.queryAllCrowdFundingVerificationByInfoUuid(crowdFundingInfoId);
        } else {
            return crowdFundingVerificationBiz.queryCrowdFundingVerificationByInfoUuid(crowdFundingInfoId, limit);
        }
    }

    public CrowdFundingVerificationListVo getVerificationList(CfInfoSimpleModel simpleModel, long currentUserId, long anchorId, int size, int removeFirstPlace) {

        CrowdFundingVerificationListVo verificationListVo = new CrowdFundingVerificationListVo();
        verificationListVo.setSize(size);

        List<CrowdFundingVerification> verifications = getAllVerificationFilterUgc(simpleModel.getId(), simpleModel.getInfoId(), currentUserId);
        blackListService.handleVerification(verifications, currentUserId);

        verificationListVo.setTotalCount(verifications.size());
        if (removeFirstPlace == 1) {
            //代表是详情页调用，排除一下单独第一个位置接口获取的证实，防止展示重复
            Optional<CrowdFundingVerification> first = getFirstPlaceVerification(verifications, currentUserId);
            if (first.isPresent()) {
                verifications = verifications.stream().filter(v -> v.getId() != first.get().getId()).collect(Collectors.toList());
            }
        }
        //字数排序
        verifications.sort(compByLength);
        // 基金会志愿者证实 放在首位
        List<CrowdFundingVerification> volunteerVerify = verifications.stream().filter(v -> v.isShowLabel() == true)
                                                        .sorted(Comparator.comparing(CrowdFundingVerification :: getId).reversed()).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(volunteerVerify)){
            verifications.removeAll(volunteerVerify);
            verifications.addAll(0,volunteerVerify);
        }

        int skip = 0;
        for (int i = 0; anchorId != Long.MAX_VALUE && i < verifications.size(); i++) {
            if (verifications.get(i).getId() == anchorId) {
                skip = i;
                break;
            }
        }

        List<CrowdFundingVerification> resultVerifications = verifications.stream().skip(skip ==0 ? 0 : skip + 1).limit(size + 1).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(resultVerifications)) {
            return verificationListVo;
        }
        if (resultVerifications.size() > size) {
            verificationListVo.setHasNext(resultVerifications.size() > size);
            resultVerifications = resultVerifications.subList(0, size);
        }

        verificationListVo.setAnchorId(resultVerifications.get(resultVerifications.size() -1).getId());

        List<CrowdFundingVerificationVo> vos = queryVerifyUserService.queryVerifyUserList(resultVerifications);
        cfCommentHandlerService.replaceVerificationVo(vos);
        queryVerifyUserService.queryIdcardVerifyStatus(vos);
        queryVerifyUserService.fillLoveScore(vos);
        queryVerifyUserService.hasFollow(currentUserId, vos);
        dataDesensitization(vos);

        verificationListVo.setList(vos);
        return verificationListVo;
    }

    public CrowdFundingVerificationListVo getAuthenticityVerificationList(CfInfoSimpleModel simpleModel, long currentUserId, long anchorId, int size) {
        CrowdFundingVerificationListVo verificationListVo = new CrowdFundingVerificationListVo();
        verificationListVo.setSize(size);
        List<CrowdFundingVerification> verifications = getAllVerificationFilterUgc(simpleModel.getId(), simpleModel.getInfoId(), currentUserId);
        blackListService.handleVerification(verifications, currentUserId);
        verificationListVo.setTotalCount(verifications.size());

        //排序逻辑,先按关系排、字数排、时间排
        verifications.forEach(f -> f.setRelationshipSort(RelationShip.getAuthenticityRelationshipSort(f.getRelationShip())));
        verifications.sort(Comparator.comparing(CrowdFundingVerification::getRelationshipSort)
                .thenComparing(compByLength));
        //最后将基金会志愿者证实 放在首位
        List<CrowdFundingVerification> volunteerVerify = verifications.stream()
                .filter(CrowdFundingVerification::isShowLabel)
                .sorted(Comparator.comparing(CrowdFundingVerification :: getId).reversed())
                .collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(volunteerVerify)) {
            verifications.removeAll(volunteerVerify);
            verifications.addAll(0, volunteerVerify);
        }


        int skip = 0;
        for (int i = 0; anchorId != Long.MAX_VALUE && i < verifications.size(); i++) {
            if (verifications.get(i).getId() == anchorId) {
                skip = i;
                break;
            }
        }
        List<CrowdFundingVerification> resultVerifications = verifications.stream()
                .skip(skip ==0 ? 0 : skip + 1)
                .limit(size + 1)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(resultVerifications)) {
            return verificationListVo;
        }
        if (resultVerifications.size() > size) {
            verificationListVo.setHasNext(true);
            resultVerifications = resultVerifications.subList(0, size);
        }

        verificationListVo.setAnchorId(resultVerifications.get(resultVerifications.size() -1).getId());

        List<CrowdFundingVerificationVo> vos = queryVerifyUserService.queryVerifyUserList(resultVerifications);
        cfCommentHandlerService.replaceVerificationVo(vos);
        queryVerifyUserService.queryIdcardVerifyStatus(vos);
        queryVerifyUserService.fillLoveScore(vos);
        queryVerifyUserService.hasFollow(currentUserId, vos);
        dataDesensitization(vos);

        verificationListVo.setList(vos);
        return verificationListVo;
    }


    public Optional<CrowdFundingVerificationVo> getFirstPlaceVerification(CfInfoSimpleModel simpleModel, long currentUserId) {
        int caseId = simpleModel.getId();
        String infoUuid = simpleModel.getInfoId();

        Optional<CrowdFundingVerification> firstVerification = getFirstPlaceVerification(caseId, infoUuid, currentUserId);
        if (firstVerification.isEmpty()) {
            return Optional.empty();
        }

        // 为了复用下面的公共方法，放在list里面
        List<CrowdFundingVerification> resultVerifications = Lists.newArrayList(firstVerification.get());

        List<CrowdFundingVerificationVo> vos = queryVerifyUserService.queryVerifyUserList(resultVerifications);
        cfCommentHandlerService.replaceVerificationVo(vos);
        queryVerifyUserService.queryIdcardVerifyStatus(vos);
        queryVerifyUserService.fillLoveScore(vos);
        queryVerifyUserService.hasFollow(currentUserId, vos);
        dataDesensitization(vos);

        return CollectionUtils.isEmpty(vos) ?  Optional.empty() : Optional.of(vos.get(0));
    }

    private Optional<CrowdFundingVerification> getFirstPlaceVerification(int caseId, String infoUuid, long currentUserId) {
        List<CrowdFundingVerification> verifications = getAllVerificationFilterUgc(caseId, infoUuid, currentUserId);
        return getFirstPlaceVerification(verifications, currentUserId);
    }

    private Optional<CrowdFundingVerification> getFirstPlaceVerification(List<CrowdFundingVerification> verifications, long currentUserId) {
        if (CollectionUtils.isEmpty(verifications)) {
            return Optional.empty();
        }

        //个人或者医护证实列表
        List<CrowdFundingVerification> collect = verifications.stream().filter(x -> x.getVerifyUserId() == currentUserId).collect(Collectors.toList());
//        if (CollectionUtils.isEmpty(collect)) {
//            collect = verifications.stream().filter(x -> x.getRelationShip() == RelationShip.HEALTH_CARE.getCode()).collect(Collectors.toList());
//        }

        if (CollectionUtils.isEmpty(collect)) {
            return Optional.empty();
        }
        //字数降序排列
        collect.sort(compByLength);
        return Optional.of(collect.get(0));
    }


    public List<String> getVerifyLabels(CfInfoSimpleModel simpleModel, long currentUserId){
        int caseId = simpleModel.getId();
        String infoUuid = simpleModel.getInfoId();

        List<String> labels = Lists.newArrayList();
        //有手动文案的话，先展示手动文案
        AdminCaseDetailsMsg adminCaseDetailsMsg = adminCaseDetailsMsgBiz.getCaseDetailsMsgByInfoUuid(simpleModel.getInfoId());
        if (Objects.nonNull(adminCaseDetailsMsg) && StringUtils.isNotEmpty(adminCaseDetailsMsg.getCarouselText())) {
            labels.add(adminCaseDetailsMsg.getCarouselText());
        }

        if (governmentCaseIds.contains(caseId)) {
            labels.add("政府机构已经走访证实");
        }

        if (foundationCaseIds.contains(caseId)) {
            labels.add("基金会已经走访证实");
        }

        List<CrowdFundingVerification> verifications = getAllVerificationFilterUgc(caseId, infoUuid, currentUserId);

//        long healthCareCount = verifications.stream().filter(v -> v.getRelationShip() == RelationShip.HEALTH_CARE.getCode()).count();
//        if (healthCareCount > 0) {
//            labels.add(String.format("该案例已有%d名医务人员证实", healthCareCount));
//        }

        int countVerifyUser = verifications.size();
        if (countVerifyUser > 0) {
            labels.add(String.format("该案例已有%d人证实", countVerifyUser));
        }
        labels.add("打击虚假筹款，假案例先行赔付");
        labels.add("大数据持续验证中");
        labels.add("该案例所筹资金由第三方银行专管中");

        return labels;
    }

    /**
     * 获取过滤UGC风控后的所有证实记录
     *
     * @param caseId 案例ID
     * @param infoUuid 案例UUID
     * @param currentUserId 当前用户ID
     * @return 过滤后的证实记录列表
     */
    private List<CrowdFundingVerification> getAllVerificationFilterUgc(int caseId, String infoUuid, long currentUserId) {
        // 获取该案例的风控UGC验证记录（命中敏感词的证实）
        List<RiskUgcVerifyDO> riskUgcVerifyDOS = riskUgcVerifyService.getByCaseIdAndUgcType(caseId, UgcTypeEnum.VERIFICATION);
        // 提取所有命中敏感词的证实ID集合
        Set<Long> ugcVerifies = riskUgcVerifyDOS.stream().map(RiskUgcVerifyDO::getUgcId).collect(Collectors.toSet());

        // 查询该案例的所有证实记录
        List<CrowdFundingVerification> allVerifications = crowdFundingVerificationBiz.queryAllCrowdFundingVerificationByInfoUuid(infoUuid);

        // 初始化最终返回的证实列表
        List<CrowdFundingVerification> totalVerfications = Lists.newArrayList();
        // 获取证实黑名单用户集合
        Set<Long> blackHitUsers = blackListService.getVerificationHitUser(totalVerfications, currentUserId);

        // 检查基金会标签显示配置
        ShowFundationLabel showFundationLabel = checkFoundationLabel(infoUuid);

        // 遍历所有证实记录，根据不同条件进行过滤和处理
        for(CrowdFundingVerification verification : allVerifications){
            // 跳过无效的证实记录
            if(verification.getValid() == 0){
                continue;
            }

            // 处理他人的证实且命中敏感词的情况
            if(verification.getVerifyUserId() != currentUserId && ugcVerifies.contains(verification.getId().longValue())){
                // 检查是否为基金会志愿者证实且命中关键字
                if(booleanFoundationHitKeyWordsAndVolunteer(verification, showFundationLabel)) {
                    // 设置显示标签并添加到结果列表
                    verification.setShowLabel(true);
                    totalVerfications.add(verification);
                }
                continue;
            }

            // 处理当前用户自己的证实且命中敏感词的情况
            if(verification.getVerifyUserId() == currentUserId && ugcVerifies.contains(verification.getId().longValue())){
                // 检查是否为基金会志愿者证实且命中关键字
                if(booleanFoundationHitKeyWordsAndVolunteer(verification, showFundationLabel)){
                    // 设置显示标签
                    verification.setShowLabel(true);
                }
                // 本人的证实记录始终添加到结果列表
                totalVerfications.add(verification);
                continue;
            }

            // 处理未命中敏感词但有机构标签的证实
            if(!ugcVerifies.contains(verification.getId().longValue())
                    && showFundationLabel.getOrgLabel() > 0){
                // 设置显示标签并添加到结果列表
                verification.setShowLabel(true);
                totalVerfications.add(verification);
                continue;
            }

            // 过滤黑名单用户的证实记录
            if (blackHitUsers.contains(verification.getVerifyUserId())) {
                continue;
            }

            // 添加通过所有过滤条件的证实记录
            totalVerfications.add(verification);
        }

        // 返回过滤后的证实列表
        return totalVerfications;
    }

    /**
     * 校验案例是否有基金会标签
     * @param infoId
     * @return
     */
    public ShowFundationLabel checkFoundationLabel(String infoId){
        ShowFundationLabel result = new ShowFundationLabel();
        CaseLabelsManagement label = labelsManagementService.getLabel(infoId);
        if(label == null){
            return result;
        }
        result.setOrgLabel(label.getOrgLabel());
        return result;
    }

    public boolean booleanFoundationHitKeyWordsAndVolunteer(CrowdFundingVerification verification,
                                                            ShowFundationLabel showFoundationLabel){
        return labelsManagementService.booleanFoundationHitKeyWordsAndVolunteer(verification, showFoundationLabel);
    }

    private void dataDesensitization(List<CrowdFundingVerificationVo> vos) {
        if (CollectionUtils.isEmpty(vos)) {
            return;
        }

        vos.stream().forEach(vo -> {
            vo.setCrowdFundingInfoId("");
            vo.setOpenId("");
            vo.setVerifyUserId(-1);
            vo.setPatientUserId(-1);
        });
    }
}
