package com.shuidihuzhu.cf.biz.crowdfunding.verification.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.CfInfoStatBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfUserStatBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoSimpleBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.verification.CfConfirmBlacklistBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.verification.CrowdFundingVerificationBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.verification.impl.ModifyVerificationBizImpl;
import com.shuidihuzhu.cf.biz.task.CfInfoTaskBiz;
import com.shuidihuzhu.cf.client.base.page.v1.model.PaginationListVO;
import com.shuidihuzhu.cf.config.ProjectConfig;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.crowdfunding.status.RelationShip;
import com.shuidihuzhu.cf.constants.crowdfunding.status.Validate;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.enhancer.subject.redislock.RedisLock;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.CfVolunteerTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfTaskEnum;
import com.shuidihuzhu.cf.enums.risk.UgcTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.deliver.CrowdFundingVerificationDeliver;
import com.shuidihuzhu.cf.model.crowdfunding.param.CrowdFundingVerificationParam;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.cf.service.caseinfo.LoveStoryVerifyService;
import com.shuidihuzhu.cf.service.risk.channel.RiskChannelUgcService;
import com.shuidihuzhu.cf.service.risk.limit.ICfRiskService;
import com.shuidihuzhu.cf.vo.crowdfunding.StorageCrowdFundingVerificationResultVO;
import com.shuidihuzhu.client.cf.growthtool.client.CfGrowthtoolVolunteerFeignClient;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.client.cf.risk.client.CfApiChaifenRiskClient;
import com.shuidihuzhu.client.cf.risk.model.enums.UserOperationEnum;
import com.shuidihuzhu.client.cf.search.param.table.WorkOrderExtTableParam;
import com.shuidihuzhu.client.cf.workorder.CfUgcWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.core.WorkOrderCoreFeignClient;
import com.shuidihuzhu.client.cf.workorder.helper.Von;
import com.shuidihuzhu.client.cf.workorder.helper.model.OrderSearchParam;
import com.shuidihuzhu.client.cf.workorder.model.HandleOrderParam;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.client.cf.workorder.read.BasicWorkOrder;
import com.shuidihuzhu.client.cf.workorder.read.WorkOrderReadFeignClient;
import com.shuidihuzhu.common.web.enums.Platform;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.logger.SDBizStatLogger;
import com.shuidihuzhu.data.analytics.javasdk.core.Analytics;
import com.shuidihuzhu.data.analytics.javasdk.core.UserTagTypeEnum;
import com.shuidihuzhu.data.servicelog.meta.cf.CaseVerify;
import com.shuidihuzhu.infra.starter.rocketmq.core.DelayLevel;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Created by zhangyiqian on 16/10/18.
 */
@Slf4j
@Service
@RefreshScope
public class CrowdFundingVerificationStorageService {

    @Autowired
    private CrowdFundingVerificationBiz crowdFundingVerificationBiz;
    @Autowired
    CfInfoStatBiz cfInfoStatBiz;
    @Autowired
    CrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    CfUserStatBiz cfUserStatBiz;
    @Autowired
    ProjectConfig projectConfig;
    @Autowired
    private CfInfoTaskBiz cfInfoTaskBiz;
    @Autowired(required = false)
    Producer producer;

    @Resource
    private RiskChannelUgcService riskChannelUGCService;
    @Autowired
    private Analytics analytics;

    @Autowired
    private UserInfoDelegate userInfoDelegate;
    @Autowired
    private LoveStoryVerifyService loveStoryVerifyService;
    @Autowired
    private ICfRiskService cfRiskService;
    @Resource
    private CfGrowthtoolVolunteerFeignClient growthtoolVolunteerFeignClient;
    @Autowired
    private CfConfirmBlacklistBiz cfConfirmBlacklistBiz;
    @Value("${apollo.confirm.limit.count:4}")
    private int confirmLimitCount;
    @Autowired
    private ApplicationService applicationService;
    @Resource
    private CrowdfundingInfoSimpleBiz crowdfundingInfoSimpleBiz;
    @Resource
    private ModifyVerificationBizImpl modifyVerificationBiz;
    @Resource
    private WorkOrderCoreFeignClient workOrderCoreFeignClient;
    @Resource
    private WorkOrderReadFeignClient workOrderReadFeignClient;
    @Resource
    private CfApiChaifenRiskClient cfApiChaifenRiskClient;

    // 设置锁时间0.5s
    @RedisLock(key = "get-verification-#{crowdFundingVerificationParam.infoUuid}-#{userId}", leaseTimeMills = 500)
    public Response<StorageCrowdFundingVerificationResultVO> storageVerification(CrowdFundingVerificationParam crowdFundingVerificationParam,
                                                                                  String clientIp, String channel, Platform platform, long userId) {
        // 记录证实信息提交日志
        log.info("提交保存爱心筹救助人为受助人提供的证实信息参数, param={};userId={}", crowdFundingVerificationParam, userId);

        // 检查用户是否在证实黑名单中
        CfConfirmBlacklist confirmBlacklist = cfConfirmBlacklistBiz.getMsgByUserId(userId);
        if (Objects.nonNull(confirmBlacklist) && confirmBlacklist.getValid() == 1) {
            return NewResponseUtil.makeError(CfErrorCode.VERIFICATION_BLACKLIST);
        }

        // 获取当天的开始和结束时间，用于统计当日证实次数
        LocalDate localDate = LocalDate.now();
        Timestamp startTime = Timestamp.valueOf(LocalDateTime.of(localDate.getYear(), localDate.getMonthValue(), localDate.getDayOfMonth(), 0, 0, 0));
        Timestamp endTime = Timestamp.valueOf(LocalDateTime.of(localDate.getYear(), localDate.getMonthValue(), localDate.getDayOfMonth(), 23, 59, 59));

        // 统计用户当天的证实次数
        int count = crowdFundingVerificationBiz.countCrowdFundingVerificationByVerifyUserIdAndTime(startTime, endTime, userId);
        log.info("countCrowdFundingVerificationByVerifyUserIdAndTime userId:{} count:{}", userId, count);

        // 如果证实次数超过限制，将用户加入黑名单
        if (count > confirmLimitCount) {
            // 设置黑名单解除时间：生产环境1天后，测试环境5分钟后
            LocalDateTime localDateTime;
            if (applicationService.isProduction()) {
                localDateTime = LocalDateTime.now().plusDays(1);      // 生产环境：1天后解除
            } else {
                localDateTime = LocalDateTime.now().plusMinutes(5);   // 测试环境：5分钟后解除
            }
            Timestamp nowTime = Timestamp.valueOf(localDateTime);
            int res = 0;

            // 根据黑名单记录是否存在，选择新增或更新操作
            if (Objects.isNull(confirmBlacklist)) {
                // 新增黑名单记录
                res = cfConfirmBlacklistBiz.saveCfConfirmBlacklist(userId, 1, nowTime);
            } else {
                // 更新现有黑名单记录的解除时间
                res = cfConfirmBlacklistBiz.updateBlacklistTime(userId, nowTime);
            }

            // 如果黑名单操作成功，发送定时消息用于自动解除黑名单
            if (res > 0) {
                try {
                    // 发送定时MQ消息，在指定时间自动解除用户黑名单状态
                    producer.send(Message.ofSchedule(MQTopicCons.CF,
                            MQTagCons.BLACKLIST_CONFIRMED_USERS_RELEASE,
                            MQTagCons.BLACKLIST_CONFIRMED_USERS_RELEASE + "_" + userId,
                            userId, nowTime.getTime() / 1000));
                } catch (Exception e) {
                    // 如果MQ发送失败，将用户的黑名单状态设为失效，避免用户永久被拉黑
                    cfConfirmBlacklistBiz.updateValid(userId);
                    log.error("error msg", e);
                }
            }
            return NewResponseUtil.makeError(CfErrorCode.VERIFICATION_BLACKLIST);
        }

        // 获取用户信息
        UserInfoModel userInfoModel = this.userInfoDelegate.getUserInfoByUserId(userId);
        if (userInfoModel == null) {
            return NewResponseUtil.makeError(CfErrorCode.CF_USER_ACCOUNT_NO_LOGIN);
        }


        // 参数校验：检查证实参数是否为空
        if (null == crowdFundingVerificationParam) {
            return NewResponseUtil.makeResponse(CfErrorCode.SYSTEM_PARAM_ERROR.getCode(), CfErrorCode.SYSTEM_PARAM_ERROR.getMsg(), null);
        }

        // 处理自定义标签：如果标签长度超过40个字符，截取前40个字符
        String selfTag = crowdFundingVerificationParam.getSelfTag();
        if (StringUtils.isNotBlank(selfTag) && selfTag.length() > 40){
            selfTag = selfTag.substring(0,40);
            crowdFundingVerificationParam.setSelfTag(selfTag);
        }

        // 校验用户名不能为空
        if (Strings.isNullOrEmpty(crowdFundingVerificationParam.getUserName())) {
            log.info("用户名不能为空");
            return NewResponseUtil.makeResponse(CfErrorCode.VERIFICATION_USERNAME_IS_NULL.getCode(), CfErrorCode.VERIFICATION_USERNAME_IS_NULL.getMsg(), null);
        }

        // 记录详细的证实参数日志
        log.info("提交保存爱心筹救助人为受助人提供的证实信息参数, crowdFundingVerificationParam={}", JSON.toJSONString(crowdFundingVerificationParam));

        // 校验证实描述不能为空
        if (Strings.isNullOrEmpty(crowdFundingVerificationParam.getDescription())) {
            log.info("证实描述为空, 请重新填写");
            return NewResponseUtil.makeError(CfErrorCode.VERIFICATION_DESCRIPTION_IS_NULL);
        }

        // 校验证实描述长度不能超过400个字符
        if (crowdFundingVerificationParam.getDescription().length() > 400) {
            log.info("证实描述太长, 请重新填写");
            return NewResponseUtil.makeError(CfErrorCode.VERIFICATION_DESCRIPTION_TOO_LONG);
        }

        // 校验手机号格式（如果提供了手机号）
        if (!StringUtils.isEmpty(crowdFundingVerificationParam.getMobile())) {
            if (!crowdFundingVerificationParam.getMobile().trim().matches("^1[0-9]{10}$")) {
                log.info("手机号格式不正确,请重新填写手机号");
                return NewResponseUtil.makeError(CfErrorCode.USER_ACCOUNT_MOBILE_ERROR);
            }
            // 去除手机号前后空格
            crowdFundingVerificationParam.setMobile(crowdFundingVerificationParam.getMobile().trim());
        }

        // 设置筹款信息ID
        crowdFundingVerificationParam.setCrowdFundingInfoId(crowdFundingVerificationParam.getInfoUuid());

        // 校验筹款信息ID不能为空
        if (Strings.isNullOrEmpty(crowdFundingVerificationParam.getCrowdFundingInfoId())) {
            return NewResponseUtil.makeResponse(CfErrorCode.SYSTEM_PARAM_ERROR.getCode(), CfErrorCode.SYSTEM_PARAM_ERROR.getMsg(), null);
        }

        // 根据筹款信息ID查询筹款案例信息
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(crowdFundingVerificationParam.getCrowdFundingInfoId());
        if (null == crowdfundingInfo) {
            return NewResponseUtil.makeResponse(CfErrorCode.CF_NOT_FOUND.getCode(), CfErrorCode.CF_NOT_FOUND.getMsg(), null);
        }

        // 防止用户为自己的案例进行证实
        if(userId == crowdfundingInfo.getUserId()){
            return NewResponseUtil.makeError(CfErrorCode.VERIFICATION_USER_IS_SELF);
        }

        // 风控检查：验证用户是否有权限进行证实操作
        if (!cfRiskService.operatorValid(userId, crowdfundingInfo.getId(), UserOperationEnum.VERIFY)) {
            return NewResponseUtil.makeError(CfErrorCode.RISK_VERIFY_PERMITTED);
        }

        // 检查证实用户是否为BD（业务发展人员）
        if(StringUtils.isNotEmpty(userInfoModel.getCryptoMobile())) {
            // 根据手机号查询志愿者信息
            Response<List<CrowdfundingVolunteer>> response = growthtoolVolunteerFeignClient.getCrowdfundingVolunteerByMobile(Lists.newArrayList(userInfoModel.getCryptoMobile()));
            log.info("get bd data: {}, result: {}", userInfoModel.getCryptoMobile(), response);

            // 判断是否为团队类型的志愿者（BD）
            boolean present = Optional.ofNullable(response)
                    .filter(Response::ok)
                    .map(Response::getData)
                    .filter(CollectionUtils::isNotEmpty)
                    .map(v -> v.get(0))
                    .map(CrowdfundingVolunteer::getVolunteerType)
                    .filter(v -> v == CfVolunteerTypeEnum.TEAM.getValue())
                    .isPresent();

            // 如果是BD且关系不是"其他"，则设置证实为无效（需要审核）
            if (present && crowdFundingVerificationParam.getRelationShip() != RelationShip.OTHER.getCode()){
                // 说明此人是BD，设置为无效状态，需要后台审核
                crowdFundingVerificationParam.setValid(0);
            }
        }

        // 限制医护影像件最多上传三张
        List<String> medicalImageList = crowdFundingVerificationParam.getMedicalImageList();
        if (CollectionUtils.isNotEmpty(medicalImageList)) {
            if (medicalImageList.size() > 3) {
                // 如果超过3张，只保留前3张
                crowdFundingVerificationParam.setMedicalImageList(medicalImageList.subList(0,3));
            }
        }

        // 设置相关用户ID和案例ID
        long patientUserId = crowdfundingInfo.getUserId();                    // 患者用户ID
        crowdFundingVerificationParam.setPatientUserId(patientUserId);        // 设置患者用户ID
        crowdFundingVerificationParam.setVerifyUserId(userInfoModel.getUserId()); // 设置证实用户ID
        crowdFundingVerificationParam.setCaseId(crowdfundingInfo.getId());    // 设置案例ID

        // 验证患者用户是否存在
        UserInfoModel userAccount = userInfoDelegate.getUserInfoByUserId(
                crowdFundingVerificationParam.getPatientUserId());
        if (null == userAccount) {
            return NewResponseUtil.makeResponse(CfErrorCode.PATIENT_USER_NO_EXIST.getCode(), CfErrorCode.PATIENT_USER_NO_EXIST.getMsg(), null);
        }

        // 保存用户标签信息
        loveStoryVerifyService.saveLabel(crowdFundingVerificationParam);

        // 调用实际的证实信息存储方法
        return storageCrowdFundingVerification(crowdFundingVerificationParam,clientIp,channel,platform);
    }

    public Response<StorageCrowdFundingVerificationResultVO> storageCrowdFundingVerification(@NotNull final CrowdFundingVerificationParam param,String clientIp,
                                                                                             String channel, Platform platform) {
        int flag = 0;
        boolean repeat = false;
        if (!Strings.isNullOrEmpty(param.getOpenId())) {
            flag = crowdFundingVerificationBiz
                    .countCrowdFundingVerificationByInfoUuidAndOpenId(param.getOpenId(),
                            param.getCrowdFundingInfoId());
            repeat = true;
        } else if (param.getVerifyUserId() > 0) {
            flag = crowdFundingVerificationBiz
                    .countCrowdFundingVerificationByInfoUuidAndVerifyUserId(param.getVerifyUserId(),
                            param.getCrowdFundingInfoId());
            repeat = true;
        }

        if (flag > 0 && repeat) {
            log.info("证实人 {}, 已经为受助人 {} 证实过了, 不需要重复证实", param.getVerifyUserId(), param.getPatientUserId());
            return NewResponseUtil
                    .makeResponse((CfErrorCode.VERIFICATION_REPEAT_ERROR.getCode()), CfErrorCode.VERIFICATION_REPEAT_ERROR.getMsg(), null);
        }

        CrowdFundingVerification crowdFundingVerification = buildCrowdFundingVerification(param);
        try {
            crowdFundingVerificationBiz.saveCrowdFundingVerification(crowdFundingVerification);
        } catch (Exception e) {
            log.error("保存证实失败, crowdFundingVerification={}", JSON.toJSONString(crowdFundingVerification), e);
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_ERROR);
        }

        // 删除证实缓存
        crowdFundingVerificationBiz.deleteCaseVerificationCache(param.getCrowdFundingInfoId());

        StorageCrowdFundingVerificationResultVO result = null;
        try {
            if (param.getValid() == Validate.VALID.getCode()) {
                result = otherEvent(param, crowdFundingVerification,channel,platform);
            }
            CrowdFundingVerificationDeliver crowdFundingVerificationDeliver = new CrowdFundingVerificationDeliver();
            BeanUtils.copyProperties(crowdFundingVerification, crowdFundingVerificationDeliver);
            crowdFundingVerificationDeliver.setUniquelyIdentifies("verification");
            //发证实消息，给后台生成具体ugc审核任务
            sendVerificationUgcMq(crowdFundingVerificationDeliver);

            track(param, crowdFundingVerificationDeliver.getId(), crowdFundingVerificationDeliver.getUniquelyIdentifies(), clientIp);
        } catch (Exception e) {
            log.error("case_verify 数据上报异常", e);
        }
        return NewResponseUtil.makeSuccess(result);

    }

    public void editVerificationContent(String infoUuid, String verificationContent, long userId) {

        CfInfoSimpleModel fundingInfo = crowdfundingInfoSimpleBiz.getFundingInfo(infoUuid);
        if (Objects.isNull(fundingInfo)) {
            log.info("证实内容编辑失败, 众筹信息不存在, infoUuid={}", infoUuid);
            return;
        }

        // 改变证实信息
        changeVerificationInfo(userId, fundingInfo, verificationContent);

    }

    private void changeVerificationInfo(long userId, CfInfoSimpleModel fundingInfo, String verificationContent) {
        // 查询目前的证实信息
        CrowdFundingVerification crowdFundingVerification = crowdFundingVerificationBiz.getByUserIdAndInfoUuid(userId, fundingInfo.getInfoId());
        if (Objects.isNull(crowdFundingVerification)) {
            log.info("证实内容编辑失败, 证实信息不存在, caseId={}, userId={}", fundingInfo.getId(), userId);
            return ;
        }

        // 查询证实更改记录 更改过的不让更新
        ModifyVerificationDO modifyVerificationDO = modifyVerificationBiz.selectByVerifyId(crowdFundingVerification.getId());
        if (Objects.nonNull(modifyVerificationDO)) {
            log.info("证实内容编辑失败, 证实信息已经被修改过, caseId={}, userId={}", fundingInfo.getId(), userId);
            return ;
        }


        // 更新证实内容
        final int updateCount = crowdFundingVerificationBiz.updateVerificationDescription(crowdFundingVerification.getId(), verificationContent);
        if (updateCount <= 0) {
            log.info("证实内容编辑失败, 证实信息更新失败, caseId={}, userId={}", fundingInfo.getId(), userId);
            return ;
        }

        // 保存更改记录
        ModifyVerificationDO modifyVerification = ModifyVerificationDO.builder()
                .verifyId((long) crowdFundingVerification.getId())
                .caseId(fundingInfo.getId())
                .verifyUserId(userId)
                .modifyNum(1)
                .build();
        final int insertNum = modifyVerificationBiz.insertModifyVerificationRecord(modifyVerification);
        if (insertNum <= 0) {
            log.info("更改记录保存失败 caseId={} userId={}", fundingInfo.getId(), userId);
        }

        // 删除证实缓存
        crowdFundingVerificationBiz.deleteCaseVerificationCache(fundingInfo.getInfoId());

        // 关闭更新前的工单
        closeUgcWorkOrder(fundingInfo, userId, (long) crowdFundingVerification.getId());

        // 删除更新前证实的ugc记录
        deleteUgcRecord(fundingInfo, (long) crowdFundingVerification.getId());

        //发证实消息，给后台生成具体ugc审核任务
        CrowdFundingVerificationDeliver crowdFundingVerificationDeliver = new CrowdFundingVerificationDeliver();
        BeanUtils.copyProperties(crowdFundingVerification, crowdFundingVerificationDeliver);
        crowdFundingVerificationDeliver.setUniquelyIdentifies("verification");
        crowdFundingVerificationDeliver.setDescription(verificationContent);
        sendVerificationUgcMq(crowdFundingVerificationDeliver);

    }

    private void sendVerificationUgcMq(CrowdFundingVerificationDeliver crowdFundingVerificationDeliver) {
        MessageResult messageResult = producer.send(new Message<>(MQTopicCons.CF,
                MQTagCons.CF_VERIFICATION_MSG,
                MQTagCons.CF_VERIFICATION_MSG + "_" + crowdFundingVerificationDeliver.getCrowdFundingInfoId(),
                crowdFundingVerificationDeliver));
        log.info("发证实消息，给后台生成具体ugc审核任务, crowdFundingVerificationDeliver={} messageResult={}",
                JSON.toJSONString(crowdFundingVerificationDeliver), JSON.toJSONString(messageResult));
    }

    private void deleteUgcRecord(CfInfoSimpleModel fundingInfo, Long verifyId) {
        cfApiChaifenRiskClient.deleteVerify((long) fundingInfo.getId(), UgcTypeEnum.VERIFICATION.getValue(), verifyId);
    }

    private void closeUgcWorkOrder(CfInfoSimpleModel fundingInfo, long userId, Long verifyId) {

        // 查询目前的UGC工单
        final OrderSearchParam searchParam = new OrderSearchParam();
        searchParam.setCaseId(fundingInfo.getId());
        searchParam.setOrderType(WorkOrderType.ugcpinglun.getType() + "");
        searchParam.setWorkOrderExtTableParamList(Lists.newArrayList(WorkOrderExtTableParam.create("extId", verifyId + "")));
        Response<PaginationListVO<BasicWorkOrder>> response = workOrderReadFeignClient.search(searchParam);
        if (response.notOk()) {
            log.info("关闭工单失败, 查询UGC工单失败, caseId={}, userId={}", fundingInfo.getId(), userId);
            return;
        }

        List<BasicWorkOrder> orderList = Optional.ofNullable(response)
                .map(Response::getData)
                .map(PaginationListVO::getList)
                .orElse(Lists.newArrayList());
        if (CollectionUtils.isEmpty(orderList)) {
            log.info("关闭工单失败, 查询UGC工单为空, caseId={}, userId={}", fundingInfo.getId(), userId);
            return;
        }

        BasicWorkOrder workOrder = orderList.get(0);
        if (workOrder.getHandleResult() == HandleResultEnum.pass_show.getType()
                || workOrder.getHandleResult() == HandleResultEnum.only_self.getType()) {
            log.info("工单已经处理完毕, caseId={}, userId={}", fundingInfo.getId(), userId);
            return;
        }

        // 将当前的工单处理为手动关闭
        HandleOrderParam handleParam = new HandleOrderParam();
        handleParam.setCaseId(fundingInfo.getId());
        handleParam.setWorkOrderId(workOrder.getId());
        handleParam.setUserId(102L);
        handleParam.setHandleResult(HandleResultEnum.manual_lock.getType());
        handleParam.setOrderType(WorkOrderType.ugcpinglun.getType());
        Response<Void> result = workOrderCoreFeignClient.handle(handleParam);
        log.info("关闭工单, caseId={}, userId={}, workOrderId={} result={}", fundingInfo.getId(), userId, workOrder.getId(), result);
    }

    //埋点上报给大数据
    private CaseVerify track(CrowdFundingVerificationParam crowdFundingVerificationParam, Integer id,String uniquelyIdentifies,String clientIp) {
        if (Objects.isNull(crowdFundingVerificationParam)) {
            return null;
        }

        long verifyUserId = crowdFundingVerificationParam.getVerifyUserId();

        CaseVerify caseVerify = new CaseVerify();
        try {
            caseVerify.setLaunch_user_id(crowdFundingVerificationParam.getPatientUserId());
            caseVerify.setOperate_time(DateUtil.formatDateTime(new Date()));
            caseVerify.setInfo_id(Long.valueOf(crowdFundingVerificationParam.getCaseId()));
            caseVerify.setCase_id(crowdFundingVerificationParam.getCrowdFundingInfoId());
            caseVerify.setVerify_user_id(verifyUserId);
            caseVerify.setLaunch_user_name(crowdFundingVerificationParam.getUserName());
            caseVerify.setIp(clientIp);
            caseVerify.setVerify_id(Optional.ofNullable(id).map(Long::valueOf).orElse(0l));
            caseVerify.setShare_dv(0l);
            caseVerify.setUniquely_identifies(uniquelyIdentifies);

            caseVerify.setUser_tag(String.valueOf(verifyUserId));
            caseVerify.setUser_tag_type(UserTagTypeEnum.userid);

            analytics.track(caseVerify);
            log.info("大数据打点上报,案例证实成功:{}", JSONObject.toJSONString(caseVerify));
        } catch (Exception e) {
            log.error("大数据打点上报异常,案例证实成功:{}", JSONObject.toJSONString(caseVerify));
        }
        return caseVerify;
    }

    public StorageCrowdFundingVerificationResultVO otherEvent(CrowdFundingVerificationParam param, CrowdFundingVerification
            crowdFundingVerification, String channel, Platform platform) {
        //记数
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(param.getCrowdFundingInfoId());
        if (crowdfundingInfo != null) {
            RelationShip relationShip = RelationShip.codeOf(crowdFundingVerification.getRelationShip());
            if (relationShip != null && (relationShip == RelationShip.HEALTH_CARE || relationShip == RelationShip.SICK_FRIEND)) {
                cfInfoStatBiz.incVerifyHospitalCount(crowdfundingInfo.getId());
            } else {
                cfInfoStatBiz.incVerifyFriendCount(crowdfundingInfo.getId());
            }
            cfInfoStatBiz.incVerifyUserCount(crowdfundingInfo.getId());
            this.cfInfoTaskBiz.updateTimes(crowdfundingInfo.getInfoId(), CfTaskEnum.Rule.INVITE_FRIEND_TO_VERIFY_NEW);
        }
        try {
            JSONObject o = (JSONObject) JSONObject.toJSON(crowdFundingVerification);
            o.put("infoUuid", param.getCrowdFundingInfoId());
            o.put("createTime", DateUtil.getYmdhmsFromTimestamp(crowdFundingVerification.getCreateTime().getTime()));
            o.put("operateTime", DateUtil.getYmdhmsFromTimestamp(crowdFundingVerification.getOperateTime().getTime()));
            o.remove("crowdFundingInfoId");
            if (param.getUserThirdType() != null) {
                SDBizStatLogger.getCfLog().autoLoadUserThirdInfo(crowdFundingVerification.getVerifyUserId(), param.getUserThirdType(),
                        crowdFundingVerification.getSelfTag(), "verify", channel, "", platform)
                        .addToExtInfo(o).info();
            }
        } catch (Exception e) {
            log.error("error msg", e);
        }

        // 记录高风险渠道 <a href="https://wiki.shuiditech.com/pages/viewpage.action?pageId=178028611">wiki</a>
        riskChannelUGCService.onCreate(UgcTypeEnum.VERIFICATION, param.getChannel(), crowdFundingVerification.getId());

        StorageCrowdFundingVerificationResultVO result = new StorageCrowdFundingVerificationResultVO();
        result.setScore(cfUserStatBiz.incVerifyUserCount(crowdFundingVerification));

        if (param.getValid() == Validate.VALID.getCode()) {
            log.info("推送后发送证实消息... 1分钟后推送消息 userId: {}", param.getVerifyUserId());
            producer.send(new Message<>(MQTopicCons.CF,
                    MQTagCons.CF_VERIFICATION_SUCCESS,
                    MQTagCons.CF_VERIFICATION_SUCCESS + "_" + crowdFundingVerification.getCrowdFundingInfoId(),
                    param, DelayLevel.M1));
        }

        return result;
    }


    private CrowdFundingVerification buildCrowdFundingVerification(@NotNull final CrowdFundingVerificationParam param) {
        CrowdFundingVerification crowdFundingVerification = new CrowdFundingVerification();

        crowdFundingVerification.setPatientUserId(param.getPatientUserId());
        if (param.getVerifyUserId() > 0) {
            crowdFundingVerification.setVerifyUserId(param.getVerifyUserId());
        } else {
            crowdFundingVerification.setVerifyUserId(0);
        }
        crowdFundingVerification.setUserName(param.getUserName());
        crowdFundingVerification.setDescription(param.getDescription());
        if (null == param.getRelationShip() || param.getRelationShip() < 0) {
            crowdFundingVerification.setRelationShip(RelationShip.OTHER.getCode());
        } else {
            crowdFundingVerification.setRelationShip(param.getRelationShip());
        }
        crowdFundingVerification.setValid(param.getValid());
        if (!Strings.isNullOrEmpty(param.getOpenId())) {
            crowdFundingVerification.setOpenId(param.getOpenId());
        } else {
            crowdFundingVerification.setOpenId(StringUtils.EMPTY);
        }
        if (StringUtils.isEmpty(param.getMobile())) {
            param.setMobile("");
        }
        crowdFundingVerification.setMobile(param.getMobile());
        crowdFundingVerification.setCrowdFundingInfoId(param.getCrowdFundingInfoId());
        crowdFundingVerification.setSelfTag(StringUtils.trimToEmpty(param.getSelfTag()));
        crowdFundingVerification.setCreateTime(new Timestamp(System.currentTimeMillis()));
        crowdFundingVerification.setOperateTime(new Timestamp(System.currentTimeMillis()));
        crowdFundingVerification.setShareSourceId(StringUtils.defaultIfEmpty(param.getShareSourceId(), ""));

        // 医护标签升级需求字段,均为选填
        crowdFundingVerification.setProvinceCode( Objects.nonNull(param.getProvinceCode()) ? param.getProvinceCode().toString() : "0");
        crowdFundingVerification.setHospitalName(StringUtils.isNotEmpty(param.getHospitalName()) ? param.getHospitalName() : StringUtils.EMPTY);
        if (CollectionUtils.isNotEmpty(param.getMedicalImageList())) {
            crowdFundingVerification.setMedicalImageList(param.getMedicalImageList().stream().collect(Collectors.joining(",")));
        } else {
            crowdFundingVerification.setMedicalImageList(StringUtils.EMPTY);
        }
        return crowdFundingVerification;
    }

}
