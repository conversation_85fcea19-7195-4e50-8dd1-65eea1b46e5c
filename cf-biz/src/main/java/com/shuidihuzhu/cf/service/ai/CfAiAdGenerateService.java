package com.shuidihuzhu.cf.service.ai;

import com.shuidihuzhu.cf.ai.ProcessAdParam;
import com.shuidihuzhu.cf.vo.AiAdGenerateVO;
import com.shuidihuzhu.client.cf.api.model.AdGenerationResult;

/**
 * @Description:
 * @Author: pangh<PERSON><PERSON>
 * @Date: 2025/6/20 15:04
 */
public interface CfAiAdGenerateService {

    void insertAiAd(AdGenerationResult adGenerationResult);

    /**
     * 查询ai生成的广告语
     */
    AiAdGenerateVO queryUserAd(String infoUuid, long userId);

    /**
     * 查询按用户标签分组的广告语
     */
    AiAdGenerateVO queryGroupUserAd(String infoUuid, long userId);

    void processUserAd(ProcessAdParam processAdParam);

}
