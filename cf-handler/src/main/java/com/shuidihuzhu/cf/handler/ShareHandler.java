package com.shuidihuzhu.cf.handler;


import com.shuidihuzhu.cf.dao.crowdfunding.CfStatDataDao;
import com.shuidihuzhu.cf.event.CfSatDateEvent;
import com.shuidihuzhu.cf.model.crowdfunding.CfStatData;
import com.shuidihuzhu.cf.model.crowdfunding.message.CfShareRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ShareHandler {

    @Autowired
    private CfStatDataDao statDataDao;

    @EventListener(classes = CfSatDateEvent.class)
    public void onApplicationEvent(CfSatDateEvent cfSatDateEvent) {
        if (cfSatDateEvent == null){
            log.error("ShareHandler error cfSatDateEvent=null");
            return;
        }
        CfShareRecord shareRecord = cfSatDateEvent.getCfShareRecord();
        if (shareRecord != null){
            statDataDao.insertOrUpdate(new CfStatData(shareRecord));
        }
    }
}
