package com.shuidihuzhu.cf.handler;

import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoSimpleBiz;
import com.shuidihuzhu.cf.constants.AsyncPoolConstants;
import com.shuidihuzhu.cf.delegate.bigdata.FaceDelegate;
import com.shuidihuzhu.cf.delegate.risk.RiskEngineDelegate;
import com.shuidihuzhu.cf.event.OrderAddEvent;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSimpleModel;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.data.analytics.javasdk.core.Analytics;
import com.shuidihuzhu.data.analytics.javasdk.core.UserTagTypeEnum;
import com.shuidihuzhu.data.servicelog.meta.cf.CaseOrderAdd;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class OrderAddListener {

    @Autowired
    private FaceDelegate faceDelegate;

    @Autowired
    private Analytics analytics;

    @Autowired
    private RiskEngineDelegate riskEngineDelegate;

    @Autowired
    private CrowdfundingInfoSimpleBiz crowdfundingInfoSimpleBiz;

    @Async(AsyncPoolConstants.ORDER_ADD)
    @EventListener(classes = OrderAddEvent.class)
    public void orderAddStat(OrderAddEvent event) {
        log.info("orderAddStat {}", event);
        int caseId = event.getCaseId();
        long userId = event.getUserId();
        Long orderId = event.getOrderId();
        Response<Date> resp = faceDelegate.getLastViewCaseDetailTime(userId, caseId);
        if (resp == null || resp.notOk()) {
            return;
        }
        Date data = resp.getData();
        if (data == null) {
            return;
        }
        CfInfoSimpleModel fundingInfo = crowdfundingInfoSimpleBiz.getFundingInfoById(caseId);
        String infoUuid = fundingInfo.getInfoId();
        CaseOrderAdd v = new CaseOrderAdd();
        v.setCase_id(infoUuid);
        v.setOrder_id(orderId);
        v.setCreate_time(System.currentTimeMillis());
        v.setLast_view_detail_time(data);

        v.setUser_tag(String.valueOf(userId));
        v.setUser_tag_type(UserTagTypeEnum.userid);
        analytics.track(v);
        log.info("orderAddStat data {}", v);
    }

//    @Async(AsyncPoolConstants.ORDER_ADD)
//    @EventListener(classes = OrderAddEvent.class)
//    public void onOrderAddRiskCheck(OrderAddEvent event) {
//        log.info("onOrderAddRiskCheck {}", event);
//        int caseId = event.getCaseId();
//        long userId = event.getUserId();
//        Long orderId = event.getOrderId();
//        String channel = event.getChannel();
//        // 风控校验下单
//        riskEngineDelegate.handleOrderAdd(caseId, userId, orderId, channel);
//    }



}
