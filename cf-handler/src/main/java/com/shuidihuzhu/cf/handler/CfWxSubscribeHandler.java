package com.shuidihuzhu.cf.handler;


import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.account.model.UserThirdModel;
import com.shuidihuzhu.cf.biz.crowdfunding.toufang.ICfToufangInviteService;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.delegate.UserThirdDelegate;
import com.shuidihuzhu.cf.domain.dedicated.CfToufangInvitorVisitDO;
import com.shuidihuzhu.cf.event.CfWxSubscribeEvent;
import com.shuidihuzhu.cf.mq.payload.WxSubscribePayload;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 处理微信关注事件
 *
 * <AUTHOR>
 * 打散延迟消息触发时间  削尖峰
 */
@Component
@Slf4j
public class CfWxSubscribeHandler {
    @Autowired
    private ApplicationService applicationService;
    @Autowired
    private UserThirdDelegate userThirdDelegate;
    @Autowired
    private ICfToufangInviteService cfToufangInviteService;
    @Autowired(required = false)
    private Producer producer;


    @EventListener(classes = CfWxSubscribeEvent.class)
    public void onApplicationEvent(CfWxSubscribeEvent event) {
        if (event == null) {
            log.error("error event null");
            return;
        }
        WxSubscribePayload payload = new WxSubscribePayload(event.getOpenId(),event.getUserThirdType(),event.getEventKey());
        DateTime now = new DateTime();
        long expectTime = 0;
        //第二天10点左右发送
        if (applicationService.isProduction()) {
            expectTime = now.plusDays(1).withHourOfDay(10).withMinuteOfHour(RandomUtils.nextInt(0, 30)).getMillis();
        } else {
            expectTime = now.plusDays(0).plusHours(0).plusMinutes(2).getMillis();
        }
        Message message = Message.ofSchedule(MQTopicCons.CF, MQTagCons.CF_WX_SUBSCRIBE_TRANSFORM_TO_OTHER, event.getOpenId() + event.getUserThirdType(), payload, expectTime / 1000);
        producer.send(message);
        sendWxPyqTouFangCallback(event);
    }

    /**
     * 微信朋友圈投放关注广告记录关注记录
     * @param subscribeEvent
     */
    public void sendWxPyqTouFangCallback(CfWxSubscribeEvent subscribeEvent) {
        try {
            if (subscribeEvent.getUserThirdType() != 75) {
                //只处理水滴筹服务号，其它号不处理
                return;
            }
            log.info("sendWxPyqTouFangCallback wxMessage:{}", JSON.toJSONString(subscribeEvent));
            UserThirdModel userThirdModel = userThirdDelegate.getThirdModelWithOpenId(subscribeEvent.getOpenId());
            long userId = -1;
            if (userThirdModel != null) {
                userId = userThirdModel.getUserId();
            }
            if (userId <= 0) {
                return;
            }
            CfToufangInvitorVisitDO visitDO = new CfToufangInvitorVisitDO();
            visitDO.setChannel("ad_wxpyq_jiafen");
            visitDO.setCreateTime(new Date());
            visitDO.setInvitorUserId(userId);
            visitDO.setSourceUserId(0l);
            cfToufangInviteService.add(visitDO);
        }catch (Exception e){
            log.error("sendWxPyqTouFangCallback Exception ",e);
        }
    }

}
