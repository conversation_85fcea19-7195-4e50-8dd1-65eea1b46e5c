package com.shuidihuzhu.cf.handler.userquery4sdb;

import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.cf.biz.crowdfunding.CfFirstApproveBiz;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceRefundFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.model.crowdfunding.AdminCfRefund;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.vo.SdbTFUserInfoVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 发起人表数据源处理器
 *
 * <AUTHOR>
 * @since 2025/5/7
 */
public class MaterialDataSourceHandler implements UserInfoDataSourceHandler {
    /**
     * 直系亲属关系ID列表（1,2,3,4,10,11,12,13）
     * 其中10,11,12,13合并到3中
     */
    private static final Set<Integer> FAMILY_REL_TYPES = new HashSet<>(Arrays.asList(
            UserRelTypeEnum.SELF.getValue(),
            UserRelTypeEnum.COUPON.getValue(),
            UserRelTypeEnum.PARENT.getValue(),
            UserRelTypeEnum.CHILDREN.getValue(),
            UserRelTypeEnum.SELFFATHER.getValue(),
            UserRelTypeEnum.SELFMOTHER.getValue(),
            UserRelTypeEnum.COUPONFATHER.getValue(),
            UserRelTypeEnum.COUPONMOTHER.getValue()));
            
    private final CfFirstApproveBiz cfFirstApproveBiz;
    private final CfFinanceRefundFeignClient cfFinanceRefundFeignClient;
    
    public MaterialDataSourceHandler(CfFirstApproveBiz cfFirstApproveBiz, CfFinanceRefundFeignClient cfFinanceRefundFeignClient) {
        this.cfFirstApproveBiz = cfFirstApproveBiz;
        this.cfFinanceRefundFeignClient = cfFinanceRefundFeignClient;

    }
    
    @Override
    public List<SdbTFUserInfoVo> process(Long userId) {
        // 步骤1：查询发起人信息
        List<CfFirsApproveMaterial> materialList = cfFirstApproveBiz.getByUserId(userId);
        
        // 步骤2：过滤非直系亲属数据
        List<CfFirsApproveMaterial> filteredMaterialList = filterFamilyRelations(materialList);
        // 过滤整体退款的案例
        filteredMaterialList = filterAllRefund(filteredMaterialList);
        
        // 步骤3：组装数据
        return assembleMaterialData(filteredMaterialList);
    }

    private List<CfFirsApproveMaterial> filterAllRefund(List<CfFirsApproveMaterial> filteredMaterialList) {
        if (CollectionUtils.isEmpty(filteredMaterialList)) {
            return Collections.emptyList();
        }
        List<String> infoUuids = filteredMaterialList.stream().map(CfFirsApproveMaterial::getInfoUuid).distinct().collect(Collectors.toList());
        FeignResponse<List<AdminCfRefund>> response = cfFinanceRefundFeignClient.getListByInfoUuids(infoUuids);
        return Optional.ofNullable(response)
                .filter(FeignResponse::ok)
                .filter(r -> CollectionUtils.isNotEmpty(r.getData()))
                .map(r -> {
                    List<AdminCfRefund> data = response.getData();
                    Set<String> allRefundInfoUuids = data.stream().map(AdminCfRefund::getInfoUuid).collect(Collectors.toSet());
                    return filteredMaterialList.stream()
                            .filter(item -> !allRefundInfoUuids.contains(item.getInfoUuid()))
                            .collect(Collectors.toList());
                })
                .orElse(filteredMaterialList);
    }

    @Override
    public String getDataSourceName() {
        return "发起人表";
    }
    
    /**
     * 过滤直系亲属关系
     */
    private List<CfFirsApproveMaterial> filterFamilyRelations(List<CfFirsApproveMaterial> materialList) {
        if (CollectionUtils.isEmpty(materialList)) {
            return new ArrayList<>();
        }
        
        return materialList.stream()
                .filter(material -> FAMILY_REL_TYPES.contains(material.getUserRelationType()))
                .collect(Collectors.toList());
    }

    /**
     * 组装发起人表数据
     */
    private List<SdbTFUserInfoVo> assembleMaterialData(List<CfFirsApproveMaterial> filteredMaterialList) {
        if (CollectionUtils.isEmpty(filteredMaterialList)) {
            return new ArrayList<>();
        }
        List<SdbTFUserInfoVo> result = new ArrayList<>();
        SdbTFUserInfoVo selfInfo = null;
        for (CfFirsApproveMaterial material : filteredMaterialList) {
            // 设置用户姓名和身份证
            if (material.getUserRelationType() == UserRelTypeEnum.SELF.getValue()) {
                // 如果是本人，使用患者信息
                if (selfInfo == null) {
                    selfInfo = new SdbTFUserInfoVo(material.getPatientCryptoIdcard(), material.getPatientRealName(), UserRelTypeEnum.SELF.getValue());
                }
            } else {
                // 关系类型转换,合并到父母
                SdbTFUserInfoVo other = getSdbTFUserInfoVo(material);
                result.add(other);
                if (selfInfo == null) {
                    selfInfo = new SdbTFUserInfoVo(material.getSelfCryptoIdcard(), material.getSelfRealName(), UserRelTypeEnum.SELF.getValue());
                }
            }
        }
        if (selfInfo != null) {
            result.add(selfInfo);
        }
        Map<String, SdbTFUserInfoVo> distinctMap = result.stream()
                .filter(item -> StringUtils.isNotBlank(item.getCryptoIdCard()))
                .collect(Collectors.toMap(
                        SdbTFUserInfoVo::getCryptoIdCard,
                        Function.identity(),
                        (existing, replacement) -> replacement));

        return new ArrayList<>(distinctMap.values());
    }

    @NotNull
    private static SdbTFUserInfoVo getSdbTFUserInfoVo(CfFirsApproveMaterial material) {
        int relType = material.getUserRelationType();
        if (relType == UserRelTypeEnum.SELFFATHER.getValue()
                || relType == UserRelTypeEnum.SELFMOTHER.getValue()
                || relType == UserRelTypeEnum.COUPONFATHER.getValue()
                || relType == UserRelTypeEnum.COUPONMOTHER.getValue()) {
            relType = UserRelTypeEnum.PARENT.getValue();
        }
        SdbTFUserInfoVo other = new SdbTFUserInfoVo(material.getPatientCryptoIdcard(), material.getPatientRealName(), relType);
        return other;
    }
} 