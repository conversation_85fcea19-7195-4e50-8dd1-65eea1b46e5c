package com.shuidihuzhu.cf.handler;


import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.CfStatDataDao;
import com.shuidihuzhu.cf.event.CfSatDateEvent;
import com.shuidihuzhu.cf.model.crowdfunding.CfStatData;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.message.CfPayRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;

@Component
@Slf4j
public class DonationHandler {

    @Autowired
    private CfStatDataDao statDataDao;

    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;


    @EventListener(classes = CfSatDateEvent.class)
    public void onApplicationEvent(CfSatDateEvent cfSatDateEvent) {
        if (cfSatDateEvent == null){
            log.error("DonationHandler error cfSatDateEvent=null");
            return;
        }

        CfPayRecord payRecord = cfSatDateEvent.getPayRecord();

        if (payRecord != null){
            CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(Long.valueOf(payRecord.getCaseId()).intValue());
            payRecord.setCaseCreateTime(new Timestamp(crowdfundingInfo.getCreateTime().getTime()));
            // 需要注意  筹款结束时间是会变动的
            payRecord.setFinishTime(new Timestamp(crowdfundingInfo.getEndTime().getTime()));
            statDataDao.insertOrUpdate(new CfStatData(payRecord));
        }
    }
}
