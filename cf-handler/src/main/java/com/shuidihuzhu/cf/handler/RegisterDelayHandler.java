package com.shuidihuzhu.cf.handler;


import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.biz.crowdfunding.CfAdRegisterBiz;
import com.shuidihuzhu.cf.biz.wx.CfWxMpCommonBiz;
import com.shuidihuzhu.cf.constants.MsgRecordContant;
import com.shuidihuzhu.cf.constants.WxConstants;
import com.shuidihuzhu.cf.event.CfRegisterDelayDateEvent;
import com.shuidihuzhu.cf.model.crowdfunding.CfAdRegister;
import com.shuidihuzhu.cf.service.msg.MsgClientService;
import com.shuidihuzhu.cf.service.msg.MsgClientV2Service;
import com.shuidihuzhu.msg.enums.RecordConstant;

import com.shuidihuzhu.msg.vo.rpc.MsgRecord;
import com.shuidihuzhu.wx.enums.AccountThirdTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
@RefreshScope
public class RegisterDelayHandler {

    @Resource
    protected CfWxMpCommonBiz cfWxMpCommonBiz;

    @Resource
    private CfAdRegisterBiz cfAdRegisterBiz;

    @Autowired
    private MsgClientService msgService;

    @Resource
    private MsgClientV2Service msgClientV2Service;

    @EventListener(classes = CfRegisterDelayDateEvent.class)
    public void onApplicationEvent(CfRegisterDelayDateEvent event) {
        if (event == null) {
            log.error("error event null");
            return;
        }
        long id = event.getId();
        send(id);
    }

    private void send(long id) {
        if (id == 0) {
            log.warn("id = 0");
            return;
        }
        CfAdRegister cfAdRegister = cfAdRegisterBiz.getById(id);

        if (cfAdRegister == null) {
            log.warn("register null id: {}", id);
            return;
        }

        String mobile = cfAdRegister.getMobile();
        if (StringUtils.isEmpty(mobile)) {
            log.warn("mobile null");
            return;
        }

        Long userId = cfAdRegister.getUserId();

        if (null == userId || userId <= 0) {
            log.warn("openId null mobile: {}", mobile);
            return;
        }

        // 是否关注了水滴筹(3)(899)
        boolean newSubscribe = cfWxMpCommonBiz.checkHasSubscribe(userId, WxConstants.FUNDRAISER_THIRD_TYPE);
        if (newSubscribe) {
            return;
        }

        //发短信
        log.info("send sms to userId={}, mobile={}", userId, mobile);

        String modelNum0 = "271duanxin";
        msgClientV2Service.sendSmsMsg(modelNum0, Lists.newArrayList(mobile), false);
    }

}
