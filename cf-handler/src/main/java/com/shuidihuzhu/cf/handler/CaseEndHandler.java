package com.shuidihuzhu.cf.handler;


import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.activity.enums.DonateCooperateActivityStatusEnum;
import com.shuidihuzhu.cf.activity.feign.CaseCountFeignClient;
import com.shuidihuzhu.cf.biz.crowdfunding.PatientEvaluationRecordBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enums.activity.Activity111CaseTypeEnum;
import com.shuidihuzhu.cf.event.CaseEndEvent;
import com.shuidihuzhu.cf.finance.mq.FinanceMQTagCons;
import com.shuidihuzhu.cf.model.CaseEndModel;
import com.shuidihuzhu.cf.mq.producer.CommonMessageHelperService;
import com.shuidihuzhu.cf.mq.producer.MessageBuilder;
import com.shuidihuzhu.cf.service.activity.ActivityVenueManageService;
import com.shuidihuzhu.cf.service.caseinfo.CaseEndRecordService;
import com.shuidihuzhu.cf.service.remark.RemarkService;
import com.shuidihuzhu.cf.service.tog.HainanService;
import com.shuidihuzhu.cf.service.tog.JinYunService;
import com.shuidihuzhu.client.cf.search.client.CfSearchClient;
import com.shuidihuzhu.client.cf.search.model.EsCaseLabelColum;
import com.shuidihuzhu.client.cf.search.model.enums.CaseTypeEnum;
import com.shuidihuzhu.infra.starter.rocketmq.core.DelayLevel;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 案例结束spring事件监听
 * <AUTHOR>
 */
@Component
@Slf4j
public class CaseEndHandler {

    @Resource
    private CommonMessageHelperService commonMessageHelperService;

    @Resource
    private CaseEndRecordService caseEndRecordService;

    @Resource
    private CaseCountFeignClient caseCountFeignClient;

    @Resource
    private JinYunService jinYunService;

    @Resource
    private HainanService hainanService;

    @Resource
    private PatientEvaluationRecordBiz patientEvaluationRecordBiz;

    @Resource
    private CfSearchClient cfSearchClient;

    private static final String MQ_TAG = MQTagCons.CF_CASE_END;

    @EventListener(classes = CaseEndEvent.class)
    public void onApplicationEvent(CaseEndEvent caseEndEvent) {
        if (caseEndEvent == null){
            log.error("CaseEndHandler error caseEndEvent=null");
            return;
        }
        CaseEndModel caseEndModel = caseEndEvent.getCaseEndModel();
        if (caseEndModel == null){
            log.error("CaseEndHandler error caseEndModel=null");
            return;
        }
        log.info("CaseEndHandler caseId:{}, finishStatus:{}", caseEndModel.getCaseId(), caseEndModel.getFinishStatus());
        int caseId = caseEndModel.getCaseId();

        // 发送案例结束mq
        sendEndMQ(caseEndModel, caseId);

        // 保存案例结束记录
        saveRecord(caseEndModel, caseId);

        try {
            caseCountFeignClient.onCaseEnd(caseId);
        } catch (Exception e) {
            log.error("案例结束", e);
        }

        try {
            jinYunService.updateFinishStatus(caseEndModel.getFinishStatus(), caseEndModel.getCaseId());
        } catch (Exception e) {
            log.error("jinYunService updateFinishStatus", e);
        }

        try {
            hainanService.updateFinishStatus(caseEndModel.getFinishStatus(), caseEndModel.getCaseId());
        } catch (Exception e) {
            log.error("hainanService updateFinishStatus", e);
        }

        try {
            patientEvaluationRecordBiz.sendEvaluationMsg(caseId);
        } catch (Exception e) {
            log.error("patientEvaluationRecordBiz sendEvaluationMsg", e);
        }

        try {
            // 案例结束打标签
            caseEndAddTag(caseEndModel);
        } catch (Exception e) {
            log.error("patientEvaluationRecordBiz caseEndAddTag ", e);
        }

    }

    private void caseEndAddTag(CaseEndModel caseEndModel) {
        Map<String, Object> param = Maps.newHashMap();
        param.put(EsCaseLabelColum.ID, caseEndModel.getCaseId());
        param.put(EsCaseLabelColum.CASE_TYPE, CaseTypeEnum.END_CASE.getDesc());
        param.put(EsCaseLabelColum.CASE_END_ENUM, caseEndModel.getReasonType());
        param.put(EsCaseLabelColum.CASE_END_REASON, caseEndModel.getDesc());
        param.put(EsCaseLabelColum.CASE_FINISH_STATUS, caseEndModel.getFinishStatus());

        // sea后台结束的话，没有枚举
        if (caseEndModel.getOperatorId() != 0) {
            param.put(EsCaseLabelColum.CASE_END_ENUM, -1);
        }

        cfSearchClient.updateCfCase(param);
    }

    private void saveRecord(CaseEndModel caseEndModel, int caseId) {
        try {
            caseEndRecordService.add(caseEndModel);
        } catch (Exception e) {
            log.error("save record error caseId:{}", caseId, e);
        }
    }

    private void sendEndMQ(CaseEndModel caseEndEvent, int caseId) {
        try {
            // 发送案例结束MQ
            Message<CaseEndModel> message = MessageBuilder
                    .createWithPayload(caseEndEvent)
                    .addKey(MQ_TAG, caseId)
                    .setTags(MQ_TAG)
                    .setDelayLevel(DelayLevel.S1)
                    .build();
            commonMessageHelperService.send(message);
        } catch (Exception e) {
            log.error("send end mq error caseId:{}", caseId, e);
        }
    }

}
