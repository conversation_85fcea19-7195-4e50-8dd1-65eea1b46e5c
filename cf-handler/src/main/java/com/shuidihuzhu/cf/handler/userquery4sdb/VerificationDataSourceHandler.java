package com.shuidihuzhu.cf.handler.userquery4sdb;

import com.google.common.collect.Lists;
import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.UserRealInfo;
import com.shuidihuzhu.cf.service.huzhugrpc.HzUserRealInfoService;
import com.shuidihuzhu.cf.vo.SdbTFUserInfoVo;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 证实表数据源处理器
 *
 * <AUTHOR>
 * @since 2025/5/7
 */
public class VerificationDataSourceHandler implements UserInfoDataSourceHandler {
    private final HzUserRealInfoService hzUserRealInfoService;
    
    public VerificationDataSourceHandler(HzUserRealInfoService hzUserRealInfoService) {
        this.hzUserRealInfoService = hzUserRealInfoService;
    }
    
    @Override
    public List<SdbTFUserInfoVo> process(Long userId) {
        // 查询证实表
        UserRealInfo verificationUserRealInfo = queryVerificationsByUserId(userId);
        
        // 组装证实数据
        return assembleVerificationData(verificationUserRealInfo);
    }
    
    /**
     * 查询证实表数据
     */
    private UserRealInfo queryVerificationsByUserId(Long userId) {
        if (userId == null || userId <= 0) {
            return null;
        }
        return hzUserRealInfoService.getLastOne(userId);
    }
    
    /**
     * 组装证实表数据
     */
    private List<SdbTFUserInfoVo> assembleVerificationData(UserRealInfo verificationUserRealInfo) {
        if (Objects.isNull(verificationUserRealInfo)) {
            return Collections.emptyList();
        }
        SdbTFUserInfoVo sdbTFUserInfoVo = new SdbTFUserInfoVo(verificationUserRealInfo.getCryptoIdCard(),
                verificationUserRealInfo.getName(), UserRelTypeEnum.SELF.getValue());
        return Lists.newArrayList(sdbTFUserInfoVo);
    }
    
    @Override
    public String getDataSourceName() {
        return "证实表";
    }
} 