package com.shuidihuzhu.cf.handler;

import com.shuidihuzhu.cf.biz.crowdfunding.broadcast.CfBroadcastBiz;
import com.shuidihuzhu.cf.event.CfSatDateEvent;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.model.crowdfunding.message.CfPayRecord;
import com.shuidihuzhu.cf.model.crowdfunding.message.CfShareRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @DATE 2018/8/6
 * 参考代码发现  只有捐款才进行记录
 */
@Component
@Slf4j
public class BroadcastDonationHandler {


    private static final int amount = 2000;
    private static final int size = 20;

    @Autowired
    private CfBroadcastBiz cfBroadcastBiz;

    @EventListener(classes = CfSatDateEvent.class)
    public void onApplicationEvent(CfSatDateEvent cfSatDateEvent) {
        if (cfSatDateEvent == null){
            log.error("ShareHandler error cfSatDateEvent=null");
            return;
        }

        CrowdfundingOrder crowdfundingOrder = cfSatDateEvent.getCrowdfundingOrder();
        //捐款金额大于20 并且不是匿名
        if (crowdfundingOrder!= null
                && !crowdfundingOrder.isAnonymous()
                && crowdfundingOrder.getAmount() >= amount){
            cfBroadcastBiz.calBroadCast(crowdfundingOrder,size);
        }
    }
}



