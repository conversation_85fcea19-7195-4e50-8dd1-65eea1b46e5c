package com.shuidihuzhu.cf.handler.userquery4sdb;

import com.shuidihuzhu.cf.vo.SdbTFUserInfoVo;

import java.util.List;

/**
 * 用户信息数据源处理器接口
 * 采用责任链模式，按照优先级依次尝试不同的数据源获取用户信息
 *
 * <AUTHOR>
 * @since 2025/5/7
 */
public interface UserInfoDataSourceHandler {
    /**
     * 处理获取用户信息的逻辑
     *
     * @param userId 用户ID
     * @return 用户信息列表
     */
    List<SdbTFUserInfoVo> process(Long userId);
    
    /**
     * 获取数据源名称，用于日志记录
     *
     * @return 数据源名称
     */
    String getDataSourceName();
} 