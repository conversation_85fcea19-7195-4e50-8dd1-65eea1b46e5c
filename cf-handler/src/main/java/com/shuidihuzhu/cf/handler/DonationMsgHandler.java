package com.shuidihuzhu.cf.handler;


import com.shuidihuzhu.cf.event.CfSatDateEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.time.LocalDate;

/**
 * https://wiki.shuiditech.com/pages/viewpage.action?pageId=141132717
 * 当日发起的案例且没有关注公众号的收到捐款发信息
 */
@Component
@Slf4j
public class DonationMsgHandler {


    @EventListener(classes = CfSatDateEvent.class)
    public void onApplicationEvent(CfSatDateEvent cfSatDateEvent) {

        // 无用，删除
    }

    private String getKey(int caseId){
        return "1502_"+ LocalDate.now()+"_"+caseId;
    }

}
