package com.shuidihuzhu.cf.handler.userquery4sdb;

import com.shuidihuzhu.cf.biz.crowdfunding.CfFirstApproveBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingReportBiz;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceRefundFeignClient;
import com.shuidihuzhu.cf.service.huzhugrpc.HzUserRealInfoService;
import com.shuidihuzhu.cf.vo.SdbTFUserInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 用户信息数据源处理器管理类
 * 负责初始化和管理所有处理器
 *
 * <AUTHOR>
 * @since 2025/5/7
 */
@Component
@Slf4j
public class UserInfoDataSourceHandlerManager {
    
    @Autowired
    private CfFirstApproveBiz cfFirstApproveBiz;
    
    @Autowired
    private HzUserRealInfoService hzUserRealInfoService;
    
    @Autowired
    private CrowdfundingReportBiz crowdfundingReportBiz;
    @Autowired
    private CfFinanceRefundFeignClient cfFinanceRefundFeignClient;
    
    /**
     * 数据源处理器链
     */
    private List<UserInfoDataSourceHandler> handlers;
    
    /**
     * 初始化处理器链
     */
    @PostConstruct
    public void init() {
        this.handlers = new ArrayList<>();
        // 初始化处理器，按照优先级顺序添加
        this.handlers.add(new MaterialDataSourceHandler(cfFirstApproveBiz, cfFinanceRefundFeignClient));
        this.handlers.add(new VerificationDataSourceHandler(hzUserRealInfoService));
//        this.handlers.add(new ReportDataSourceHandler(crowdfundingReportBiz));
        log.info("用户信息数据源处理器初始化完成，共{}个处理器", handlers.size());
    }
    
    /**
     * 处理获取用户信息的逻辑
     *
     * @param userId 用户ID
     * @return 用户信息列表
     */
    public List<SdbTFUserInfoVo> process(Long userId) {
        if (userId == null || userId <= 0) {
            log.warn("查询水滴保投放用户信息，userId为空或小于等于0");
            return Collections.emptyList();
        }
        
        log.info("查询水滴保投放用户信息，userId={}", userId);
        
        // 责任链模式处理，依次尝试不同数据源
        for (UserInfoDataSourceHandler handler : handlers) {
            List<SdbTFUserInfoVo> result = handler.process(userId);
            if (CollectionUtils.isNotEmpty(result)) {
                log.info("从{}获取到水滴保投放用户信息，userId={}，dataSize={}",
                        handler.getDataSourceName(), userId, result.size());
                return result;
            }
        }
        
        log.info("所有数据源均未获取到水滴保投放用户信息，userId={}", userId);
        return Collections.emptyList();
    }
} 