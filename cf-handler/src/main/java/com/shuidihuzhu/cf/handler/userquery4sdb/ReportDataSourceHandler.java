package com.shuidihuzhu.cf.handler.userquery4sdb;

import com.google.common.collect.Lists;
import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingReportBiz;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport;
import com.shuidihuzhu.cf.vo.SdbTFUserInfoVo;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 举报表数据源处理器
 *
 * <AUTHOR>
 * @since 2025/5/7
 */
public class ReportDataSourceHandler implements UserInfoDataSourceHandler {
    private final CrowdfundingReportBiz crowdfundingReportBiz;
    
    public ReportDataSourceHandler(CrowdfundingReportBiz crowdfundingReportBiz) {
        this.crowdfundingReportBiz = crowdfundingReportBiz;
    }
    
    @Override
    public List<SdbTFUserInfoVo> process(Long userId) {
        // 查询举报表
        CrowdfundingReport crowdfundingReport = queryReportsByUserId(userId);
        
        // 组装举报数据
        return assembleReportData(crowdfundingReport);
    }
    
    /**
     * 查询举报表数据
     */
    private CrowdfundingReport queryReportsByUserId(Long userId) {
        if (userId == null || userId <= 0) {
            return null;
        }
        // 使用crowdfundingReportBiz服务查询
        return crowdfundingReportBiz.queryLastReportByUserWithReal(userId.longValue());
    }
    
    /**
     * 组装举报表数据
     */
    private List<SdbTFUserInfoVo> assembleReportData(CrowdfundingReport crowdfundingReport) {
        if (Objects.isNull(crowdfundingReport)) {
            return Collections.emptyList();
        }
        SdbTFUserInfoVo sdbTFUserInfoVo = new SdbTFUserInfoVo(crowdfundingReport.getIdentity(),
               crowdfundingReport.getName(), UserRelTypeEnum.SELF.getValue());
        return Lists.newArrayList(sdbTFUserInfoVo);
    }
    
    @Override
    public String getDataSourceName() {
        return "举报表";
    }
} 