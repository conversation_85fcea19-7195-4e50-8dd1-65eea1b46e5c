package com.shuidihuzhu.cf.mq.consumers.transform2other;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.shuidihuzhu.client.dataservice.bi.v1.BiApiClient;
import com.shuidihuzhu.client.dataservice.bi.v1.dto.UserLocationsDTO;
import com.shuidihuzhu.client.model.Response;
import com.shuidihuzhu.common.web.util.http.HttpResponseModel;
import com.shuidihuzhu.common.web.util.http.HttpUtil;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.message.BasicNameValuePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

/**
 * @author: Alvin Tian
 * @date: 2018/5/2 14:19
 */
@Service
@Slf4j
public class UserLocationService {

	@Autowired
	private BiApiClient biApiClient;



	public Map<Long, UserLocation> mapLastAreas(List<Long> userIds) {
		userIds = userIds.stream().distinct().collect(Collectors.toList());

		if (CollectionUtils.isEmpty(userIds)){
			return MapUtils.EMPTY_MAP;
		}
		Long[] u = new Long[userIds.size()];
		Response<List<UserLocationsDTO>> response =  biApiClient.listLocationV2(userIds.toArray(u));

		if (response == null || response.getCode() > 0 || CollectionUtils.isEmpty(response.getData())){
			log.info("listLocationV2 response={} userIds={}",JSON.toJSONString(response),userIds);
			return MapUtils.EMPTY_MAP;
		}
		List<UserLocationsDTO> list = response.getData();

		Map<Long, UserLocation> map = Maps.newHashMap();

		list.stream()
			.filter(r -> StringUtils.isNotEmpty(r.getUserId()))
		    .forEach(o -> {
					UserLocation userLocation = new UserLocation();
					userLocation.setUserId(Long.valueOf(o.getUserId()));
					userLocation.setLastVisitCity(o.getLast_visit_city());
					userLocation.setLastVisitProvince(o.getLast_visit_province());
					userLocation.setLastVisitCountry(o.getLast_visit_country());
					map.put(Long.valueOf(o.getUserId()),userLocation);
					});

		return map;


	}

	@Deprecated
	public Map<Long, UserLocation> mapLastAreasOld(List<Long> userIds) {
		userIds = userIds.stream().distinct().collect(Collectors.toList());
		List<org.apache.http.NameValuePair> nameValuePairs = Lists.newArrayList();
		nameValuePairs.add(new BasicNameValuePair("userId", Joiner.on(",").join(userIds)));
		HttpResponseModel httpResponseModel =
				HttpUtil.httpPost("https://api-bi.shuidihuzhu.com/api/bi-service/user/listLocation",
						nameValuePairs,
						"utf-8",
						20000,
						20000,
						20000);
		if (httpResponseModel.getStatusCode() == 200 && httpResponseModel.isHasException() == false) {
			String bodyString = httpResponseModel.getBodyString();
			if (StringUtils.isEmpty(bodyString)) {
				log.error("mapLastAreas: {}", httpResponseModel.toString());
				return null;
			}
			LocationResult locationResult = JSON.parseObject(bodyString, LocationResult.class);
			if (locationResult.getRet() == 0 && !CollectionUtils.isEmpty(locationResult.getContent())) {
				return locationResult.getContent().stream()
				                     .collect(Collectors.toMap(UserLocation::getUserId, Function.identity(), (o, o2) -> o2));
			} else {
				log.warn("mapLastAreas: {}", bodyString);
			}
		} else {
			log.error("mapLastAreas: error: {}", httpResponseModel);
		}
		return MapUtils.EMPTY_MAP;

	}

	public String getUserArea(long userId, String lable) {
		Map<Long, UserLocation> locationMap = mapLastAreas(Arrays.asList(userId));
		if (MapUtils.isNotEmpty(locationMap)) {
			UserLocation userLocation = locationMap.get(userId);
			if (userLocation == null) {
				return lable;
			}
			String unkown = "unkown";
			if (StringUtils.isNotEmpty(userLocation.getLastVisitCity()) && !unkown.equals(userLocation.getLastVisitCity())) {
				return userLocation.getLastVisitCity();
			}
			if (StringUtils.isNotEmpty(userLocation.getLastVisitProvince()) && !unkown.equals(userLocation.getLastVisitProvince())) {
				return userLocation.getLastVisitProvince();
			}
			return lable;
		}
		return "";
	}
}
