package com.shuidihuzhu.cf.mq.consumers.activity;


import com.shuidihuzhu.cf.activity.enums.FeeDetailStatusEnum;
import com.shuidihuzhu.cf.activity.enums.FeeTypeEnum;
import com.shuidihuzhu.cf.activity.model.DonateCooperateFeeVO;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoSimpleBiz;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enums.activity.ActivityRedPocketStatusEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSimpleModel;
import com.shuidihuzhu.cf.mq.common.BaseMessageConsumer;
import com.shuidihuzhu.cf.service.activity.subsidy.IActivityRedPocketService;
import com.shuidihuzhu.cf.vo.activity.ActivityRedPacketNodeVO;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * Created by sven on 2020/4/26.
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RocketMQListener(id = "DONATE_COOPERATE_BILL_USEFUL",
        tags = "DONATE_COOPERATE_BILL_USEFUL",
        group = "cf-api" + "DONATE_COOPERATE_BILL_USEFUL",
        topic = MQTopicCons.CF)
public class ActivityNodeWaterBillsConsumer  extends BaseMessageConsumer<DonateCooperateFeeVO>
        implements MessageListener<DonateCooperateFeeVO> {

    @Resource
    private IActivityRedPocketService redPocketService;

    @Resource
    private CrowdfundingInfoSimpleBiz simpleBiz;

    @Override
    protected boolean handle(ConsumerMessage<DonateCooperateFeeVO> consumerMessage) {

        DonateCooperateFeeVO payload = consumerMessage.getPayload();
        int caseId = payload.getCaseId();

        if(payload.getFeeTypeEnum() != FeeTypeEnum.SHARE_COOPERATE_FEE
            && payload.getFeeTypeEnum() != FeeTypeEnum.RED_POCKET_FEE) {
            return true;
        }

        CfInfoSimpleModel cfInfoSimpleModel = simpleBiz.getFundingInfoById(caseId);
        if(cfInfoSimpleModel == null){
            return true;
        }

        FeeDetailStatusEnum feeDetailStatusEnum = consumerMessage.getPayload().getStatus();
        if(feeDetailStatusEnum == null || feeDetailStatusEnum != FeeDetailStatusEnum.USEFUL){
            return true;
        }

        List<ActivityRedPacketNodeVO> list = redPocketService.getByCaseId(caseId);

        if(CollectionUtils.isEmpty(list)){
            return true;
        }

        Optional<ActivityRedPacketNodeVO> optional = list.stream().filter(r->r.getStatus() == ActivityRedPocketStatusEnum.DOING.getCode()).findFirst();
        if(!optional.isPresent()){
            return true;
        }

        ActivityRedPacketNodeVO vo = optional.get();

        ActivityRedPacketNodeVO next = null;
        for(ActivityRedPacketNodeVO nodeVO : list){
            if(nodeVO.getStatus() == ActivityRedPocketStatusEnum.UNDO.getCode()){
                next = nodeVO;
                break;
            }
        }

        redPocketService.addMoney(caseId, vo.getNodeId(), consumerMessage.getPayload().getCount());

        if(vo.getCurrentMoney() + consumerMessage.getPayload().getCount() >= vo.getTotalMoney()){

            boolean isLastNode = (next== null?false:redPocketService.isLastNode(list, next));

            redPocketService.openOnePocket(caseId, vo.getNodeId(), next!=null?next.getNodeId():0L,isLastNode);

            if(next != null) {
                redPocketService.sendEventCenter(cfInfoSimpleModel.getUserId(), 29, caseId, 20L, next.getTotalMoney());
            }
        }

        return true;
    }

    @Override
    protected Logger getLogger() {
        return log;
    }
}
