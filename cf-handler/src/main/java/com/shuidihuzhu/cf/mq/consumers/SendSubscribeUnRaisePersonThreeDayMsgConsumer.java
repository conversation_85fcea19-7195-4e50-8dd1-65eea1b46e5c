package com.shuidihuzhu.cf.mq.consumers;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.account.model.service.OpenIdUserIdModel;
import com.shuidihuzhu.cf.biz.crowdfunding.CfRecallUnRaiseUserInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingOrderBiz;
import com.shuidihuzhu.cf.biz.wx.Cf111WxEventRecordBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingInfoSlaveDao;
import com.shuidihuzhu.cf.delegate.SimpleUserAccountDelegate;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.model.crowdfunding.CfRecallUnRaiseUserInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.model.user.WxMpEvent;
import com.shuidihuzhu.cf.mq.payload.WxSubscribePayload;
import com.shuidihuzhu.cf.service.msg.MsgClientV2Service;
import com.shuidihuzhu.cf.service.user.SlaveWxMpEventService;
import com.shuidihuzhu.client.account.v1.accountservice.OpenIdUserIdResponse;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import com.shuidihuzhu.wx.enums.AccountThirdTypeEnum;
import com.shuidihuzhu.wx.grpc.enums.EventType;
import com.shuidihuzhu.wx.grpc.enums.WxMpBizType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019-07-31
 */
@Service
@RocketMQListener(id = MQTagCons.SEND_SUBSCRIBE_UN_RAISE_PERSON_THREEDAY_MSG,
        tags = MQTagCons.SEND_SUBSCRIBE_UN_RAISE_PERSON_THREEDAY_MSG,
        group = "cf-" + MQTagCons.SEND_SUBSCRIBE_UN_RAISE_PERSON_THREEDAY_MSG + "-group",
        topic = MQTopicCons.CF)
@Slf4j
public class SendSubscribeUnRaisePersonThreeDayMsgConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<WxSubscribePayload> {

    @Resource
    private SimpleUserAccountDelegate simpleUserAccountDelegate;
    @Autowired
    private SlaveWxMpEventService slaveWxMpEventService;
    @Autowired
    private CrowdfundingInfoSlaveDao crowdfundingInfoSlaveDao;
    @Resource
    private UserInfoDelegate userInfoDelegate;
    @Resource
    private CfRecallUnRaiseUserInfoBiz cfRecallUnRaiseUserInfoBizImpl;
    @Resource
    private CrowdfundingOrderBiz crowdfundingOrderBizImpl;
    @Autowired
    private Cf111WxEventRecordBiz cf111WxEventRecordBiz;
    @Resource
    private MsgClientV2Service msgClientV2Service;

    private static final String SHIELDVOLUNTEEREEVENTKEY1 = "cf_volunteer_";
    private static final String SHIELDVOLUNTEEREEVENTKEY2 = "qrscene_cf_volunteer_";


    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<WxSubscribePayload> mqMessage) {
        // payload 校验
        WxSubscribePayload payload = mqMessage.getPayload();
        if (payload == null) {
            log.debug("SEND_SUBSCRIBE_UN_RAISE_PERSON_THREEDAY_MSG_payload:{}",payload);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        String openId = payload.getOpenId();
        if (StringUtils.isEmpty(openId)) {
            log.debug("SEND_SUBSCRIBE_UN_RAISE_PERSON_THREEDAY_MSG_openId is null. ConsumerMessage:{}", mqMessage);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        //根据eventKey过滤
        String eventKey = payload.getEventKey();
        log.debug("UN_RAISE_PERSON_THREEDAY_MSG_eventKey:{}",eventKey);
        if(StringUtils.isNotEmpty(eventKey)){
            if (StringUtils.startsWith(eventKey,SHIELDVOLUNTEEREEVENTKEY1)){
                log.debug("UN_RAISE_PERSON_THREEDAY_MSG_cf_volunteer_eventKey:{}",eventKey);
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            if (StringUtils.startsWith(eventKey,SHIELDVOLUNTEEREEVENTKEY2)){
                log.debug("UN_RAISE_PERSON_THREEDAY_MSG_qrscene_cf_volunteer_eventKey:{}",eventKey);
                return ConsumeStatus.CONSUME_SUCCESS;
            }
        }
        // 根据 openId 获取 userId
        OpenIdUserIdModel openIdUserIdResponse = simpleUserAccountDelegate.getUserIdByOpenId(openId);
        if (openIdUserIdResponse == null) {
            log.debug("UN_RAISE_PERSON_THREEDAY_MSG_OpenIdUserIdResponse is null. openId:{}", openId);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        long userId = openIdUserIdResponse.getUserId();
        // 设置关注时间范围:关注时间在10点-24点
        LocalDateTime beginningOfToday = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
        Timestamp beginTime = Timestamp.valueOf(beginningOfToday.minusDays(2).plusHours(10));
        Timestamp endTime = Timestamp.valueOf(beginningOfToday.minusDays(1));
        // 获取关注信息
        WxMpEvent wxMpEvent = slaveWxMpEventService.getByUserIdAndEventTimeAndMpTypeAndEventType(userId, WxMpBizType.WXMPTYPE_CROWDFUNDING.getKey(), EventType.SUBSCRIBE.getKey(), beginTime, endTime);
        if (wxMpEvent == null) {
            log.info("UN_RAISE_PERSON_THREEDAY_MSGW xMpEvent is null. openId:{}", openId);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        // 检查是否有发起案例
        CrowdfundingInfo raisedInfoId = crowdfundingInfoSlaveDao.getLastNotEndByUserId(userId);
        if (raisedInfoId != null) {
            log.info("UN_RAISE_PERSON_THREEDAY_MSG raisedInfoId is not null. userId:{} begin:{} end:{}",
                    userId, beginTime, DateUtil.getCurrentTimestamp());
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        // 校验是否符合催发规则
        // if (!urgeRaiseMsgService.checkCouldSend(userId, UrgeRaiseMsgType.RAISE)) {
        if (false) {
            log.debug("UN_RAISE_PERSON_THREEDAY_MSG checkCouldSend is false. userId:{}", userId);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        // 去 account-service 查询用户详细信息
        UserInfoModel userInfoModel = userInfoDelegate.getUserInfoByUserId(userId);
        if (userInfoModel == null) {
            log.info("UN_RAISE_PERSON_THREEDAY_MSG userInfoModel is null. userId:{}", userId);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        // 查询用户近期的捐款情况，如果捐过则不发消息
        List<CrowdfundingOrder> orders = crowdfundingOrderBizImpl.getByUserIds(Sets.newHashSet(userId));
        if (CollectionUtils.isNotEmpty(orders)){
            List<CrowdfundingOrder> collect = orders.stream().filter(order -> beginTime.before(order.getCtime()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                log.info("UN_RAISE_PERSON_THREEDAY_MSG 当前用户userId:{} 在日期：{} 之后捐过款，被过滤掉 {}", userId, beginTime, collect);
                return ConsumeStatus.CONSUME_SUCCESS;
            }
        }

        // 组装结构写入"用户关注未发起推送消息备份表 cf_recall_unraise_user_info"
        CfRecallUnRaiseUserInfo cfRecallUnRaiseUserInfo = new CfRecallUnRaiseUserInfo();
        cfRecallUnRaiseUserInfo.setOpenId("");
        cfRecallUnRaiseUserInfo.setUserId(userId);
        cfRecallUnRaiseUserInfo.setSubscribeEventTime(new Timestamp(wxMpEvent.getEventTime().getTime()));
        cfRecallUnRaiseUserInfo.setIsSend(1);
        cfRecallUnRaiseUserInfoBizImpl.insertList(Lists.newArrayList(cfRecallUnRaiseUserInfo));
        send1966ModelNumMsg(userId);
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private void send1966ModelNumMsg(long userId) {
        String modelNum0 = "1966model2";
        msgClientV2Service.sendWxMsg(modelNum0, Lists.newArrayList(userId));
    }
}
