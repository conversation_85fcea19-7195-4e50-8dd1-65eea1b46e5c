package com.shuidihuzhu.cf.mq.consumers.app;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.dao.crowdfunding.app.AppDeviceBindDao;
import com.shuidihuzhu.cf.dao.crowdfunding.app.AppEventDao;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.CfVersion;
import com.shuidihuzhu.cf.enums.crowdfunding.appevent.AppPushEnums;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingBaseInfoBackup;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.app.vo.AppDeviceBindVo;
import com.shuidihuzhu.cf.model.crowdfunding.app.vo.AppEventVo;
import com.shuidihuzhu.cf.mq.consumers.BaseConsumerPrintReceiveLog;
import com.shuidihuzhu.cf.service.crowdfunding.CrowdfundingInfoService;
import com.shuidihuzhu.cf.service.msg.MsgClientV2Service;
import com.shuidihuzhu.cf.service.user.UserCfVersionService;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackApiClient;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewBaseInfoDO;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-06-24
 */
@RefreshScope
@Service
@RocketMQListener(id = MQTagCons.OPEN_APP_7_DAYS_MSG,
        tags = MQTagCons.OPEN_APP_7_DAYS_MSG,
        group = MQTagCons.OPEN_APP_7_DAYS_MSG +"_cf_api_group",
        topic = MQTopicCons.CF)
@Slf4j
public class CfOpenAppSevenDaysMsgConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<String> {
    @Autowired
    private AppEventDao appEventDao;
    @Autowired
    private AppDeviceBindDao appDeviceBindDao;
    @Autowired
    private CfClewtrackApiClient cfClewtrackApiClient;
    @Resource
    private UserInfoDelegate userInfoDelegate;
    @Autowired
    private CrowdfundingInfoService crowdfundingInfoService;
    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private UserCfVersionService userCfVersionService;

    @Autowired
    private MsgClientV2Service msgClientV2Service;
    @Autowired
    private ShuidiCipher shuidiCipher;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<String> mqMessage) {
        String appUniqId = mqMessage.getPayload();
        AppDeviceBindVo appDeviceBindVo = appDeviceBindDao.getAppDeviceBindVoByAppUnqiueId(appUniqId);
        if (appDeviceBindVo != null){
            sendOpenApp7days(appUniqId,appDeviceBindVo);
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private void sendOpenApp7days(String appUniqId, AppDeviceBindVo appDeviceBindVo) {
        log.debug("sendOpenApp7daysMMsg_appUniqId:{}", appUniqId);
        AppEventVo appEventVo = appEventDao.getAppEventDo(appUniqId, AppPushEnums.EventEnum.APP_INITIATE.getCode());
        Long userId = appDeviceBindVo.getUserId();
        UserInfoModel userInfoModel = userInfoDelegate.getUserInfoByUserId(userId);
        if (userInfoModel == null || StringUtils.isBlank(userInfoModel.getCryptoMobile())){
            return;
        }
        if (appEventVo == null){
            //用户没有进入发起筹款流程
            Response<CfClewBaseInfoDO> cfClewBaseInfoDOResponse = cfClewtrackApiClient.getLatestClewbaseByMobile(shuidiCipher.decrypt(userInfoModel.getCryptoMobile()));
            log.debug("sendOpenApp7days_cfClewBaseInfoDOResponse:{}", JSONObject.toJSONString(cfClewBaseInfoDOResponse));
            if (cfClewBaseInfoDOResponse !=null && cfClewBaseInfoDOResponse.getCode() == 0 && cfClewBaseInfoDOResponse.getData() != null){
                CfClewBaseInfoDO cfClewBaseInfoDO = cfClewBaseInfoDOResponse.getData();
                //判断登记是否在24小时内
                if (isInner24Hours(cfClewBaseInfoDO)){
                    //在24小时内有登记
                    if (StringUtils.isBlank(cfClewBaseInfoDO.getInfoUuid())){
                        //用户没有进入发起筹款流程，用户有登记，但用户没有发起
                        log.debug("sendOpenApp7days_sendAppMsg_1868");
                    }
                }else{
                    //在24小时内没有登记
                    //用户没有进入发起筹款流程，且用户没有登记
                    log.debug("sendOpenApp7days_sendAppMsg_1867");
                }
            }else{
                //用户没有进入发起筹款流程，且用户没有登记
                log.debug("sendOpenApp7days_sendAppMsg_1867");
            }
        }else{
            CrowdfundingBaseInfoBackup draft = crowdfundingInfoService.selectRecentlyCfByUserId(userId, 0);
            List<CrowdfundingInfo> crowdfundingInfoList = crowdfundingInfoBiz.getCrowdfundingInfoListByUserId(userId);
            CfVersion cfVersion = userCfVersionService.getCfVersion(userId);
            //未发起
            if(CollectionUtils.isEmpty(crowdfundingInfoList)){
                //进入过发起筹款流程，无草稿
                if (draft == null){
                    log.debug("sendOpenApp7days_sendAppMsg_1869");
                    msgClientV2Service.sendAppMsg("SGV0336", Lists.newArrayList(appDeviceBindVo.getUserId()));
                }else if (cfVersion.getCode() < CfVersion.definition_20191231.getCode()){
                    //进入过发起筹款流程，有草稿
                    log.debug("sendOpenApp7days_sendAppMsg_1870");
                }
            }
        }
    }

    private boolean isInner24Hours(CfClewBaseInfoDO cfClewBaseInfoDO) {
        Date createTime = cfClewBaseInfoDO.getCreateTime();
        Date standardTime = DateUtil.addHours(DateUtil.getCurrentTimestamp(),-24);
        if (createTime.after(standardTime)){
            return true;
        }
        return false;
    }
}
