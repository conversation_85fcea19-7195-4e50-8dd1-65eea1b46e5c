package com.shuidihuzhu.cf.mq.consumers;

import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingComment;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Created by sven on 18/8/2.
 *
 * <AUTHOR>
 */
@Service
@RocketMQListener(id = MQTagCons.CF_COMMENT_MSG,
        group = "cf-comment-group",
        tags = MQTagCons.CF_COMMENT_MSG,
        topic = MQTopicCons.CF)
@Slf4j
public class CfCommentConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<CrowdfundingComment> {

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CrowdfundingComment> mqMessage) {

        printReceiveMqMessageLog(mqMessage);

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
