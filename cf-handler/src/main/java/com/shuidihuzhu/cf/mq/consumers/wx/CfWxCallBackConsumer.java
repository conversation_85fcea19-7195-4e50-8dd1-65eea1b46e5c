package com.shuidihuzhu.cf.mq.consumers.wx;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.shuidi.weixin.common.util.crypto.WxCryptUtil;
import com.shuidi.weixin.mp.bean.WxMpXmlMessage;
import com.shuidihuzhu.cf.constants.AsyncPoolConstants;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.util.CFWxService;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.cf.mq.consumers.BaseConsumerPrintReceiveLog;
import com.shuidihuzhu.cf.wx.WxCallBackOrigin;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import com.shuidihuzhu.wx.grpc.client.feign.WxConfigServiceClient;
import com.shuidihuzhu.wx.grpc.model.SimpleWxConfig;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Set;
import java.util.StringJoiner;
import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 * @date 2025/4/7  15:26
 */
@Service
@RocketMQListener(id = MQTagCons.WX_CALLBACK_ORIGIN_TAG,
        group = "cf-" + MQTagCons.WX_CALLBACK_ORIGIN_TAG + "-group",
        tags = MQTagCons.WX_CALLBACK_ORIGIN_TAG,
        topic = MQTopicCons.WX_CALLBACK_ORIGIN_TOPIC)
@Slf4j
public class CfWxCallBackConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<WxCallBackOrigin> {

    @Resource
    private WxConfigServiceClient wxConfigServiceClient;
    private static final String MESSAGE_ID_SP = "-";
    @Autowired
    private MeterRegistry meterRegistry;
    @Resource(name = "cfRedissonHandler")
    private RedissonHandler redissonHandler;
    @Resource(name = AsyncPoolConstants.WX_CALLBACK_POOL)
    protected Executor executorService;
    //后续新增消息类型需要在配置文件中添加，快速支持
    @Value("#{'${apollo.cf.wx.callBack.thirdTypes:}'.split(',')}")
    private Set<Integer> thirdTypes;

    //集福气公众号
    private static final Set<Integer> CF_THIRDTYPE_SET = Sets.newHashSet(783, 103, 114, 115, 116, 134, 136, 143, 17, 18, 3, 31,
            32, 33, 34, 35, 36, 37, 43, 38, 39, 40, 409, 41, 412, 42, 44, 45, 453, 454, 46, 463, 466, 47, 48, 49, 50, 509, 51, 518,
            52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 66, 67, 68, 69, 695, 70, 700, 704, 71, 72, 73, 74, 75, 76, 77, 78,
            784, 79, 80, 82, 83, 84, 85, 86, 87, 89, 899, 904, 91, 92, 93, 95, 96);

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<WxCallBackOrigin> mqMessage) {
        if (mqMessage == null || mqMessage.getPayload() == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        //
        try {
//            WxCallBackOrigin payload = mqMessage.getPayload();
//            //过滤其他业务的公众号消息
//            if (!CF_THIRDTYPE_SET.contains(payload.getThirdType()) && !thirdTypes.contains(payload.getThirdType())) {
//                return ConsumeStatus.CONSUME_SUCCESS;
//            }
//            // 调用方法获取公众号配置信息
//            SimpleWxConfig simpleWxConfig = wxConfigServiceClient.getSimpleWxConfig(payload.getThirdType());
//            if (simpleWxConfig == null) {
//                log.error("获取公众号配置失败, thirdType:{}", payload.getThirdType());
//                return ConsumeStatus.CONSUME_SUCCESS;
//            }
//            WxMpXmlMessage wxMpXmlMessage = decryptWxMessage(simpleWxConfig, payload.getMsgXmlEncrypt(), payload.getMsgSignature(), payload.getNonce(), payload.getTimestamp());
//            if (wxMpXmlMessage == null) {
//                log.error("decryptWxMessage error, thirdType:{}, msgXmlEncrypt:{}", payload.getThirdType(), payload.getMsgXmlEncrypt());
//                return ConsumeStatus.CONSUME_SUCCESS;
//            }
//            wxMpXmlMessage.setSelf(true);
//            if (isDuplicateMessage(payload.getThirdType(), wxMpXmlMessage)) {
//                return ConsumeStatus.CONSUME_SUCCESS;
//            }
//            log.info("callback receive params :{}", JSON.toJSONString(wxMpXmlMessage));
//            executorService.execute(new CfWxCallbackRunnable(payload.getThirdType(), wxMpXmlMessage));
            return ConsumeStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            log.error("CfWxCallBackConsumer post Error!", e);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
    }


    private WxMpXmlMessage decryptWxMessage(SimpleWxConfig simpleWxConfig, String msgXmlEncrypt, String msgSignature, String nonce, String timestamp) {
        String token = simpleWxConfig.getToken();
        String appId = simpleWxConfig.getAppId();
        String aesKey = simpleWxConfig.getAesKey();
        WxCryptUtil cryptUtil = new WxCryptUtil(token, aesKey, appId);
        // 回调中包含已解密xml,此处会解密异常
        String plainText = "";
        try {
            plainText = cryptUtil.decrypt(msgSignature, timestamp, nonce, msgXmlEncrypt);
        } catch (Exception e) {
            log.info("callback event decrypt error, msgXmlEncrypt:{}", msgXmlEncrypt, e);
            return null;
        }
        // 返回的WxMpXmlMessage即为解密后的对象
        return WxMpXmlMessage.fromXml(plainText);
    }


    /**
     * 消息排重
     */
    public boolean isDuplicateMessage(int thirdType, WxMpXmlMessage wxMessage) {
        String messageId;
        if (wxMessage.getMsgId() == null) {
            messageId = new StringJoiner(MESSAGE_ID_SP).add(wxMessage.getCreateTime().toString())
                    .add(wxMessage.getFromUserName())
                    .add(StringUtils.trimToEmpty(wxMessage.getEventKey()))
                    .add(wxMessage.getEvent()).toString();
        } else {
            messageId = String.valueOf(wxMessage.getMsgId());
        }
        if (log.isDebugEnabled()) {
            log.debug(" isDuplicateMessage thirdType:{} messageId:{}", thirdType, messageId);
        }
        String tryLock = null;
        try {
            tryLock = redissonHandler.tryLock(messageId, 0, 60 * 60 * 1000L);
        } catch (Exception e) {
            log.error("", e);
        }
        if (!StringUtils.isBlank(tryLock)) {
            return false;
        }
        log.warn(" isDuplicateMessage thirdType:{} messageId:{}", thirdType, messageId);
        return true;
    }

    /**
     * 事件信息打点
     */
    public void incEvent2Chronograf(String thirdType, String event, String msgType) {
        try {
            Tags of = Tags.of("thirdType", StringUtils.trimToEmpty(thirdType),
                    "event", StringUtils.isEmpty(event) ? "None" : StringUtils.trimToEmpty(event),
                    "MsgType", StringUtils.isEmpty(msgType) ? "None" : StringUtils.trimToEmpty(msgType));
            Counter.builder("cf-wx-callback")
                    .tags(of)
                    .register(meterRegistry)
                    .increment();
        } catch (Exception e) {
            log.error("事件回调数据打点异常 error", e);
        }
    }

    @Getter
    @Setter
    public class CfWxCallbackRunnable implements Runnable {
        private int thirdType;
        private WxMpXmlMessage wxMpXmlMessage;

        @Override
        public void run() {
            CFWxService.getInstance(thirdType).responseWxMessage(wxMpXmlMessage);
            incEvent2Chronograf(String.valueOf(thirdType), wxMpXmlMessage.getEvent(), wxMpXmlMessage.getMsgType());
        }

        public CfWxCallbackRunnable(int thirdType, WxMpXmlMessage wxMpXmlMessage) {
            this.thirdType = thirdType;
            this.wxMpXmlMessage = wxMpXmlMessage;
        }
    }

}
