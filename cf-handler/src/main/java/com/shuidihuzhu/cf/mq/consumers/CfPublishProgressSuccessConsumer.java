package com.shuidihuzhu.cf.mq.consumers;

import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingPushBiz;
import com.shuidihuzhu.cf.biz.ugc.IProgressUnReadBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingProgress;
import com.shuidihuzhu.cf.service.crowdfunding.CfAllRelatedUserService;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 发表动态成功
 * <p>
 * Created by wangsf on 18/4/2.
 */
@Service
@RefreshScope
@Slf4j
@RocketMQListener(id = MQTagCons.CF_PUBLISH_PROGRESS_NOTICE,
        topic = MQTopicCons.CF,
        tags = MQTagCons.CF_PUBLISH_PROGRESS_NOTICE,
        group = "Cf-ConsumerGroup_CF_PUBLISH_PROGRESS_NOTICE")
public class CfPublishProgressSuccessConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<CrowdFundingProgress> {

    @Resource(name = "cfRedissonHandler")
    private RedissonHandler redissonHandler;

    @Autowired
    private CrowdfundingPushBiz crowdfundingPushBiz;

    @Autowired
    private CfAllRelatedUserService allRelatedUserService;

    @Resource
    private IProgressUnReadBiz progressUnReadBiz;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CrowdFundingProgress> consumerMessage) {

        printReceiveMqMessageLog("发布动态消息", consumerMessage);

        if (consumerMessage.getReconsumeTimes() > 5) {
            log.info("发布动态消息, reconsumeTime:{}", consumerMessage.getReconsumeTimes());
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        String key = consumerMessage.getKeys();
        String tryLock = null;
        try {
            tryLock = redissonHandler.tryLock(key, 0, 60 * 1000L);
        } catch (InterruptedException e) {
            log.error("tryLock error.", e);
        }

        if (StringUtils.isBlank(tryLock)) {
            log.info("发布动态消息，tryLock is null. {}", tryLock);
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        try {

            //获取所有要被推送的用户
            CrowdFundingProgress progress = consumerMessage.getPayload();

            List<Long> userIds = allRelatedUserService.getAllRelatedUserIds(progress.getActivityId());

            //发推送
            crowdfundingPushBiz.pushProgress(consumerMessage.getPayload(), userIds);

        } catch (Exception e) {
            log.error("发布动态消息 error. ", e);
        } finally {
            try {
                redissonHandler.unLock(key, tryLock);
            } catch (Exception e) {
                log.warn("consumeMessage unLock err", e);
            }
        }

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
