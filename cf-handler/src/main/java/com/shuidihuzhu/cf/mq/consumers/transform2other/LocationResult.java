package com.shuidihuzhu.cf.mq.consumers.transform2other;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: <PERSON>
 * @date: 2018/4/19 16:57
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class LocationResult {
	private Integer ret;
	private String msg;
	@JSONField
	private List<UserLocation> content;
}
