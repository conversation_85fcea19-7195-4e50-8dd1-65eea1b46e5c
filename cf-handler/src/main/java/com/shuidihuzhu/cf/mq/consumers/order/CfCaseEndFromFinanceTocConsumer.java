package com.shuidihuzhu.cf.mq.consumers.order;

import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.event.CaseEndEvent;
import com.shuidihuzhu.cf.model.CaseEndModel;
import com.shuidihuzhu.cf.service.EventPublishService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 案例结束消息
 *
 * <AUTHOR>
 * @since 2025/1/7
 */
@Slf4j
@Service
@RocketMQListener(id = "cf-api" + MQTagCons.CF_CASE_END_FROM_FINANCE_TOC_MSG,
        group = "cf-api" + MQTagCons.CF_CASE_END_FROM_FINANCE_TOC_MSG + "group",
        tags = MQTagCons.CF_CASE_END_FROM_FINANCE_TOC_MSG,
        topic = MQTopicCons.CF)
public class CfCaseEndFromFinanceTocConsumer implements MessageListener<CaseEndModel> {

    @Resource
    private EventPublishService eventPublishService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CaseEndModel> mqMessage) {
        eventPublishService.publish(new CaseEndEvent(this, mqMessage.getPayload()));
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
