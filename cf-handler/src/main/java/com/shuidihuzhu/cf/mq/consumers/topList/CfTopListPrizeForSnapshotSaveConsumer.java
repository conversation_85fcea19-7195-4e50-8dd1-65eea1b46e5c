package com.shuidihuzhu.cf.mq.consumers.topList;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.biz.crowdfunding.CfUserCaseInfoService;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.topList.CfTopListLifeCycleRecordBiz;
import com.shuidihuzhu.cf.biz.topList.CfTopListSummaryBiz;
import com.shuidihuzhu.cf.biz.topList.CfTopListSummaryFacade;
import com.shuidihuzhu.cf.biz.topList.CfTopListUtil;
import com.shuidihuzhu.cf.clinet.event.center.enums.UserOperationTypeEnum;
import com.shuidihuzhu.cf.clinet.event.center.model.CfUserEvent;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.topList.CfTopListConstant;
import com.shuidihuzhu.cf.model.topList.CfTopListSummary;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
@Service
@RocketMQListener(id = MQTagCons.CASE_111_PRIZE_TOP_LIST_SAVE,
        tags = MQTagCons.CASE_111_PRIZE_TOP_LIST_SAVE,
        topic = MQTopicCons.CF,
        group = "cf-api_" + MQTagCons.CASE_111_PRIZE_TOP_LIST_SAVE)
@RefreshScope
public class CfTopListPrizeForSnapshotSaveConsumer implements MessageListener<Integer> {

    @Autowired
    private CfTopListSummaryBiz summaryBiz;
    @Autowired
    private Producer producer;
    @Autowired
    private CrowdfundingInfoBiz infoBiz;
    @Autowired
    private CfTopListSummaryFacade summaryFacade;
    @Autowired
    private CfUserCaseInfoService userCaseInfoService;
    @Autowired
    private CfTopListLifeCycleRecordBiz listLifeCycleRecordBiz;

    @Value("${apollo.no.prize.caseIds:1}")
    private String noPrizeCaseIds;
    @Resource(name = "cfRedissonHandler")
    private RedissonHandler redissonHandler;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<Integer> mqMessage) {

        log.info("收到初审通过第5天的消息,保存榜单.caseId:{}", mqMessage);
        if (mqMessage.getPayload() == null) {
            log.info("收到初审通过第5天的消息. caseId为null");
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        if (StringUtils.isNotBlank(noPrizeCaseIds) && noPrizeCaseIds.contains(mqMessage.getPayload() + "")) {
            log.info("当前案例不领奖， caseId:{}", mqMessage.getPayload());
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        String key = "CASE_111_PRIZE_TOP_LIST_SAVE_" + mqMessage.getPayload();
        if (!redissonHandler.setNX(key, 1, 3600*1000)) {
            log.info("当前案例没有获取到锁，不做领奖通知 caseId:{}", mqMessage.getPayload());
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        log.info("当前案例获取到锁了,保存领奖。caseId:{}", mqMessage.getPayload());
        CfTopListSummary summary = summaryBiz.selectSummaryByCaseId(mqMessage.getPayload());
        int topListType = CfTopListConstant.TopListType.DONATE_TOP_LIST_TYPE.getCode();
//                summaryFacade.queryCaseTopListTypeByCaseId(mqMessage.getPayload());
        if (summary == null || StringUtils.isBlank(summary.getActivityEndTime())
            || StringUtils.isNotBlank(summary.getCanPrizeUsers())) {
            log.info("收到初审通过第5天的消息. 案例的榜单数据为空或榜单已做处理：caseId:{} topListType:{} summary:{}", mqMessage.getPayload(),
                    topListType, summary);
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        CrowdfundingInfo info = infoBiz.getFundingInfoById(mqMessage.getPayload());

        if (info == null || summaryBiz.isCaseIdInBlack(mqMessage.getPayload())) {
            log.info("找不到案例数据、 或者案例在黑名单.caseId:{}", mqMessage.getPayload());
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        CfTopListSummary.CfDonorPrizeData prizeData = getCanPrizePerson(summary, topListType, info.getUserId());

        log.info("收到初审通过第5天的消息。得到案例可领奖的userId. caseId:{}, prizeData:{}", summary.getCaseId(), prizeData);

        if (prizeData != null && (CollectionUtils.isNotEmpty(prizeData.getCfDonorDataList()) ||
                CollectionUtils.isNotEmpty(prizeData.getVerificateCfDonorDataList()))) {
            sendUserEventForAcceptThePrize(infoBiz.getFundingInfoById(summary.getCaseId()));
            summaryBiz.updatePrizeUserIds(summary.getCaseId(), prizeData);
            saveCanPrizeSnapshot(info.getId(), prizeData);
        } else {
            log.info("当前案例的榜单数据为空，不发领奖通知.caseId:{}", info.getId());
        }

        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private void saveCanPrizeSnapshot(int caseId, CfTopListSummary.CfDonorPrizeData prizeData) {
        if (prizeData == null) {
            return;
        }
        actualSave(caseId, prizeData.getCfDonorDataList(), prizeData.getTopListType(), 0);
        actualSave(caseId, prizeData.getVerificateCfDonorDataList(), prizeData.getTopListType(), 1);
    }

    private void actualSave(int caseId, List<CfTopListSummary.CfDonorData> canPrizePersons, int topListType, int type) {

        if (CollectionUtils.isEmpty(canPrizePersons)) {
            return;
        }

        List<Long> userIds = Lists.newArrayList();
        for (CfTopListSummary.CfDonorData data : canPrizePersons) {
            userIds.add(data.getUserId());
        }

        if (type == 0) {
            int topListTypeId = listLifeCycleRecordBiz.getTopListTypeRecordId(CfTopListConstant
                    .TopListScope.SCOPE_ALL_LIST.getCode(), topListType);
            listLifeCycleRecordBiz.asyncSaveTopListOperate(caseId, userIds, topListTypeId,
                    CfTopListConstant.CAN_PRIZE, "");
        } else {
            listLifeCycleRecordBiz.updateVerificationTopList(caseId, userIds, CfTopListConstant.CAN_PRIZE, "");
        }
    }

    private CfTopListSummary.CfDonorPrizeData getCanPrizePerson(CfTopListSummary summary, int topListType, long userId) {

        CfTopListSummary.CfDonorPrizeData prizeData = new CfTopListSummary.CfDonorPrizeData();

        if (topListType != CfTopListConstant.TopListType.NO_TOP_LIST.getCode()) {
            // 全部的榜单
            List<CfTopListSummary.CfDonorData> allTopLists = (topListType == CfTopListConstant.TopListType.SHARE_TOP_LIST_TYPE.getCode()
                    ? CfTopListSummary.parseArray(summary.getAllShareTopInfo()) : CfTopListSummary.parseArray(summary.getAllDonateTopInfo()));
            List<CfTopListSummary.CfDonorData> totalAllCanPrizePersons = getTopKDataList(allTopLists);

            prizeData.setTopListType(topListType);
            prizeData.setCfDonorDataList(totalAllCanPrizePersons);
        }

        // 证明人的榜单
        Set<Long> verificateUserIds = summaryFacade.getVerificationUserIds(summary.getInfoUuid());

        CfTopListSummary.DonorDataList dataList = summaryBiz.callAllRankCaseInfos(userCaseInfoService.getByCaseId(summary.getCaseId()),
                CfTopListUtil.buildVerificateCallParam(verificateUserIds, userId));

        listLifeCycleRecordBiz.asyncSaveVerificationTopList(summary, dataList, false);

        List<CfTopListSummary.CfDonorData> verifyCanPrizePersons = getTopKDataList(dataList != null ? dataList.getAllTopInfo() : null);

        prizeData.setVerificateTopListType(CfTopListConstant.TopListType.DONATE_TOP_LIST_TYPE.getCode());
        prizeData.setVerificateCfDonorDataList(verifyCanPrizePersons);

        return prizeData;
    }

    private  List<CfTopListSummary.CfDonorData> getTopKDataList(List<CfTopListSummary.CfDonorData> allTopLists) {

        if (allTopLists == null || allTopLists.size() < CfTopListConstant.TOTAL_PRIZE_LIMIT) {
            return allTopLists;
        }

        List<CfTopListSummary.CfDonorData> canPrizeLists = Lists.newArrayList(allTopLists.subList(0, CfTopListConstant.TOTAL_PRIZE_LIMIT));
        for (int i = CfTopListConstant.TOTAL_PRIZE_LIMIT; i < allTopLists.size(); ++i) {
            if (allTopLists.get(i).getRank() == allTopLists.get(CfTopListConstant.TOTAL_PRIZE_LIMIT - 1).getRank()) {
                canPrizeLists.add(allTopLists.get(i));
            } else {
                break;
            }
        }

        return canPrizeLists;
    }

    private void sendUserEventForAcceptThePrize(CrowdfundingInfo crowdfundingInfo) {

        String tag = com.shuidihuzhu.cf.clinet.event.center.constants.MQTagCons.USER_EVENT_FOR_EVENT_CENTER;
        String topic = com.shuidihuzhu.cf.clinet.event.center.constants.MQTopicCons.CF;

        long userId = crowdfundingInfo.getUserId();
        int caseId = crowdfundingInfo.getId();

        CfUserEvent cfUserEvent = new CfUserEvent();
        Map<String,String> dataMap = Maps.newHashMap();
        dataMap.put("infoUuid", crowdfundingInfo.getInfoId());
        cfUserEvent.setUserId(userId);
        cfUserEvent.setCaseId(caseId);
        cfUserEvent.setType(UserOperationTypeEnum.CASE_AUDIT_PASS_ACCEPT_THE_PRIZE.getCode());
        cfUserEvent.setDataMap(dataMap);

        Message<CfUserEvent> message = new Message<>(topic, tag, tag + "_" + caseId, cfUserEvent);
        sendUserEventCommon(message);
    }

    private void sendUserEventCommon(Message<CfUserEvent> message) {
        if (producer == null) {
            return;
        }

        MessageResult messageResult = producer.send(message);
        log.info("活动期间催用户领奖sendUserEventCommon message:{}, messageResult:{}", message, messageResult);
    }
}
