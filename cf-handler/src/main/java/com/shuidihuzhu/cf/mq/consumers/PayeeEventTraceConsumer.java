package com.shuidihuzhu.cf.mq.consumers;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoPayeeBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingConstant;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingRelationType;
import com.shuidihuzhu.cf.enums.crowdfunding.HonestPersonChangeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoPayee;
import com.shuidihuzhu.client.cf.api.model.DishonestPayload;
import com.shuidihuzhu.data.analytics.javasdk.core.Analytics;
import com.shuidihuzhu.data.analytics.javasdk.core.UserTagTypeEnum;
import com.shuidihuzhu.data.servicelog.meta.cf.PayeeChange;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @time 2019/7/31 上午11:46
 * @desc
 */
@Slf4j
@Service
@RocketMQListener(id = MQTagCons.CF_DISHONEST_VALIDATE_MQ + "_PAYEE_EVENT_TRACE",
        tags = MQTagCons.CF_DISHONEST_VALIDATE_MQ,
        topic = MQTopicCons.CF,
        group = MQTagCons.CF_DISHONEST_VALIDATE_MQ + "_PAYEE_EVENT_TRACE")
public class PayeeEventTraceConsumer implements MessageListener<DishonestPayload> {

    @Autowired
    private Analytics analytics;

    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;

    @Autowired
    private CrowdfundingInfoPayeeBiz crowdfundingInfoPayeeBiz;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<DishonestPayload> mqMessage) {
        if(Objects.isNull(mqMessage) || Objects.isNull(mqMessage.getPayload())){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        DishonestPayload payload = mqMessage.getPayload();
        int changeType = payload.getChangeType();
        String infoUuid = payload.getInfoUuid();

        if(changeType != HonestPersonChangeEnum.PAYEE.getKey() || StringUtils.isEmpty(infoUuid)){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        CrowdfundingInfo cfInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        if(Objects.isNull(cfInfo)){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        CrowdfundingRelationType relationType = cfInfo.getRelationType();
        boolean hospital = (relationType == CrowdfundingRelationType.LOCATION_HOSPITAL_ACCOUNT || relationType == CrowdfundingRelationType.charitable_organization) ? true : false;
        if(hospital){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        CrowdfundingInfoPayee payeeInfo = crowdfundingInfoPayeeBiz.getByInfoUuid(infoUuid);
        String payeeName = Objects.nonNull(payeeInfo) ? payeeInfo.getName() : StringUtils.EMPTY;
        String payeeIdentity = Objects.nonNull(payeeInfo) ? payeeInfo.getIdCard() : StringUtils.EMPTY;

        if(StringUtils.isEmpty(payeeName) || StringUtils.isEmpty(payeeIdentity)){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        PayeeChange payeeChange = new PayeeChange();
        try {
            payeeChange.setPayee_encrypt_idcard(payeeIdentity);
            payeeChange.setPayee_name(payeeName);
            payeeChange.setInfo_id(Long.valueOf(cfInfo.getId()));
            payeeChange.setCase_id(StringUtils.trimToEmpty(cfInfo.getInfoId()));

            payeeChange.setUser_tag(String.valueOf(cfInfo.getUserId()));
            payeeChange.setUser_tag_type(UserTagTypeEnum.userid);

            analytics.track(payeeChange);
            log.info("大数据打点上报,修改收款人:{}", JSONObject.toJSONString(payeeChange));
        } catch (Exception e) {
            log.error("大数据打点上报异常,修改收款人:{}", JSONObject.toJSONString(payeeChange), e);
        }

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
