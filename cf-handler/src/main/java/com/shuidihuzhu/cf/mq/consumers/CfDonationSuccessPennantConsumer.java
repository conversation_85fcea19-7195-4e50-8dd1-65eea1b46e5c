package com.shuidihuzhu.cf.mq.consumers;

import com.shuidihuzhu.cf.biz.activity.PennantInfoBiz;
import com.shuidihuzhu.cf.biz.activity.PennantUserRelationBiz;
import com.shuidihuzhu.cf.biz.activity.PennantUserStatBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enums.activity.PennantAwardTypeEnum;
import com.shuidihuzhu.cf.enums.activity.PennantReceiveStatusEnum;
import com.shuidihuzhu.cf.model.activity.PennantUserRelation;
import com.shuidihuzhu.cf.model.activity.PennantUserStat;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 捐款成功，给锦旗用户发消息
 * Created by dongcf on 2020/3/17
 */
@Slf4j
@Service
@RefreshScope
@RocketMQListener(id = "CF_DONATION_SUCCESS_PENNANT_CONSUMER", topic = MQTopicCons.CF,
        tags = MQTagCons.CF_DONATION_SUCCESS_BIZ,
        group = "Cf-ConsumerGroup_DONATION_SUCESS_PENNANT")
public class CfDonationSuccessPennantConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<CrowdfundingOrder> {

    private static final String PENNANT_RELATION_LOCK = "PENNANT_RELATION_LOCK_";

    @Value("${pennant.switch:false}")
    private boolean pennantSwitch;
    @Autowired
    private PennantUserStatBiz pennantUserStatBiz;
    @Autowired
    private PennantUserRelationBiz pennantUserRelationBiz;
    @Autowired
    private PennantInfoBiz pennantInfoBiz;
    @Resource(name = "cfRedissonHandler")
    private RedissonHandler cfRedissonHandler;


    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CrowdfundingOrder> mqMessage) {
        CrowdfundingOrder order = mqMessage.getPayload();

        if(pennantSwitch) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        PennantUserStat userStat = this.pennantUserStatBiz.getByUserId(order.getUserId());
        if(userStat == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        PennantUserRelation userRelation = this.pennantUserRelationBiz.checkDonateAward(order.getUserId(), order.getInfoId());
        if(userRelation != null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }


        long newPennantId = this.pennantUserRelationBiz.randomPennantId(userStat, this.pennantInfoBiz.findAllIds());

        String key = PENNANT_RELATION_LOCK + order.getUserId() + "_" + order.getInfoId();
        boolean lock = this.cfRedissonHandler.setNX(key, newPennantId, RedissonHandler.ONE_MINUTE * 2);
        if(!lock) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        //创建业务数据
        PennantUserRelation relation = new PennantUserRelation();
        relation.setPennantId(newPennantId);
        relation.setInfoId(order.getInfoId());
        relation.setUserId(order.getUserId());
        relation.setPayUid(order.getPayUid());
        relation.setAwardType(PennantAwardTypeEnum.DONATE.getType());
        relation.setReceiveStatus(PennantReceiveStatusEnum.UNRECEIVED.getStatus());

        this.pennantUserRelationBiz.insert(relation);
        this.pennantUserStatBiz.updateAwardById(userStat.getPennantIdStr(), userStat.getId());

        return ConsumeStatus.CONSUME_SUCCESS;
    }

}
