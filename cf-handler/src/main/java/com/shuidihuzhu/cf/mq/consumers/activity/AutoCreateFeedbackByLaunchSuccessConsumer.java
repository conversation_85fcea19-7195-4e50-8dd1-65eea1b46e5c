package com.shuidihuzhu.cf.mq.consumers.activity;

import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.domain.activity.SummaryLetterDO;
import com.shuidihuzhu.cf.enums.activity.summary.SummaryLetterTypeEnum;
import com.shuidihuzhu.cf.finance.mq.CfInfoLaunchSuccess;
import com.shuidihuzhu.cf.finance.mq.FinanceMQTagCons;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.mq.consumers.BaseConsumerPrintReceiveLog;
import com.shuidihuzhu.cf.service.activity.SummaryLetterService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * @author: fengxuan
 * @create 2019-10-24 13:19
 **/
@Service
@Slf4j
@RocketMQListener(id = MQTagCons.AUTO_CREATE_FEEDBACK,
        group = "cf-" + MQTagCons.AUTO_CREATE_FEEDBACK + "-group",
        tags = FinanceMQTagCons.INFO_LAUNCH_SUCCESS,
        topic = MQTopicCons.CF)
public class AutoCreateFeedbackByLaunchSuccessConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<CfInfoLaunchSuccess> {

    private static final long AMOUNT = 10000L;

    private static final int DONATOR_COUNT = 100;

    private static final String DATE_FORMAT = "yyyy年MM月";

    @Autowired
    SummaryLetterService summaryLetterService;

    @Autowired
    private CrowdfundingOrderBiz crowdfundingOrderBiz;

    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;

    @Autowired
    private CfFirstApproveBiz cfFirstApproveBiz;

    @Autowired
    private CrowdfundingAuthorBiz crowdfundingAuthorBiz;

    @Autowired
    CfUserBindFeedbackCaseService cfUserBindFeedbackCaseService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CfInfoLaunchSuccess> mqMessage) {
        printReceiveMqMessageLog("auto create feedback message", mqMessage);
        CfInfoLaunchSuccess payload = mqMessage.getPayload();
        if (Objects.isNull(payload)) {
            log.warn("launch success message is null, message:{}", mqMessage);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        long caseId = payload.getCaseId();
        if (caseId <= 0) {
            log.warn("launch success case id is illegal, message:{}", mqMessage);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        try {
            //生成反馈信息
            String content = createContent(payload);
            if (StringUtils.isEmpty(content)) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            //保存反馈信息
            SummaryLetterDO sum = summaryLetterService.add((int) caseId, content, null, SummaryLetterTypeEnum.PLATFORM);
            if (sum == null) {
                log.info("add error caseId:{}", caseId);
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            List<CfUserBindFeedbackCaseDO> cfUserBindFeedbackCaseDOList = cfUserBindFeedbackCaseService.listByFeedbackId(sum.getId());
            //如果不为空，说明之前处理过
            if (CollectionUtils.isNotEmpty(cfUserBindFeedbackCaseDOList)) {
                log.info("重复提现案例,SummaryLetterDO:{}", sum);
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            //调用一次绑定反馈信息
            cfUserBindFeedbackCaseService.bindSingleCase(sum);
        } catch (Exception e) {
            log.error("auto create feedback info error, message:{}", mqMessage, e);
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }


    private String createContent(CfInfoLaunchSuccess payload) {
        StringBuilder contentBuilder = new StringBuilder();
        long amountFen = payload.getAmountFen() / 100;
        int caseId = (int) payload.getCaseId();
        String launchDateString = new DateTime(payload.getLaunchSuccessTime())
                .toString(DATE_FORMAT);

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        Date beginTime = crowdfundingInfo.getBeginTime();
        String caseStartDateString = new DateTime(beginTime)
                .toString(DATE_FORMAT);

        String patientName = getPatientName(caseId);

        //捐款人数
        int donatorCount = crowdfundingOrderBiz.donatorCountByCaseId(caseId);
        if (amountFen >= AMOUNT) {
            contentBuilder.append(String.format("你在%s", caseStartDateString))
                    .append(String.format("帮助过的患者%s，已于%s", patientName, launchDateString))
                    .append(String.format("拿到了%d元救命钱。你的慷慨善良，带动着更多人和你一起，行小善聚大爱！", amountFen));

        } else if (donatorCount > DONATOR_COUNT) {
            contentBuilder.append(String.format("患者%s在%s得到过你的帮助，", patientName, caseStartDateString))
                    .append(String.format("并在近30天的筹款过程中一共得到了%d位好心人的捐助。你的爱心凝聚着力量，延续着生命的希望。", donatorCount));
        } else {
            log.info("no need create feedback info, caseId:{}", caseId);
        }

        return contentBuilder.toString();
    }


    /**
     * 获取患者姓名
     */
    private String getPatientName(int caseId) {
        // 取前置信息中患者姓名
        CfFirsApproveMaterial firstApproveInfo = cfFirstApproveBiz.getByInfoId(caseId);
        if (firstApproveInfo != null) {
            return firstApproveInfo.getPatientRealName();
        }

        // 取材料审核中患者姓名
        CrowdfundingAuthor crowdfundingAuthor = crowdfundingAuthorBiz.get(caseId);
        if (crowdfundingAuthor != null) {
            return crowdfundingAuthor.getName();
        }
        throw new RuntimeException(String.format("提现成功，获取患者信息失败.caseId:%d", caseId));
    }

}
