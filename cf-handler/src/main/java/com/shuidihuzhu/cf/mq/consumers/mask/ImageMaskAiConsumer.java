package com.shuidihuzhu.cf.mq.consumers.mask;

import com.shuidihuzhu.alps.feign.config.OceanApiMqConfig;
import com.shuidihuzhu.alps.feign.ocean.OceanApiMQResponse;
import com.shuidihuzhu.cf.service.mask.impl.ImageMaskProcessServiceImpl;
import com.shuidihuzhu.client.model.ImageMaskInform;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Description: 消费AI图片掩码消息
 * @Author: panghairui
 * @Date: 2022/9/8 4:51 下午
 */
@Slf4j
@Service
@RocketMQListener(id = OceanApiMqConfig.TOPIC,
        tags = "ai-info-mosaic_10007",
        topic = OceanApiMqConfig.TOPIC)
public class ImageMaskAiConsumer implements MessageListener<OceanApiMQResponse> {

    @Resource
    private ImageMaskProcessServiceImpl imageMaskProcessService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<OceanApiMQResponse> mqMessage) {

        log.info("ImageMaskAiConsumer is begin {}", mqMessage);
        if (Objects.isNull(mqMessage)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        OceanApiMQResponse apiMQResponse = mqMessage.getPayload();
        if (Objects.isNull(apiMQResponse)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        imageMaskProcessService.submitAiMaskImage(apiMQResponse);

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
