package com.shuidihuzhu.cf.mq.consumers;

import com.shuidihuzhu.cf.biz.crowdfunding.CfUserStatBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.crowdfunding.UserPointsHistory;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * @package: com.shuidihuzhu.cf.mq.consumers
 * @Author: liujiawei
 * @Date: 2018/10/30  13:43
 */
@Service
@Slf4j
@RocketMQListener(id = MQTagCons.BONUS_POINTS_MSG,
        group = "cf-" + MQTagCons.BONUS_POINTS_MSG + "-group",
        tags = MQTagCons.BONUS_POINTS_MSG,
        topic = MQTopicCons.USER_POINTS)
public class BonusPointsSendMsgConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<UserPointsHistory> {
    @Autowired
    private CfUserStatBiz cfUserStatBiz;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<UserPointsHistory> mqMessage) {
        log.info("(增加积分消息消费端)consumeMessage:{}", mqMessage);
        UserPointsHistory userPointsHistory = mqMessage.getPayload();
        try {
            //积分于2018-12-31到期
            cfUserStatBiz.expried(userPointsHistory.getUserId());
        } catch (Exception e) {
            log.error("积分到期扣除异常：", e);
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }

}