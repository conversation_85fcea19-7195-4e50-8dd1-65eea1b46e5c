package com.shuidihuzhu.cf.mq.consumers;

import com.shuidihuzhu.cf.activity.enums.DonateCooperateActivityStatusEnum;
import com.shuidihuzhu.cf.activity.feign.CfActivityFeignClient;
import com.shuidihuzhu.cf.activity.model.ActivityDetail;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingOrderShardingBiz;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.service.activity.subsidy.ActivitySubsidyOrderService;
import com.shuidihuzhu.cf.service.activity.IActivityDonatorRedPocketService;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.enums.PayStatus;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.DateFormat;
import java.text.SimpleDateFormat;

/**
 * <AUTHOR>
 * @time 2019/1/23 下午11:13
 * @desc 春节红包活动，每捐款成功一次，redis记录一次，当日捐款次数到达某一值，送一个实物红包
 */
@Slf4j
@Service
@RocketMQListener(id = MQTagCons.CF_DONATION_SUCCESS_COUNT, topic = MQTopicCons.CF,
                  tags = MQTagCons.CF_PAY_SUCCESS_MSG, group = "CF_API_DONATION_SUCCESS_COUNT")
public class DonateSuccessCountConsumer  extends BaseConsumerPrintReceiveLog implements MessageListener<CrowdfundingOrder> {

    @Resource
    private IActivityDonatorRedPocketService redPocketService;

    @Resource
    private ActivitySubsidyOrderService activitySubsidyOrderService;

    @Resource(name = "cfShareViewRedissonHandler")
    private RedissonHandler redissonHandler;

    @Resource(name = "crowdfundingOrderShardingBizImpl")
    private CrowdfundingOrderShardingBiz orderShardingBiz;

    private final static String KEY_PREFIX = "CASE_RED_POCKET_ATTENDER";

    private final static String USER_PREFIX = "USER_RED_POCKET_ATTENDER";

    private DateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");

    private final static long EXPIRE_TIME = 24 * 60 * 60 * 1000;

    @Resource
    private CfActivityFeignClient activityFeignClient;
    

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CrowdfundingOrder> mqMessage) {

        CrowdfundingOrder order = mqMessage.getPayload();

        if(null == order || null == order.getPayStatus() || PayStatus.PAY_SUCCESS.getCode() != order.getPayStatus() || null == order.getAmount() || order.getAmount() <= 0){
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        int caseId = order.getCrowdfundingId();
        long orderId = order.getId();

        RpcResult<ActivityDetail> rpcResult =  activityFeignClient.getActivityForRaiser(order.getCrowdfundingId());
        if(rpcResult ==null || rpcResult.getData()==null || rpcResult.getData().getStatus() != DonateCooperateActivityStatusEnum.APPROVE){

            log.info("case {} order {} activity status not right {}", caseId, orderId, rpcResult);

            return ConsumeStatus.CONSUME_SUCCESS;
        }

        if(rpcResult.getData().getPocketType() != 2){
            log.info("case {} order {} getPocketType id not right {}", caseId, orderId, rpcResult);

            return ConsumeStatus.CONSUME_SUCCESS;
        }

        ActivitySubsidyOrderService.PaySuccessSubsidyModel paySuccessSubsidyModel = activitySubsidyOrderService.getOrderSubsidyModel(order.getId());
        if(paySuccessSubsidyModel ==null){

            log.info("case {} order {} PaySuccessSubsidyModel status not right {}", caseId, orderId, paySuccessSubsidyModel);
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        if(rpcResult.getData().getActivityId() == 35 || rpcResult.getData().getActivityId() == 26){
            if(paySuccessSubsidyModel.getShareDv() < 2){
                log.info("case {} order {} PaySuccessSubsidyModel shareDv not right {}", caseId, orderId, paySuccessSubsidyModel);
                return ConsumeStatus.CONSUME_SUCCESS;
            }
        }
        
        boolean canAttendCase = redPocketService.canAttendPocket(caseId, order.getUserId());
        boolean userAttendToday = redPocketService.getTodayAttend(order.getUserId());

        if(canAttendCase && !userAttendToday) {
            redPocketService.add(order.getCrowdfundingId(), paySuccessSubsidyModel.getShareDv(), order.getUserId(),rpcResult.getData().getActivityId());
            redPocketService.setTodayAttend(order.getUserId());
        } else {
            log.info("user {} case {} reach limit {} {}", order.getUserId(), caseId, userAttendToday, canAttendCase);
        }

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
