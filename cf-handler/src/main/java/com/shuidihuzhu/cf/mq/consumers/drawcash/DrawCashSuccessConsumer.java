package com.shuidihuzhu.cf.mq.consumers.drawcash;

import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.client.ugc.caseprocessstatus.model.CaseProcessStatusEnum;
import com.shuidihuzhu.cf.client.ugc.caseprocessstatus.service.CaseProcessStatusClient;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.finance.mq.FinanceMQTagCons;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.mq.common.BaseMessageConsumer;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RocketMQListener(id = FinanceMQTagCons.DRAW_CASH_PAY_SUCCESS_TO_DONOR,
        tags = FinanceMQTagCons.DRAW_CASH_PAY_SUCCESS_TO_DONOR,
        group = "cf-api" + FinanceMQTagCons.DRAW_CASH_PAY_SUCCESS_TO_DONOR,
        topic = MQTopicCons.CF)
public class DrawCashSuccessConsumer
        extends BaseMessageConsumer<String>
        implements MessageListener<String>{

    @Resource
    private CrowdfundingInfoBiz crowdfundingInfoBiz;

    @Autowired
    private CaseProcessStatusClient caseProcessStatusClient;

    @Override
    protected boolean handle(ConsumerMessage<String> consumerMessage) {
        String infoUuid = consumerMessage.getPayload();
        if (StringUtils.isBlank(infoUuid)) {
            log.error("infoUuid blank");
            return true;
        }
        CrowdfundingInfo fundingInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        return caseProcessStatusClient.update(fundingInfo.getId(), CaseProcessStatusEnum.DRAW_CASH_SUCCESS).ok();
    }

    @Override
    protected Logger getLogger() {
        return log;
    }
}
