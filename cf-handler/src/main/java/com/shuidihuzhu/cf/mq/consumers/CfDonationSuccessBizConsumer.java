package com.shuidihuzhu.cf.mq.consumers;

import brave.Tracing;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.cf.biz.crowdfunding.CfInfoShareRecordBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfSharePromoteOrderBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.clinet.event.center.enums.UserOperationTypeEnum;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingType;
import com.shuidihuzhu.cf.enums.crowdfunding.UserTagGroup;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoShareRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CfSharePromoteOrder;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.service.crowdfunding.IPService;
import com.shuidihuzhu.cf.service.event.EventCenterService;
import com.shuidihuzhu.cf.vo.UserInfoVo;
import com.shuidihuzhu.client.cf.olap.client.CaseBaseDataFeignClient;
import com.shuidihuzhu.client.cf.olap.model.CfCaseBaseData;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.logger.SDBizStatLogger;
import com.shuidihuzhu.data.analytics.javasdk.core.Analytics;
import com.shuidihuzhu.data.analytics.javasdk.core.UserTagTypeEnum;
import com.shuidihuzhu.data.servicelog.meta.cf.CaseOrder;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.*;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Optional;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @author: lixuan
 * @date: 2018/4/18 11:03
 */
@Slf4j
@Service
@RefreshScope
@RocketMQListener(id = MQTagCons.CF_DONATION_SUCCESS_BIZ,
        topic = MQTopicCons.CF,
        tags = MQTagCons.CF_DONATION_SUCCESS_BIZ,
        group = "Cf-ConsumerGroup_CF_DONATION_SUCCESS_BIZ")
public class CfDonationSuccessBizConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<CrowdfundingOrder> {

    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private CfInfoShareRecordBiz cfInfoShareRecordBiz;
    @Autowired
    private CfSharePromoteOrderBiz cfSharePromoteOrderBiz;
    @Autowired
    private MeterRegistry meterRegistry;
    @Autowired(required = false)
    private Producer producer;
    @Resource(name = "cfRedissonHandler")
    private RedissonHandler cfRedissonHandler;
    @Autowired
    private Tracing tracing;
    // 用于发送转发引导捐款的
    private static ExecutorService executorService;

    @Autowired
    private Analytics analytics;


    @Resource
    private EventCenterService eventCenterService;

    @Autowired
    private IPService ipService;

    @Resource
    private CaseBaseDataFeignClient cfCaseBaseDataFeignClient;

    @PostConstruct
    public void init() {
        executorService = tracing.currentTraceContext().executorService(new ThreadPoolExecutor(5, 20, 60L, TimeUnit.SECONDS,
                new ArrayBlockingQueue<Runnable>(1000)));
    }

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CrowdfundingOrder> mqMessage) {
        printReceiveMqMessageLog(mqMessage);
        //key: MQTagCons.CF_DONATION_SUCCESS_BIZ + "_" + orderId
        String key = mqMessage.getKeys();
        String redisKey = MQTagCons.CF_DONATION_SUCCESS_BIZ + "_" + key;
        if(cfRedissonHandler.exists(redisKey)) {
            log.info("CfDonationSuccessBizConsumer repeat msg:{}", mqMessage);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        String tryLock = null;
        try {
            tryLock = cfRedissonHandler.tryLock(key, 0, 60 * 1000L);
        } catch (Exception e) {
            log.error("", e);
        }

        if(StringUtils.isBlank(tryLock)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        try {
            CrowdfundingOrder order = mqMessage.getPayload();
            boolean x = handle(order);
            if(x) {
                cfRedissonHandler.setNX(redisKey, "1", RedissonHandler.ONE_DAY);
                return ConsumeStatus.CONSUME_SUCCESS;
            } else {
                return ConsumeStatus.RECONSUME_LATER;
            }
        } catch (Exception e) {
            log.error("捐款成功事件，异常重试", e);
            return ConsumeStatus.RECONSUME_LATER;
        } finally {
            try {
                if(StringUtils.isNotBlank(tryLock)) {
                    cfRedissonHandler.unLock(key, tryLock);
                }
            } catch (Exception e) {
                log.info("", e);
            }
        }
    }


    private boolean handle(CrowdfundingOrder successOrder) {
        try {
            CrowdfundingInfo crowdfundingInfo = this.crowdfundingInfoBiz.getFundingInfoByIdFromSlaveOrMaster(successOrder.getCrowdfundingId());
            // 发分享推送
            this.saveOrderRelatedMsg(crowdfundingInfo, successOrder);
            // 业务日志
            String caseId = crowdfundingInfo == null ? "" : crowdfundingInfo.getInfoId();
            JSONObject o = (JSONObject) JSONObject.toJSON(successOrder);
            o.put("infoUuid", caseId);
            o.put("ctime", DateUtil.getYmdhmsFromTimestamp(successOrder.getCtime().getTime()));
            o.put("payTime", DateUtil.getYmdhmsFromTimestamp(successOrder.getPayTime().getTime()));
            SDBizStatLogger.getCfLog().autoLoadUserThirdInfo(successOrder.getUserId(), successOrder.getUserThirdType(),
                    successOrder.getSelfTag(), "donate", successOrder.getChannel(), "", null)
                    .addToExtInfo(o).info();
            incCount(successOrder);

            String diseaseName = "";
            String diseaseNorm = "";
            int cfServiceTag=0;
            Response<CfCaseBaseData> cfCaseBaseDataResponse = cfCaseBaseDataFeignClient.getCaseBaseData(crowdfundingInfo.getId());
            if(cfCaseBaseDataResponse != null && cfCaseBaseDataResponse.getData() != null){
                diseaseName = cfCaseBaseDataResponse.getData().getDiseaseName();
                diseaseNorm = cfCaseBaseDataResponse.getData().getDiseaseNorm();
                cfServiceTag= cfCaseBaseDataResponse.getData().getServiceTag();
            }

            CaseOrder caseOrder = new CaseOrder();

            try {
                // https://wiki.shuiditech.com/pages/viewpage.action?pageId=380862503
                // https://wiki.shuiditech.com/pages/viewpage.action?pageId=469828819
                caseOrder.setPay_time(successOrder.getPayTime().getTime());
                caseOrder.setCase_id(Optional.ofNullable(crowdfundingInfo).map(CrowdfundingInfo::getInfoId).orElse(""));
                caseOrder.setInfo_id(Optional.ofNullable(crowdfundingInfo).map(p -> Long.valueOf(p.getId())).orElse(0L));
                caseOrder.setCreate_time(successOrder.getCtime().getTime());

                caseOrder.setCurrentStep(2);
                caseOrder.setTotalStep(2);
                caseOrder.setJoinValue(String.valueOf(successOrder.getId()));
                caseOrder.setUser_tag(String.valueOf(successOrder.getUserId()));
                caseOrder.setUser_tag_type(UserTagTypeEnum.userid);

                caseOrder.setThird_type(Optional.ofNullable(successOrder.getUserThirdType()).map(Long::valueOf).orElse(0l));
                caseOrder.setOrder_id(Optional.ofNullable(successOrder.getId()).orElse(0l));
                caseOrder.setDonate_amt(Optional.ofNullable(successOrder.getAmount()).map(Long::valueOf).orElse(0l));
                caseOrder.setPay_seq(successOrder.getPayUid());
                caseOrder.setIp(Optional.ofNullable(successOrder.getIp()).map(String::valueOf).orElse(""));
                caseOrder.setIs_anonymous(Optional.ofNullable(successOrder.isAnonymous()).map(String::valueOf).orElse(""));
                caseOrder.setPay_status(Optional.ofNullable(successOrder.getPayStatus()).map(Long::valueOf).orElse(0l));

                caseOrder.setDisease_name(diseaseName);
                caseOrder.setNorm_disease_name(diseaseNorm);
                caseOrder.setCf_service_tag((long)cfServiceTag);

                caseOrder.setUser_tag(String.valueOf(successOrder.getUserId()));
                caseOrder.setUser_tag_type(UserTagTypeEnum.userid);
                if(successOrder.getActivityId() == 0) {
                    caseOrder.setActivity_id("");
                } else {
                    caseOrder.setActivity_id(String.valueOf(successOrder.getActivityId()));
                }

                caseOrder.setShare_dv(Long.valueOf(successOrder.getShareDv()));

                analytics.track(caseOrder);
                log.info("大数据打点上报,案例捐款成功-回调:{}", JSONObject.toJSONString(caseOrder));
            } catch (Exception e) {
                log.error("大数据打点上报异常,案例捐款成功-回调 异常:{}", JSONObject.toJSONString(caseOrder), e);
            }
        } catch (Exception e) {
            log.error("case_order 数据上报异常", e);
        }
        try {
            addTag(successOrder.getUserId(), successOrder.getUserThirdType(), successOrder.getCode());
        } catch (Exception e) {
            log.error("donation add tag to user error", e);
        }
        return true;
    }

    public void addTag(long userId, Integer thirdType, String code) {
        UserInfoVo userInfoVo = new UserInfoVo(userId, "", UserTagGroup.DONATION, thirdType);
        log.info("add tag to donation user userInfoVo:{}", userInfoVo);
        producer.send(new Message<>(MQTopicCons.CF,
                MQTagCons.CF_ADD_WX_TAG_TO_USER, MQTagCons.CF_ADD_WX_TAG_TO_USER + "-" + code + "-"
                + System.currentTimeMillis(), userInfoVo, DelayLevel.S1));
    }

    private void incCount(CrowdfundingOrder successOrder) {
        // 记数
        try {
//            String ip = IPDistrictExt.numberToIp(successOrder.getIp());
//            Tags of = Tags.of("status", "success")
//                    .and("type", "count")
//                    .and("anonymous", String.valueOf(successOrder.isAnonymous()))
//                    .and("userThirdType", successOrder.getUserThirdType() + "");
//            if(StringUtils.isNotEmpty(ip)) {
//                Map<String, String> map = ipService.getAdderFromIP(ip);
//                if(MapUtils.isNotEmpty(map) && ProvinceEnum.getByName(map.get(IPService.province)) != ProvinceEnum.EMPTY ) {
//                    of = of.and("state", map.get(IPService.province));
//                }
//            }
//            Counter.builder("order")
//                    .tags(of)
//                    .register(meterRegistry)
//                    .increment();
//
//            double value = successOrder.getAmountInYuanDouble();
//
//            Counter.builder("order")
//                    .tags(Tags.of("status", "success", "type", "amount"))
//                    .register(meterRegistry)
//                    .increment(value);

        } catch (Exception e) {
            log.error("记数错误 error", e);
        }
    }

    private void saveOrderRelatedMsg(final CrowdfundingInfo crowdfundingInfo, final CrowdfundingOrder order) {
        if(order == null) {
            return;
        }

        // 发送转发效果消息
        executorService.execute(() -> {

            // 保存分享效果，并按需发送分享效果消息
            final String uuid = order.getFrom();
            if(!StringUtils.isEmpty(uuid)
                    && CrowdfundingType.SERIOUS_ILLNESS.value() == crowdfundingInfo.getType().intValue()) {
                sendShareEffectMsg(order, uuid, crowdfundingInfo);
            }
        });
    }

    private void sendShareEffectMsg(CrowdfundingOrder crowdfundingOrder, String uuid, CrowdfundingInfo crowdfundingInfo) {
        int infoId = crowdfundingOrder.getCrowdfundingId();
        CfInfoShareRecord cfInfoShareRecord = cfInfoShareRecordBiz.findByUuidAndInfoId(uuid, infoId);
        log.info("sendShareEffectMsg infoShareRecord:{}", cfInfoShareRecord);
        if(cfInfoShareRecord != null && cfInfoShareRecord.getUserId() > 0) {
            CfSharePromoteOrder cfSharePromoteOrder = new CfSharePromoteOrder();
            cfSharePromoteOrder.setUserId(crowdfundingOrder.getUserId());
            cfSharePromoteOrder.setSource(uuid);
            cfSharePromoteOrder.setInfoId(crowdfundingOrder.getCrowdfundingId());
            cfSharePromoteOrder.setSourceUserId(cfInfoShareRecord.getUserId());
            cfSharePromoteOrder.setOrderId(crowdfundingOrder.getId());
            cfSharePromoteOrder.setAmount(crowdfundingOrder.getAmount());
            cfSharePromoteOrder.setAmmountInYuan(crowdfundingOrder.getAmountInYuanDouble());
            cfSharePromoteOrder.setAnonymous(crowdfundingOrder.isAnonymous() ? 1 : 0);

            CfSharePromoteOrder order = cfSharePromoteOrderBiz.findLast(cfInfoShareRecord.getUserId(), infoId);
            log.info("sendShareEffectMsg sharePromoteOrder:{}, userId={}, sourceId={}", order,
                    crowdfundingOrder.getUserId(), cfInfoShareRecord.getUserId());

            //订单维度唯一
            CfSharePromoteOrder sharePromoteOrder = cfSharePromoteOrderBiz
                    .getByOrderId(crowdfundingOrder.getCrowdfundingId(), crowdfundingOrder.getId());
            log.info("分享带来的订单：caseId:{}, orderId:{}, userId:{}, sourceId:{}, sharePromoteOrder:{}",
                    crowdfundingOrder.getCrowdfundingId(), crowdfundingOrder.getId(), crowdfundingOrder.getUserId(),
                    cfInfoShareRecord.getUserId(), JSON.toJSONString(sharePromoteOrder));
            if(sharePromoteOrder == null) {
                cfSharePromoteOrderBiz.insert(cfSharePromoteOrder);
            }

            if(order == null && crowdfundingOrder.getUserId() != cfInfoShareRecord.getUserId()
                    && !crowdfundingOrder.isAnonymous()) {   // 这是该用户针对此案例的第一个转化，发信息鼓励他

                String key = cfSharePromoteOrder.getInfoId() + "_" + cfSharePromoteOrder.getSourceUserId();
                String redisKey = "share_first_amount_" + key;
                boolean suc = cfRedissonHandler.setNX(redisKey, 1, TimeUnit.DAYS.toMillis(1));
                if(!suc) {
                    log.debug("markAndSendEvent 已经发送过，不再发送。key={}", key);
                    return;
                }
                //发送转发事件 300消息 315消息
                eventCenterService.sendEventCenter(UserOperationTypeEnum.CASE_SHARE_FIRST_DONATION.getCode(), cfSharePromoteOrder, crowdfundingInfo.getInfoId());
            }
        }
    }

    @Override
    public int getOrder() {
        return 0;
    }
}
