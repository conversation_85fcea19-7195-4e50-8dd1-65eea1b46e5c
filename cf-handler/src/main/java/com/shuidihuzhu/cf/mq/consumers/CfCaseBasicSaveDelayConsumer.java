package com.shuidihuzhu.cf.mq.consumers;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.account.model.UserThirdModel;
import com.shuidihuzhu.cf.biz.wx.Cf111WxEventRecordBiz;
import com.shuidihuzhu.cf.biz.wx.CfWxMpCommonBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.WxConstants;
import com.shuidihuzhu.cf.constants.crowdfunding.RedisKeyCons;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.delegate.UserThirdDelegate;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingBaseInfoBackup;
import com.shuidihuzhu.cf.mq.common.CommonConsumerHelper;
import com.shuidihuzhu.cf.service.msg.MsgClientV2Service;
import com.shuidihuzhu.cf.service.notice.urgeraise.UrgeRaiseMsgService;
import com.shuidihuzhu.cf.service.notice.urgeraise.UrgeRaiseMsgType;
import com.shuidihuzhu.cf.service.task.CfCaseBasicSaveDelayService;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import com.shuidihuzhu.wx.enums.AccountThirdTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户保存基本信息后 延时3分钟发送
 */
@Service
@RocketMQListener(id = MQTagCons.CF_SNAPSHOT_SAVE_SUCESS,
        tags = MQTagCons.CF_SNAPSHOT_SAVE_SUCESS,
        topic = MQTopicCons.CF,
        group = MQTagCons.CF_SNAPSHOT_SAVE_SUCESS + "-10M-DELAY")
@RefreshScope
@Slf4j
public class CfCaseBasicSaveDelayConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<CrowdfundingBaseInfoBackup> {

    @Resource
    private CommonConsumerHelper commonConsumerHelper;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CrowdfundingBaseInfoBackup> consumerMessage) {
        printReceiveMqMessageLog(consumerMessage);
        return commonConsumerHelper.consumeMessage(consumerMessage, this::handle, true);
    }

    private boolean handle(ConsumerMessage<CrowdfundingBaseInfoBackup> consumerMessage) {
        return true;
    }

}
