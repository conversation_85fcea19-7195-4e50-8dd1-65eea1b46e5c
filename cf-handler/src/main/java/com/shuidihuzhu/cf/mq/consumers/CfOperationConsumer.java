package com.shuidihuzhu.cf.mq.consumers;

import com.shuidihuzhu.cf.biz.crowdfunding.CfInfoExtBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSimpleModel;
import com.shuidihuzhu.cf.model.crowdfunding.CfOperatingRecord;
import com.shuidihuzhu.cf.service.deposit.ICfDepositAccountService;
import com.shuidihuzhu.cf.service.tog.GuangzhouService;
import com.shuidihuzhu.cf.service.tog.JinYunService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.DelayLevel;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * Created by sven on 18/8/2.
 *
 * <AUTHOR>
 */
@Service
@RocketMQListener(id = MQTagCons.CF_OPERATION_MSG,
        group = "cf-operation-group",
        tags = MQTagCons.CF_OPERATION_MSG,
        topic = MQTopicCons.CF)
@Slf4j
public class CfOperationConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<CfOperatingRecord> {

    @Autowired
    private CfInfoExtBiz cfInfoExtBiz;
    @Autowired
    private CrowdfundingInfoBiz infoBiz;
    @Autowired
    private ICfDepositAccountService depositAccountService;

    @Resource
    private JinYunService jinYunService;

    @Resource
    private GuangzhouService guangzhouService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CfOperatingRecord> mqMessage) {

        printReceiveMqMessageLog("receive operation",mqMessage);

        if(mqMessage == null || mqMessage.getPayload() == null
                || StringUtils.isBlank(mqMessage.getPayload().getInfoUuid())) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        CfInfoExt infoExt = cfInfoExtBiz.getByInfoUuid(mqMessage.getPayload().getInfoUuid());
        if (infoExt == null) {
            log.warn("infoExt为空。infoUuid:{}", mqMessage.getPayload().getInfoUuid());
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        CfInfoSimpleModel simpleModel = infoBiz.getFundingInfoFromMaster(mqMessage.getPayload().getInfoUuid());
        if (simpleModel == null) {
            log.warn("案例为空。infoUuid:{}", mqMessage.getPayload().getInfoUuid());
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        depositAccountService.sendCaseDepositMQ(simpleModel.getId(),
                mqMessage.getPayload().getInfoUuid(),
                infoExt.getProductName(),
                DelayLevel.M3);

        // toG案例打标通用
        try {
            jinYunService.saveJinYunClewChannel(mqMessage.getPayload());
        } catch (Exception e) {
            log.info("CfOperationConsumer saveJinYunClewChannel error", e);
        }

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
