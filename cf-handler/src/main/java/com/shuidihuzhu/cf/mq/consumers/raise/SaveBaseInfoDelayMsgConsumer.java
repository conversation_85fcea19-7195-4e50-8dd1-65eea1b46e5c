package com.shuidihuzhu.cf.mq.consumers.raise;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.stat.feign.stat.CfStatBaseInfoPointClient;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.MsgRecordContant;
import com.shuidihuzhu.cf.delegate.WxSubscribeEventDelegate;
import com.shuidihuzhu.cf.mq.common.BaseMessageConsumer;
import com.shuidihuzhu.cf.service.notice.urgeraise.UrgeRaiseMsgService;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import com.shuidihuzhu.wx.grpc.model.WxSubscribeModel;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2019-03-24  17:51
 */
@Slf4j
@Service
@RocketMQListener(id = MQTagCons.CF_SAVE_BASE_INFO_DELAY_MSG,
        tags = MQTagCons.CF_SAVE_BASE_INFO_DELAY_MSG,
        topic = MQTopicCons.CF)
@RefreshScope
public class SaveBaseInfoDelayMsgConsumer extends BaseMessageConsumer<Long> implements MessageListener<Long>{

    @Resource
    private UrgeRaiseMsgService urgeRaiseMsgService;

    @Resource
    private CrowdfundingInfoBiz crowdfundingInfoBiz;

    @Resource
    private WxSubscribeEventDelegate wxSubscribeEventDelegate;

    @Autowired
    private CfStatBaseInfoPointClient cfStatBaseInfoPointClient;

    @Resource
    private MeterRegistry meterRegistry;

    @Resource(name = "cfRedissonHandler")
    protected RedissonHandler redissonHandler;

    private final String CF_CLEWTRACK_API_SENDMSG_211 = "cf-clewtrack-api-sendmsg_211";

    private static final String METER_NAME = "msg-save-base-info-delay-msg";
    private static final String METER_ACTION = "action";

    private static final int SUBSCRIBE = 1;

    @Value("${msg-config.save-base-info-delay-msg.real-send:false}")
    private boolean realSend;

    private static final String SHIELDVOLUNTEEREEVENTKEY1 = "cf_volunteer_";
    private static final String SHIELDVOLUNTEEREEVENTKEY2 = "qrscene_cf_volunteer_";

    @Override
    protected boolean handle(ConsumerMessage<Long> consumerMessage) {

        statCount("consumedCount");

        Long userId = consumerMessage.getPayload();
        if (userId == null || userId == 0) {
            log.info("userId empty", userId);
            statCount("userIdEmpty");
            return true;
        }

        // 检查是否已经发送
        ArrayList<Long> sendList = Lists.newArrayList(userId);
        //cfWithoutRaiseUserIdRecordBiz.removeRaisedList(sendList);
        /*if (CollectionUtils.isEmpty(sendList)) {
            log.info("hasSend userId: {}", userId);
            statCount("hasSend");
            return true;
        }*/

        //1小时
        String sendMsg = redissonHandler.get(CF_CLEWTRACK_API_SENDMSG_211 + userId, String.class);
        if(StringUtils.isNotBlank(sendMsg)){
            log.info("211、212消息一小时内已发送 userId：", userId);
            return true;
        }
        redissonHandler.setEX(CF_CLEWTRACK_API_SENDMSG_211 + userId, "211、212消息一小时内已发送已发送"+userId, RedissonHandler.ONE_HOUR);

        // 发起消息解决冲突 过滤掉不符合规则的 211 212
        // https://wiki.shuiditech.com/pages/viewpage.action?pageId=99485020
        boolean raise = urgeRaiseMsgService.checkRaise(userId);
        if (!raise) {
            log.info("urge interrupt userId: {}", userId);
            statCount("urgeInterrupt");
            return true;
        }

        // 有未结束案例 不发消息
        boolean hasNoEndCase = crowdfundingInfoBiz.hasNoEndCase(userId);
        if (hasNoEndCase) {
            log.info("hasNoEndCase userId: {}", userId);
            statCount("hasNoEndCase");
            return true;
        }

        // 特殊关注eventKey不发送
        boolean except = isExceptSubscribe(userId);
        if (except) {
            statCount("exceptEventKey");
            log.info("except userId: {}", userId);
            return true;
        }

        Date current = new Date();
        Date anHourAgo = DateUtil.minusMinutes(60, current);

        // 是否有填写
        RpcResult<List<com.shuidihuzhu.cf.client.stat.model.stat.CfStatBaseInfoPoint>> rpcResult = cfStatBaseInfoPointClient.selectByUserIdAndTimeWithInput(userId, anHourAgo, current);
        List<com.shuidihuzhu.cf.client.stat.model.stat.CfStatBaseInfoPoint> resultList = Optional.ofNullable(rpcResult)
                .filter(RpcResult::isSuccess)
                .map(RpcResult::getData)
                .orElse(null);
        boolean hasInput = CollectionUtils.isNotEmpty(resultList);

        log.info("hasInput hasInput: {}, userId: {}", hasInput, userId);

        int subBizType ;
        if (hasInput) {
            subBizType = MsgRecordContant.SUB_BIZ_TYPE_FOR_USER_WITHOUT_RAISE;
            statCount("send212");
        } else {
            subBizType = MsgRecordContant.SUB_BIZ_TYPE_FOR_USER_WITHOUT_FILL_IN;
            statCount("send211");
        }
        statCount("total");

        if (!realSend) {
            return true;
        }

        log.info("send userId: {}", userId);
        return true;
    }

    private boolean isExceptSubscribe(Long userId) {
        List<WxSubscribeModel> subscribeList = wxSubscribeEventDelegate.getSubscribesByUserId(userId);
        for (WxSubscribeModel m : subscribeList) {
            if (SUBSCRIBE != m.getIsSubscribe()){
                continue;
            }
            String eventKey = m.getEventKey();
            if (StringUtils.equals(eventKey, "qrscene_引导关注公众号")
                    || StringUtils.equals(eventKey, "qrscene_引导关注公众号1")){
                return true;
            }
            if (StringUtils.startsWith(eventKey,SHIELDVOLUNTEEREEVENTKEY1)
                    || StringUtils.startsWith(eventKey,SHIELDVOLUNTEEREEVENTKEY2)){
                log.info("Shield_volunteer_eventKey:{}",eventKey);
                return true;
            }
        }
        return false;
    }

    @Override
    protected Logger getLogger() {
        return log;
    }


    private void statCount(String action) {
        try {
            meterRegistry.counter(METER_NAME, METER_ACTION, action).increment();
        } catch (Exception e) {
            log.error("statCount error action: {}", action, e);
        }
    }
}
