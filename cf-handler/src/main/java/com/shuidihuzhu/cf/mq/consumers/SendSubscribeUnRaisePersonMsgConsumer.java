package com.shuidihuzhu.cf.mq.consumers;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.account.model.service.OpenIdUserIdModel;
import com.shuidihuzhu.cf.biz.crowdfunding.CfRecallUnRaiseUserInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingOrderBiz;
import com.shuidihuzhu.cf.biz.wx.Cf111WxEventRecordBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingInfoSlaveDao;
import com.shuidihuzhu.cf.delegate.SimpleUserAccountDelegate;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.model.crowdfunding.CfRecallUnRaiseUserInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.model.user.WxMpEvent;
import com.shuidihuzhu.cf.mq.payload.WxSubscribePayload;
import com.shuidihuzhu.cf.service.msg.MsgClientV2Service;
import com.shuidihuzhu.cf.service.notice.urgeraise.UrgeRaiseMsgService;
import com.shuidihuzhu.cf.service.user.SlaveWxMpEventService;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import com.shuidihuzhu.wx.enums.AccountThirdTypeEnum;
import com.shuidihuzhu.wx.grpc.enums.EventType;
import com.shuidihuzhu.wx.grpc.enums.WxMpBizType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 已关注未发起案例的催发消息的延迟MQ消费者
 * 由 cf-task 中的 SendSubscribeUnRaisePersonMsgJob 定时触发，改为通过"关注"事件触发
 *
 * <AUTHOR>
 * @date 2019/06/13
 */
@Service
@RocketMQListener(id = MQTagCons.SEND_SUBSCRIBE_UN_RAISE_PERSON_MSG,
        tags = MQTagCons.SEND_SUBSCRIBE_UN_RAISE_PERSON_MSG,
        group = "cf-" + MQTagCons.SEND_SUBSCRIBE_UN_RAISE_PERSON_MSG + "-group",
        topic = MQTopicCons.CF)
@Slf4j
public class SendSubscribeUnRaisePersonMsgConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<WxSubscribePayload> {

    @Resource
    private SimpleUserAccountDelegate simpleUserAccountDelegate;
    @Resource
    private CrowdfundingInfoSlaveDao crowdfundingInfoSlaveDao;
    @Resource
    private UrgeRaiseMsgService urgeRaiseMsgService;
    @Resource
    private UserInfoDelegate userInfoDelegate;
    @Resource
    private CrowdfundingOrderBiz crowdfundingOrderBizImpl;
    @Resource
    private CfRecallUnRaiseUserInfoBiz cfRecallUnRaiseUserInfoBizImpl;
    @Autowired
    private Cf111WxEventRecordBiz cf111WxEventRecordBiz;
    @Autowired
    private SlaveWxMpEventService slaveWxMpEventService;
    @Resource
    private MsgClientV2Service msgClientV2Service;

    private static final String SHIELDVOLUNTEEREEVENTKEY1 = "cf_volunteer_";
    private static final String SHIELDVOLUNTEEREEVENTKEY2 = "qrscene_cf_volunteer_";

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<WxSubscribePayload> mqMessage) {
        // payload 校验
        WxSubscribePayload payload = mqMessage.getPayload();
        if (payload == null) {
            log.info("payload is null. ConsumerMessage:{}", mqMessage);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        String openId = payload.getOpenId();
        if (StringUtils.isEmpty(openId)) {
            log.info("openId is null. ConsumerMessage:{}", mqMessage);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        //根据eventKey过滤
        String eventKey = payload.getEventKey();
        log.debug("get_eventKey:{}",eventKey);
        if(StringUtils.isNotEmpty(eventKey)){
            log.debug("get_inner_eventKey:{}",eventKey);
            if (StringUtils.startsWith(eventKey,SHIELDVOLUNTEEREEVENTKEY1)){
                log.info("cf_volunteer_eventKey:{}",eventKey);
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            if (StringUtils.startsWith(eventKey,SHIELDVOLUNTEEREEVENTKEY2)){
                log.info("qrscene_cf_volunteer_eventKey:{}",eventKey);
                return ConsumeStatus.CONSUME_SUCCESS;
            }
        }
        // 根据 openId 获取 userId
        OpenIdUserIdModel openIdUserIdResponse = simpleUserAccountDelegate.getUserIdByOpenId(openId);
        if (openIdUserIdResponse == null) {
            log.info("OpenIdUserIdResponse is null. openId:{}", openId);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        long userId = openIdUserIdResponse.getUserId();

        // 设置时间范围：前一天
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime beginningOfToday = now.withHour(0).withMinute(0).withSecond(0).withNano(0);
        Timestamp endTime = Timestamp.valueOf(beginningOfToday);
        Timestamp beginTime = Timestamp.valueOf(beginningOfToday.minusDays(1));

        // 获取关注信息
        WxMpEvent wxMpEvent = slaveWxMpEventService.getByUserIdAndEventTimeAndMpTypeAndEventType(userId,
                WxMpBizType.WXMPTYPE_CROWDFUNDING.getKey(), EventType.SUBSCRIBE.getKey(), beginTime, endTime);
        if (wxMpEvent == null) {
            log.info("WxMpEvent is null. openId:{}", openId);
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        // 检查是否有发起案例
        Integer raisedInfoId = crowdfundingInfoSlaveDao.getIdByUserIdAndCreateTime(userId, beginTime,
                DateUtil.getCurrentTimestamp());
        if (raisedInfoId != null) {
            log.info("raisedInfoId is not null. userId:{} begin:{} end:{}", userId, beginTime, DateUtil.getCurrentTimestamp());
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        // 校验是否符合催发规则
        if (!urgeRaiseMsgService.checkRaise(userId)) {
            log.info("checkCouldSend is false. userId:{}", userId);
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        // 去 account-service 查询用户详细信息
        UserInfoModel userInfoModel = userInfoDelegate.getUserInfoByUserId(userId);
        if (userInfoModel == null) {
            log.info("userInfoModel is null. userId:{}", userId);
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        // 查询用户近期的捐款情况，如果捐过则不发消息
        List<CrowdfundingOrder> orders = crowdfundingOrderBizImpl.getByUserIds(Sets.newHashSet(userId));
        if (CollectionUtils.isNotEmpty(orders)){
            List<CrowdfundingOrder> collect = orders.stream().filter(a -> beginTime.before(a.getCtime()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                log.info("当前用户userId:{} 在日期：{} 之后捐过款，被过滤掉 {}", userId, beginTime, collect);
                return ConsumeStatus.CONSUME_SUCCESS;
            }
        }

        // 组装结构写入"用户关注未发起推送消息备份表 cf_recall_unraise_user_info"
        CfRecallUnRaiseUserInfo cfRecallUnRaiseUserInfo = new CfRecallUnRaiseUserInfo();
        cfRecallUnRaiseUserInfo.setOpenId("");//走多号推送逻辑，所以去掉了openId
        cfRecallUnRaiseUserInfo.setUserId(userId);
        cfRecallUnRaiseUserInfo.setSubscribeEventTime(new Timestamp(wxMpEvent.getEventTime().getTime()));
        cfRecallUnRaiseUserInfo.setIsSend(1);
        cfRecallUnRaiseUserInfoBizImpl.insertList(Lists.newArrayList(cfRecallUnRaiseUserInfo));

        // 发送294消息
        String nickname = userInfoModel.getNickname();
        if (StringUtils.isBlank(nickname)) {
            nickname = "你好";
        }
        send294ModelNumMsg(userId, nickname);

        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private void send294ModelNumMsg(long userId, String nickName) {
        String modelNum0 = "294model3";
        Map<Long, Map<Integer, String>> msgMap = Maps.newHashMap();
        Map<Integer, String> params = Maps.newHashMap();
        params.put(1, nickName);
        msgMap.put(userId, params);
        msgClientV2Service.sendWxParamsMsg(modelNum0, msgMap);
    }

}
