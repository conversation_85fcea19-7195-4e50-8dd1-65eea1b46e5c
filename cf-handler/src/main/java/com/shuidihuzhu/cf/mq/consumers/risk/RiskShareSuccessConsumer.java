package com.shuidihuzhu.cf.mq.consumers.risk;

import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enhancer.mq.MaliBaseMQConsumer;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoShareRecord;
import com.shuidihuzhu.cf.risk.RiskShareService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 **/
@Slf4j
@Service
@RocketMQListener(id = MQTagCons.CF_SHARE_SUCCESS_REAL_TIME_MSG,
        tags = MQTagCons.CF_SHARE_SUCCESS_REAL_TIME_MSG,
        group = "cf-api_risk_" + MQTagCons.CF_SHARE_SUCCESS_REAL_TIME_MSG,
        topic = MQTopicCons.CF
)
public class RiskShareSuccessConsumer
        extends MaliBaseMQConsumer<CfInfoShareRecord>
        implements MessageListener<CfInfoShareRecord> {

    @Autowired
    private RiskShareService riskShareService;

    @Override
    protected boolean handle(ConsumerMessage<CfInfoShareRecord> consumerMessage) {
        CfInfoShareRecord shareRecord = consumerMessage.getPayload();
        int caseId = shareRecord.getInfoId();
        long userId = shareRecord.getUserId();
        riskShareService.onShareSuccess(caseId, userId);
        return true;
    }

    @Override
    protected Logger getLogger() {
        return log;
    }
}
