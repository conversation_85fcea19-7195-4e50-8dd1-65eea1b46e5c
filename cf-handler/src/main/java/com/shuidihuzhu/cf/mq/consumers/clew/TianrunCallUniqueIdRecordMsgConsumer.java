package com.shuidihuzhu.cf.mq.consumers.clew;

import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.mq.common.BaseMessageConsumer;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RocketMQListener(id = MQTagCons.CF_TIANRUN_CALL_UNIQUEID_RECORD_MSG,
        tags = MQTagCons.CF_TIANRUN_CALL_UNIQUEID_RECORD_MSG,
        group = "cf-api" + MQTagCons.CF_TIANRUN_CALL_UNIQUEID_RECORD_MSG,
        topic = MQTopicCons.CF)
public class TianrunCallUniqueIdRecordMsgConsumer
        extends BaseMessageConsumer<String>
        implements MessageListener<String>{

    @Override
    protected boolean handle(ConsumerMessage<String> consumerMessage) {

        // 通话完成通知 可能以后能用
        return true;
    }

    @Override
    protected Logger getLogger() {
        return log;
    }
}
