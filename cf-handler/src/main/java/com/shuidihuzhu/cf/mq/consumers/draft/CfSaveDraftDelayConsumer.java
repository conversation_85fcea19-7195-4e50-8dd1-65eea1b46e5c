package com.shuidihuzhu.cf.mq.consumers.draft;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingBaseInfoBackup;
import com.shuidihuzhu.cf.mq.consumers.BaseConsumerPrintReceiveLog;
import com.shuidihuzhu.cf.mq.payload.SaveDraftPayload;
import com.shuidihuzhu.cf.service.crowdfunding.CrowdfundingInfoService;
import com.shuidihuzhu.cf.service.msg.MsgClientV2Service;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019/12/16
 */

@Slf4j
@Service
@RocketMQListener(id = MQTagCons.CF_SAVE_DRAFT_DELAY,
        tags = MQTagCons.CF_SAVE_DRAFT_DELAY,
        group = MQTagCons.CF_SAVE_DRAFT_DELAY + "-app-push-group",
        topic = MQTopicCons.CF)
public class CfSaveDraftDelayConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<SaveDraftPayload> {

    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;

    @Autowired
    private CrowdfundingInfoService crowdfundingInfoService;

    @Autowired
    private MsgClientV2Service msgClientV2Service;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<SaveDraftPayload> mqMessage) {
        printReceiveMqMessageLog(mqMessage);

        SaveDraftPayload payload = null;
        if (mqMessage == null || (payload = mqMessage.getPayload()) == null || StringUtils.isBlank(payload.getPageFlag())) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        long userId = payload.getUserId();
        String pageFlag = payload.getPageFlag();
        String pageFlagFromDb = getPageFlag(userId);

        // 1：当前页是否正确
        if (!StringUtils.equals(pageFlag, pageFlagFromDb)) {
            log.info("CfSaveDraftDelayConsumer 页面已保存，不需要催填写. payload:{}, pageFlagFromDb:{}", payload, pageFlagFromDb);
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        // 2. 存在未结束的筹款案例，不发送
        if (crowdfundingInfoBiz.hasNoEndCase(userId)) {
            log.info("CfSaveDraftDelayConsumer 用户有在筹案例，不需要催填写. payload:{}", payload);
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        String templateId = payload.getTemplateId();

        msgClientV2Service.sendAppMsg(templateId, Lists.newArrayList(userId));
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private String getPageFlag(long userId) {

        CrowdfundingBaseInfoBackup info = null;

        try {
            info = crowdfundingInfoService.selectRecentlyCfByUserId(userId, 0);
        } catch (Exception e) {
            log.error("CfSaveDraftDelayConsumer selectRecentlyCfByUserId error. userId:{}", userId, e);
        }
        if (info == null || StringUtils.isBlank(info.getPageFlag())) {
            return StringUtils.EMPTY;
        }
        return info.getPageFlag();
    }
}
