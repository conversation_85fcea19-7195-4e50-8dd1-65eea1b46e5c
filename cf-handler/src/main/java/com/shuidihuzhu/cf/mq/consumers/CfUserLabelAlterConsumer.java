package com.shuidihuzhu.cf.mq.consumers;

import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.ICfUserCaseLabelService;
import com.shuidihuzhu.cf.biz.crowdfunding.ICfUserLabelService;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.dao.crowdfunding.CfFirstApproveMaterialDao;
import com.shuidihuzhu.cf.dao.crowdfunding.CfInfoExtDao;
import com.shuidihuzhu.cf.enums.crowdfunding.CaseRaiseChannelEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.UserLabelAlterEnum;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.service.crowdfunding.CfRaisePageUserLabelService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @time 2019/3/22 下午7:48
 * @desc https://wiki.shuiditech.com/pages/viewpage.action?pageId=164201711
 */
@Slf4j
@Service
@RocketMQListener(id = MQTagCons.CF_USER_LABEL_ALTER_MSG, group = "USER_LABEL_ALTER_" + MQTagCons.CF_USER_LABEL_ALTER_MSG,
                  topic = MQTopicCons.CF, tags = MQTagCons.CF_USER_LABEL_ALTER_MSG)
public class CfUserLabelAlterConsumer  extends BaseConsumerPrintReceiveLog implements MessageListener<CfCommonPayload> {
    private final static int FIRST_APPROVE_UGC_ORDER_CONTENT = 7;
    private final static int FIRST_APPROVE_REJECT = 15;

    @Autowired
    private CfInfoExtDao cfInfoExtDao;

    @Autowired
    private ICfUserLabelService cfUserLabelService;

    @Autowired
    private ICfUserCaseLabelService cfUserCaseLabelService;

    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;

    @Autowired
    private CfRaisePageUserLabelService raisePageUserLabelService;

    @Autowired
    private CfFirstApproveMaterialDao cfFirstApproveMaterialDao;


    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CfCommonPayload> mqMessage) {
        printReceiveMqMessageLog(mqMessage);

        CfCommonPayload cfCommonPayload = mqMessage.getPayload();

        if(null == cfCommonPayload){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        long userId = cfCommonPayload.getUserId();
        int caseId = cfCommonPayload.getCaseId();
        UserLabelAlterEnum alterEnum = cfCommonPayload.getAlterEnum();

        CfUserLabelDO userLabelDO = cfUserLabelService.queryLabelByUser(userId);
        List<CfUserCaseLabelDO> userCaseLabelDOS = cfUserCaseLabelService.queryCaseLabel(userId);
        CrowdfundingInfo info = crowdfundingInfoBiz.getFundingInfoById(caseId);

        /**
         * 发起案例时
         * 1:如果没有用户标签信息，则insert用户标签信息
         * 2:如果有用户标签信息，则自增用户的案例数量并且新增一条案例标签信息
         */
        if(alterEnum == UserLabelAlterEnum.RAISE){

            //1:如果没有用户标签信息，则insert用户标签信息
            raisePageUserLabelService.createUserLabel(userId, userLabelDO, userCaseLabelDOS);

            //2:如果有用户标签信息，则自增用户的案例数量 && 新增一条案例标签信息
            if(null != userLabelDO && CollectionUtils.isNotEmpty(userCaseLabelDOS) && null != info){
                //筹款用户的案例数量自增1
                cfUserLabelService.incrCaseNum(userId);

                //新增一条案例标签信息
                insertLabel(info, userId, caseId);
            }
        }

        /**
         * 填写或删除草稿
         * 用户填写草稿，并且有标签信息时，更新用户草稿信息为有草稿
         * 用户发起案例时会发送删除草稿的mq，草稿删除后更新用户草稿信息为没有草稿
         */
        if((alterEnum == UserLabelAlterEnum.WRITE_DRAFT || alterEnum == UserLabelAlterEnum.DELETE_DRAFT) && null != userLabelDO){
            int draft = alterEnum == UserLabelAlterEnum.WRITE_DRAFT ? 1 : 0;
            cfUserLabelService.updateDraft(draft, userId);
        }

        /**
         * cf-task的CfCaseEndTraceJob扫结束的案例时发送案例结束的mq
         * 1:如果没用用户标签信息，则insert用户标签信息
         * 2:如果有用户标签信息
         *   2.1:如果存在该案例的标签，则更新该案例的结束状态为结束
         *   2.2:如果不存在该案例的标签，则insert该案例的标签
         */
        if(alterEnum == UserLabelAlterEnum.CASE_END){
            //1:如果没用用户标签信息，则insert用户标签信息
            raisePageUserLabelService.createUserLabel(userId, userLabelDO, userCaseLabelDOS);

            //2:如果有用户标签信息
            if(null != userLabelDO && CollectionUtils.isNotEmpty(userCaseLabelDOS)){
                CfUserCaseLabelDO cfUserCaseLabelDO = cfUserCaseLabelService.queryCaseLabelByUserAndCase(userId, caseId);

                //2.1:如果存在该案例的标签，则更新该案例的结束状态为结束
                if(null != cfUserCaseLabelDO){
                    cfUserCaseLabelService.updateEnd(userId, caseId);
                }

                //2.2:如果不存在该案例的标签，则insert该案例的标签
                if(null == cfUserCaseLabelDO && null != info){
                    insertLabel(info, userId, caseId);
                }
            }
        }

        /**
         * cf-admin-api前置审核通过
         * 1:如果没用用户标签信息，则insert用户标签信息
         * 2:如果有用户标签信息
         *   2.1:如果存在该案例的标签，则更新该案例的结束状态为结束
         *   2.2:如果不存在该案例的标签，则insert该案例的标签
         */
        if(alterEnum == UserLabelAlterEnum.FIRST_PPROVE){
            //1:如果没用用户标签信息，则insert用户标签信息
            raisePageUserLabelService.createUserLabel(userId, userLabelDO, userCaseLabelDOS);

            //2:如果有用户标签信息
            if(null != userLabelDO && CollectionUtils.isNotEmpty(userCaseLabelDOS) && null != info){
                CfUserCaseLabelDO cfUserCaseLabelDO = cfUserCaseLabelService.queryCaseLabelByUserAndCase(userId, caseId);

                //2.1:如果存在该案例的标签，则更新该案例的结束时间和前置审核状态为通过
                if(null != cfUserCaseLabelDO){
                    CfInfoExt ext = cfInfoExtDao.getByInfoUuid(info.getInfoId());
                    Date endTime = info.getEndTime();
                    boolean firstApprove = null != ext && ext.getFirstApproveStatus() != FirstApproveStatusEnum.DEFAULT.getCode() ? true : false;
                    int approveStatus = null != ext ? ext.getFirstApproveStatus() : 0;

                    cfUserCaseLabelService.updateEndTimeAndStatus(firstApprove, userId, caseId, endTime, approveStatus, null != ext ? ext.getFirstApproveTime() : new Date());
                }

                //2.2:如果不存在该案例的标签，则insert该案例的标签
                if(null == cfUserCaseLabelDO){
                    insertLabel(info, userId, caseId);
                }
            }
        }

        /**
         * cf-admin-api前置审核驳回
         * 1:如果没用用户标签信息，则insert用户标签信息
         * 2:如果有用户标签信息
         *   2.1:如果存在该案例的标签，则自增该案例的驳回次数
         *   2.2:如果不存在该案例的标签，则insert该案例的标签
         */
        if(alterEnum == UserLabelAlterEnum.FIRST_REJECT){
            //1:如果没用用户标签信息，则insert用户标签信息
            raisePageUserLabelService.createUserLabel(userId, userLabelDO, userCaseLabelDOS);

            //2:如果有用户标签信息
            if(null != userLabelDO && CollectionUtils.isNotEmpty(userCaseLabelDOS) && null != info){
                CfUserCaseLabelDO caseLabelDO = cfUserCaseLabelService.queryCaseLabelByUserAndCase(userId, caseId);

                //2.1:如果存在该案例的标签，则自增该案例的驳回次数
                if(null != caseLabelDO){
                    CfInfoExt ext = cfInfoExtDao.getByInfoUuid(info.getInfoId());
                    cfUserCaseLabelService.updateRejectAndStatus(userId, caseId, null != ext ? ext.getFirstApproveStatus() : 0);
                }

                //2.2:如果不存在该案例的标签，则insert该案例的标签
                if(null == caseLabelDO){
                    insertLabel(info, userId, caseId);
                }
            }
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private void insertLabel(CrowdfundingInfo info,long userId, int caseId){
        CaseRaiseChannelEnum raiseChannelEnum = raisePageUserLabelService.buildRaiseChannel(info.getChannel());
        int rejectCount = cfFirstApproveMaterialDao.countWorkOrderByCaseIdAndApproveStatus(info.getId(), FIRST_APPROVE_UGC_ORDER_CONTENT, FIRST_APPROVE_REJECT);
        CfInfoExt cfInfoExt = cfInfoExtDao.getByInfoUuid(info.getInfoId());
        boolean firstApprove = null != cfInfoExt && cfInfoExt.getFirstApproveStatus() != FirstApproveStatusEnum.DEFAULT.getCode() ? true : false;

        CfUserCaseLabelDO labelDO = new CfUserCaseLabelDO();
        labelDO.setUserId(userId);
        labelDO.setCaseId(caseId);
        labelDO.setEnd(new Date().after(info.getEndTime()));
        labelDO.setRaiseTime(info.getCreateTime());
        labelDO.setEndTime(info.getEndTime());
        labelDO.setChannel(StringUtils.isNotEmpty(info.getChannel()) ? info.getChannel() : "");
        labelDO.setRaiseChannel(raiseChannelEnum.getCode());
        labelDO.setFirstApprove(firstApprove);
        labelDO.setApproveStatus(null != cfInfoExt ? cfInfoExt.getFirstApproveStatus() : 0);
        labelDO.setApproveTime(null != cfInfoExt ? cfInfoExt.getFirstApproveTime() : new Date());
        labelDO.setRejectCount(rejectCount);
        cfUserCaseLabelService.insertCaseLabel(labelDO);
    }
}
