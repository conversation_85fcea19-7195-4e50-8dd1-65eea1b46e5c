package com.shuidihuzhu.cf.mq.consumers;

import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.service.crowdfunding.CrowdfundingFinishService;
import com.shuidihuzhu.cf.service.crowdfunding.UserWxTagService;
import com.shuidihuzhu.cf.vo.UserInfoVo;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> Ahrievil
 * @date : 2018/5/17 17:20
 */
@Slf4j
@Service
@RocketMQListener(id = MQTagCons.CF_ADD_WX_TAG_TO_USER,
        tags = MQTagCons.CF_ADD_WX_TAG_TO_USER,
        topic = MQTopicCons.CF)
public class CfWxTagToUserConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<UserInfoVo> {

    @Autowired
    private UserWxTagService userWxTagService;
    @Autowired
    private CrowdfundingFinishService crowdfundingFinishService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<UserInfoVo> message) {
        printReceiveMqMessageLog(message);

        if (message.getReconsumeTimes() > 10) {
            log.debug("添加用户的微信标签失败，已经尝试10次 {}", message);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        UserInfoVo userInfoVo = message.getPayload();
        if (userInfoVo == null || userInfoVo.getUserTagGroup() == null) {
            log.error("回调消息body为空！");
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        log.debug("CfWxTagToUserConsumer consumeMessage userInfoVo:{}", userInfoVo);
        boolean result = false;
        try {
            switch (userInfoVo.getUserTagGroup()) {
                case SUBSCRIBE:
                    result = userWxTagService.subscribeHandler(userInfoVo.getUserId());
                    break;
                case BASE_INFO:
                    result = userWxTagService.raiseCaseHandler(userInfoVo.getOpenId(), userInfoVo.getUserId());
                    break;
                case END_CASE:
                    if (StringUtils.isBlank(userInfoVo.getInfoUuid()) ||
                            userInfoVo.getCfFinishStatus() == null || userInfoVo.getUserId() <= 0) {
                        log.error("添加用户的微信标签失败，参数错误 userInfoVo:{}", userInfoVo);
                        return ConsumeStatus.CONSUME_SUCCESS;
                    }
                    result = crowdfundingFinishService.handleFinish(userInfoVo);
                    break;
                case DONATION:
                    result = userWxTagService.handleDonation(userInfoVo.getUserId(), userInfoVo.getThirdType());
                    break;
                default:
            }
        } catch (Exception e) {
            log.error("CfWxTagToUserConsumer consumeMessage error", e);
        }
        log.debug("CfWxTagToUserConsumer consumeMessage add user tag result:{}", result);
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
