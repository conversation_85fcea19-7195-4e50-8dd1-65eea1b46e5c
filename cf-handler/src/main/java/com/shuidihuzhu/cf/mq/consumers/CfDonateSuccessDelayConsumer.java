package com.shuidihuzhu.cf.mq.consumers;

import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.service.crowdfunding.CfUserCaseStatService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @time 2019/2/22 上午10:07
 * @desc
 */
@Slf4j
@Service
@RocketMQListener(id = MQTagCons.CF_DONATE_SUCCESS_DELAY_MSG,
                  tags = MQTagCons.CF_DONATE_SUCCESS_DELAY_MSG,
                  group = "DONATE_SUCCESS_DELAY" + MQTagCons.CF_DONATE_SUCCESS_DELAY_MSG,
                  topic = MQTopicCons.CF)
public class CfDonateSuccessDelayConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<CrowdfundingOrder> {

    @Autowired
    private CfUserCaseBaseStatService cfUserCaseBaseStatService;

    @Autowired
    private CfUserBaseStatService cfUserBaseStatService;

    @Autowired
    private CfUserCaseStatService cfUserCaseStatService;

    @Autowired
    private CrowdfundingOrderBiz crowdfundingOrderBiz;

    @Autowired
    private CfInfoShareRecordBiz cfInfoShareRecordBiz;

    @Autowired
    private CfSharePromoteOrderBiz cfSharePromoteOrderBiz;

    @Resource
    private CfUserCaseRefundStatService cfUserCaseRefundStatService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CrowdfundingOrder> mqMessage) {
        printReceiveMqMessageLog("variable convergence donate mq", mqMessage);

        CrowdfundingOrder order = mqMessage.getPayload();

        if(null == order){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        long userId = order.getUserId();
        int caseId = order.getCrowdfundingId();

        CfUserBaseStatDO userBaseStatDO = cfUserBaseStatService.selectByUser(userId);
        CfUserCaseBaseStatDO userCaseBaseStatDO = cfUserCaseBaseStatService.selectByUserAndCase(userId, caseId);

        //如果数据库没记录，则从大数据获得基础数据并入库
        cfUserCaseStatService.createUserCaseStatInfo(userId, caseId, userBaseStatDO, userCaseBaseStatDO);

        //数据库有数据则自增
        if(null != userBaseStatDO && null != userCaseBaseStatDO){
            int donateCaseIncr = 0;
            int donateAmountIncr = order.getAmount();
            int currentCaseAmountIncr = order.getAmount();

            List<CrowdfundingOrder> orders = crowdfundingOrderBiz.getListByUserId(caseId, userId, 2);
            if(CollectionUtils.isNotEmpty(orders) && 1 == orders.size()){
                donateCaseIncr++;
            }

            //更新用户的总捐款案例数，总捐款次数，总捐款金额
            cfUserBaseStatService.updateDonateStatInfo(userId, donateCaseIncr, donateAmountIncr);

            //更新用户对当前案例的捐款金额
            cfUserCaseBaseStatService.currentCaseDonateAmountIncr( userId, caseId, currentCaseAmountIncr);
        }

        //更新转发该案例导致该笔捐款的用户【转发当前案例带来的总捐款金额】
        int shareCurrentCaseBroughtAmount = order.getAmount();
        CfInfoShareRecord cfInfoShareRecord = cfInfoShareRecordBiz.findByUuidAndInfoId(order.getFrom(), caseId);
        long shareUserId = null != cfInfoShareRecord ? cfInfoShareRecord.getUserId() : 0;
        CfUserCaseBaseStatDO otherUserCaseBaseStatDO = cfUserCaseBaseStatService.selectByUserAndCase(shareUserId, caseId);
        if(null != cfInfoShareRecord && cfInfoShareRecord.getUserId() > 0 && cfInfoShareRecord.getUserId() != userId && null != otherUserCaseBaseStatDO){
            cfUserCaseBaseStatService.updateShareBroughtAmount(cfInfoShareRecord.getUserId(), caseId, shareCurrentCaseBroughtAmount);
        }

        //判断该次捐款是哪位用户转发带来的捐款，如果是转发带来的第一笔捐款则更新【用户转发当前案例带来的第一笔捐款金额】
        List<CfSharePromoteOrder> promoteOrders = cfSharePromoteOrderBiz.findByUserAndCase(shareUserId, caseId);
        if(CollectionUtils.isNotEmpty(promoteOrders)
                && promoteOrders.get(0).getOrderId() == order.getId()
                && order.getUserId() != shareUserId
                && !order.isAnonymous()
                && null != otherUserCaseBaseStatDO){

            cfUserCaseBaseStatService.updateShareFirstDonateAmount(shareUserId, caseId, order.getAmount());
        }

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
