package com.shuidihuzhu.cf.mq.consumers.activity;

import com.shuidihuzhu.cf.activity.constants.ActivityMQConstants;
import com.shuidihuzhu.cf.activity.mq.ActivityCaseBasicPayload;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.mq.common.BaseMessageConsumer;
import com.shuidihuzhu.cf.service.activity.subsidy.ActivityKeyAreaService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RocketMQListener(id = ActivityMQConstants.REACH_THRESHOLD_V2_TAG,
        tags = ActivityMQConstants.REACH_THRESHOLD_V2_TAG,
        group = "cf-api" + ActivityMQConstants.REACH_THRESHOLD_V2_TAG,
        topic = MQTopicCons.CF)
public class ActivityCaseReachThresholdConsumer
        extends BaseMessageConsumer<ActivityCaseBasicPayload>
        implements MessageListener<ActivityCaseBasicPayload> {

    @Autowired
    private ActivityKeyAreaService activityKeyAreaService;

    @Override
    protected boolean handle(ConsumerMessage<ActivityCaseBasicPayload> consumerMessage) {
        ActivityCaseBasicPayload payload = consumerMessage.getPayload();
        if (payload == null) {
            log.error("ActivityCaseReachThresholdConsumer payload null");
            return true;
        }
//        return activityKeyAreaService.onReachThreshold(payload).ok();
        return true;
    }

    @Override
    protected Logger getLogger() {
        return log;
    }
}
