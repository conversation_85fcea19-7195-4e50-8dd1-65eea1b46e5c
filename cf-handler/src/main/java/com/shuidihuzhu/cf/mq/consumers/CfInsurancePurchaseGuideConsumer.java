package com.shuidihuzhu.cf.mq.consumers;

import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.delegate.WxUserEventStatusDelegate;
import com.shuidihuzhu.cf.service.msg.SdAdMsgClientService;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import com.shuidihuzhu.sdb.order.client.InsuranceOrderQueryClient;
import com.shuidihuzhu.sdb.order.model.constant.common.OrderResponse;
import com.shuidihuzhu.sdb.order.model.po.InsuranceOrderPO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 保险复购延迟消息接收者
 * <p>
 * https://wiki.shuiditech.com/pages/viewpage.action?pageId=264209733
 * <p>
 * 本类功能：接收 CfInsurancePurchaseGuideForwardConsumer 转发的延时MQ，检查是否满足消息发送条件。如果满足，则向广告系统发送消息。
 *
 * <AUTHOR>
 * @date 2019/07/31
 */
@Service("cfInsurancePurchaseGuideConsumer")
@RocketMQListener(id = MQTagCons.CF_INSURANCE_PURCHASE_GUIDE_MSG,
        tags = MQTagCons.CF_INSURANCE_PURCHASE_GUIDE_MSG,
        group = "cf-" + MQTagCons.CF_INSURANCE_PURCHASE_GUIDE_MSG + "-group",
        topic = MQTopicCons.CF)
@Slf4j
public class CfInsurancePurchaseGuideConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<Long> {

    @Resource
    private InsuranceOrderQueryClient queryClient;

    @Resource
    private WxUserEventStatusDelegate wxUserEventStatusDelegate;

    @Resource(name = "sdAdMsgClientService")
    private SdAdMsgClientService adMsgService;

    @Resource
    private UserInfoDelegate userInfoDelegate;

    @Resource(name = "cfRedissonHandler")
    private RedissonHandler redissonHandler;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<Long> mqMessage) {
        this.printReceiveMqMessageLog(mqMessage);
        // 校验 payload
        if (mqMessage == null || mqMessage.getPayload() == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        long userId = mqMessage.getPayload();
        if (!this.checkAvailability(userId)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        // 给广告系统发消息
        this.send(userId);
        // 记录发送标记
        record(userId);
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private boolean checkAvailability(long userId) {
        // 1. 检查该用户是否仅有一个有效保单
        // status [3,4] 为 有效保单的状态 (待生效和保障中), pageNo 传 1
        OrderResponse<List<InsuranceOrderPO>> response = queryClient.queryByUserIdAndStatus(
                userId, Lists.newArrayList(3, 4), 1, 2);
        if (response == null || response.getCode() > 0) {
            log.info("保险复购延迟消息接收者 校验保单有效性失败，接口错误。userId={}, errMsg={}",
                    userId, response == null ? null : response.getMsg());
            return false;
        }
        if (CollectionUtils.isEmpty(response.getData()) || response.getData().size() > 1) {
            log.info("保险复购延迟消息接收者 没有或有多个有效保险订单。userId={}", userId);
            return false;
        }
        // 2. 检查该用户是否未关注保险公众号 false 未关注任何号
        boolean hasSubscribed = wxUserEventStatusDelegate.checkHasSubscribedByUserId(userId, Lists.newArrayList(7));
        if (hasSubscribed) {
            log.info("保险复购延迟消息接收者 该用户已关注保险公众号。 userId={}", userId);
            return false;
        }
        return true;
    }

    private void send(long userId) {
        UserInfoModel userInfoModel = userInfoDelegate.getUserInfoByUserId(userId);
        String nickname = "";
        if (userInfoModel != null) {
            nickname = userInfoModel.getNickname();
        }
        adMsgService.sendAdMsg("posad15643848188231352", userId, "", 0, "1", nickname);
    }

    private void record(long userId) {
        String key = "InsurancePurchaseGuide_" + userId + "_" + DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.now());
        redissonHandler.setEX(key, true, TimeUnit.DAYS.toMillis(1));
    }
}
