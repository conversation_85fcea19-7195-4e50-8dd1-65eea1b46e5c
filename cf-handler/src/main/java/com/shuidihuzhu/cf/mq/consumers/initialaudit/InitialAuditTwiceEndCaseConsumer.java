package com.shuidihuzhu.cf.mq.consumers.initialaudit;


import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.msg.initialaudit.InitialAuditMsgService;
import com.shuidihuzhu.client.cf.admin.model.InitialAuditTwiceEndCaseInfo;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RocketMQListener(id = CfClientMQTagCons.INITIAL_AUDIT_TWICE_END_CASE_MSG + "-cf-api",
        tags = CfClientMQTagCons.INITIAL_AUDIT_TWICE_END_CASE_MSG,
        group = "cf-api" + CfClientMQTagCons.INITIAL_AUDIT_TWICE_END_CASE_MSG,
        topic = MQTopicCons.CF)
public class InitialAuditTwiceEndCaseConsumer implements MessageListener<InitialAuditTwiceEndCaseInfo> {

    @Autowired
    private InitialAuditMsgService initialAuditMsgService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<InitialAuditTwiceEndCaseInfo> mqMessage) {

        log.info("初审收到二次结束案例的消息.msg:{}", mqMessage.getPayload());

        initialAuditMsgService.sendOnApprove(mqMessage.getPayload().getCaseId());

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
