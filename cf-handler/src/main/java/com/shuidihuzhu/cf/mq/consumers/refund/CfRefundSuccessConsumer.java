package com.shuidihuzhu.cf.mq.consumers.refund;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.dao.crowdfunding.CfFundraiserManagementDao;
import com.shuidihuzhu.cf.enhancer.mq.MQHelperService;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingConstant;
import com.shuidihuzhu.cf.finance.model.CfOrderRefundSuccessModel;
import com.shuidihuzhu.cf.finance.mq.FinanceMQTagCons;
import com.shuidihuzhu.cf.model.CaseEndModel;
import com.shuidihuzhu.cf.model.crowdfunding.CfFundraiserManagement;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoShareRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.mq.consumers.BaseConsumerPrintReceiveLog;
import com.shuidihuzhu.cf.mq.producer.CommonMessageHelperService;
import com.shuidihuzhu.cf.mq.producer.MessageBuilder;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.data.analytics.javasdk.core.Analytics;
import com.shuidihuzhu.data.analytics.javasdk.core.UserTagTypeEnum;
import com.shuidihuzhu.data.servicelog.meta.cf.CaseOrderRefund;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Date;
import java.util.Optional;

/**
 * @author: fengxuan
 * @create 2019-10-30 12:07
 **/
@Slf4j
@Service
@RefreshScope
@RocketMQListener(id = "CfRefundSuccessConsumer",
        tags = FinanceMQTagCons.CF_ORDER_REFUND_SUCCESS,
        topic = MQTopicCons.CF,
        group = "Cf-ConsumerGroup_" + FinanceMQTagCons.CF_ORDER_REFUND_SUCCESS)
public class CfRefundSuccessConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<CfOrderRefundSuccessModel> {
    @Autowired
    private Analytics analytics;
    @Autowired
    private CfSharePromoteOrderBiz cfSharePromoteOrderBiz;
    @Resource(name = "cfRedissonHandler")
    private RedissonHandler cfRedissonHandler;
    @Autowired
    CrowdfundingOrderBiz crowdfundingOrderBiz;
    @Autowired
    private CfInfoShareRecordBiz cfInfoShareRecordBiz;

    @Autowired
    private MQHelperService mqHelperService;

    @Autowired
    private CfFundraiserManagementBiz cfFundraiserManagementBiz;

    /**
     * 发送时机，整单退款后发送
     *
     * @param mqMessage
     * @return
     */
    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CfOrderRefundSuccessModel> mqMessage) {
        printReceiveMqMessageLog("refund success msg", mqMessage);
        CfOrderRefundSuccessModel refundSuccessModel = mqMessage.getPayload();
        if (refundSuccessModel == null) {
            log.warn("msg is null");
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        long orderId = refundSuccessModel.getOrderId();
        //查找这个退款的转发来源
        CrowdfundingOrder crowdfundingOrder = crowdfundingOrderBiz.getById(orderId);
        if (crowdfundingOrder == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        //需要确保幂等
        String tryLock = null;
        String lockKey = "CfRefundSuccessConsumer_" + orderId;
        try {
            tryLock = cfRedissonHandler.tryLock(lockKey, 0, 60 * 60 * 1000L);
            //未获取到锁
            if (StringUtils.isBlank(tryLock)) {
                log.warn("repeat refund success msg orderId:{}", orderId);
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            // 发送活动用订单退款消息
            mqHelperService.builder()
                    .setPayload(refundSuccessModel)
                    .addKey(MQTagCons.CF_ACTIVITY_REFUND_SUCCESS, refundSuccessModel.getOrderId())
                    .setTags(MQTagCons.CF_ACTIVITY_REFUND_SUCCESS)
                    .send();

            //修改cf_share_promote_order状态
            this.changePromoteRefundStatus(orderId, crowdfundingOrder.getCrowdfundingId());

            //修改筹款人管理当天筹款金额(那天捐的款，退款就退到那天的筹款金额上)
            cfFundraiserManagementBiz.changeFundraiserManagementMoney(crowdfundingOrder,refundSuccessModel);

            String from = crowdfundingOrder.getFrom();
            CfInfoShareRecord cfInfoShareRecord = cfInfoShareRecordBiz.findByUuidAndInfoId(from, refundSuccessModel.getCaseId());
            log.info("cfInfoShareRecord:{}", JSON.toJSONString(cfInfoShareRecord));
            Long sourceUserId = Optional.ofNullable(cfInfoShareRecord).map(CfInfoShareRecord::getUserId).orElse(0L);

            //查找这个退款是不是匿名捐的
            int anonymous = 0;
            anonymous = crowdfundingOrder.isAnonymous()  ? 1 : 0;

            //埋点上报
            // https://wiki.shuiditech.com/pages/viewpage.action?pageId=380862503
            CaseOrderRefund cor = new CaseOrderRefund();
            try {
                cor.setIs_anonymous(String.valueOf(anonymous));
                cor.setOrder_id(orderId);
                cor.setShare_user_id(sourceUserId);
                cor.setDonate_amt((long) refundSuccessModel.getRefundAmountInFen());
                cor.setPay_status(1L);
                cor.setRefund_time(Optional.ofNullable(refundSuccessModel.getRefundTime()).orElse(new Date()).getTime());
                cor.setIp("");
                cor.setInfo_id((long) refundSuccessModel.getCaseId());
                cor.setCase_id(refundSuccessModel.getInfoUuid());
                cor.setUser_tag(String.valueOf(refundSuccessModel.getUserId()));
                cor.setUser_tag_type(UserTagTypeEnum.userid);

                analytics.track(cor);
                log.info("大数据打点上报，退款:{}", JSONObject.toJSONString(cor));
            } catch (Exception e) {
                log.error("大数据打点上报异常，退款:{}", JSONObject.toJSONString(cor), e);
            }

            return ConsumeStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            log.error("refund success consume error, msg:{}", mqMessage, e);
            try {
                if (StringUtils.isNotBlank(tryLock)) {
                    cfRedissonHandler.unLock(lockKey, tryLock);
                }
            } catch (Exception ex) {
                log.error("refund consumer unlock error, orderId:{}", orderId, ex);
            }
            return ConsumeStatus.RECONSUME_LATER;
        }
    }

    private void changePromoteRefundStatus(long orderId, int caseId) {
        cfSharePromoteOrderBiz.refundOrder(orderId, caseId);
    }
}
