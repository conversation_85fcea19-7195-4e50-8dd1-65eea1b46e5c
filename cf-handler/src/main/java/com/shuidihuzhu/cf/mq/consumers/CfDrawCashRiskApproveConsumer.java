package com.shuidihuzhu.cf.mq.consumers;

import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.domain.CfCaseRiskDO;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.DrawCashRiskApprovePushService;
import com.shuidihuzhu.cf.service.DrawCashRiskVo;
import com.shuidihuzhu.client.cf.risk.model.DrawCashRiskMsgModel;
import com.shuidihuzhu.client.cf.risk.model.enums.DrawCashRiskApproveSourceEnum;
import com.shuidihuzhu.client.constant.MQTagCons;
import com.shuidihuzhu.client.constant.MQTopicCons;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @time 2019/3/19 下午7:37
 * @desc
 */
@Service
@Slf4j
@RocketMQListener(id = MQTagCons.CF_RISK_DRAW_CASH_APPROVE_MSG,
        tags = MQTagCons.CF_RISK_DRAW_CASH_APPROVE_MSG,
        topic = MQTopicCons.CF,
        group = "DRAW_CASH_APPROVE_" + MQTagCons.CF_RISK_DRAW_CASH_APPROVE_MSG)
public class CfDrawCashRiskApproveConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<DrawCashRiskMsgModel> {

    @Autowired
    private DrawCashRiskApprovePushService drawCashRiskApprovePushService;

    @Resource
    private CrowdfundingInfoBiz crowdfundingInfoBiz;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<DrawCashRiskMsgModel> mqMessage) {

        printReceiveMqMessageLog(mqMessage);

        DrawCashRiskMsgModel model = mqMessage.getPayload();

        if(null == model || StringUtils.isEmpty(model.getInfoUuid()) || null == model.getSourceEnum()){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        String infoUuid = model.getInfoUuid();
        CfCaseRiskDO cfCaseRiskDO = DrawCashRiskVo.convert(model.getDrawCashDO());
        DrawCashRiskApproveSourceEnum approveSourceEnum = model.getSourceEnum();
        CrowdfundingInfo info = crowdfundingInfoBiz.getFundingInfo(infoUuid);

        if(approveSourceEnum == DrawCashRiskApproveSourceEnum.NEW_DRAW_CASH_RISK_APPROVE || approveSourceEnum == DrawCashRiskApproveSourceEnum.MANUAL_APPROVE){
            drawCashRiskApprovePushService.onInfoRiskPassed(cfCaseRiskDO);
        }

        if(approveSourceEnum == DrawCashRiskApproveSourceEnum.OLD_MATERIAL_APPROVE){
            drawCashRiskApprovePushService.sendInfoPassedNotice(infoUuid, null);
        }


        if(approveSourceEnum == DrawCashRiskApproveSourceEnum.NEW_NOT_VALIDATE_RISK){
            drawCashRiskApprovePushService.sendInfoPassedNotice(infoUuid, info);
        }

        if(approveSourceEnum == DrawCashRiskApproveSourceEnum.NEW_VALIDATE_RISK){
            log.info("CF_RISK_DRAW_CASH_APPROVE_MSG touchSendNotice");
            drawCashRiskApprovePushService.touchSendNotice(cfCaseRiskDO, info);
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
