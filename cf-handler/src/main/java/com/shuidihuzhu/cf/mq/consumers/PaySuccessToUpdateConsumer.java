package com.shuidihuzhu.cf.mq.consumers;

import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoSimpleBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSimpleModel;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.service.crowdfunding.CfCaseDonatorService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RocketMQListener(id = "PAY_SUCCESS_TO_UPDATE",
        group = "cf-PaySuccessToUpdateConsumer_PAY_SUCCESS_TO_UPDATE",
        tags = MQTagCons.PAY_SUCCESS_TO_UPDATE,
        topic = MQTopicCons.CF)
public class PaySuccessToUpdateConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<CrowdfundingOrder> {

    @Autowired
    private CrowdfundingInfoSimpleBiz crowdfundingInfoSimpleBiz;

    @Resource
    private CfCaseDonatorService cfCaseDonatorService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CrowdfundingOrder> mqMessage) {

        CrowdfundingOrder order = mqMessage.getPayload();
        if(order == null){
            log.info("CrowdfundingOrder is null");
            return ConsumeStatus.RECONSUME_LATER;
        }
        log.info("PaySuccessToUpdateConsumer payload = {}", order);
        CfInfoSimpleModel cfInfoSimpleModel = crowdfundingInfoSimpleBiz.getFundingInfoById(order.getCrowdfundingId());
        if(cfInfoSimpleModel == null){
            log.info("cfInfoSimpleModel is null, caseId = {}", order.getCrowdfundingId());
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        log.info("PaySuccessToUpdateConsumer RECONSUME_LATER! payload = {}", order);
        if(!cfCaseDonatorService.handleDonatorCountOnPaySuccess(cfInfoSimpleModel, order)){
            return ConsumeStatus.RECONSUME_LATER;
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
