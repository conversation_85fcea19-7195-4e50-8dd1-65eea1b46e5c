package com.shuidihuzhu.cf.mq.consumers;


import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.event.CfSatDateEvent;
import com.shuidihuzhu.cf.model.crowdfunding.message.CfRefundRecord;
import com.shuidihuzhu.cf.service.EventPublishService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
@RocketMQListener(id = MQTagCons.CF_REFUND_SUCCESS_MSG,
        group = "cf-refund-group",
        tags = MQTagCons.CF_REFUND_SUCCESS_MSG,
        topic = MQTopicCons.CF)
@Slf4j
public class CfRefundRecordConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<CfRefundRecord> {


    @Autowired
    private EventPublishService publishService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CfRefundRecord> consumerMessage) {
        printReceiveMqMessageLog(consumerMessage);

        try {

            CfRefundRecord message = consumerMessage.getPayload();

            if (message != null){
                log.info("CfPayRecordConsumer Record case_id={}",message.getCaseId());
                publishService.publish(new CfSatDateEvent(this,message));
                return ConsumeStatus.CONSUME_SUCCESS;
            }

        }catch (Exception e){
            log.error("CfRefundRecordConsumer error consumerMessage={}",consumerMessage,e);
        }

        return ConsumeStatus.RECONSUME_LATER;
    }
}
