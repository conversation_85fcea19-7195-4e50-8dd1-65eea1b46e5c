package com.shuidihuzhu.cf.mq.consumers;

import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.wx.CfWxMpCommonBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.service.msg.SdAdMsgClientService;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import com.shuidihuzhu.wx.grpc.model.WxMpSubscribeModel;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RefreshScope
@RocketMQListener(id = MQTagCons.DOUBLE_DONATION_CASHING_NEWS_1h_LATER,
        topic = MQTopicCons.CF,
        tags = MQTagCons.DOUBLE_DONATION_CASHING_NEWS_1h_LATER,
        group = "Cf-ConsumerGroup_DOUBLE_DONATION_CASHING_NEWS_1h_LATER")
public class CfDoubleDonationCashingNewsOneHourConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<CrowdfundingOrder> {

    @Resource(name = "cfRedissonHandler")
    private RedissonHandler cfRedissonHandler;

    @Autowired
    private CfWxMpCommonBiz cfWxMpCommonBiz;

    @Resource
    private UserInfoDelegate userInfoDelegate;

    @Resource(name = "sdAdMsgClientService")
    private SdAdMsgClientService adMsgService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CrowdfundingOrder> mqMessage) {
        printReceiveMqMessageLog(mqMessage);
        String key = mqMessage.getKeys();
        String redisKey = MQTagCons.DOUBLE_DONATION_CASHING_NEWS_1h_LATER + "_" + key;
        if (cfRedissonHandler.exists(redisKey)) {
            log.info("CfDoubleDonationCashingNewsOneHourConsumer repeat msg:{}", mqMessage);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        String tryLock = null;
        try {
            tryLock = cfRedissonHandler.tryLock(key, 0, 60 * 1000L);
        } catch (Exception e) {
            log.error("", e);
        }

        if (StringUtils.isBlank(tryLock)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        try {
            CrowdfundingOrder order = mqMessage.getPayload();
            boolean x = handle(order);
            if (x) {
                return ConsumeStatus.CONSUME_SUCCESS;
            } else {
                return ConsumeStatus.RECONSUME_LATER;
            }
        } catch (Exception e) {
            return ConsumeStatus.RECONSUME_LATER;
        } finally {
            try {
                if (StringUtils.isNotBlank(tryLock)) {
                    cfRedissonHandler.unLock(key, tryLock);
                }
            } catch (Exception e) {
                log.info("", e);
            }
        }
    }

    private boolean handle(CrowdfundingOrder successOrder) {
        List<Integer> userThirds = Lists.newArrayList();
        userThirds.add(3);
        userThirds.add(17);
        userThirds.add(54);
        userThirds.add(61);
        userThirds.add(92);
        userThirds.add(115);
        userThirds.add(116);
        userThirds.add(454);
        userThirds.add(136);
        userThirds.add(518);

        long userId = successOrder.getUserId();
        List<WxMpSubscribeModel> wxMpSubscribeModels = cfWxMpCommonBiz.listSubscribeByUserId(userId, userThirds);
        log.info("CfDoubleDonationCashingNewsOneHourConsumer successOrder:{} wxMpSubscribeModels:{}",successOrder,wxMpSubscribeModels);
        //如果用户没有关注过这几个公众号，发消息
        if (CollectionUtils.isEmpty(wxMpSubscribeModels)) {
            sendMsg(userId);
        } else {
            wxMpSubscribeModels = wxMpSubscribeModels.stream().filter(v ->
                    DateUtil.formatDate(v.getSubscribeTime()).equals(LocalDate.now().toString())).collect(Collectors.toList());
            //如果用户今天截止到当前时间没有关注过这几个公众号，发消息
            if (CollectionUtils.isEmpty(wxMpSubscribeModels)) {
                sendMsg(userId);
            }
        }
        return true;
    }

    private void sendMsg(long userId) {
        UserInfoModel userInfoModel = userInfoDelegate.getUserInfoByUserId(userId);
        String nickname = "小水滴";
        if (userInfoModel != null && !StringUtil.isEmpty(userInfoModel.getNickname())) {
            nickname = userInfoModel.getNickname();
        }
        adMsgService.sendAdMsg("posad15918423266206579", userId, "", 0, "1", nickname);
    }
}
