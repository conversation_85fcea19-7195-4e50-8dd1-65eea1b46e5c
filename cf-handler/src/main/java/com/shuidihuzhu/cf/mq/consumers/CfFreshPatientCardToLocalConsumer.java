package com.shuidihuzhu.cf.mq.consumers;

import com.shuidihuzhu.cf.biz.crowdfunding.material.CfPatientCardWarmService;
import com.shuidihuzhu.cf.biz.crowdfunding.material.IMaterialReadService;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.mq.common.CommonConsumerHelper;
import com.shuidihuzhu.cf.mq.model.CfPatientChangeModel;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RocketMQListener(id = MQTagCons.CF_PATIENT_CHANGE_INFO_BROADCASTING,
        group = "cf-api-" + MQTagCons.CF_PATIENT_CHANGE_INFO_BROADCASTING + "-group",
        tags = MQTagCons.CF_PATIENT_CHANGE_INFO_BROADCASTING,
        topic = MQTopicCons.CF)
public class CfFreshPatientCardToLocalConsumer implements MessageListener<String> {

    @Autowired
    private IMaterialReadService materialReadService;

    @Autowired
    private CfPatientCardWarmService patientWarmService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<String> mqMessage) {

        log.info("收到病人的身份证数据. String:{}", mqMessage.getPayload());

        if (StringUtils.isBlank(mqMessage.getPayload())) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        log.info("刷新本地缓存患者身份证:{}", mqMessage.getPayload());
        patientWarmService.freshPatientIdCardToLocal(mqMessage.getPayload());
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
