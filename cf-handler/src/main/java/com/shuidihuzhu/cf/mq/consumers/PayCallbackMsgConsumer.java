package com.shuidihuzhu.cf.mq.consumers;

import com.google.common.collect.ImmutableMap;
import com.shuidihuzhu.cf.biz.combine.CfCombineOrderManageBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.biz.pay.service.PayCallbackService;
import com.shuidihuzhu.cf.clinet.event.center.enums.BizParamEnum;
import com.shuidihuzhu.cf.clinet.event.center.enums.UserOperationTypeEnum;
import com.shuidihuzhu.cf.clinet.event.center.model.CfUserEvent;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enums.crowdfunding.CfFinishStatus;
import com.shuidihuzhu.cf.enums.crowdfunding.HonestPersonChangeEnum;
import com.shuidihuzhu.cf.model.contribute.CfCombineOrderManage;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSimpleModel;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.model.crowdfunding.deliver.CrowdfundingOrderDeliver;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.cf.service.crowdfunding.CrowdfundingFinishService;
import com.shuidihuzhu.cf.service.crowdfunding.casematerial.CfMaterialStatusService;
import com.shuidihuzhu.cf.service.event.EventCenterService;
import com.shuidihuzhu.cf.service.hostcase.HotCaseService;
import com.shuidihuzhu.client.cf.api.model.DishonestPayload;
import com.shuidihuzhu.client.cf.api.model.MaterialVersion;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.data.analytics.javasdk.core.Analytics;
import com.shuidihuzhu.data.analytics.javasdk.core.UserTagTypeEnum;
import com.shuidihuzhu.data.servicelog.meta.cf.UgcOrder;
import com.shuidihuzhu.infra.neptune.common.utils.TimeUtils;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 *
 * 统一支付回调mq发送
 * <AUTHOR>
 * @DATE 2019/3/6
 */

@Service
@RocketMQListener(id = "PAY_CALLBACK_MSG",
        group = "CF_PAY_ONLY_SUCCESS_MSG_GROUP",
        tags = MQTagCons.CF_PAY_ONLY_SUCCESS_MSG,
        topic = MQTopicCons.CF)
@Slf4j
public class PayCallbackMsgConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<CrowdfundingOrder> {

    @Autowired(required = false)
    private Producer producer;

    @Autowired
    private PayCallbackService payCallbackService;

    @Autowired
    private CrowdfundingFinishService crowdfundingFinishService;

    @Autowired
    private CrowdfundingInfoSimpleBiz crowdfundingInfoSimpleBiz;

    @Resource(name = "cfRedissonHandler")
    private RedissonHandler cfRedissonHandler;

    @Resource(name = "cf2RedissonHandler")
    private RedissonHandler cf2RedissonHandler;

    @Autowired
    private EventCenterService eventCenterService;

    @Autowired
    private CfFundraiserManagementBiz cfFundraiserManagementBiz;

    @Autowired
    private CfNoHandlingFeeBiz cfNoHandlingFeeBiz;

    @Autowired
    private ApplicationService applicationService;

    @Autowired
    private CfMaterialStatusService materialStatusService;

    @Resource
    private HotCaseService hotCaseService;

    @Autowired
    private Analytics analytics;

    @Autowired
    private CrowdfundingOrderBiz crowdfundingOrderBiz;

    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private CfCombineOrderManageBiz cfCombineOrderManageBiz;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CrowdfundingOrder> mqMessage) {

        printReceiveMqMessageLog(mqMessage);

        CrowdfundingOrder successOrder = mqMessage.getPayload();

        if (successOrder == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        /** 防止捐款消息重复发送多次 */
        if(mqMessage.getPayload().getConsumeStatus() != 0){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        String key = "cf_pay_callback_msg_" + successOrder.getPayUid() + "_" + successOrder.getId();
        String tryLock = null;
        try {
            tryLock = cfRedissonHandler.tryLock(key, 0, TimeUnit.MINUTES.toMillis(1));
        } catch (Exception e) {
            log.error("PayCallbackMsgConsumer tryLock error. key:{}", key, e);
        }

        if (StringUtils.isBlank(tryLock)) {
            log.info("PayCallbackMsgConsumer tryLock is null. key:{}", key);
            return ConsumeStatus.RECONSUME_LATER;
        }


        try {
            return doConsumeMessage(successOrder);
        } catch (Exception e) {
            log.error("PayCallbackMsgConsumer doConsumeMessage error. crowdfundingOrder:{}", successOrder, e);
        } finally {
            try {
                if (StringUtils.isNotBlank(tryLock)) {
                    cfRedissonHandler.unLock(key, tryLock);
                }
            } catch (Exception e) {
                log.info("PayCallbackMsgConsumer unLock error. key:{}, tryLock:{}", key, tryLock, e);
            }
        }

        return ConsumeStatus.RECONSUME_LATER;
    }

    private ConsumeStatus doConsumeMessage(CrowdfundingOrder successOrder) {
        long orderId = successOrder.getId();

        //增加redis  防止消息重复消费`
        if(cf2RedissonHandler.exists(getKey(orderId))){
            log.info("PayCallbackMsgConsumer repeat orderId={}",orderId);
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        //自增当日订单量
        payCallbackService.setOrderNum();

        //筹款事件入库
        payCallbackService.addCrowdfundingInfoEvent(successOrder);

//        //捐款成功后记录 https://wdh.feishu.cn/wiki/wikcn2Su6k8ihaBdDVtx8Mbe7E4 需求需要的统计数据
//        statisticalDataOfFellowVillagers(successOrder);

        try {
            //记录每日捐款金额
            cfFundraiserManagementBiz.fundraisingAmount(successOrder.getInfoId(),successOrder.getAmount());
        }catch (Exception e){
            log.error("每次捐款金额 infoUuid={} amount={}",successOrder.getInfoId(),successOrder.getAmount(),e);
        }

        try {
            cfNoHandlingFeeBiz.getHotCaseFeeFree(successOrder.getInfoId());
        }catch (Exception e){
            log.error("是否免手续费 infoUuid={} ",successOrder.getInfoId(),e);
        }

        try {
            // 捐款成功发送消息 ----》新需求不要在这个consumer里加,这里发送的874消息有重新调用mq机制,有可能多次触发此consumer
            track(successOrder);

            CrowdfundingOrderDeliver crowdfundingOrderDeliver = new CrowdfundingOrderDeliver();
            BeanUtils.copyProperties(successOrder, crowdfundingOrderDeliver);
            crowdfundingOrderDeliver.setUniquelyIdentifies("order");

            producer.send(new Message(MQTopicCons.CF, MQTagCons.CF_DONATION_SUCCESS_MSG,
                    MQTagCons.CF_DONATION_SUCCESS_MSG + "_" + orderId, crowdfundingOrderDeliver));
        } catch (Exception e) {
            log.error("CF_DONATION_SUCCESS_MSG addPay Error! orderId={}", orderId, e);
        }
        try {
            // 捐款成功其他业务处理
            producer.send(new Message(MQTopicCons.CF, MQTagCons.CF_DONATION_SUCCESS_BIZ,
                    MQTagCons.CF_DONATION_SUCCESS_BIZ + "_" + orderId, successOrder));
        } catch (Exception e) {
            log.error("CF_DONATION_SUCCESS_BIZ error orderId={}",orderId, e);
        }

        try {
            // 单独捐款成功发送消息
            producer.send(new Message(MQTopicCons.CF, MQTagCons.CF_PAY_SUCCESS_MSG,
                    MQTagCons.CF_PAY_SUCCESS_MSG + "_" + successOrder.getId(), successOrder));
        } catch (Exception e) {
            log.error("CF_PAY_SUCCESS_MSG error orderId={}",orderId, e);
        }

        try {
            producer.send(new Message(MQTopicCons.CF, MQTagCons.CF_DONATE_SUCCESS_DELAY_MSG,
                    MQTagCons.CF_DONATE_SUCCESS_DELAY_MSG + "_" + orderId, successOrder, DelayLevel.S30));
        } catch (Exception e) {
            log.error("send Pay succ delay msg Error! orderId={}",orderId, e);
        }

        try{
            // 自动给捐款人回复感谢时间调整 -> [15 ,30]分钟内自动回复
            // 随机数公式 [min,max] -> random.nextInt(max - min +1) + min
            int randomInt = new Random().nextInt(16) + 15;

            // 分钟 -> 秒
            int seconds = randomInt * 60;
            long scheduleTimeStamp = TimeUtils.unixTimeStamp() + seconds;
            Message message = Message.ofSchedule(MQTopicCons.CF,
                    MQTagCons.CF_DONATE_AUTO_REPLY_MSG,
                    MQTagCons.CF_DONATE_AUTO_REPLY_MSG + "_" + orderId,
                    successOrder,
                    scheduleTimeStamp);
            producer.send(message);

        }catch (Exception e){
            log.error("CF_DONATE_AUTO_REPLY_MSG error orderId:{}",orderId,e);
        }

        try {
            producer.send(new Message<>(MQTopicCons.CF, MQTagCons.CF_DONATION_SUCCESS_SUBSIDY,
                    MQTagCons.CF_DONATION_SUCCESS_SUBSIDY + "_" + successOrder.getId(), successOrder));
        } catch (Exception e) {
            log.error("send subsidy pay success msg fail", e);
        }

        try {
            doubleDonationCashingNews(successOrder);
        } catch (Exception e) {
            log.error("send subsidy pay success msg fail", e);
        }

        try {
            CfInfoSimpleModel simpleModel =crowdfundingInfoSimpleBiz.getFundingInfo(successOrder.getInfoId());
            crowdfundingFinishService.addTag(simpleModel.getUserId(),simpleModel.getInfoId(), CfFinishStatus.FUNDING_REACH_TARGET);
            // 爱心首页合并支付不发送此消息
            CfCombineOrderManage combineOrderManage = cfCombineOrderManageBiz.getByOrderId(successOrder.getId());
            eventCenterService.sendEventCenter(successOrder, UserOperationTypeEnum.CASE_ORDER.getCode(), simpleModel, combineOrderManage);
        } catch (Exception e) {
            log.error("addTag Error! orderId={}",orderId, e);
        }

        try {
            DishonestPayload dishonestPayload = new DishonestPayload();
            dishonestPayload.setInfoUuid(successOrder.getInfoId());
            dishonestPayload.setChangeType(HonestPersonChangeEnum.AMOUNT.getKey());

            producer.send(new Message<>(MQTopicCons.CF,
                    MQTagCons.CF_DISHONEST_VALIDATE_MQ,
                    MQTagCons.CF_DISHONEST_VALIDATE_MQ + "-" + successOrder.getInfoId() + "-" + System.currentTimeMillis(),
                    dishonestPayload,
                    DelayLevel.S1));
        } catch (Exception e){
            log.error("send dishonest validate mq Exception.", e);
        }

        try{
            // 统计每分钟案例支付成功数量
            hotCaseService.incretmentPaySuccessOrderCountEveryMinute(successOrder);
        } catch (Exception e){
            log.error("incretment paySuccessOrderCountEveryMinute fail", e);
        }



        //处理案例第一笔捐款情况
        caseFirstOrder(successOrder);

        cf2RedissonHandler.setNX(getKey(orderId),"1",RedissonHandler.ONE_DAY);

        //当筹款人收到3笔>=20元捐款
        try {
            CrowdfundingInfo fundingInfo = crowdfundingInfoBiz.getFundingInfoById(successOrder.getCrowdfundingId());
            //捐单金额>=20 且案例未结束
            if(successOrder.getAmount() >= 2000 && (fundingInfo != null && fundingInfo.getEndTime().after(new Date()))){
                //上次触发消息的orderId
                String donateOrderIdKey = "cf_donate_order_id_v1_"+successOrder.getCrowdfundingId();
                Long sendMsgOrderId = cf2RedissonHandler.get(donateOrderIdKey, Long.class);
                //历史数据兼容 默认查询最近10条
                int limit = sendMsgOrderId == null ? 10 : 0;
                sendMsgOrderId = sendMsgOrderId == null ? 0 : sendMsgOrderId;
                List<CrowdfundingOrder> orderList = crowdfundingOrderBiz.getByAmountAndCaseId(successOrder.getCrowdfundingId(),sendMsgOrderId,successOrder.getAmount(),limit);
                if(orderList.size() >= 3){
                    CrowdfundingOrder order = orderList.get(0);
                    log.info("send msg for amount > 20 orderId:{}",order.getId());
                    CfUserEvent cfUserEvent = new CfUserEvent();
                    cfUserEvent.setCaseId(successOrder.getCrowdfundingId());
                    cfUserEvent.setType(UserOperationTypeEnum.CASE_DONATE_ORDER.getCode());
                    cfUserEvent.setDataMap(ImmutableMap.of(
                            BizParamEnum.ORDER_AMOUNT.getDesc(),String.valueOf(order.getAmount()),
                            BizParamEnum.INFO_UUID.getDesc(), fundingInfo.getInfoId(),
                            BizParamEnum.ORDER_USER_ID.getDesc(), String.valueOf(order.getUserId()),
                            BizParamEnum.ORDER_ID.getDesc(),String.valueOf(order.getId())));
                    producer.send(new Message<>(MQTopicCons.CF, MQTagCons.USER_EVENT_FOR_EVENT_CENTER, MQTagCons.USER_EVENT_FOR_EVENT_CENTER + "_" + successOrder.getCrowdfundingId(), cfUserEvent));
                    cf2RedissonHandler.setEX(donateOrderIdKey,order.getId());
                }
            }
        }catch (Exception e){
            log.error("send msg for amount > 20 error",e);
        }


        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private void doubleDonationCashingNews(CrowdfundingOrder successOrder) {
        String redisKey = "cf_double_donation_cashing_news_" + DateUtil.getCurrentDateStr() + "_" + successOrder.getUserId();
        Integer value = cf2RedissonHandler.get(redisKey, Integer.class);
        if (value != null && value == 1) {
            return;
        } else {
            cf2RedissonHandler.setEX(redisKey, 1, TimeUnit.DAYS.toMillis(1));
            //半个小时
            producer.send(new Message<>(MQTopicCons.CF, MQTagCons.DOUBLE_DONATION_CASHING_NEWS_HALF_HOUR_LATER,
                    MQTagCons.DOUBLE_DONATION_CASHING_NEWS_HALF_HOUR_LATER + "_" + successOrder.getId(), successOrder, DelayLevel.M30));
            //一个小时
            producer.send(new Message<>(MQTopicCons.CF, MQTagCons.DOUBLE_DONATION_CASHING_NEWS_1h_LATER,
                    MQTagCons.DOUBLE_DONATION_CASHING_NEWS_1h_LATER + "_" + successOrder.getId(), successOrder, DelayLevel.H1));
        }
    }


    private void caseFirstOrder(CrowdfundingOrder successOrder) {
        try {
            int caseId = successOrder.getCrowdfundingId();

            String redisKey = "cf_case_first_order_" + caseId;
            if (cfRedissonHandler.exists(redisKey)) {
                return;
            }

            eventCenterService.sendEventCenter(successOrder, UserOperationTypeEnum.CASE_FIRST_ORDER.getCode(), caseId, successOrder.getInfoId());
            cfRedissonHandler.setNX(redisKey, 1, TimeUnit.DAYS.toMillis(30));
            //2763app消息
            sendAppMsg(successOrder);
        } catch (Exception e) {
            log.error("caseFirstOrder error. successOrder:{}", successOrder, e);
        }
    }
    private String getKey(long orderId){

        return "CALLBACK_CHECK_"+orderId;

    }

    private void sendAppMsg(CrowdfundingOrder successOrder) {
        MaterialVersion materialVersion = materialStatusService.getMaterialsVersion(successOrder.getInfoId());
        if (!materialVersion.isMaterial100()) {
            return;
        }
        DateTime now = new DateTime();
        long expectTime = now.plusDays(3).getMillis();
        if (!applicationService.isProduction()) {
            expectTime = now.plusDays(0).plusHours(0).plusMinutes(10).getMillis();
        }
        Message msg = Message.ofSchedule(MQTopicCons.CF, MQTagCons.MATERIAL_REVIEW_SEND_APP_MSG, MQTagCons.MATERIAL_REVIEW_SEND_APP_MSG + "_" + successOrder.getInfoId(),
                successOrder, expectTime / 1000);
        producer.send(msg);
    }

    private void track(CrowdfundingOrder successOrder) {
        UgcOrder ugcOrder = new UgcOrder();
        try {
            ugcOrder.setOrder_id(successOrder.getId());
            ugcOrder.setUniquely_identifies("order");

            ugcOrder.setUser_tag(String.valueOf(successOrder.getUserId()));
            ugcOrder.setUser_tag_type(UserTagTypeEnum.userid);
            analytics.track(ugcOrder);
        } catch (Exception e) {
            log.error("大数据打点上报异常,ugc订单过敏感词 订单id:{}", successOrder.getId());
        }
    }


//    /**
//     * 捐款成功后记录 https://wdh.feishu.cn/wiki/wikcn2Su6k8ihaBdDVtx8Mbe7E4 需求需要的统计数据
//     *
//     * @param successOrder
//     */
//    private void statisticalDataOfFellowVillagers(CrowdfundingOrder successOrder) {
//        try {
//
//            //渠道入口 > ip入口
//
//            //渠道入口
//            boolean channelEntryFlag = cfDonateFellowRecordBiz.channelEntry(successOrder);
//            //数据已经从渠道入口保存，不需要再次保存
//            if (channelEntryFlag) {
//                return;
//            }
//
//            //ip入口
//            cfDonateFellowRecordBiz.ipEntry(successOrder);
//        } catch (Exception e) {
//            log.error("捐款成功后记录异常 订单id:{}", successOrder.getId());
//        }
//    }

}
