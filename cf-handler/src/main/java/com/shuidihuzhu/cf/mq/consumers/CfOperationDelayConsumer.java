package com.shuidihuzhu.cf.mq.consumers;

import com.google.common.base.Joiner;
import com.shuidihuzhu.cf.biz.crowdfunding.CfInfoExtBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoDataStatusTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CfOperatingRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingIdCase;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.cf.service.crowdfunding.CrowdfundingIdCaseService;
import com.shuidihuzhu.cf.util.crowdfunding.CrowdfundingUtil;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created by sven on 18/8/2.
 *
 * <AUTHOR>
 */
@Service
@RocketMQListener(id = MQTagCons.CF_OPERATION_DELAY_10M_MSG,
        group = "cf-"+MQTagCons.CF_OPERATION_DELAY_10M_MSG+"-group",
        tags = MQTagCons.CF_OPERATION_DELAY_10M_MSG,
        topic = MQTopicCons.CF)
@Slf4j
public class CfOperationDelayConsumer extends  BaseConsumerPrintReceiveLog implements MessageListener<CfOperatingRecord> {

    @Resource
    private CrowdfundingInfoBiz crowdfundingInfoBiz;

    @Resource
    private CfInfoExtBiz cfInfoExtBiz;

    @Resource
    private CrowdfundingIdCaseService crowdfundingIdCaseService;

    @Autowired(required = false)
    private Producer producer;

    @Autowired
    private ApplicationService applicationService;

    /**
     * 主要逻辑：
     *
     * 收到了以后
     * 1，用户是否需要填写身份证
     * 2，如果需要，检查一下用户是否填写
     * 3，如果没有填写，下发微信通知
     * 4，发送明日18:00的延迟队列到MQTagCons.CF_OPERATION_DELAY_10M_MSG
     * 5，收到后再从1开始check
     *
     * @param mqMessage
     * @return
     */
    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CfOperatingRecord> mqMessage) {

        printReceiveMqMessageLog(mqMessage);

        if(mqMessage == null || mqMessage.getPayload() == null){
            //返回成功不然会一直卡住
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        CfOperatingRecord cfOperatingRecord = mqMessage.getPayload();

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(cfOperatingRecord.getInfoUuid());

        //案例结束就没必要发了
        if(crowdfundingInfo == null || crowdfundingInfo.getEndTime() == null || crowdfundingInfo.getEndTime().getTime() <= System.currentTimeMillis()){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        // 1，用户是否需要填写身份证

        CfInfoExt cfInfoExt = cfInfoExtBiz.getByInfoUuid(cfOperatingRecord.getInfoUuid());

        List<CrowdfundingInfoDataStatusTypeEnum> requiredList = CrowdfundingUtil.getRequiredCaseList(cfInfoExt);

        if(!requiredList.contains(CrowdfundingInfoDataStatusTypeEnum.ID_VERIFY)){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        // 2，如果需要，检查一下用户是否填写
        CrowdfundingIdCase crowdfundingIdCase = crowdfundingIdCaseService.getByInfoId(crowdfundingInfo.getId());
        if(crowdfundingIdCase != null){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        // 3，如果没有填写，下发微信通知
        crowdfundingIdCaseService.sendIdCaseReqeustMsg(crowdfundingInfo);

        // 4, 明日18:00继续check
        DateTime now = new DateTime();
        long expectTime = now.plusDays(1).withHourOfDay(18).withMinuteOfHour(RandomUtils.nextInt(0, 30)).getMillis();
        if (!applicationService.isProduction()) {
            expectTime = now.plusDays(0).plusHours(0).plusMinutes(2).getMillis();
        }
        String targetKeys = Joiner.on("-").join(MQTagCons.CF_OPERATION_DELAY_10M_MSG, System.currentTimeMillis(), cfOperatingRecord.getId());
        Message msg = Message.ofSchedule(MQTopicCons.CF, MQTagCons.CF_OPERATION_DELAY_10M_MSG, targetKeys,
                cfOperatingRecord, expectTime / 1000);
        MessageResult  result =  producer.send(msg);
        log.info("send delay mq {}, result {}", msg, result);

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
