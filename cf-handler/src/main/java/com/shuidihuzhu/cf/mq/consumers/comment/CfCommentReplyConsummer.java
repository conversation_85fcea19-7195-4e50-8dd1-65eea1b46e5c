package com.shuidihuzhu.cf.mq.consumers.comment;

import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.crowdfunding.CfRedisKvKey;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingCommentType;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingType;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingProgress;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingComment;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.model.crowdfunding.param.PushCommentMessage;
import com.shuidihuzhu.cf.mq.consumers.BaseConsumerPrintReceiveLog;
import com.shuidihuzhu.cf.service.msg.MsgClientV2Service;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.common.web.util.MobileUtil;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Service
@RefreshScope
@Slf4j
@RocketMQListener(id = MQTagCons.CF_COMMENT_REPLY,
        topic = MQTopicCons.CF,
        tags = MQTagCons.CF_COMMENT_REPLY,
        group = MQTagCons.CF_COMMENT_REPLY)
public class CfCommentReplyConsummer extends BaseConsumerPrintReceiveLog implements MessageListener<CrowdfundingComment> {
    @Autowired
    private CrowdFundingProgressBiz crowdFundingProgressBiz;
    @Autowired
    private CrowdfundingCommentBiz crowdfundingCommentBiz;
    @Autowired
    private CrowdfundingOrderBiz crowdfundingOrderBiz;
    @Autowired
    private CfRedisKvBiz cfRedisKvBiz;
    @Autowired
    private UserInfoDelegate userInfoDelegate;
    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;
    @Resource
    private MsgClientV2Service msgClientV2Service;
    @Autowired
    private ShuidiCipher shuidiCipher;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;

    @Value("${apollo.CfCommentReplyConsummer.switch:false}")
    private boolean msgSwitch;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CrowdfundingComment> mqMessage) {
        printReceiveMqMessageLog(mqMessage);
        if (null == mqMessage || null == mqMessage.getPayload()) {
            log.debug("CfCommentReplyConsummer mqMessage={}", mqMessage);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        CrowdfundingComment crowdfundingComment = mqMessage.getPayload();
        if (crowdfundingComment.getType() == null || crowdfundingComment.getParentId() == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        // 评论者的名称
        String commentName = "";
        if (crowdfundingComment.getUserId() > 0) {
            UserInfoModel userAccount = userInfoDelegate.getUserInfoByUserId(crowdfundingComment.getUserId());
            if (userAccount == null) {
                log.info("comment push no userAccount find");
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            commentName = MobileUtil.getAppropriateNickname(userAccount.getNickname(), shuidiCipher.decrypt(userAccount.getCryptoMobile()));
        }

        //接收消息的用户id
        PushCommentMessage pushCommentMessage = new PushCommentMessage();
        CrowdfundingInfo crowdfundingInfo = null;
        if (crowdfundingComment.getType() == CrowdfundingCommentType.CONTRIBUTE_RECORD.value()) {
            //捐款记录评论
            crowdfundingInfo = manageOrderRecord(crowdfundingComment, pushCommentMessage, commentName);
        } else if (crowdfundingComment.getType() == CrowdfundingCommentType.CROWDFUNDING_TRENDS.value()) {
            //患者动态评论
            crowdfundingInfo = manageProcessRecord(crowdfundingComment, pushCommentMessage, commentName);
        }
        if (crowdfundingInfo == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        UserInfoModel userAccount = userInfoDelegate.getUserInfoByUserId(crowdfundingInfo.getUserId());
        if(userAccount != null) {
            pushCommentMessage.setInfoLaunchName(StringUtils.trimToEmpty(userAccount.getNickname()));
        }
        if (crowdfundingComment.getCommentId() != null && crowdfundingComment.getCommentId() > 0L) {
            // 二级评论
            CrowdfundingComment comment = crowdfundingCommentBiz.getByIdNoCareDeleted(crowdfundingComment.getCommentId());

            //防止恶意用户乱写commentId，影响用户
            if (comment == null || crowdfundingInfo.getId() != comment.getCrowdfundingId()) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            pushCommentMessage.setUserId(comment.getUserId());
        }
        //发送消息
        abTest(pushCommentMessage, crowdfundingComment, crowdfundingInfo);

        return ConsumeStatus.CONSUME_SUCCESS;
    }


    private void abTest(PushCommentMessage pushCommentMessage, CrowdfundingComment crowdfundingComment,
                        CrowdfundingInfo crowdfundingInfo) {
        if(crowdfundingInfo == null || crowdfundingInfo.getEndTime() == null || crowdfundingInfo.getEndTime().getTime() <= System.currentTimeMillis()){
            return;
        }

        Integer type = crowdfundingInfo.getType();
        if (type != CrowdfundingType.SERIOUS_ILLNESS.value()) {
            return;
        }
        long userId = pushCommentMessage.getUserId();

        String commentId = String.valueOf(crowdfundingComment.getId());
        if (msgSwitch) {
            try {
                commentId = oldShuidiCipher.aesEncrypt(commentId);
                commentId = URLEncoder.encode(commentId, "UTF-8");
            } catch (UnsupportedEncodingException e) {
                log.error("232消息发送错误 userId:{} InfoId:{}", userId, crowdfundingInfo.getInfoId());
                return;
            }
        }

        String infoUuid = crowdfundingInfo.getInfoId();


        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date createTime = crowdfundingComment.getCreateTime();
        String time = sdf.format(createTime);

        // 回复人是否是筹款人
        boolean isRaiser = crowdfundingComment.getUserId() == crowdfundingInfo.getUserId();

        String modelNum;
        if (isRaiser) {
//            modelNum = "templateCkrReply232-2";
            modelNum = "GAF3014";
        } else {
            modelNum = "templateJkrReply232-2";
        }
        HashMap<Integer, String> params = Maps.newHashMap();
        params.put(1, commentId);
        params.put(2, infoUuid);
        params.put(3, pushCommentMessage.getInfoLaunchName());
        params.put(4, crowdfundingInfo.getTitle());
        params.put(5, time);
        params.put(6, pushCommentMessage.getReplyName());

        Map<Long, Map<Integer, String>> msgMap = Maps.newHashMap();
        msgMap.put(userId, params);

        msgClientV2Service.sendWxParamsMsg(modelNum, msgMap);
    }

    private CrowdfundingInfo manageOrderRecord(CrowdfundingComment crowdfundingComment, PushCommentMessage pushCommentMessage,
                                               String commentName) {
        CrowdfundingOrder crowdfundingOrder = crowdfundingOrderBiz.getById(crowdfundingComment.getParentId());
        CrowdfundingInfo crowdfundingInfo=null;
        if (crowdfundingOrder != null) {
            crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(crowdfundingOrder.getCrowdfundingId());
        }

        // 匿名用户处理
        if (crowdfundingOrder != null && crowdfundingOrder.isAnonymous()
                && crowdfundingOrder.getUserId() > 0 && crowdfundingComment.getUserId() > 0
                && crowdfundingOrder.getUserId() == crowdfundingComment.getUserId()) {
            String anonymousName = this.cfRedisKvBiz.queryByKeyWithLocalCache(CfRedisKvKey.KEY_ANONYMOUS_DEFAULT_NAME);
            commentName = StringUtils.trimToEmpty(anonymousName);
        }

        if (crowdfundingComment.getCommentId() == null || crowdfundingComment.getCommentId() == 0L) {
            // 评论的捐款记录主体
            pushCommentMessage.setUserId(crowdfundingOrder.getUserId());
        }
        pushCommentMessage.setReplyName(commentName + "\n");
        return crowdfundingInfo;
    }


    private CrowdfundingInfo manageProcessRecord(CrowdfundingComment crowdfundingComment, PushCommentMessage pushCommentMessage,
                                                 String commentName) {
        if (crowdfundingComment.getCommentId() == null || crowdfundingComment.getCommentId() == 0L) {
            log.info("comment push no 受助人动态被评论不推送");
            return null;
        }
        CrowdFundingProgress progress = crowdFundingProgressBiz.getProgress(Math.toIntExact(crowdfundingComment.getParentId()));
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(progress.getActivityId());
        pushCommentMessage.setReplyName(commentName + "\n");
        return crowdfundingInfo;
    }

}
