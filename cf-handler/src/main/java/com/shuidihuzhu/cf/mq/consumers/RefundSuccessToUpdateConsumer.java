package com.shuidihuzhu.cf.mq.consumers;

import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoSimpleBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.finance.model.CfDonorRefundApply;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSimpleModel;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.service.crowdfunding.CfCaseDonatorService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RocketMQListener(id = "REFUND_SUCCESS_TO_UPDATE",
        group = "cf-RefundSuccessToUpdateConsumer_REFUND_SUCCESS_TO_UPDATE",
        tags = MQTagCons.REFUND_SUCCESS_TO_UPDATE,
        topic = MQTopicCons.CF)
public class RefundSuccessToUpdateConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<CfDonorRefundApply> {

    @Resource
    private CfCaseDonatorService cfCaseDonatorService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CfDonorRefundApply> mqMessage) {
        CfDonorRefundApply cfDonorRefundApply = mqMessage.getPayload();
        log.info("RefundSuccessToUpdateConsumer payload = {}",cfDonorRefundApply);
        if(cfDonorRefundApply == null){
            log.info("cfDonorRefundApply is null");
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        if(!cfCaseDonatorService.handleDonatorCountOnRefundSuccess(cfDonorRefundApply)){
            log.info("RefundSuccessToUpdateConsumer RECONSUME_LATER! payload = {}",cfDonorRefundApply);
            return ConsumeStatus.RECONSUME_LATER;
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
