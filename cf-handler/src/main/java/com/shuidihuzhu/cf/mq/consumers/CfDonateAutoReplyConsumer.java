package com.shuidihuzhu.cf.mq.consumers;

import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableList;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingCommentBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoSimpleBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingPushBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.ModuleKeyCons;
import com.shuidihuzhu.cf.constants.WxConstants;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingCommentType;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSimpleModel;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingComment;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.service.crowdfunding.CfBlackListCacheService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.frame.client.api.platform.NewUserClient;
import com.shuidihuzhu.frame.client.model.platform.ModuleNewUser;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import com.shuidihuzhu.wx.enums.AccountThirdTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

/**
 * Created by dongcf on 2020/10/26
 */
@Slf4j
@Service
@RefreshScope
@RocketMQListener(id = MQTagCons.CF_DONATE_AUTO_REPLY_MSG,
        topic = MQTopicCons.CF,
        tags = MQTagCons.CF_DONATE_AUTO_REPLY_MSG,
        group = "CF_GROUP_CF_DONATE_AUTO_REPLY_MSG")
public class CfDonateAutoReplyConsumer implements MessageListener<CrowdfundingOrder> {

    @Value("${auto_reply.content:}")
    private String autoReplyContent;
    @Autowired
    private CrowdfundingInfoSimpleBiz crowdfundingInfoSimpleBiz;
    @Autowired
    private CrowdfundingCommentBiz crowdfundingCommentBiz;
    @Autowired
    private NewUserClient newUserClient;
    @Autowired
    private CfBlackListCacheService cfBlackListCacheService;
    @Autowired
    private CrowdfundingPushBiz crowdfundingPushBiz;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CrowdfundingOrder> mqMessage) {
        CrowdfundingOrder orderInfo = mqMessage.getPayload();
        CfInfoSimpleModel simpleModel = crowdfundingInfoSimpleBiz.getFundingInfoById(orderInfo.getCrowdfundingId());
        if(simpleModel == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        long userId = simpleModel.getUserId();
        CrowdfundingComment comment = crowdfundingCommentBiz.getByUserId(orderInfo.getId(), userId, CrowdfundingCommentType.CONTRIBUTE_RECORD);
        //用户已主动评论
        if(comment != null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        //判断用户是否关闭自动回复
        String key = ModuleKeyCons.DONATE_AUTO_REPLY;
        Response<ModuleNewUser> userResponse = newUserClient.get(key, userId);
        if(userResponse == null || userResponse.notOk()) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        if(userResponse.getData() != null && "false".equals(userResponse.getData().getExtInfo())) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        List<String> contents = ImmutableList.of("谢谢好心人的帮助！求转发，求扩散～");
        if(StringUtils.isNotBlank(autoReplyContent)) {
            contents = Splitter.on("@@@").splitToList(autoReplyContent);
        }
        comment = this.crowdfundingCommentBiz.addCommentContributeRecord
                (orderInfo.getId(), userId,
                        -1, 3, 0L,
                        contents.get(ThreadLocalRandom.current().nextInt(contents.size())), simpleModel.getId());
        if(comment != null) {
            if(!cfBlackListCacheService.isContainsByUgc(userId)) {
                crowdfundingPushBiz.pushCommentMessage(comment);
            }
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
