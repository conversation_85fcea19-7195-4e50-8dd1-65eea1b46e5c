package com.shuidihuzhu.cf.mq.consumers;


import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.event.CfRegisterDelayDateEvent;
import com.shuidihuzhu.cf.mq.common.CommonConsumerHelper;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEvent;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@RocketMQListener(id = MQTagCons.CF_DELAY_WITH_REGISTER,
        tags = MQTagCons.CF_DELAY_WITH_REGISTER,
        topic = MQTopicCons.CF)
@Slf4j
public class CfRegisterDelayConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<Long> {

    @Resource
    private CommonConsumerHelper commonConsumerHelper;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<Long> consumerMessage) {
        printReceiveMqMessageLog(consumerMessage);

        return commonConsumerHelper.consumeMessageWithPublishSpringEvent(
                consumerMessage,
                (CommonConsumerHelper.Mapper<ApplicationEvent, Long>) this::map,
                true);
    }

    private CfRegisterDelayDateEvent map(ConsumerMessage<Long> consumerMessage) {
        return new CfRegisterDelayDateEvent(CfRegisterDelayConsumer.this, consumerMessage.getPayload());
    }
}
