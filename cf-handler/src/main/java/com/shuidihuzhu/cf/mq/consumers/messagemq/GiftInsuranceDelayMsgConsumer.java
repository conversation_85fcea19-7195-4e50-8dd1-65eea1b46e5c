package com.shuidihuzhu.cf.mq.consumers.messagemq;

import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.mq.consumers.BaseConsumerPrintReceiveLog;
import com.shuidihuzhu.cf.service.GiftInsuranceMsgService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import com.shuidihuzhu.msg.vo.MessageFeedBack;
import com.shuidihuzhu.msg.vo.MsgResponse;
import com.shuidihuzhu.msg.vo.rpc.MsgRecordBatch;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/2/18
 */

@Slf4j
@Service
@RocketMQListener(id = MQTagCons.CF_GIFT_INSURANCE_DELAY_MSG,
        tags = MQTagCons.CF_GIFT_INSURANCE_DELAY_MSG,
        topic = MQTopicCons.CF,
        group = "cf-api-" + MQTagCons.CF_GIFT_INSURANCE_DELAY_MSG + "-" + "group")
public class GiftInsuranceDelayMsgConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<List<MsgRecordBatch>>  {

    @Autowired
    private GiftInsuranceMsgService giftInsuranceMsgService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<List<MsgRecordBatch>> mqMessage) {
        printReceiveMqMessageLog(mqMessage);

        List<MsgRecordBatch> msgRecordBatches = mqMessage.getPayload();
        if (CollectionUtils.isEmpty(msgRecordBatches)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        giftInsuranceMsgService.sendMsg(msgRecordBatches);
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
