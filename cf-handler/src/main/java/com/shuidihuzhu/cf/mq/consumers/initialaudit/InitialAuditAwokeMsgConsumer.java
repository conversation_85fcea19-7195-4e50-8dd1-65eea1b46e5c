package com.shuidihuzhu.cf.mq.consumers.initialaudit;

import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.InitialAuditItem;
import com.shuidihuzhu.cf.mq.common.BaseMessageConsumer;
import com.shuidihuzhu.cf.service.msg.initialaudit.InitialAuditMsgService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 前置驳回提醒消息
 * https://wiki.shuiditech.com/pages/viewpage.action?pageId=204308737
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RocketMQListener(id = MQTagCons.CF_INITIAL_AUDIT_OPERATION_RECALL_MSG + "-awoke-msg",
        tags = MQTagCons.CF_INITIAL_AUDIT_OPERATION_RECALL_MSG,
        group = "cf-api" + MQTagCons.CF_INITIAL_AUDIT_OPERATION_RECALL_MSG + "-awoke-msg",
        topic = MQTopicCons.CF)
public class InitialAuditAwokeMsgConsumer
        extends BaseMessageConsumer<InitialAuditItem.InitialAuditRecall>
        implements MessageListener<InitialAuditItem.InitialAuditRecall> {

    @Resource
    private InitialAuditMsgService initialAuditMsgService;

    @Override
    protected boolean handle(ConsumerMessage<InitialAuditItem.InitialAuditRecall> consumerMessage) {
        InitialAuditItem.InitialAuditRecall payload = consumerMessage.getPayload();
        if (payload == null) {
            log.error("payload null");
            return true;
        }
        return initialAuditMsgService.sendOnAwokeReSubmit(payload).isSuccess();
    }

    @Override
    protected Logger getLogger() {
        return log;
    }
}
