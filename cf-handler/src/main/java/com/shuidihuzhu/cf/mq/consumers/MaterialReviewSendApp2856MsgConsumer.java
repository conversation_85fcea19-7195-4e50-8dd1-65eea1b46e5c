package com.shuidihuzhu.cf.mq.consumers;

import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.delegate.finance.ICfDrawCashFeignDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.service.msg.MsgClientV2Service;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;


/**
 * <AUTHOR>
 * @time 2019/5/23 下午3:19
 * @desc
 */
@Slf4j
@Service
@RocketMQListener(id = MQTagCons.MATERIAL_REVIEW_SEND_APP_MSG + "-2856",
        group = "cf-api-" + MQTagCons.MATERIAL_REVIEW_SEND_APP_MSG + "-2856" + "-group",
        tags = MQTagCons.MATERIAL_REVIEW_SEND_APP_MSG,
        topic = MQTopicCons.CF)
public class MaterialReviewSendApp2856MsgConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<CrowdfundingOrder> {

    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;

    @Autowired
    private ICfDrawCashFeignDelegate cfDrawCashFeignDelegate;

    @Autowired
    private MsgClientV2Service msgClientV2Service;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CrowdfundingOrder> mqMessage) {
        printReceiveMqMessageLog(mqMessage);

        CrowdfundingOrder payload = mqMessage.getPayload();

        if (payload == null || StringUtils.isEmpty(payload.getInfoId())) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        //判断是否是分流用户 不是分流用户不发消息
        if (!cfDrawCashFeignDelegate.getFundingABTest(payload.getCrowdfundingId())) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        //判断是否有提现草稿 有草稿不发消息
        if (cfDrawCashFeignDelegate.getFundingValidParam(payload.getCrowdfundingId())) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoFromSlave(payload.getInfoId());
        if (crowdfundingInfo == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        //判断材审是否通过  材审未通过不发消息
        if (crowdfundingInfo.getStatus().value() != CrowdfundingStatus.CROWDFUNDING_STATED.value()) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        Map<Long, Map<Integer, String>> appMsgMap = Maps.newHashMap();

        Map<Integer, String> paramMap = Maps.newHashMap();
        paramMap.put(1, DateUtil.getCurrentDateTimeStr());
        paramMap.put(2, crowdfundingInfo.getInfoId());

        appMsgMap.put(crowdfundingInfo.getUserId(), paramMap);
        msgClientV2Service.sendAppParamsMsg("DTS7871", appMsgMap);

        return ConsumeStatus.CONSUME_SUCCESS;
    }

}
