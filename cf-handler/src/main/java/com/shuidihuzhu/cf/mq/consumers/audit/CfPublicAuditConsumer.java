package com.shuidihuzhu.cf.mq.consumers.audit;

import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.audit.CfPublicAuditServiceImpl;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.client.model.CfPublicAuditAction;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/5/17 8:27 PM
 */
@Slf4j
@Service
@RefreshScope
@RocketMQListener(id = CfClientMQTagCons.CF_PUBLIC_AUDIT_INFO,
        tags = CfClientMQTagCons.CF_PUBLIC_AUDIT_INFO,
        topic = MQTopicCons.CF,
        group = CfClientMQTagCons.CF_PUBLIC_AUDIT_INFO)
public class CfPublicAuditConsumer implements MessageListener<CfPublicAuditAction> {

    @Resource
    private CfPublicAuditServiceImpl cfPublicAuditService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CfPublicAuditAction> mqMessage) {

        if (Objects.isNull(mqMessage) || Objects.isNull(mqMessage.getPayload())) {
            log.info("CfPublicAuditConsumer mqMessage={}", mqMessage);
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        CfPublicAuditAction cfPublicAuditAction = mqMessage.getPayload();
        cfPublicAuditService.savePublicAudit(cfPublicAuditAction);

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
