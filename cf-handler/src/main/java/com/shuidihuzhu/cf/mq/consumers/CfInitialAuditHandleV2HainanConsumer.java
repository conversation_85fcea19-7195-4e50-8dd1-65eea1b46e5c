package com.shuidihuzhu.cf.mq.consumers;

import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.crowdfunding.InitialAuditOperationMqVO;
import com.shuidihuzhu.cf.service.tog.HainanService;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
@RocketMQListener(id = "cf-api-hainan-" + MQTagCons.CF_INITIAL_AUDIT_HANDLE_V2,
        group = "cf-api-hainan-" + MQTagCons.CF_INITIAL_AUDIT_HANDLE_V2,
        tags = MQTagCons.CF_INITIAL_AUDIT_HANDLE_V2,
        topic = MQTopicCons.CF)
@Slf4j
public class CfInitialAuditHandleV2HainanConsumer implements MessageListener<InitialAuditOperationMqVO> {

    @Resource
    private HainanService hainanService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<InitialAuditOperationMqVO> mqMessage) {

        if (Objects.isNull(mqMessage) || Objects.isNull(mqMessage.getPayload())) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        log.info("CfInitialAuditHandleV2HainanConsumer is begin : {}", mqMessage.getPayload());
        InitialAuditOperationMqVO handleCaseInfoParam = mqMessage.getPayload();
        // 只保存审核通过的
        int handleResult = handleCaseInfoParam.getCaseInitialAuditResult().getHandleResult();
        if (handleResult != HandleResultEnum.audit_pass.getType()) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        hainanService.saveHainanShowRecord(handleCaseInfoParam);
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
