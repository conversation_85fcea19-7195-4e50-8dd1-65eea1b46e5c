package com.shuidihuzhu.cf.mq.consumers;

import com.google.common.base.Splitter;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.dto.HastenShareMsgDto;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.msg.MsgClientV2Service;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RocketMQListener(id = MQTagCons.HASTEN_SHARE_MSG,
        tags = MQTagCons.HASTEN_SHARE_MSG,
        topic = MQTopicCons.CF,
        group = "cf-api-" + MQTagCons.HASTEN_SHARE_MSG + "-group")
public class HastenShareMsgConsumer implements MessageListener<HastenShareMsgDto> {

    @Autowired
    private MsgClientV2Service msgClientV2Service;

    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;

    @Autowired
    private UserInfoDelegate userInfoDelegate;


    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<HastenShareMsgDto> mqMessage) {
        if (Objects.isNull(mqMessage) || Objects.isNull(mqMessage.getPayload())) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        HastenShareMsgDto dto = mqMessage.getPayload();

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoFromSlave(dto.getInfoUuid());
        if (Objects.isNull(crowdfundingInfo)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        //{1} 筹款人姓名
        String realName = "患者";
        UserInfoModel userInfoModel = userInfoDelegate.getUserInfoByUserId(crowdfundingInfo.getUserId());
        if (Objects.nonNull(userInfoModel) && StringUtils.isNotBlank(userInfoModel.getRealName())) {
            realName = userInfoModel.getRealName();
        }
        //{2} 标题
        String title = crowdfundingInfo.getTitle();
        if (StringUtils.isNotEmpty(title)) {
            List<String> strings = Splitter.fixedLength(17).splitToList(title);
            title = strings.get(0) + "...";
        }

        //{3} =目标筹款额-已筹金额
        int diffAmount = (crowdfundingInfo.getTargetAmount() - crowdfundingInfo.getAmount()) / 100;

        Map<Integer, String> params = Maps.newHashMap();
        params.put(1, realName);
        params.put(2, title);
        params.put(3, String.valueOf(diffAmount));
        params.put(4, dto.getInfoUuid());

        Map<Long, Map<Integer, String>> paramsMap = Maps.newHashMap();
        paramsMap.put(dto.getUserId(), params);

        msgClientV2Service.sendWxParamsMsg(dto.getModelNum(), paramsMap);

        return ConsumeStatus.CONSUME_SUCCESS;
    }

}
