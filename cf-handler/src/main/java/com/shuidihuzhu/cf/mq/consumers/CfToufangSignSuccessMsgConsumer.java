package com.shuidihuzhu.cf.mq.consumers;

import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.service.MobileUserIdModel;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.WxConstants;
import com.shuidihuzhu.cf.delegate.SimpleUserAccountDelegate;
import com.shuidihuzhu.cf.delegate.WxUserEventStatusDelegate;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;

import com.shuidihuzhu.cf.service.msg.MsgClientV2Service;
import com.shuidihuzhu.client.account.v1.accountservice.MobileUserIdResponse;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import com.shuidihuzhu.msg.model.SmsRecord;
import com.shuidihuzhu.wx.enums.AccountThirdTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * Created by lixurui on 18/11/20.
 */
@Service
@RefreshScope
@RocketMQListener(id = MQTagCons.CLICK_ON_DELAY_PAGE_THR_MIN_MSG,
        tags = MQTagCons.CLICK_ON_DELAY_PAGE_THR_MIN_MSG,
        group = MQTagCons.CLICK_ON_DELAY_PAGE_THR_MIN_MSG + "-group",
        topic = MQTopicCons.CF)
@Slf4j
public class CfToufangSignSuccessMsgConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<SmsRecord> {

    @Autowired
    private WxUserEventStatusDelegate wxUserEventStatusDelegate;

    @Autowired
    private SimpleUserAccountDelegate simpleUserAccountDelegate;

    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;

    @Resource
    private MsgClientV2Service msgClientV2Service;


    //关注状态判定
    private boolean getSubscribeStatus(MobileUserIdModel mobileUserIdResponse) {
        int userNewThirdType = WxConstants.FUNDRAISER_THIRD_TYPE;
        boolean result = false;

        try {
            if (mobileUserIdResponse != null) {
                boolean rpcNewResponse = wxUserEventStatusDelegate.checkSubscribeByUserId(mobileUserIdResponse.getUserId(), userNewThirdType);
                log.info("CfToufangSignSuccessMsgConsumer getSubscribeStatus request:{},response:{}", mobileUserIdResponse, rpcNewResponse);
                return rpcNewResponse;
            }
        } catch (Exception e) {
            log.error("CfToufangSignSuccessMsgConsumer getSubscribeStatus request:{},exception", mobileUserIdResponse);
        }
        return result;
    }


    //筹款发起的判定
    private boolean getCrowdfundingStatus(Long userId) {

        boolean result = false;//默认筹款未发起
        if (userId <= 0) {
            return result;
        }
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getLastByUserId(userId);//根据用户id获取筹款信息
        if (crowdfundingInfo == null) {
            return result;
        }

        //在当前日期之前发起过筹款
        if (crowdfundingInfo.getCreateTime().before(new Date(System.currentTimeMillis()))) {
            result = true;
        }

        return result;

    }

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<SmsRecord> mqMessage) {
        SmsRecord smsRecordMqInfo = mqMessage.getPayload();
        MobileUserIdModel userInfoByPhone = getUserInfoByPhone(smsRecordMqInfo.getMobile());

        boolean sResult = getSubscribeStatus(userInfoByPhone);//关注结果
        boolean cfResult = getCrowdfundingStatus(userInfoByPhone == null ? 0L : userInfoByPhone.getUserId());//筹款发起结果

        if (cfResult && sResult) {
            return ConsumeStatus.CONSUME_SUCCESS;//不返回任何结果
        } else if (cfResult && sResult == false) {
            String modelNum0 = "code1";
            this.handleMsgStatus(modelNum0, smsRecordMqInfo.getMobile());//引导关注
        } else if (cfResult == false && sResult) {
            String modelNum0 = "code2";
            this.handleMsgStatus(modelNum0, smsRecordMqInfo.getMobile());//引导发起
        } else {
            String modelNum0 = "code3";
            this.handleMsgStatus(modelNum0, smsRecordMqInfo.getMobile());//引导发起和关注
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private MobileUserIdModel getUserInfoByPhone(String phone) {
        if (StringUtils.isEmpty(phone)) {
            return null;
        }
        try {
            MobileUserIdModel userInfoByMobile = this.simpleUserAccountDelegate.getUserIdByMobile(phone);
            log.info("CfToufangSignSuccessMsgConsumer getSubscribeStatus request:{},response", phone, userInfoByMobile);
            return userInfoByMobile;
        } catch (Exception e) {
            log.error("CfToufangSignSuccessMsgConsumer getSubscribeStatus request:{},exception", phone);
        }
        return null;
    }

    private void handleMsgStatus(String modelNum0, String mobile) {
        msgClientV2Service.sendSmsMsg(modelNum0, Lists.newArrayList(mobile), false);
    }

}

