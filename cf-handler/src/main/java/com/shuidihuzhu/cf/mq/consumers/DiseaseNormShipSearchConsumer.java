package com.shuidihuzhu.cf.mq.consumers;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.PropertyFilter;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.cf.service.disease.DiseaseService;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.client.model.event.InfoApproveEvent;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * @Author：liuchangjun
 * @Date：2021/9/2
 */
@Service
@Slf4j
@RocketMQListener(id = MQTagCons.CF_DISEASE_NORM_SHIP_FIRST,
        tags = CfClientMQTagCons.FIRST_SEARCH_DISEASE_NORM_SHIP,
        topic = MQTopicCons.CF,
        group = CfClientMQTagCons.FIRST_SEARCH_DISEASE_NORM_SHIP+MQTagCons.CF_DISEASE_NORM_SHIP_FIRST)
public class DiseaseNormShipSearchConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<Integer> {
    @Autowired
    private DiseaseService diseaseService;


    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<Integer> mqMessage) {

        PropertyFilter filter = new PropertyFilter() {
            public boolean apply(Object source, String name, Object value) {
                if ("bornHostBytes".equals(name) || "storeHostBytes".equals(name)) {
                    return false;
                }
                return true;
            }
        };

        log.info(this.getClass().getSimpleName()+ " " +" receive topicName:{} ,tags:{}  msg:{}",mqMessage.getTopic(),mqMessage.getTags(), JSON.toJSONString(mqMessage, filter));
        if(Objects.isNull(mqMessage) || Objects.isNull(mqMessage.getPayload())){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        int caseId = mqMessage.getPayload();

        diseaseService.insertDiseaseNorm(caseId);
        return ConsumeStatus.CONSUME_SUCCESS;
    }


}
