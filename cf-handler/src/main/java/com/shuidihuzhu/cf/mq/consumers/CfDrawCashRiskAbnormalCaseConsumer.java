package com.shuidihuzhu.cf.mq.consumers;

import com.shuidihuzhu.cf.mq.payload.CaseRiskHighPayload;
import com.shuidihuzhu.cf.mq.producer.CommonMessageHelperService;
import com.shuidihuzhu.cf.mq.producer.MessageBuilder;
import com.shuidihuzhu.client.cf.risk.model.DrawCashRiskMsgModel;
import com.shuidihuzhu.client.constant.MQTagCons;
import com.shuidihuzhu.client.constant.MQTopicCons;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @time 2019/3/19 下午8:11
 * @desc
 */
@Service
@Slf4j
@RocketMQListener(id = MQTagCons.CF_RISK_DRAW_CASH_ABNORMAL_CASE_MSG,
        tags = MQTagCons.CF_RISK_DRAW_CASH_ABNORMAL_CASE_MSG,
        topic = MQTopicCons.CF,
        group = "DRAW_CASH_ABNORMAL_CASE_" + MQTagCons.CF_RISK_DRAW_CASH_ABNORMAL_CASE_MSG)
public class CfDrawCashRiskAbnormalCaseConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<DrawCashRiskMsgModel> {

    @Resource
    private CommonMessageHelperService commonMessageHelperService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<DrawCashRiskMsgModel> mqMessage) {
        printReceiveMqMessageLog(mqMessage);

        DrawCashRiskMsgModel model = mqMessage.getPayload();
        if(null == model || StringUtils.isEmpty(model.getInfoUuid()) || null == model.getRiskType()){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        String infoUuid = model.getInfoUuid();
        Integer riskType = model.getRiskType();

        // 发送异常案例mq消息
        CaseRiskHighPayload payload = new CaseRiskHighPayload(infoUuid, riskType);
        Message<CaseRiskHighPayload> message = MessageBuilder.createWithPayload(payload)
                .setTags("CASE_DATE_RISK_CHECK_NON_PASSED").build();
        commonMessageHelperService.asyncSend(message);

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
