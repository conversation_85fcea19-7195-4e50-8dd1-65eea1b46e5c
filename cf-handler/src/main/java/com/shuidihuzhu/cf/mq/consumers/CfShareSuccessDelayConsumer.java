package com.shuidihuzhu.cf.mq.consumers;

import com.shuidihuzhu.cf.biz.crowdfunding.CfInfoShareRecordBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfUserBaseStatService;
import com.shuidihuzhu.cf.biz.crowdfunding.CfUserCaseBaseStatService;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoShareRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CfUserBaseStatDO;
import com.shuidihuzhu.cf.model.crowdfunding.CfUserCaseBaseStatDO;
import com.shuidihuzhu.cf.service.activity.impl.PennantService;
import com.shuidihuzhu.cf.service.crowdfunding.CfUserCaseStatService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2019/2/21 下午7:32
 * @desc
 */
@Slf4j
@Service
@RocketMQListener(id = MQTagCons.CF_SHARE_SUCCESS_DELAY_MSG,
        tags = MQTagCons.CF_SHARE_SUCCESS_DELAY_MSG,
        group = "SHARE_SUCCESS" + MQTagCons.CF_SHARE_SUCCESS_DELAY_MSG,
        topic = MQTopicCons.CF)
public class CfShareSuccessDelayConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<CfInfoShareRecord> {

    @Autowired
    private CfUserCaseBaseStatService cfUserCaseBaseStatService;

    @Autowired
    private CfUserBaseStatService cfUserBaseStatService;

    @Autowired
    private CfInfoShareRecordBiz cfInfoShareRecordBiz;

    @Autowired
    private CfUserCaseStatService cfUserCaseStatService;

    @Autowired
    private PennantService pennantService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CfInfoShareRecord> mqMessage) {
        printReceiveMqMessageLog("variable convergence share mq", mqMessage);

        CfInfoShareRecord cfShareRecord = mqMessage.getPayload();

        if(null == cfShareRecord){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        long caseId = cfShareRecord.getInfoId();
        long userId = cfShareRecord.getUserId();

        int shareCaseIncr = 0;
        List<CfInfoShareRecord> shareRecords = cfInfoShareRecordBiz.selectByUserAndCase(userId, (int) caseId);
        if(CollectionUtils.isNotEmpty(shareRecords) && 1 == shareRecords.size()){
            shareCaseIncr++;
        }

        CfUserBaseStatDO userBaseStatDO = cfUserBaseStatService.selectByUser(userId);
        CfUserCaseBaseStatDO userCaseBaseStatDO = cfUserCaseBaseStatService.selectByUserAndCase(userId, (int) caseId);

        //如果数据库没记录，则从大数据获得基础数据并入库
        cfUserCaseStatService.createUserCaseStatInfo(userId, caseId, userBaseStatDO, userCaseBaseStatDO);

        //数据库有数据则自增
        if(null != userBaseStatDO && null != userCaseBaseStatDO){
            //更新用户维度 总转发案例数、总转发次数
            cfUserBaseStatService.updateUserShareInfo(userId, shareCaseIncr);
            //更新案例维度 用户对当前案例的转发次数
            cfUserCaseBaseStatService.updateCaseShareInfo(userId, (int)caseId);
        }

        try{
            this.pennantService.shareAward(cfShareRecord.getUserId(),cfShareRecord.getInfoId(),cfShareRecord.getTradeNo(),cfShareRecord.getPathName());
        }catch (Exception e){
            log.error("pennant share award error",e);
        }

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
