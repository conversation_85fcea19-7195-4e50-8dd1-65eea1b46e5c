package com.shuidihuzhu.cf.mq.consumers;


import com.shuidihuzhu.cf.biz.crowdfunding.CfFirstApproveBiz;
import com.shuidihuzhu.cf.constants.ApplicationConstants;
import com.shuidihuzhu.cf.constants.MQGroupAppenderConstants;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.mq.common.BaseMessageConsumer;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@RocketMQListener(id = ApplicationConstants.PROJECT_NAME + MQGroupAppenderConstants.RISK_CHECK +
        MQTagCons.ADMIN_MQ_TAG_FIRST_APPROVE_SUCCESS,
        group = MQTagCons.ADMIN_MQ_TAG_FIRST_APPROVE_SUCCESS + MQGroupAppenderConstants.RISK_CHECK,
        tags = MQTagCons.ADMIN_MQ_TAG_FIRST_APPROVE_SUCCESS,
        topic = MQTopicCons.CF)
@Slf4j
public class CfFirstApproveConsumer extends BaseMessageConsumer<CrowdfundingInfo> implements MessageListener<CrowdfundingInfo> {

    @Autowired
    private CfFirstApproveBiz cfFirstApproveBiz;

    @Override
    protected boolean handle(ConsumerMessage<CrowdfundingInfo> consumerMessage) {
        CrowdfundingInfo crowdfundingInfo = consumerMessage.getPayload();
        if (crowdfundingInfo == null) {
            log.error("crowdfundingInfo null message: {}", consumerMessage);
            return false;
        }
        cfFirstApproveBiz.onFirstApproveSuccess(crowdfundingInfo);
        return true;
    }

    @Override
    protected Logger getLogger() {
        return log;
    }

}
