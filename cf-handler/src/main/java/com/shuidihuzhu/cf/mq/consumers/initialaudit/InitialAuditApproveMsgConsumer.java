package com.shuidihuzhu.cf.mq.consumers.initialaudit;

import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSimpleModel;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.InitialAuditItem;
import com.shuidihuzhu.cf.mq.common.BaseMessageConsumer;
import com.shuidihuzhu.cf.service.msg.initialaudit.InitialAuditMsgService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RocketMQListener(id = MQTagCons.CF_INITIAL_AUDIT_OPERATION_MSG + "-approve-msg",
        tags = MQTagCons.CF_INITIAL_AUDIT_OPERATION_MSG,
        group = "cf-api" + MQTagCons.CF_INITIAL_AUDIT_OPERATION_MSG + "-approve-msg",
        topic = MQTopicCons.CF)
public class InitialAuditApproveMsgConsumer
        extends BaseMessageConsumer<InitialAuditItem.InitialAuditOperation>
        implements MessageListener<InitialAuditItem.InitialAuditOperation>{

    @Resource
    private InitialAuditMsgService initialAuditMsgService;

    @Resource
    private CrowdfundingInfoBiz crowdfundingInfoBiz;

    @Resource
    private CfInfoExtBiz cfInfoExtBiz;

    @Resource
    private CfFirstApproveBiz cfFirstApproveBiz;
    @Resource
    private CfFastDrawVersionBiz cfFastDrawVersionBiz;

    @Resource
    private MeterRegistry meterRegistry;

    @Override
    protected boolean handle(ConsumerMessage<InitialAuditItem.InitialAuditOperation> consumerMessage) {
        InitialAuditItem.InitialAuditOperation payload = consumerMessage.getPayload();
        if (payload == null) {
            log.error("payload null");
            return true;
        }
        int caseId = payload.getCaseId();
        CrowdfundingInfo fundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        String infoId = fundingInfo.getInfoId();

        // 初审通过逻辑
        CfInfoExt ext = cfInfoExtBiz.getByInfoUuid(infoId);
        int firstApproveStatus = ext.getFirstApproveStatus();
        FirstApproveStatusEnum firstApproveStatusEnum = FirstApproveStatusEnum.parse(firstApproveStatus);
        if (FirstApproveStatusEnum.isPassed(firstApproveStatusEnum)) {
            cfFirstApproveBiz.onFirstApproveSuccess(fundingInfo);

            // todo zy 标记案例极速提现版本，后续全量后，需要督促整体下掉
            cfFastDrawVersionBiz.save(fundingInfo);
        }

        // 上报初审通过/驳回
        try {
            meterRegistry.counter("initial-audit", "status", firstApproveStatusEnum.name()).increment();
        } catch (Exception e) {
            log.error("stat initial-audit caseId:{}", caseId, e);
        }

        // 发送初审动作对应的消息
        return initialAuditMsgService.sendOnApprove(caseId).isSuccess();
    }

    @Override
    protected Logger getLogger() {
        return log;
    }
}
