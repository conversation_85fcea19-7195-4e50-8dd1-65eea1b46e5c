package com.shuidihuzhu.cf.mq.consumers;

import com.shuidihuzhu.cf.biz.crowdfunding.CfUserStatBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingPushBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.DelayLevel;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * Author: Alvin Tian
 * Date: 2017/11/10 12:40
 */
@Slf4j
@Service
@RefreshScope
@RocketMQListener(id = MQTagCons.CF_DONATION_SUCCESS_MSG,
		topic = MQTopicCons.CF,
		tags = MQTagCons.CF_DONATION_SUCCESS_MSG,
		group = "Cf-ConsumerGroup_CF_DONATION_SUCCESS_MSG")
public class CfDonationSuccessMsgConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<CrowdfundingOrder> {

	private static final Logger LOGGER = LoggerFactory.getLogger(CfDonationSuccessMsgConsumer.class);
	@Autowired
	private CrowdfundingPushBiz crowdfundingPushBiz;
	@Autowired
	private CfUserStatBiz cfUserStatBiz;
	@Resource(name = "cfRedissonHandler")
	private RedissonHandler redissonHandler;
	@Autowired(required = false)
	private Producer producer;
	public static final int NORMAL = 0;
	public static final int DELAY_FOR_SUBSCRIBE = 4;

	@Value("${apollo.boolean.874:true}")
	private boolean flag;

	@Override
	public ConsumeStatus consumeMessage(ConsumerMessage<CrowdfundingOrder> mqMessage) {
		printReceiveMqMessageLog("捐款成功回调",mqMessage);

		if(mqMessage == null || mqMessage.getPayload() == null){
			return ConsumeStatus.CONSUME_SUCCESS;
		}
		if (mqMessage.getReconsumeTimes() > 10) {
			LOGGER.debug("捐款通知消息发送失败，已经尝试10次 {}", mqMessage);
			return ConsumeStatus.CONSUME_SUCCESS;
		}
		String key = mqMessage.getKeys();
		String tryLock = null;
		try {
			tryLock = redissonHandler.tryLock(key, 0, 10 * 1000L);
		} catch (Exception e) {
			LOGGER.error("redissonHandler.tryLock exception:", e);
		}
		if (StringUtils.isBlank(tryLock)) {
			return ConsumeStatus.CONSUME_SUCCESS;
		}
		try {
			CrowdfundingOrder order = mqMessage.getPayload();
			//支付成功后，这个消息会在发一个一样的消息，只处理第一个消息
			if (order.getConsumeStatus() == NORMAL) {
				int score = this.cfUserStatBiz.addAmount(order);
				order.setScore(score);
			}
			if (flag) {
				boolean x = pushMessage(mqMessage, order);
				if (x) {
					return ConsumeStatus.CONSUME_SUCCESS;
				} else {
					return ConsumeStatus.RECONSUME_LATER;
				}
			}
			return ConsumeStatus.CONSUME_SUCCESS;
		} catch (Exception e) {
			LOGGER.debug("捐款成功事件，异常重试", e);
			return ConsumeStatus.RECONSUME_LATER;
		} finally {
			try {
				if (StringUtils.isNotBlank(tryLock)) {
					redissonHandler.unLock(key, tryLock);
				}
			} catch (Exception e) {
				log.debug("", e);
			}
		}
	}


	/**
	 * @param mqMessage
	 * @param order
	 * @return
	 */
	Boolean pushMessage(ConsumerMessage<CrowdfundingOrder> mqMessage, CrowdfundingOrder order) {
		if (order.getConsumeStatus() == DELAY_FOR_SUBSCRIBE) {
			log.info("捐款成功消息 id:{}", order.getId());
			return crowdfundingPushBiz.pushPayOrderByOrder(order);
		}

		if (mqMessage.getReconsumeTimes() == 0) {
			log.info("捐款成功消息延迟一分钟后发送 getReconsumeTimes:{}", mqMessage.getReconsumeTimes());
			order.setConsumeStatus(DELAY_FOR_SUBSCRIBE);
			producer.send(
					new Message<>(MQTopicCons.CF,
							MQTagCons.CF_DONATION_SUCCESS_MSG,
							MQTagCons.CF_DONATION_SUCCESS_MSG + "_" + order.getId(),
							order,
							DelayLevel.M1));
			return true;
		}

		return crowdfundingPushBiz.pushPayOrderByOrder(order);
	}

}
