package com.shuidihuzhu.cf.mq.consumers.activity;

import com.shuidihuzhu.cf.activity.constants.ActivityMQConstants;
import com.shuidihuzhu.cf.activity.enums.MutexActivityTypeEnum;
import com.shuidihuzhu.cf.activity.feign.v2.ActivityMutexFeignClient;
import com.shuidihuzhu.cf.activity.mq.ActivityCaseBasicPayload;
import com.shuidihuzhu.cf.enhancer.mq.BaseMessageConsumer;
import com.shuidihuzhu.charity.client.message.LoveHomeAddCaseMsg;
import com.shuidihuzhu.charity.client.message.MqTagCons;
import com.shuidihuzhu.charity.client.message.MqTopicCons;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RocketMQListener(id = MqTagCons.LOVE_HOME_ADD_CASE,
        tags = MqTagCons.LOVE_HOME_ADD_CASE,
        group = "cf-api" + MqTagCons.LOVE_HOME_ADD_CASE,
        topic = MqTopicCons.FRAME)
public class LoveHomeEnableConsumer
        extends BaseMessageConsumer<LoveHomeAddCaseMsg>
        implements MessageListener<LoveHomeAddCaseMsg> {

    @Autowired
    private ActivityMutexFeignClient activityMutexFeignClient;

    @Override
    protected boolean handle(ConsumerMessage<LoveHomeAddCaseMsg> consumerMessage) {
        LoveHomeAddCaseMsg payload = consumerMessage.getPayload();
        if (payload == null) {
            log.error("LoveHomeEnbaleConsumer payload null");
            return true;
        }
        int caseId = payload.getCaseId();
        activityMutexFeignClient.onMutexActivityJoin(caseId, MutexActivityTypeEnum.HOME_PAGE.getValue());
        return true;
    }

    @Override
    protected Logger getLogger() {
        return log;
    }
}
