package com.shuidihuzhu.cf.mq.consumers.usertransfer;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.model.UserTransferData;
import com.shuidihuzhu.cf.biz.crowdfunding.CfInfoShareRecordShardingBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingOrderShardingBiz;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.facade.loverank.UserLoveRankFace;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoShareRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.model.crowdfunding.loverank.UserDonateInfo;
import com.shuidihuzhu.cf.mq.consumers.BaseConsumerPrintReceiveLog;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.client.protocol.ScoredEntry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 账号转移更新榜单
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RocketMQListener(id = "CfUserTransferDelayMQListner", tags = MQTopicCons.DEDALY_HZ_DATA_TRANSFER, topic = MQTopicCons.HZ_DATA_TRANSFER)
public class CfUserTransferDelayMQListner extends BaseConsumerPrintReceiveLog implements MessageListener<UserTransferData> {

    @Resource(name = "crowdfundingOrderShardingBizImpl")
    private CrowdfundingOrderShardingBiz crowdfundingOrderShardingBiz;
    @Autowired
    private CfInfoShareRecordShardingBiz cfInfoShareRecordShardingBiz;
    @Resource(name = "redis-cf-api-new")
    private RedissonHandler redissonHandler;
    @Autowired
    private ShuidiCipher shuidiCipher;
    @Autowired
    private UserLoveRankFace userLoveRankFace;
    private static final String USER_LOVE_RANK_CASE = "USER_LOVE_RANK_CASE_ID_";


    private String getKey(int caseId) {
        return USER_LOVE_RANK_CASE + caseId;
    }

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<UserTransferData> mqMessage) {
        UserTransferData userTransferData = mqMessage.getPayload();
        if (null == userTransferData) {
            log.error("--CfUserTransferDelayMQListner fail. message is null");
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        long fromUserId = userTransferData.getFromUserId();
        long toUserId = userTransferData.getToUserId();
        Set<Integer> caseIdList = Sets.newHashSet();
        handelDonate(userTransferData, caseIdList);
        handelShare(userTransferData, caseIdList);
        if (CollectionUtils.isEmpty(caseIdList)) {
            log.info("CfUserTransferDelayMQListner user have help case ,userId:{}", userTransferData.getToUserId());
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        for (Integer caseId : caseIdList) {
            Collection<ScoredEntry<UserDonateInfo>> key = redissonHandler.zrangeByScore(getKey(caseId), 0, true, 1000000000, true);
            Iterator<ScoredEntry<UserDonateInfo>> iterator = key.iterator();
            List<UserDonateInfo> infoList = Lists.newArrayList();
            while (iterator.hasNext()) {
                ScoredEntry<UserDonateInfo> first = iterator.next();

                UserDonateInfo value = first.getValue();
                infoList.add(value);
            }
            //查看榜单中是否包含旧账号userid,
            boolean exist = infoList.stream()
                    .anyMatch(s -> s.getUserId() == fromUserId);
            if (exist){
                /**
                 *  是否存在新的账号userId的信息，
                 * 如果存在，直接删除旧账号的user信息，
                 * 如果不存在，先删掉旧账号的信息，在添加一个新账号信息
                 */
                boolean existNew = infoList.stream()
                        .anyMatch(s -> s.getUserId() == toUserId);
                Optional<UserDonateInfo> first = infoList.stream()
                        .filter(s -> s.getUserId() == fromUserId)
                        .findFirst();
                if (first.isEmpty()){
                    log.info("CfUserTransferDelayMQListner 没有旧账号信息,fromUserId:{},toUserid:{}，infoList:{}",fromUserId,toUserId,infoList);
                    continue;
                }
                UserDonateInfo info = first.get();
                redissonHandler.zrem(getKey(caseId),info);
                if (!existNew){
                    info.setCipherUserId(shuidiCipher.encrypt(toUserId+""));
                    info.setUserId(toUserId);
                    double score = info.getAmount() + (double) info.getShareCount()/100;
                    redissonHandler.zadd(getKey(caseId),score,info);
                    log.info("CfUserTransferDelayMQListner 加入新账号信息，info:{}",info);
                }
                userLoveRankFace.shareSuccess(caseId,toUserId,"0","shareSourceId");

            }
        }
        log.info("CfUserTransferDelayMQListner success. info{}", JSON.toJSONString(userTransferData));
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    /**
     * 获取 最近一个月捐款的案例
     *
     * @param userTransferData
     * @param caseIdList
     */
    private void handelDonate(UserTransferData userTransferData, Set<Integer> caseIdList) {
        long toUserId = userTransferData.getToUserId();
        //查询当前用户捐款记录
        String now = DateUtil.formatDateTime(new Date());
        String lastMonth = DateUtil.formatDateTime(DateUtil.addMonth(new Date(), -1));
        List<CrowdfundingOrder> orderList = crowdfundingOrderShardingBiz.getOrderByUserIdAndRangeTime(toUserId, now, lastMonth);
        if (CollectionUtils.isEmpty(orderList)) {
            log.info("CfUserTransferDelayMQListner have not donate: userId:{}", toUserId);
            return;
        }
        boolean b = orderList.stream().anyMatch(s -> s.getValid() == 1);
        if (!b) {
            log.info("CfUserTransferDelayMQListner user refund ,have not donate :userId:{},orderList:{}", toUserId, JSON.toJSONString(orderList));
        }
        caseIdList.addAll(orderList.stream().filter(s -> s.getValid() == 1).map(CrowdfundingOrder::getCrowdfundingId).collect(Collectors.toSet()));

    }

    /**
     * 获取 最近一个月分享的案例
     *
     * @param userTransferData
     * @param caseIdList
     */
    private void handelShare(UserTransferData userTransferData, Set<Integer> caseIdList) {
        long toUserId = userTransferData.getToUserId();
        //查询当前用户捐款记录
        Timestamp now = new Timestamp(System.currentTimeMillis());
        Timestamp lastMonth = new Timestamp(DateUtil.addMonth(new Date(), -1).getTime());
        List<CfInfoShareRecord> cfInfoShareRecords = cfInfoShareRecordShardingBiz.selByUserIds(Sets.newHashSet(toUserId), lastMonth, now);
        if (CollectionUtils.isEmpty(cfInfoShareRecords)) {
            log.info("CfUserTransferDelayMQListner have not share userId:{}", toUserId);
            return;
        }
        caseIdList.addAll(cfInfoShareRecords.stream().map(CfInfoShareRecord::getInfoId).collect(Collectors.toSet()));
        return;
    }

}
