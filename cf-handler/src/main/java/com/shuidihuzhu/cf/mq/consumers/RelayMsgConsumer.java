package com.shuidihuzhu.cf.mq.consumers;

import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.mq.message.RelayMsg;
import com.shuidihuzhu.cf.mq.producer.IRelayMsgProducer;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by wangsf on 18/7/11.
 */
@Service
@RocketMQListener(id = MQTagCons.CF_RELAY_MSG,
		tags = MQTagCons.CF_RELAY_MSG,
		topic = MQTopicCons.CF)
@Slf4j
public class RelayMsgConsumer  extends  BaseConsumerPrintReceiveLog implements MessageListener<RelayMsg> {

	@Autowired
	private IRelayMsgProducer relayMsgProducer;

	@Autowired(required = false)
	private Producer producer;

	@Override
	public ConsumeStatus consumeMessage(ConsumerMessage<RelayMsg> mqMessage) {
     log.info("RelayMsgConsumer consumeMessage");

//		printReceiveMqMessageLog(mqMessage);
//
//		RelayMsg relayMsg = mqMessage.getPayload();
//		if(relayMsg == null) {
//			return ConsumeStatus.CONSUME_SUCCESS;
//		}
//
//		long targetTime = relayMsg.getTargetTime();
//		long current = System.currentTimeMillis();
//		 //已经过时
//		if(current >= targetTime) {
//			log.info("msg expired; msg:{}", relayMsg);
//			return ConsumeStatus.CONSUME_SUCCESS;
//		}
//
//		DelayLevel delayLevel = this.relayMsgProducer.calculateDelayLevel(targetTime);
//		//仍然需要接力
//		if(delayLevel != null) {
//			this.relayMsgProducer.send(relayMsg.getPayload(), targetTime);
//		} else { //无需接力了, 发原始消息
//			this.producer.send(relayMsg.getPayload());
//		}

		return ConsumeStatus.CONSUME_SUCCESS;
	}

	@Override
	public int getOrder() {
		return 0;
	}
}
