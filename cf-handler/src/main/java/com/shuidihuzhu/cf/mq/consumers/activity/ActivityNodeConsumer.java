package com.shuidihuzhu.cf.mq.consumers.activity;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.activity.mq.CaseStatusChangeVo;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.mq.common.BaseMessageConsumer;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.*;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by sven on 2020/4/26.
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RocketMQListener(id = "DONATE_COOPERATE_CASE",
        tags = "DONATE_COOPERATE_CASE",
        group = "cf-api" + "DONATE_COOPERATE_CASE",
        topic = MQTopicCons.CF)
public class ActivityNodeConsumer extends BaseMessageConsumer<CaseStatusChangeVo>
        implements MessageListener<CaseStatusChangeVo> {

    @Autowired(required = false)
    private Producer producer;

    @Override
    protected boolean handle(ConsumerMessage<CaseStatusChangeVo> consumerMessage) {

        if(consumerMessage ==null || consumerMessage.getPayload() == null || consumerMessage.getPayload().getSourceStatus() != null){
            return true;
        }
        CaseStatusChangeVo payload = consumerMessage.getPayload();
        Message message = new Message(MQTopicCons.CF, "DONATE_COOPERATE_CASE_DELAY", "DONATE_COOPERATE_CASE_DELAY" + payload.getCaseId(), payload, DelayLevel.M10);
        MessageResult send = producer.send(message);
        log.info("ActivityNodeConsumer param:{},message:{},resp:{}", JSON.toJSONString(payload),message,send);

        return true;
    }

    @Override
    protected Logger getLogger() {
        return log;
    }
}
