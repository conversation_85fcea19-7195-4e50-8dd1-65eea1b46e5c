package com.shuidihuzhu.cf.mq.consumers.bigdata;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.stat.feign.stat.CfDetailAccessFriendClient;
import com.shuidihuzhu.cf.client.stat.model.stat.CfDetailAccessFriendNew;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2022/2/25 3:30 下午
 */
@Service
@Slf4j
@RefreshScope
@RocketMQListener(id = MQTagCons.STAT_DELAY_DETAIL_ACCESS_FRIEND,
        tags = MQTagCons.STAT_DELAY_DETAIL_ACCESS_FRIEND,
        topic = MQTopicCons.CF)
public class StatDetailAccessDelayFriendDataConsumer implements MessageListener<String> {

    @Autowired
    private CfDetailAccessFriendClient cfDetailAccessFriendClient;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<String> mqMessage) {

        if (mqMessage == null || mqMessage.getPayload() == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        log.info("StatDetailAccessDelayFriendDataConsumer start");
        String payload = mqMessage.getPayload();

        CfDetailAccessFriendNew data = null;
        try {
            data = JSON.parseObject(payload, CfDetailAccessFriendNew.class);
        } catch (Exception e) {
            log.error("StatDetailAccessFriendDataSyncConsumer error payload={}", payload, e);
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        RpcResult<Integer> rpcResult = cfDetailAccessFriendClient.insert(data);
        int res = Optional.ofNullable(rpcResult).filter(RpcResult::isSuccess).map(RpcResult::getData).orElse(0);
        log.info("StatDetailAccessFriendDataSyncConsumer res:{} data:{}", res, data);
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
