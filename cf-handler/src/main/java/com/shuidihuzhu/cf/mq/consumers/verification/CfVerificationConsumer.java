package com.shuidihuzhu.cf.mq.consumers.verification;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.event.CfVerificationEvent;
import com.shuidihuzhu.cf.model.crowdfunding.param.CrowdFundingVerificationParam;
import com.shuidihuzhu.cf.mq.consumers.BaseConsumerPrintReceiveLog;
import com.shuidihuzhu.cf.service.EventPublishService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RocketMQListener(id = MQTagCons.CF_VERIFICATION_MSG + "-realTime",
        tags = MQTagCons.CF_VERIFICATION_MSG,
        topic = MQTopicCons.CF,
        group = MQTagCons.CF_VERIFICATION_MSG + "-realTime")
public class CfVerificationConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<CrowdFundingVerificationParam> {
    @Autowired
    private EventPublishService publishService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CrowdFundingVerificationParam> mqMessage) {
        printReceiveMqMessageLog(mqMessage);
        log.info("CfVerificationConsumer, mqMessage.getPayload()={}", JSON.toJSONString(mqMessage));
        CrowdFundingVerificationParam msg = mqMessage.getPayload();
        if (null == msg || msg.getVerifyUserId() <= 0 || StringUtils.isEmpty(msg.getCrowdFundingInfoId())) {
            log.debug("CfVerificationConsumer, mqMessage.getPayload()={}", msg);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        publishService.publish(new CfVerificationEvent(this, msg.getVerifyUserId(), msg.getCrowdFundingInfoId()));
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
