package com.shuidihuzhu.cf.mq.consumers.activity;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Stopwatch;
import com.shuidihuzhu.cf.activity.feign.CaseCountFeignClient;
import com.shuidihuzhu.cf.activity.feign.CfActivityFeignClient;
import com.shuidihuzhu.cf.activity.model.DonateCooperateFeeVO;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingAttachmentBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.delegate.ICaseChannelDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingAttachmentVo;
import com.shuidihuzhu.cf.mq.consumers.BaseConsumerPrintReceiveLog;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Created by sven on 2020/4/22.
 *
 * <AUTHOR>
 */
@Service
@RocketMQListener(id = "DONATE_COOPERATE_CREATE", topic = MQTopicCons.CF,
        tags = MQTagCons.DONATE_COOPERATE_CREATE)
@Slf4j
@RefreshScope
public class DonateCooperateConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<CrowdfundingInfo> {

    @Value("${donate_cooperate.activity_id:20}")
    private long activityId;

    @Value("${donate_cooperate.cass_add.line:200000}")
    private int line;

    @Resource
    private CaseCountFeignClient caseCountFeignClient;

    @Resource
    private CrowdfundingInfoBiz crowdfundingInfoBiz;

    @Resource
    private ICaseChannelDelegate caseChannelDelegate;

    @Value("${activity.raiser-warmup.start-time:1588694400}")
    private long startTime;

    @Value("${activity.raiser-warmup.end-time:1589212800}")
    private long endTime;

    @Value("${activity.raiser-warmup.enable:false}")
    private boolean enable;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CrowdfundingInfo> mqMessage) {

//        long timestamp = System.currentTimeMillis() / 1000;
//
//        log.info("DonateCooperateConsumer {}", mqMessage.getPayload());
//
//        if(timestamp >= startTime && timestamp < endTime){
//            return ConsumeStatus.CONSUME_SUCCESS;
//        }
//
//        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(mqMessage.getPayload().getId());
//
//        log.info("DonateCooperateConsumer line {} {}", crowdfundingInfo.getAmount(), line);
//
//        long endTime = crowdfundingInfo.getEndTime().getTime();
//        if(endTime < System.currentTimeMillis()){
//            return ConsumeStatus.CONSUME_SUCCESS;
//        }
//
//        ICaseChannelDelegate.CaseChannelEnum caseChannelEnum = caseChannelDelegate.getCaseRaiseChannel(crowdfundingInfo.getId());
//
//        if(caseChannelEnum == ICaseChannelDelegate.CaseChannelEnum.OFFLINE && crowdfundingInfo.getAmount() < line){
//            return ConsumeStatus.CONSUME_SUCCESS;
//        }
//
//        RpcResult rpcResult = caseCountFeignClient.create(activityId, mqMessage.getPayload().getId());
//        log.info("caseCountFeignClient create {} {}", mqMessage.getPayload().getId(), rpcResult);

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
