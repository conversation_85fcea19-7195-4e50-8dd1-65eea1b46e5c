package com.shuidihuzhu.cf.mq.consumers.drawcash;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.activity.constants.ActivityIdConstants;
import com.shuidihuzhu.cf.activity.enums.ActivityTypeEnum;
import com.shuidihuzhu.cf.activity.enums.MutexActivityTypeEnum;
import com.shuidihuzhu.cf.activity.feign.CaseCountFeignClient;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationActionTypeEnum;
import com.shuidihuzhu.cf.client.ugc.service.CommonOperationRecordClient;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.delegate.ICfGrowthtoolDelegate;
import com.shuidihuzhu.cf.enhancer.mq.BaseMessageConsumer;
import com.shuidihuzhu.cf.enums.NoHandlingFeeEnum;
import com.shuidihuzhu.cf.finance.model.CfOrderRefundSuccessModel;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.charity.client.message.CaseJoinActivityMsg;
import com.shuidihuzhu.charity.client.message.MqTagCons;
import com.shuidihuzhu.charity.client.message.MqTopicCons;
import com.shuidihuzhu.client.cf.growthtool.model.BdCrmVolunteerOrgnizationSimpleModel;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 免手续费申请成功
 * <AUTHOR>
 */
@Slf4j
@Service
@RocketMQListener(id = CfClientMQTagCons.FEE_FREE_SEND_MQ,
        tags = CfClientMQTagCons.FEE_FREE_SEND_MQ,
        group = "cf-api" + CfClientMQTagCons.FEE_FREE_SEND_MQ,
        topic = MQTopicCons.CF)
public class ApplyForFeeFreeSuccessConsumer
        extends BaseMessageConsumer<CfInfoExt>
        implements MessageListener<CfInfoExt> {

    @Autowired
    private CommonOperationRecordClient commonOperationRecordClient;
    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired(required = false)
    private Producer producer;


    @Override
    protected boolean handle(ConsumerMessage<CfInfoExt> consumerMessage) {
        CfInfoExt model = consumerMessage.getPayload();
        if (model == null) {
            log.error("ApplyForFeeFreeSuccessConsumer ext null");
            return true;
        }
        int caseId = model.getCaseId();
        int noHandlingFee = model.getNoHandlingFee();

        // 地区免手续费不参与互斥
        if (noHandlingFee == NoHandlingFeeEnum.AREA_FEE_FREE.getValue()) {
            return true;
        }

        // 申请免手续费成功必须写入记录
        commonOperationRecordClient.create()
                .buildBasicPlatform(caseId, 0, OperationActionTypeEnum.ACTIVITY_MUTEX)
                .buildRemark(MutexActivityTypeEnum.DRAW_CASH_FEE_FREE.getName())
                .buildExtValue("mutexActivityType", MutexActivityTypeEnum.DRAW_CASH_FEE_FREE.getValue())
                .buildExtValue("noHandlingFee", noHandlingFee)
                .save();
        this.sendJoinActivityManage(caseId);
        return true;
    }

    /**
     * 发送加入支付通道补贴消息
     * @param caseId
     */
    private void sendJoinActivityManage(int caseId) {
        CrowdfundingInfo caseInfoData = crowdfundingInfoBiz.getFundingInfoById(caseId);
        if (caseInfoData == null){
            log.info("sendJoinActivityManage caseId:{}",caseId);
            return;
        }
        CaseJoinActivityMsg msg = new CaseJoinActivityMsg();
        msg.setEndTime(caseInfoData.getEndTime());
        //案例结束直接返回
        if (caseInfoData.getEndTime().before(new Date())) {
            return;
        }
        msg.setStartTime(new Date());
        msg.setActivityId(ActivityIdConstants.PAY_SUBSIDY_CHANNEL_ID);
        msg.setCaseId(caseId);
        msg.setSubsidyAmount(0);
        msg.setMoney(0);
        msg.setCaseLimitCount(ActivityIdConstants.PAY_SUBSIDY_CHANNEL_ID);
        msg.setDataMap(Maps.newHashMap());
        MessageResult messageResult = producer.send(new Message<>(MqTopicCons.CF, MqTagCons.CASE_DONATION_SUBSIDY_SYN_TAG, MqTagCons.CASE_DONATION_SUBSIDY_SYN_TAG + msg.getActivityId() + "_" + caseId, msg));
        log.info("sendJoinActivityManage send msg: {}, result= {}", msg, JSON.toJSONString(messageResult));
    }

    @Override
    protected Logger getLogger() {
        return log;
    }
}
