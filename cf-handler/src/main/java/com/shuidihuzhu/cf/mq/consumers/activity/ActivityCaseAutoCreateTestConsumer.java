package com.shuidihuzhu.cf.mq.consumers.activity;

import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.mq.common.BaseMessageConsumer;
import com.shuidihuzhu.cf.mq.payload.ActivityCaseAutoCreateTestPayload;
import com.shuidihuzhu.cf.service.activity.subsidy.ActivityJoinService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RocketMQListener(id = MQTagCons.ACTIVITY_CASE_AUTO_CREATE_TEST,
        tags = MQTagCons.ACTIVITY_CASE_AUTO_CREATE_TEST,
        group = "cf-api" + MQTagCons.ACTIVITY_CASE_AUTO_CREATE_TEST,
        topic = MQTopicCons.CF)
public class ActivityCaseAutoCreateTestConsumer
        extends BaseMessageConsumer<ActivityCaseAutoCreateTestPayload>
        implements MessageListener<ActivityCaseAutoCreateTestPayload>{

    @Autowired
    private ActivityJoinService activityJoinService;

    @Override
    protected boolean handle(ConsumerMessage<ActivityCaseAutoCreateTestPayload> consumerMessage) {
        ActivityCaseAutoCreateTestPayload payload = consumerMessage.getPayload();
        if (payload == null) {
            log.error("payload null");
            return true;
        }
        activityJoinService.joinActivity(payload);
        return true;
    }

    @Override
    protected Logger getLogger() {
        return log;
    }
}
