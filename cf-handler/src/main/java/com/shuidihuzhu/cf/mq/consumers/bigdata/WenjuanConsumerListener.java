package com.shuidihuzhu.cf.mq.consumers.bigdata;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.shuidihuzhu.client.dataservice.pgwd.v1.dto.SdQuestionAnswerDTO;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.redisson.jcache.configuration.RedissonConfiguration;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Set;

@Slf4j
@RefreshScope
@Service
@RocketMQListener(topic = "SD_DATA_COMMON_TOPIC", id = "WenjuanConsumerListener", tags = "QS_ANSWER")
public class WenjuanConsumerListener implements MessageListener<SdQuestionAnswerDTO> {

    @Resource(name = "cfApiRedisClusterHandler")
    private RedissonHandler redissonHandler;

    private final static String BIZ = "cf";

    private Set<Integer> doctorOneQuestionSet = Sets.newHashSet(4432,4431,4380,4420,4447,4433,4448,4449);
    private Set<Integer> doctorTwoQuestionSet = Sets.newHashSet(4360,4498,4500,4501,4502,4503,4516,4517,4518,4519,4520,4521);
    private final static String PREFIX_QUEST_ID = "q_";

    // 1 week
    private final static long EXPIRE_TIME = 7 * 24 * 60 *  60 * 1000;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<SdQuestionAnswerDTO> mqMessage) {
        SdQuestionAnswerDTO answerDTO = mqMessage.getPayload();

        log.info("visit答案:"+ JSON.toJSONString(answerDTO));

        if(null == answerDTO){
            log.warn("visit答案 is mull");
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        if (!BIZ.equals(answerDTO.getBiz()) || (
                !doctorOneQuestionSet.contains(answerDTO.getQuestionnaireId()) &&
                !doctorTwoQuestionSet.contains(answerDTO.getQuestionnaireId()) ) ) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        log.info("WenjuanConsumerListener {} {}", answerDTO.getQuestionnaireId(), answerDTO.getUserId());

        if(doctorOneQuestionSet.contains(answerDTO.getQuestionnaireId())) {
            redissonHandler.setNX(PREFIX_QUEST_ID + "ONE"+ "_" + answerDTO.getUserId(), "1", EXPIRE_TIME);
        } else if(doctorTwoQuestionSet.contains(answerDTO.getQuestionnaireId())) {
            redissonHandler.setNX(PREFIX_QUEST_ID + "TWO" + "_" + answerDTO.getUserId(), "1", EXPIRE_TIME);
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
