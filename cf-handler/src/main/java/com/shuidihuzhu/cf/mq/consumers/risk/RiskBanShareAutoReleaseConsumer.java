package com.shuidihuzhu.cf.mq.consumers.risk;

import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enhancer.mq.MaliBaseMQConsumer;
import com.shuidihuzhu.cf.risk.RiskShareService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 **/
@Slf4j
@Service
@RocketMQListener(id = MQTagCons.CF_RISK_BAN_SHARE_AUTO_RELEASE,
        tags = MQTagCons.CF_RISK_BAN_SHARE_AUTO_RELEASE,
        group = "cf-api_risk_" + MQTagCons.CF_RISK_BAN_SHARE_AUTO_RELEASE,
        topic = MQTopicCons.CF
)
public class RiskBanShareAutoReleaseConsumer
        extends MaliBaseMQConsumer<Long>
        implements MessageListener<Long> {

    @Autowired
    private RiskShareService riskShareService;

    @Override
    protected boolean handle(ConsumerMessage<Long> consumerMessage) {
        Long userId = consumerMessage.getPayload();
        riskShareService.releaseBanShare(userId);
        return true;
    }

    @Override
    protected Logger getLogger() {
        return log;
    }
}
