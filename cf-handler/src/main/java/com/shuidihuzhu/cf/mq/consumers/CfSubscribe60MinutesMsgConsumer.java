package com.shuidihuzhu.cf.mq.consumers;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.mq.common.BaseMessageConsumer;
import com.shuidihuzhu.cf.service.msg.MsgClientV2Service;
import com.shuidihuzhu.client.baseservice.msg.v2.MsgClientV2;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackBdCrmFeignClient;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: wanghui
 * @time: 2019/8/19 4:19 PM
 * @description:
 * @param:  * @param null :
 * @return:  * @return : null
 */
@Service
@RocketMQListener(id = MQTagCons.SUBSCRIBE_60_MINUTES_MSG,
         tags = MQTagCons.SUBSCRIBE_60_MINUTES_MSG,
        group = "cf-" + MQTagCons.SUBSCRIBE_60_MINUTES_MSG + "-group",
        topic = MQTopicCons.CF)
@Slf4j
public class CfSubscribe60MinutesMsgConsumer extends BaseMessageConsumer<String> implements MessageListener<String> {

    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBizImpl;
    @Resource
    private CfClewtrackBdCrmFeignClient clewtrackBdCrmFeignClient;
    @Resource
    private MsgClientV2Service msgClientV2Service;

    @Override
    protected boolean handle(ConsumerMessage<String> consumerMessage) {
        String value = consumerMessage.getPayload();
        if (StringUtils.isEmpty(value)) {
            log.error(this.getClass().getSimpleName()+"getPayload is null :{}", consumerMessage);
            return true;
        }
        long userId = Long.valueOf(value.split("_")[0]);
        String volunteerUniqueCode = value.split("_")[1];
        if (StringUtils.isBlank(volunteerUniqueCode)){
            log.debug(this.getClass().getSimpleName()+"  volunteerUniqueCode is blank :{}",volunteerUniqueCode);
            return true;
        }
        //判读是否有在筹的案例
        List<CrowdfundingInfo> infoList = crowdfundingInfoBizImpl.getNoFinished(userId);
        if(CollectionUtils.isNotEmpty(infoList)){
            log.debug(this.getClass().getSimpleName()+"  有在筹案例 infoList :{}",infoList);
            //有在筹案例，返回
            return true;
        }
        //没有在筹案例，判断线索是否近30天已经流转给任意bd
        com.shuidihuzhu.common.web.model.Response<Integer> clewInfos = clewtrackBdCrmFeignClient.getClewInfosbyUserId(userId,volunteerUniqueCode);
        //调用失败
        if (clewInfos==null || clewInfos.notOk() || clewInfos.getData()==null || clewInfos.getData()>0){
            log.debug(this.getClass().getSimpleName()+"  调用失败 或 30天内有流转给任意bd clewInfos :{}", JSON.toJSONString(clewInfos));
            return true;
        }

        String modelNum0 = "1985model2";
        Map<Long, Map<Integer, String>> msgMap = Maps.newHashMap();
        HashMap<Integer, String> params = Maps.newHashMap();
        params.put(1, volunteerUniqueCode);
        msgMap.put(userId, params);

        msgClientV2Service.sendWxParamsMsg(modelNum0, msgMap);
        return true;
    }

    @Override
    protected Logger getLogger() {
        return log;
    }

}
