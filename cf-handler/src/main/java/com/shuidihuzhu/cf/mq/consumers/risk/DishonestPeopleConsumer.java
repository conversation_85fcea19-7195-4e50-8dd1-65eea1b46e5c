package com.shuidihuzhu.cf.mq.consumers.risk;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enhancer.exception.ServiceResponseException;
import com.shuidihuzhu.cf.enhancer.mq.BaseMessageConsumer;
import com.shuidihuzhu.cf.mq.payload.DishonestPeoplePayload;
import com.shuidihuzhu.cf.risk.Dishonest;
import com.shuidihuzhu.cf.risk.IDishonestService;
import com.shuidihuzhu.cf.risk.client.admin.hit.RiskStrategyHitClient;
import com.shuidihuzhu.cf.risk.client.risk.BrokenPromisesClientV2;
import com.shuidihuzhu.cf.risk.model.admin.hit.StrategyCallResultDto;
import com.shuidihuzhu.cf.risk.model.enums.hit.RiskStrategyCallResultEnum;
import com.shuidihuzhu.cf.risk.model.enums.hit.RiskStrategyEnum;
import com.shuidihuzhu.cf.risk.model.enums.hit.RiskStrategySecondEnum;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.admin.client.AdminReportClient;
import com.shuidihuzhu.client.cf.admin.model.AdminMarkReportParam;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.IdCardUtil;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: zhengqiu
 * @date: 2021-03-29 11:22
 **/
@Slf4j
@Service
@RocketMQListener(id = MQTagCons.DISHONEST_PEOPLE_HANDLE_MSG,
        tags = MQTagCons.DISHONEST_PEOPLE_HANDLE_MSG,
        topic = MQTopicCons.CF
)
public class DishonestPeopleConsumer extends BaseMessageConsumer<String> implements MessageListener<String> {

    @Resource
    private BrokenPromisesClientV2 brokenPromisesClientV2;

    @Autowired
    private IDishonestService dishonestService;

    @Autowired
    private RiskStrategyHitClient riskStrategyHitClient;

    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;

    @Autowired
    private AdminReportClient adminReportClient;

    @Autowired
    private ShuidiCipher shuidiCipher;

    @Override
    protected boolean handle(ConsumerMessage<String> consumerMessage) {
        log.info("dishonest people handle consumer message begin,mqMessage:{}", consumerMessage);
        try {
            String payload = consumerMessage.getPayload();
            DishonestPeoplePayload dishonest = JSON.parseObject(payload, DishonestPeoplePayload.class);
            if (dishonest.getFlag() == 0 || dishonest.getFlag() == 1) {
                // 收款人为失信人失败处理策略
                handleDishonest(dishonest);
            }
            log.info("dishonest people handle consumer message success,caseId:{}", dishonest.getCaseId());
        } catch (ServiceResponseException e) {
            log.info("dishonest people handle consumer message fail retry : ", e);
            return false;
        } catch (Exception e) {
            log.error("dishonest people handle consumer message fail: ", e);
        }
        return true;
    }

    @Override
    protected int maxRetryCount() {
        return 5;
    }

    @Override
    protected Logger getLogger() {
        return log;
    }

    private void handleDishonest(DishonestPeoplePayload payload) {
        String username = payload.getCollectionName();
        String idCard = shuidiCipher.decrypt(payload.getCollectionIdCard());

        if (StringUtils.isBlank(username) || StringUtils.isBlank(idCard) || IdCardUtil.illegal(idCard)) {
            log.info("username or idCard error payload {}", payload);
            return ;
        }
        
        Response<Boolean> response = brokenPromisesClientV2.checkInfo(username, idCard);
        int caseId = payload.getCaseId();
        if (response == null || response.notOk()) {
            log.info("远程调用查看是否是被失信人接口失败");
            throw ServiceResponseException.create("失信人接口失败重试");
        }
        Boolean isDishonest = response.getData();
        if (!isDishonest) {
            return;
        }

        //查询该案例的处理中的举报工单
        List<Integer> result = new ArrayList<>();
        result.add(HandleResultEnum.undoing.getType());
        result.add(HandleResultEnum.doing.getType());
        result.add(HandleResultEnum.later_doing.getType());
        Response<List<WorkOrderVO>> workOrderRes = cfWorkOrderClient.listByCaseIdAndTypeAndResult(caseId, WorkOrderType.REPORT_TYPES, result);
        log.info("该案例的处理中的举报工单workOrderRes:{},caseId:{}", workOrderRes, caseId);
        if (ErrorCode.SUCCESS.getCode() != workOrderRes.getCode()) {
            log.error("查询该案例的最近一次举报工单失败,caseId:{}", caseId);
        }

        if (workOrderRes.getData() == null || workOrderRes.getData().size() == 0) {
            saveDishonestRecord(payload);
            saveCallResult(payload, RiskStrategyCallResultEnum.PAYEE_CALL_RESULT_No_REPORT.getCode());
            return;
        }
        saveCallResult(payload, RiskStrategyCallResultEnum.PAYEE_CALL_RESULT_REPORT.getCode());
        saveDishonestRecord(payload);

        // 新增举报条目
        AdminMarkReportParam param = new AdminMarkReportParam();
        param.setCaseId(caseId);
        // 9代表举报类型其他
        param.setReportTypes("9");
        param.setContent("收款人为失信被执行人");
        // ReportChannel 前端维护
        param.setReportChannel(16);
        param.setReporterName("系统");
        Response<Map<String, Integer>> mapResponse = adminReportClient.markReport(param);
        if (mapResponse.getData() == null || mapResponse.notOk()) {
            log.error("插入工单远程调用失败");
        }
    }


    private void saveDishonestRecord(DishonestPeoplePayload payload) {
        Dishonest dishonest = new Dishonest();
        dishonest.setUserId(payload.getUserId());
        dishonest.setUsername(payload.getCollectionName());
        dishonest.setUserType(1);
        dishonest.setUserIdCard(payload.getCollectionIdCard());
        dishonest.setDishonest(1);
        dishonest.setCaseId(payload.getCaseId());
        dishonestService.insert(dishonest);
    }

    private void saveCallResult(DishonestPeoplePayload payload, int callResultCode) {
        StrategyCallResultDto callResultDto = new StrategyCallResultDto();
        callResultDto.setCaseId(payload.getCaseId());
        callResultDto.setRiskStrategy(RiskStrategyEnum.DISHONEST_PEOPLE_DISCERN.getCode());
        callResultDto.setSecondStrategy(RiskStrategySecondEnum.PAYEE_DISHONEST_DISCERN.getCode());
        callResultDto.setCallResult(callResultCode);
        callResultDto.setCallTime(DateUtil.getCurrentDate());
        callResultDto.setUserName(payload.getCollectionName());
        callResultDto.setUserIdCard(payload.getCollectionIdCard());
        riskStrategyHitClient.saveCallResult(callResultDto);
    }


}
