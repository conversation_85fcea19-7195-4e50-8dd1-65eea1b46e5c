package com.shuidihuzhu.cf.mq.consumers;

import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 保险复购消息转发者
 * <p>
 * https://wiki.shuiditech.com/pages/viewpage.action?pageId=264209733
 * <p>
 * 本类功能：接收保发的MQ，校验 payload 并调用鹰眼实验。如果通过，则发送延迟队列
 *
 * <AUTHOR>
 * @date 2019/07/31
 */
@Service("cfInsurancePurchaseGuideForwardConsumer")
@RocketMQListener(id = MQTagCons.ORDER_2_CF_SALE_NEW,
        tags = MQTagCons.ORDER_2_CF_SALE_NEW,
        group = "cf-" + MQTagCons.ORDER_2_CF_SALE_NEW + "-group",
        topic = MQTopicCons.INSURANCE_ORDER)
@Slf4j
public class CfInsurancePurchaseGuideForwardConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<Long> {

    @Resource(name = "applicationServiceImpl")
    private ApplicationService applicationService;
    @Autowired(required = false)
    private Producer producer;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<Long> mqMessage) {
        this.printReceiveMqMessageLog(mqMessage);
        // 校验 payload
        if (mqMessage == null || mqMessage.getPayload() == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        // 鹰眼判断是否发送
        long userId = mqMessage.getPayload();

        // 发送延迟队列
        String mqKeys = MQTagCons.CF_INSURANCE_PURCHASE_GUIDE_MSG + "_" + userId + "_" + System.currentTimeMillis();
        DateTime now = new DateTime();
        long expectTime = 0;
        //第二天9点左右发送
        if (applicationService.isProduction()) {
            expectTime = now.plusDays(1).withHourOfDay(9).withMinuteOfHour(RandomUtils.nextInt(0, 6)).getMillis();
        } else {
            expectTime = now.plusDays(0).plusHours(0).plusMinutes(2).getMillis();
        }
        Message message = Message.ofSchedule(MQTopicCons.CF, MQTagCons.CF_INSURANCE_PURCHASE_GUIDE_MSG, mqKeys, userId, expectTime / 1000);
        MessageResult result = producer.send(message);
        log.info("保险复购消息延迟队列转发。mqKeys={}, result={}", userId, result);
        return ConsumeStatus.CONSUME_SUCCESS;
    }

}
