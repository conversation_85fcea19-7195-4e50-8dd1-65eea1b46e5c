package com.shuidihuzhu.cf.mq.consumers;

import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingApproveBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.cf.risk.model.DrawCashRiskMsgModel;
import com.shuidihuzhu.client.constant.MQTagCons;
import com.shuidihuzhu.client.constant.MQTopicCons;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @time 2019/3/19 下午7:22
 * @desc
 */
@Service
@Slf4j
@RocketMQListener(id = MQTagCons.CF_RISK_DRAW_CASH_REMARK_MSG,
        tags = MQTagCons.CF_RISK_DRAW_CASH_REMARK_MSG,
        topic = MQTopicCons.CF,
        group = "DRAW_CASH_REMARK_" + MQTagCons.CF_RISK_DRAW_CASH_REMARK_MSG)
public class CfDrawCashRiskRemarkConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<DrawCashRiskMsgModel> {

    @Resource
    private CrowdfundingInfoBiz crowdfundingInfoBiz;

    @Resource
    private CrowdfundingApproveBiz crowdfundingApproveBiz;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<DrawCashRiskMsgModel> mqMessage) {
        printReceiveMqMessageLog(mqMessage);

        DrawCashRiskMsgModel model = mqMessage.getPayload();

        log.info("CF_RISK_DRAW_CASH_REMARK_MSG pair:{}", model);
        if(null == model || StringUtils.isEmpty(model.getInfoUuid()) || StringUtils.isEmpty(model.getRemark())){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        String infoUuid = model.getInfoUuid();
        String remark = model.getRemark();

        CrowdfundingInfo fundingInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        crowdfundingApproveBiz.addSystemApprove(fundingInfo, "", remark);
        log.info("CF_RISK_DRAW_CASH_REMARK_MSG id:{}", fundingInfo.getId());

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
