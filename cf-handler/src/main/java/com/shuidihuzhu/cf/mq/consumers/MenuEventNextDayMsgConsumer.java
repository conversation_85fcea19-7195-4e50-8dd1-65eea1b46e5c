package com.shuidihuzhu.cf.mq.consumers;

import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.CfUserOrderCountService;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.model.crowdfunding.CfOperatingRecord;
import com.shuidihuzhu.cf.service.msg.SdAdMsgClientService;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;


/**
 * @package: com.shuidihuzhu.cf.mq.consumers
 * @Author: liujiawei
 * @Date: 2018/10/16  20:36
 */
@Slf4j
@Service
@RocketMQListener(id = MQTagCons.MUTUAL_WX_MENU_MSG,
        group = "cf-api-" + MQTagCons.MUTUAL_WX_MENU_MSG + "-group",
        tags = MQTagCons.MUTUAL_WX_MENU_MSG,
        topic = MQTopicCons.CF)
@RefreshScope
public class MenuEventNextDayMsgConsumer implements MessageListener<CfOperatingRecord> {

    @Autowired
    private UserInfoDelegate userInfoDelegate;
    @Autowired
    private SdAdMsgClientService sdAdMsgClientService;
    @Autowired
    private CfUserOrderCountService cfUserOrderCountService;
    @Resource(name = "cfRedissonHandler")
    private RedissonHandler redissonHandler;

    @Value("${subscribe.msg-menu24h.ad-position-id:posad15434753445609201}")
    private String adPositionId;
    @Value("${subscribe.msg.nickname:水滴用户}")
    private String defaulNickName;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CfOperatingRecord> mqMessage) {
        log.info("（菜单交互消费端)MUTUAL_WX_MENU_MSG: {}", mqMessage);

        CfOperatingRecord cfOperatingRecord = mqMessage.getPayload();
        long userId = cfOperatingRecord.getUserId();

        //已确认全量走广告系统
        sendMenuClickMsgForAd(userId);

        return ConsumeStatus.CONSUME_SUCCESS;
    }

    /**
     *     广告系统发消息
      */
    public void sendMenuClickMsgForAd(long userId) {
        try {
            UserInfoModel userInfoModel = userInfoDelegate.getUserInfoByUserId(userId);
            if (userInfoModel == null || StringUtils.isBlank(userInfoModel.getNickname())) {
                log.warn("sendMenuClickMsgForAd userThirdModel not found. userId:{} ", userId);
                return;
            }
            int orderCount = cfUserOrderCountService.getOrderCount(userId);
            String count = orderCount == 0? "多" :String.valueOf(orderCount);
            String nickname = StringUtils.isBlank(userInfoModel.getNickname()) ? defaulNickName : StringUtils.trimToEmpty(userInfoModel.getNickname());
            log.info("sendMenuClickMsgForAd params adPositionId:{} userId:{} nickname:{} orderCount:{} ", adPositionId, userId, nickname, orderCount);
            if ("posad15434753445609201".equals(adPositionId)) {
                // check 当天是否已发送过1950
                String key = "InsurancePurchaseGuide_" + userId + "_" + DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.now());
                Boolean hasSent = redissonHandler.get(key, Boolean.class);
                if (hasSent != null && hasSent) {
                    return;
                }
            }
            sdAdMsgClientService.sendAdMsg(adPositionId, userId, "", 0, "1", nickname , "5", count);
        } catch (Exception e) {
            log.warn("（菜单交互消费端)客服消息发送失败 ERROR:{}", e);
        }
    }

}
