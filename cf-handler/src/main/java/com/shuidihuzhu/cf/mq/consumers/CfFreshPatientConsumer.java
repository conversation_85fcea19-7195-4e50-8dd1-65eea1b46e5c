package com.shuidihuzhu.cf.mq.consumers;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.CfFirstApproveBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfPatientCaseInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.material.CfPatientCardWarmService;
import com.shuidihuzhu.cf.biz.crowdfunding.material.IMaterialReadService;
import com.shuidihuzhu.cf.client.material.model.CfPatientBaseInfoVo;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enums.crowdfunding.UserIdentityType;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.mq.model.CfPatientChangeModel;
import com.shuidihuzhu.cf.util.crowdfunding.CfIdCardUtil;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageQueue;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RocketMQListener(id = MQTagCons.ALL_CF_PATIENT_CHANGE_INFO,
        group = "cf-api-" + MQTagCons.ALL_CF_PATIENT_CHANGE_INFO + "-group",
        tags = MQTagCons.ALL_CF_PATIENT_CHANGE_INFO,
        topic = MQTopicCons.CF)
public class CfFreshPatientConsumer implements MessageListener<CfPatientChangeModel> {

    @Autowired
    private IMaterialReadService materialReadService;
    @Autowired
    private CfFirstApproveBiz firstApproveBiz;
    @Autowired
    private CfPatientCaseInfoBiz caseInfoBiz;
    @Autowired
    private Producer producer;
    @Autowired
    private CfPatientCardWarmService patientWarmService;
    @Autowired
    private ShuidiCipher shuidiCipher;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;



    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CfPatientChangeModel> mqMessage) {

        int caseId = mqMessage.getPayload().getCaseId();

        CfFirsApproveMaterial material = firstApproveBiz.getByInfoId(caseId);
        if (material == null || (StringUtils.isBlank(material.getPatientCryptoIdcard()) && StringUtils.isBlank(material.getPatientBornCard()))) {
            log.info("不能找到数据、案例是出生证、身份证均为空,caseId:{}", caseId);
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        String patientIdCard = StringUtils.isNotBlank(material.getPatientCryptoIdcard()) ? CfIdCardUtil.convertLastCharUpper(shuidiCipher.decrypt(material.getPatientCryptoIdcard())) : material.getPatientBornCard();

        List<CfPatientBaseInfoVo.PatientResult> patientResultList = materialReadService.queryPatientVoByIdCardsRealTime(Lists.newArrayList(patientIdCard));

        if (CollectionUtils.isEmpty(patientResultList) || CollectionUtils.isEmpty(patientResultList.get(0).getCaseList())) {
            log.error("不能找到当前身份证发起的案例. caseId:{} patientIdcard:{}", caseId, patientIdCard);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        String patientCaseInfo = JSON.toJSONString(patientResultList.get(0).getCaseList());
        log.info("更新患者的案例发起情况 caseId:{} patientIdCard:{} patientCaseInfo:{}",
                caseId, patientIdCard, patientCaseInfo);

        String cryptoIdCard = oldShuidiCipher.aesEncrypt(patientIdCard);
        caseInfoBiz.addOrUpdate(cryptoIdCard, patientCaseInfo);

        noticeRiskGroupMsg(patientResultList.get(0), material);
        sendIdcardChangeBoardCast(cryptoIdCard);
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private void noticeRiskGroupMsg(CfPatientBaseInfoVo.PatientResult patientResult, CfFirsApproveMaterial material) {
        if (material.getPatientIdType() == UserIdentityType.birth.getCode()) {
            return;
        }
        Message noticeMsg = new Message(MQTopicCons.CF, MQTagCons.CF_PATIENT_INFO_NOTICE_RISK_BUSINESS,
                "" + System.currentTimeMillis(), patientResult);

        MessageResult result = producer.send(noticeMsg, new MessageQueueSelector() {
            @Override
            public MessageQueue select(List<MessageQueue> messageQueues, Message message) {
                int hashCode = ((CfPatientBaseInfoVo.PatientResult)message.getPayload())
                        .getPatientIdCard().hashCode();
                return messageQueues.get(Math.abs(hashCode) % messageQueues.size());
            }
        });

        log.info("通知风控中台消息 msg:{} result:{}", noticeMsg, result);
    }

    private void sendIdcardChangeBoardCast(String cryptoIdcard) {
        // 刷新下redis 数据
        patientWarmService.freshPatientIdCardToRemote(cryptoIdcard);

        Message msg = new Message(MQTopicCons.CF, MQTagCons.CF_PATIENT_CHANGE_INFO_BROADCASTING,
                "" + System.currentTimeMillis(), cryptoIdcard);

        MessageResult result = producer.send(msg);
        log.info("更新本地的身份证缓存数据. msg:{} result:{}", msg, result);
    }


}
