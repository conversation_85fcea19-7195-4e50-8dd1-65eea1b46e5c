package com.shuidihuzhu.cf.mq.consumers.audit;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingAuthorBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingOrderBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAuthor;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.mq.consumers.BaseConsumerPrintReceiveLog;
import com.shuidihuzhu.cf.service.msg.MsgClientV2Service;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RefreshScope
@RocketMQListener(id = MQTagCons.CF_AUDIT_PASS_TO_DONOR_MSG,
        tags = MQTagCons.CF_AUDIT_PASS_TO_DONOR_MSG,
        topic = MQTopicCons.CF,
        group = MQTagCons.CF_AUDIT_PASS_TO_DONOR_MSG)
public class CfAuditPassToDonorConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<CrowdfundingInfo> {
    @Autowired
    private CrowdfundingAuthorBiz crowdfundingAuthorBiz;
    @Autowired
    private CrowdfundingOrderBiz crowdfundingOrderBiz;
    @Autowired
    private UserInfoDelegate userInfoDelegate;
    @Autowired
    private MsgClientV2Service msgClientV2Service;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CrowdfundingInfo> mqMessage) {
        printReceiveMqMessageLog(mqMessage);
        if (Objects.isNull(mqMessage) || Objects.isNull(mqMessage.getPayload())) {
            log.debug("CfAuditPassToDonorConsumer mqMessage={}", mqMessage);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        CrowdfundingInfo crowdfundingInfo = mqMessage.getPayload();
        this.sendCfAuditPassToDonor(crowdfundingInfo);
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private void sendCfAuditPassToDonor(CrowdfundingInfo crowdfundingInfo) {
        int caseId = crowdfundingInfo.getId();
        String infoUuid = crowdfundingInfo.getInfoId();
        List<CrowdfundingOrder> crowdfundingOrders = Lists.newArrayList();
        int size = 300;
        for (int i = 0; ; i += size) {
            List<CrowdfundingOrder> list = this.crowdfundingOrderBiz.getListByInfoId(caseId, i, size);
            if (CollectionUtils.isNotEmpty(list)) {
                crowdfundingOrders.addAll(list);
            }
            if (list.size() < size) {
                break;
            }
        }

        List<Long> userIdList = Lists.newArrayList();
        DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String cfLastModifiedDate = sdf.format(System.currentTimeMillis());
        for (CrowdfundingOrder order : crowdfundingOrders) {
            String ctime = sdf.format(order.getCtime());
            if (cfLastModifiedDate.compareTo(ctime) > 0) {
                userIdList.add(order.getUserId());
            }
        }
        if (CollectionUtils.isEmpty(userIdList)) {
            return;
        }

        String modelNum = "IFI0383";
        CrowdfundingAuthor crowdfundingAuthor = crowdfundingAuthorBiz.get(caseId);
        String patientName = "患者";
        if (Objects.nonNull(crowdfundingAuthor) && StringUtils.isNotEmpty(crowdfundingAuthor.getName())) {
            patientName = crowdfundingAuthor.getName();
            if (StringUtils.length(patientName) > 4) {
                patientName = StringUtils.substring(patientName, 0, 3) + "…";
            }
        }
        Map<Long, Map<Integer, String>> paramsMap = Maps.newHashMap();
        String finalPatientName = patientName;
        Lists.partition(userIdList, 500).forEach(v -> {
            List<UserInfoModel> userInfoModelList = userInfoDelegate.getUserInfoByUserIdBatch(v);
            Map<Long, String> map = userInfoModelList.stream().collect(Collectors.toMap(UserInfoModel::getUserId, UserInfoModel::getNickname, (o1, o2) -> o2));
            v.forEach(y -> {
                paramsMap.put(y, this.buildParamsMap(StringUtils.defaultString(map.get(y), "爱心人士"), finalPatientName, infoUuid));
            });
        });
        msgClientV2Service.sendWxParamsMsg(modelNum, paramsMap);
    }

    public Map<Integer, String> buildParamsMap(String nickname, String patientName, String infoUuid) {
        Map<Integer, String> params = Maps.newHashMap();
        params.put(1, nickname);
        params.put(2, patientName);
        params.put(3, DateUtil.formatDate(new Date()));
        params.put(4, infoUuid);
        return params;
    }
}
