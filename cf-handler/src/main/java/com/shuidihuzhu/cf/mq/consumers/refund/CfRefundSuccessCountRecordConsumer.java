package com.shuidihuzhu.cf.mq.consumers.refund;

import com.shuidihuzhu.cf.biz.crowdfunding.CfInfoStatBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfUserCaseBaseStatService;
import com.shuidihuzhu.cf.biz.crowdfunding.CfUserCaseRefundStatService;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingOrderShardingBiz;
import com.shuidihuzhu.cf.biz.pay.service.PayCallbackService;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingOrderShardingCrowdfundingIdNewDao;
import com.shuidihuzhu.cf.finance.model.CfDonorRefundApply;
import com.shuidihuzhu.cf.finance.mq.FinanceMQTagCons;
import com.shuidihuzhu.cf.model.crowdfunding.CfUserCaseBaseStatDO;
import com.shuidihuzhu.cf.model.crowdfunding.CfUserCaseRefundStatDO;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.mq.consumers.BaseConsumerPrintReceiveLog;
import com.shuidihuzhu.cf.service.crowdfunding.CfCapitalService;
import com.shuidihuzhu.cf.service.crowdfunding.CfCaseDonatorService;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.shuidihuzhu.cf.constants.AsyncPoolConstants.GET_VISITCONFIG_POOL;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RefreshScope
@RocketMQListener(id = "CfRefundSuccessCountRecordConsumer",
        tags = FinanceMQTagCons.CF_DONOR_REFUND_SUCCESS_MSG,
        topic = MQTopicCons.CF,
        group = "Cf-ConsumerCountRecordGroup_" + FinanceMQTagCons.CF_DONOR_REFUND_SUCCESS_MSG)
public class CfRefundSuccessCountRecordConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<CfDonorRefundApply> {

    @Resource(name = "cfRedissonHandler")
    private RedissonHandler cfRedissonHandler;
    @Resource
    private CfInfoStatBiz cfInfoStatBiz;
    @Resource
    private CrowdfundingOrderShardingCrowdfundingIdNewDao crowdfundingOrderShardingCrowdfundingIdNewDao;
    @Resource
    private CfCaseDonatorService cfCaseDonatorService;
    @Resource
    private CfUserCaseBaseStatService cfUserCaseBaseStatService;

    @Resource
    private CfUserCaseRefundStatService cfUserCaseRefundStatService;
    @Autowired(required = false)
    private Producer producer;

    private static final String TAG = "退款次数";

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CfDonorRefundApply> mqMessage) {
        printReceiveMqMessageLog("CfRefundSuccessCountRecordConsumer accept msg success", mqMessage);
        CfDonorRefundApply cfDonorRefundApply = mqMessage.getPayload();
        if (cfDonorRefundApply == null) {
            log.warn("{} accept msg is null",TAG);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        int caseId = cfDonorRefundApply.getInfoId();
        long orderId = cfDonorRefundApply.getOrderId();
        long userId = cfDonorRefundApply.getUserId();
        log.info("{} 参数内容 caseId:{},orderId:{},userId:{}",TAG, caseId, orderId, userId);
        String tryLock = null;
        String lockKey = "CfRefundSuccessCountRecordConsumer_" + orderId;
        try {
            tryLock = cfRedissonHandler.tryLock(lockKey, 0, TimeUnit.HOURS.toMillis(1));
            // 未获取到锁
            if (StringUtils.isBlank(tryLock)) {
                log.warn("{} repeat refund success msg caseId:{},orderId:{}", TAG, caseId, orderId);
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            // 修改订单单笔退款状态
            int result = crowdfundingOrderShardingCrowdfundingIdNewDao.updateSingleRefund(caseId, orderId);
            // 单笔退款成功 延时5s发送更新帮助人数redis和cf_info_stat表的mq
            producer.send(new Message(MQTopicCons.CF, MQTagCons.REFUND_SUCCESS_TO_UPDATE,
                    MQTagCons.REFUND_SUCCESS_TO_UPDATE + orderId, cfDonorRefundApply));
            // 写库退款次数+1
            if (result > 0) {
                result = cfInfoStatBiz.incRefundCount(caseId);
            }
            if(result > 0){
                log.debug("{},成功 caseId:{},orderId:{} update refund count success", TAG, caseId, orderId);
            } else {
                log.error("{},失败 caseId:{},orderId:{} update refund count fail", TAG, caseId, orderId);
                return ConsumeStatus.RECONSUME_LATER;
            }
            try {
                updateUserCaseStatRefund(userId, caseId, cfDonorRefundApply.getAmountInFen());
            } catch (Exception e) {
                log.info("updateUserCaseStatRefund ex {} {}", caseId, userId, e);
            }
            return ConsumeStatus.CONSUME_SUCCESS;
        } catch (Exception e){
            log.error("{} error, msg:{}", TAG, mqMessage, e);
            try {
                // 解锁
                if (StringUtils.isNotBlank(tryLock)) {
                    cfRedissonHandler.unLock(lockKey, tryLock);
                }
            } catch (Exception ex) {
                log.error("{} caseId:{},orderId:{} 解锁失败", TAG, caseId, orderId, ex);
            }
            return ConsumeStatus.RECONSUME_LATER;
        }
    }

    private void updateUserCaseStatRefund(long userId, int infoId, int amountInFen) {
        CfUserCaseRefundStatDO cfUserCaseRefundStatDO = cfUserCaseRefundStatService.selectRefundByUserAndCaseId(userId, infoId);
        if (Objects.isNull(cfUserCaseRefundStatDO)) {
            cfUserCaseRefundStatDO = new CfUserCaseRefundStatDO();
            cfUserCaseRefundStatDO.setCaseId(infoId);
            cfUserCaseRefundStatDO.setUserId(userId);
            cfUserCaseRefundStatDO.setRefundAmount(amountInFen);
            cfUserCaseRefundStatService.insertRefundStat(cfUserCaseRefundStatDO);
            return;
        }
        int updateRefundAmount = cfUserCaseRefundStatService.updateRefundAmount(userId, infoId, amountInFen);
        if (updateRefundAmount == 0) {
            log.info("updateUserCaseStatRefund is fail {} {} {}", userId, infoId, amountInFen);
        }
    }
}
