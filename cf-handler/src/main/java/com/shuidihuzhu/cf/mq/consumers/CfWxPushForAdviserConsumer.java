package com.shuidihuzhu.cf.mq.consumers;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.account.model.service.OpenIdUserIdModel;
import com.shuidihuzhu.cf.biz.wx.Cf111WxEventRecordBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingInfoSlaveDao;
import com.shuidihuzhu.cf.delegate.SimpleUserAccountDelegate;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.mq.payload.WxSubscribePayload;
import com.shuidihuzhu.cf.service.msg.MsgClientV2Service;
import com.shuidihuzhu.client.account.v1.accountservice.OpenIdUserIdResponse;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Set;

/**
 * @author: wuyubin
 * @date: 2019-12-09 15:10
 */
@Service
@RocketMQListener(id = MQTagCons.SEND_SUBSCRIBE_TEN_TIME_MSG,
        tags = MQTagCons.SEND_SUBSCRIBE_TEN_TIME_MSG,
        group = "cf-" + MQTagCons.SEND_SUBSCRIBE_TEN_TIME_MSG + "-group",
        topic = MQTopicCons.CF)
@Slf4j
public class CfWxPushForAdviserConsumer  extends BaseConsumerPrintReceiveLog implements MessageListener<WxSubscribePayload> {
    @Resource
    private SimpleUserAccountDelegate simpleUserAccountDelegate;
    @Resource
    private UserInfoDelegate userInfoDelegate;
    @Autowired
    private CrowdfundingInfoSlaveDao crowdfundingInfoSlaveDao;
    @Resource
    private MsgClientV2Service msgClientV2Service;

    private static final String SHIELDVOLUNTEEREEVENTKEY1 = "cf_volunteer_";
    private static final String SHIELDVOLUNTEEREEVENTKEY2 = "qrscene_cf_volunteer_";

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<WxSubscribePayload> mqMessage) {
        return ConsumeStatus.CONSUME_SUCCESS;
    }

}
