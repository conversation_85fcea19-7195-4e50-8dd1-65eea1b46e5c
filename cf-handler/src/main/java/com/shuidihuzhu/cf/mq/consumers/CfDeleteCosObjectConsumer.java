package com.shuidihuzhu.cf.mq.consumers;

import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.dailysign.DailySignScoreRecord;
import com.shuidihuzhu.cf.mq.model.DelCosObjectMessage;
import com.shuidihuzhu.cf.service.crowdfunding.CfCommonStoreService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RocketMQListener(id = MQTagCons.CF_DELETE_COS_TAG,
        tags = MQTagCons.CF_DELETE_COS_TAG,
        topic = MQTopicCons.CF,
        group = "cf-api_" + MQTagCons.CF_DELETE_COS_TAG)
public class CfDeleteCosObjectConsumer implements MessageListener<DelCosObjectMessage> {

    @Autowired
    private CfCommonStoreService commonStoreService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<DelCosObjectMessage> mqMessage) {

        log.info("收到删除cos对象的消息 msg:{}", mqMessage);
        if (mqMessage == null || mqMessage.getPayload() == null) {
            log.info("收到删除cos对象的消息为空 msg:{}", mqMessage);
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        commonStoreService.deleteCosObject(mqMessage.getPayload().getBucket(), mqMessage.getPayload().getUri());
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
