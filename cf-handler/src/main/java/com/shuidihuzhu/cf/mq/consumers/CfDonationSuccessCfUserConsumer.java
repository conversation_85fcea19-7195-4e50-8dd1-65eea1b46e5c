package com.shuidihuzhu.cf.mq.consumers;

import com.shuidihuzhu.cf.biz.crowdfunding.CfUserFirstDonateService;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 捐款成功,更新用户的首捐时间
 * 新的group:Cf-ConsumerGroup_CF_DONATION_SUCEESS_CF_USER
 * <AUTHOR>
 */
@Slf4j
@Service
@RocketMQListener(id = "donation_success_cf_user_consumer",
        topic = MQTopicCons.CF,
        tags = MQTagCons.CF_DONATION_SUCCESS_BIZ,
        group = "Cf-ConsumerGroup_CF_DONATION_SUCEESS_CF_USER")
public class CfDonationSuccessCfUserConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<CrowdfundingOrder> {

    @Autowired
    private CfUserFirstDonateService cfUserFirstDonateService;

    /**
     * CrowdfundingOrder
     * 需要的数据
     * id: orderId
     * crowdfundingId
     * userId
     * payTime
     * @param mqMessage
     * @return
     */

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CrowdfundingOrder> mqMessage) {
        printReceiveMqMessageLog("first donate time", mqMessage);
        CrowdfundingOrder crowdfundingOrder = mqMessage.getPayload();
        if (crowdfundingOrder == null || crowdfundingOrder.getPayStatus() != 1) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        try {
            cfUserFirstDonateService.doSetCfUserFirstDonateTime(crowdfundingOrder.getUserId(), crowdfundingOrder.getPayTime());
        } catch (Exception e) {
            log.error("consumeMessage crowdfundingOrder:{} error", crowdfundingOrder, e);
            return ConsumeStatus.RECONSUME_LATER;
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }



}
