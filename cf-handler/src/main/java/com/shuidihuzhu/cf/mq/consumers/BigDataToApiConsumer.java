package com.shuidihuzhu.cf.mq.consumers;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Joiner;
import com.shuidihuzhu.cf.biz.crowdfunding.CfUserCaseInfoService;
import com.shuidihuzhu.cf.biz.crowdfunding.CfUserInfoService;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.crowdfunding.CfUserCaseInfoDO;
import com.shuidihuzhu.cf.model.crowdfunding.CfUserInfoDO;
import com.shuidihuzhu.cf.mq.model.*;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.List;

/**
 * @author: fengxuan
 * @create 2019-09-30 14:44
 **/
@Service
@Slf4j
@RocketMQListener(id = MQTagCons.CF_SYNC_BIGDATA_MSG,
        group = "cf-" + MQTagCons.CF_SYNC_BIGDATA_MSG + "-group",
        tags = MQTagCons.CF_SYNC_BIGDATA_MSG,
        topic = MQTopicCons.BIGDATA)
public class BigDataToApiConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<List<BigDataToApiMessage>> {

    @Autowired
    CfUserInfoService cfUserInfoService;

    @Autowired
    CfUserCaseInfoService cfUserCaseInfoService;

    @Autowired(required = false)
    private Producer producer;

    //校验时间的标准，防止传入的时间为s级别
    private static DateTime judgeLeagelTimeStamp = new DateTime("2000");

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<List<BigDataToApiMessage>> mqMessage) {
        List<BigDataToApiMessage> bigDataToApiMessageList = mqMessage.getPayload();
        if (CollectionUtils.isEmpty(bigDataToApiMessageList)|| bigDataToApiMessageList.size() > 2) {
            log.warn("BigDataToApiConsumer消息体设置不对,message:{}", mqMessage);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        printReceiveMqMessageLog("recieve message from bigData", mqMessage);

        //单条消费
        try {
            for (BigDataToApiMessage bigDataToApiMessage : bigDataToApiMessageList) {
                log.debug("BigDataToApiConsumer single message:{}", bigDataToApiMessage);
                if (!validMessage(bigDataToApiMessage)) {
                    log.warn("BigDataToApiConsumer消息体设置不对,message:{}", mqMessage);
                    return ConsumeStatus.CONSUME_SUCCESS;
                }
                consumeSingleMessage(bigDataToApiMessage);
            }
        } catch (Exception e) {
            log.error("BigDataToApiConsumer消费大数据消息错误,please check, messageId:{}, mqMessage:{}",
                    mqMessage.getMsgId(), JSON.toJSONString(bigDataToApiMessageList), e);
            return ConsumeStatus.RECONSUME_LATER;
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }


    public void consumeSingleMessage(BigDataToApiMessage message) {
        Long userId = message.getUserId();
        Integer cfUserInfoDOId = null;
        Integer cfUserCaseInfoDOId = null;

        if (message.getUserInfo() != null) {
            CfUserInfoDO cfUserInfoDO = new CfUserInfoDO()
                    .create(message.getUserInfo(), userId);
            cfUserInfoService.insertOrUpdate(cfUserInfoDO);
            cfUserInfoDOId = cfUserInfoDO.getId();
        }

        if (message.getUserCaseInfo() != null) {
            CfUserCaseInfoDO cfUserCaseInfoDO = new CfUserCaseInfoDO()
                    .create(message.getUserCaseInfo(), userId);
            cfUserCaseInfoDOId = cfUserCaseInfoDO.getId();
            //查询数据库之前的数据，对比消息中的信息是否发生变化，变化同步给榜单
            CfUserCaseInfoDO oldUserCaseInfo = cfUserCaseInfoService.getByCaseIdAndUserId(cfUserCaseInfoDO.getCaseId(), userId);
            cfUserCaseInfoService.insertOrUpdate(cfUserCaseInfoDO);
            log.info("bigData sync success userId:{}, caseId:{}, cfUserInfo id:{}, cfUserCaseInfo id:{}",
                    userId, cfUserCaseInfoDO.getCaseId(), cfUserInfoDOId, cfUserCaseInfoDOId);
            if (userCaseInfoChanged(oldUserCaseInfo, cfUserCaseInfoDO, message.getUserCaseInfo())) {
                //发送消息通知
                InnerUserInfoMessage innerUserInfoMessage = new InnerUserInfoMessage();
                innerUserInfoMessage.setCaseId(cfUserCaseInfoDO.getCaseId());
                innerUserInfoMessage.setUserId(userId);

                String targetKeys = Joiner.on("-").join(MQTagCons.CASE_USER_RANK_LIST_MSG, userId, cfUserCaseInfoDO.getCaseId(), System.currentTimeMillis());
                Message sendMessage =
                        new Message(MQTopicCons.CF_LESS_QUEUE_TOPIC, MQTagCons.CASE_USER_RANK_LIST_MSG, targetKeys, innerUserInfoMessage);
                log.info("send message to CF_USER_RANK_LIST_MSG targetKey:{}, sendMessage:{}", targetKeys, sendMessage);
                producer.send(sendMessage);
            }
        }
    }


    private boolean validMessage(BigDataToApiMessage message) {
        if (message == null || illegalValue(message.getUserId())) {
            return false;
        }

        UserCaseInfoMessage userCaseInfo = message.getUserCaseInfo();
        if (userCaseInfo != null) {
            if (userCaseInfo.getCaseId() == null) {
                log.warn("userCaseInfo caseId为null");
                return false;
            }
            //校验金钱和统计数据是否传了负数
            try {
                if (!validBaseObject(userCaseInfo, UserCaseInfoMessage.class)) {
                    return false;
                }
            } catch (Exception e) {
                log.error("校验信息失败,不影响消息消费!message:{}", message, e);
            }
            if (!validTimeStamp(userCaseInfo.getShareContributeDonateTime())) {
                log.warn("分享带来的最新捐款时间设置不对:{}", userCaseInfo.getShareContributeDonateTime());
                return false;
            }
            if (!validTimeStamp(userCaseInfo.getShareContributeViewTime())) {
                log.warn("分享带来的最新转发时间设置不对:{}", userCaseInfo.getShareContributeViewTime());
                return false;
            }
        }


        UserInfoMessage userInfo = message.getUserInfo();
        if (userInfo != null) {
            try {
                if (!validBaseObject(userInfo, UserInfoMessage.class)) {
                    return false;
                }
            } catch (Exception e) {
                log.error("校验信息失败,不影响消息消费!message:{}", message, e);
            }
            //校验时间戳,是否是按照ms
            if (!validTimeStamp(userInfo.getRemotestCaseDonateTime())) {
                log.warn("捐款的距离最远案例的时间设置不对:{}", userInfo.getRemotestCaseDonateTime());
                return false;
            }
            if (!validTimeStamp(userInfo.getFirstShareTime())) {
                log.warn("首转时间设置不对:{}", userInfo.getFirstShareTime());
                return false;
            }
        }
        return true;
    }


    private boolean validTimeStamp(Long time) {
        if (time == null || time == 0L) {
            return true;
        }
        if (new DateTime(time).isBefore(judgeLeagelTimeStamp)) {
            return false;
        }
        return true;
    }


    private boolean validBaseObject(Object object, Class t) {
        Field[] declaredFields = t.getDeclaredFields();
        try {
            for (Field field : declaredFields) {
                field.setAccessible(true);
                if (illegalValue(field.get(object))) {
                    log.warn("message:{} must fill field:{}", JSON.toJSONString(object), field);
                    return false;
                }
            }
        } catch (IllegalAccessException e) {
            log.error("class:{} no such field", t, e);
            return false;
        }
        return true;
    }


    /**
     * 非法返回true
     */
    private static boolean illegalValue(Object value) {
        if (value instanceof Number) {
            return ((Number) value).longValue() < 0L;
        }
        return false;
    }


    private boolean userCaseInfoChanged(CfUserCaseInfoDO oldUserCaseInfo, CfUserCaseInfoDO newUserCaseInfo, UserCaseInfoMessage userCaseInfoMessage) {
        if (oldUserCaseInfo == null) {
            log.info("oldUserCaseInfo is not exist before and newUserCaseInfo:{}", newUserCaseInfo);
            return true;
        }
        if (userCaseInfoMessage.getAmount() != null && oldUserCaseInfo.getAmount() != newUserCaseInfo.getAmount()) {
            log.info("oldUserCaseInfo amount:{}, newUserCaseInfo amount:{}", oldUserCaseInfo.getAmount(), newUserCaseInfo.getAmount());
            return true;
        }
        if (userCaseInfoMessage.getShareContributeAmount() != null && oldUserCaseInfo.getShareContributeAmount() != newUserCaseInfo.getShareContributeAmount()) {
            log.info("oldUserCaseInfo shareContributeAmount:{}, newUserCaseInfo shareContributeAmount:{}", oldUserCaseInfo.getShareContributeAmount(), newUserCaseInfo.getShareContributeAmount());
            return true;
        }
        if (userCaseInfoMessage.getShareContributeView() != null && oldUserCaseInfo.getShareContributeView() != newUserCaseInfo.getShareContributeView()) {
            log.info("oldUserCaseInfo shareContributeView:{}, newUserCaseInfo shareContributeView:{}", oldUserCaseInfo.getShareContributeView(), newUserCaseInfo.getShareContributeView());
            return true;
        }
        return false;
    }

}
