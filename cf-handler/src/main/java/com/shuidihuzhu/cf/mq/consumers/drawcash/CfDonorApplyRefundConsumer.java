package com.shuidihuzhu.cf.mq.consumers.drawcash;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserThirdModel;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingOrderBiz;
import com.shuidihuzhu.cf.biz.pay.CfDonorApplyRefundBiz;
import com.shuidihuzhu.cf.biz.pay.CfDonorRefundBlacklistBiz;
import com.shuidihuzhu.cf.biz.pay.CfDonorRefundRecordBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.mq.consumers.BaseConsumerPrintReceiveLog;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import com.shuidihuzhu.wx.grpc.model.WxTextMessageModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 捐款人输入 我要退款 事件消费MQ
 */
@Slf4j
@Service
@RocketMQListener(id = MQTagCons.CF_DONOR_APPLY_REFUND_EVENT,
        tags = MQTagCons.CF_DONOR_APPLY_REFUND_EVENT,
        topic = MQTopicCons.CF,
        group = MQTagCons.CF_DONOR_APPLY_REFUND_EVENT)
@RefreshScope
public class CfDonorApplyRefundConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<WxTextMessageModel> {

    @Value("${refund.donor-apply:allow}")
    private String allow;

    @Resource
    private CfDonorApplyRefundBiz cfDonorApplyRefundBiz;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<WxTextMessageModel> mqMessage) {
        printReceiveMqMessageLog(mqMessage);
        if (null == mqMessage || null == mqMessage.getPayload()) {
            log.debug("CfDonorApplyRefundConsumer mqMessage={}", mqMessage);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        try {
            if ("allow".equals(allow)) {
                cfDonorApplyRefundBiz.doEvent(mqMessage.getPayload());
            } else {
                log.info("CfDonorApplyRefundConsumer.mqMessage.Payload:{} 不允许", mqMessage.getPayload());
            }
        } catch (Exception e) {
            log.error("openId:{}", mqMessage.getPayload().getOpenId(), e);
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
