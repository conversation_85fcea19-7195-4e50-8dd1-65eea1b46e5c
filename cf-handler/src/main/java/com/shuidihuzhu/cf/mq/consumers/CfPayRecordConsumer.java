package com.shuidihuzhu.cf.mq.consumers;


import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.event.CfSatDateEvent;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.model.crowdfunding.message.CfPayRecord;
import com.shuidihuzhu.cf.service.EventPublishService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
@RocketMQListener(id = "DONATION_PAY",
        group = "cf-pay-group",
        tags = MQTagCons.CF_PAY_SUCCESS_MSG,
        topic = MQTopicCons.CF)
@Slf4j
public class CfPayRecordConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<CrowdfundingOrder> {


    @Autowired
    private EventPublishService publishService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CrowdfundingOrder> consumerMessage) {
        printReceiveMqMessageLog(consumerMessage);

        try {

            CrowdfundingOrder message = consumerMessage.getPayload();

            if (message != null){

                /** 防止捐款消息重复发送多次 */

                if(consumerMessage.getPayload().getConsumeStatus() != 0){
                    return ConsumeStatus.CONSUME_SUCCESS;
                }

                log.info("CfPayRecordConsumer Record case_id={}，orderId={}",message.getCrowdfundingId(),message.getId());
                CfPayRecord cfPayRecord = new CfPayRecord();
                cfPayRecord.setCaseId(message.getCrowdfundingId());
                cfPayRecord.setPayCount(1);
                cfPayRecord.setPayMoney(message.getAmount());
                publishService.publish(new CfSatDateEvent(this,cfPayRecord,message));
                return ConsumeStatus.CONSUME_SUCCESS;
            }

        }catch (Exception e){
            log.error("CfPayRecordConsumer error consumerMessage={}",consumerMessage,e);
        }

        return ConsumeStatus.RECONSUME_LATER;
    }
}
