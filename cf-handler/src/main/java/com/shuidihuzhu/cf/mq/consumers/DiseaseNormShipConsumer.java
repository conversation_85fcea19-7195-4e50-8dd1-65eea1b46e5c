package com.shuidihuzhu.cf.mq.consumers;

import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.cf.biz.disease.DiseaseNormShipBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingTreatment;
import com.shuidihuzhu.cf.risk.client.rpc.DiseaseClient;
import com.shuidihuzhu.cf.risk.model.risk.diease.DiseaseProjectVO;
import com.shuidihuzhu.cf.service.audit.CfPublicAuditServiceImpl;
import com.shuidihuzhu.cf.service.disease.DiseaseService;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.client.model.event.InfoApproveEvent;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @Author：liuchangjun
 * @Date：2021/8/26
 */
@Service
@Slf4j
@RocketMQListener(id = MQTagCons.CF_DISEASE_NORM_SHIP,
        tags = CfClientMQTagCons.INFO_APPROVE_MSG,
        topic = MQTopicCons.CF,
        group = MQTagCons.CF_DISEASE_NORM_SHIP+CfClientMQTagCons.INFO_APPROVE_MSG)
public class DiseaseNormShipConsumer implements MessageListener<InfoApproveEvent> {

    @Autowired
    private DiseaseService diseaseService;
    @Resource
    private CfPublicAuditServiceImpl cfPublicAuditService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<InfoApproveEvent> mqMessage) {
        if(Objects.isNull(mqMessage) || Objects.isNull(mqMessage.getPayload())){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        InfoApproveEvent approveEvent = mqMessage.getPayload();
        int satus = approveEvent.getStatus();
        if(CrowdfundingStatus.CROWDFUNDING_STATED.value() == satus){
           diseaseService.insertDiseaseNorm(approveEvent.getCaseId());
        }

        // 材审保存审核信息
        try {
            cfPublicAuditService.saveMaterialPublicAudit(approveEvent);
        } catch (Exception e) {
            log.error("保存公示信息异常 ", e);
        }

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
