package com.shuidihuzhu.cf.mq.consumers.messagemq;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.account.verify.client.menu.IdCardVerifyResultEnum;
import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.account.verify.client.model.VerifyIdcardVO;
import com.shuidihuzhu.cf.biz.crowdfunding.verification.CrowdFundingVerificationBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.verification.service.CrowdFundingVerificationStorageService;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.crowdfunding.status.Validate;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.IdcardVerifyStatus;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification;
import com.shuidihuzhu.cf.model.crowdfunding.param.CrowdFundingVerificationParam;
import com.shuidihuzhu.cf.model.crowdfunding.vo.UserRealInfoVo;
import com.shuidihuzhu.cf.mq.consumers.BaseConsumerPrintReceiveLog;
import com.shuidihuzhu.cf.service.crowdfunding.IdCardVerifyService;
import com.shuidihuzhu.cf.service.huzhugrpc.HzUserRealInfoService;
import com.shuidihuzhu.cf.service.msg.MsgClientV2Service;
import com.shuidihuzhu.cipher.OldShuidiCipher;

import com.shuidihuzhu.common.web.enums.Platform;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@RocketMQListener(id = MQTagCons.CF_VERIFY_WAIT_1H_LATER,
        topic = MQTopicCons.CF,
        tags = MQTagCons.CF_VERIFY_WAIT_1H_LATER)
public class CfDelayVerifyIdCardConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<UserRealInfoVo> {
    private static final Logger LOGGER = LoggerFactory.getLogger(CfDelayVerifyIdCardConsumer.class);

    @Autowired
    private IdCardVerifyService idCardVerifyService;

    @Autowired
    private HzUserRealInfoService userRealInfoBiz;
    @Autowired
    private CrowdFundingVerificationBiz crowdFundingVerificationBiz;
    @Autowired
    private UserInfoDelegate userInfoDelegate;
    @Autowired
    private CrowdFundingVerificationStorageService storageService;

    @Autowired
    private OldShuidiCipher oldShuidiCipher;
    @Resource
    private MsgClientV2Service msgClientV2Service;

    public CfDelayVerifyIdCardConsumer() {

    }

    private boolean update(UserRealInfoVo userRealInfoVo, IdcardVerifyStatus verifyStatus,String channel, Platform platform) {
        long userId = userRealInfoVo.getUserId();

        String infoUuid = userRealInfoVo.getInfoUuid();

        if (verifyStatus == IdcardVerifyStatus.HANDLE_SUCCESS) {
            this.userRealInfoBiz.updateIdcardVerifyStatus(userRealInfoVo.getId(), IdcardVerifyStatus.HANDLE_SUCCESS,
                    "", ""
            );
            if (StringUtils.isNotEmpty(infoUuid)) {
                List<CrowdFundingVerification> verificationList =
                        this.crowdFundingVerificationBiz.getByVerifyUserIdAndInfoUuid(userId, infoUuid);
                if (CollectionUtils.isNotEmpty(verificationList)) {
                    CrowdFundingVerification verification = verificationList.get(0);
                    if (null != verification) {
                        crowdFundingVerificationBiz.updateValid(Validate.VALID.getCode(), verification.getId());
                        try {
                            CrowdFundingVerificationParam param = new CrowdFundingVerificationParam();
                            param.setCrowdFundingInfoId(infoUuid);
                            param.setVerifyUserId(userRealInfoVo.getUserId());
                            param.setValid(Validate.VALID.getCode());
                            // 这部分的用户没有记录用户的userThirdType,在用户提交证实的时候会再次写入
                            param.setUserThirdType(0);

                            storageService.otherEvent(param, verification,channel,platform);
                        } catch (Exception e) {
                            LOGGER.error("", e);
                        }
                    }
                }
            }

            UserInfoModel userInfoByUserId = userInfoDelegate.getUserInfoByUserId(userId);
            if (StringUtils.isBlank(userInfoByUserId.getCryptoIdCard())) {
                userInfoDelegate.updateRealNameAndCryptoIdCard(userId,
                        userRealInfoVo.getName(), oldShuidiCipher.aesEncrypt(userRealInfoVo.getIdCard()));
            }
        } else {
            this.userRealInfoBiz.updateIdcardVerifyStatus(userRealInfoVo.getId(), IdcardVerifyStatus.HANDLE_FAILED,
                    "", "");
        }
        return true;
    }

    private void sendVerifyFailMsg(String name, long userId, String infoUuid) {
        LOGGER.info("VerifyIdcardHandler sendVerifyFailMsg name:{}", name);
        if (userId < 0 || StringUtils.isBlank(infoUuid)) {
            return;
        }

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy年MM月dd日");
        String sendTime = simpleDateFormat.format(new Date());

        String modelNum0 = "template1114";
        Map<Long, Map<Integer, String>> msgMap = Maps.newHashMap();
        HashMap<Integer, String> params = Maps.newHashMap();
        params.put(1, sendTime);
        params.put(2, infoUuid);
        msgMap.put(userId, params);
        msgClientV2Service.sendWxParamsMsg(modelNum0, msgMap);
    }

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<UserRealInfoVo> mqMessage) {

        LOGGER.info("异步检验身份证,mqMessage:{}", JSON.toJSONString(mqMessage.getPayload()));
        printReceiveMqMessageLog(mqMessage);

        UserRealInfoVo userRealInfoVo = mqMessage.getPayload();
        if (null == userRealInfoVo) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        try {
            VerifyIdcardVO verifyIdcardVO = idCardVerifyService.verfiy(userRealInfoVo.getName(),userRealInfoVo.getCryptoIdCard(), UserRelTypeEnum.SELF,userRealInfoVo.getUserId());
            LOGGER.debug("异步检验身份证,mqMessage verifyIdcardVO:{}, verifyUserInfoParam:{}", JSON.toJSONString(verifyIdcardVO), userRealInfoVo.getName());
            IdCardVerifyResultEnum resultEnum = verifyIdcardVO.getCode();

            if (IdCardVerifyResultEnum.MATCH == resultEnum) {
                //更新材料
                update(userRealInfoVo, IdcardVerifyStatus.HANDLE_SUCCESS,"",null);
            } else {
                update(userRealInfoVo, IdcardVerifyStatus.HANDLE_FAILED,"",null);
                //不匹配,发送消息
                this.sendVerifyFailMsg(userRealInfoVo.getName(), userRealInfoVo.getUserId(), userRealInfoVo.getInfoUuid());
            }
        } catch (Exception e) {
            LOGGER.error("CfDelayVerifyIdCardConsumer", e);
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
