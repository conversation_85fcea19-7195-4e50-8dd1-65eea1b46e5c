package com.shuidihuzhu.cf.mq.consumers;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.domain.dedicated.CfUserVolunteerRelationDO;
import com.shuidihuzhu.cf.enums.CfVolunteerTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cf.service.msg.MsgClientV2Service;
import com.shuidihuzhu.cf.service.notice.RaiseSuccessNoticeService;
import com.shuidihuzhu.client.baseservice.msg.v2.MsgClientV2;
import com.shuidihuzhu.client.cf.growthtool.model.CfVolunteerMaterialDO;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import com.shuidihuzhu.wx.enums.AccountThirdTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@RocketMQListener(id = MQTagCons.ADMIN_MQ_LOVE_HOME,
        tags = MQTagCons.ADMIN_MQ_LOVE_HOME,
        group = "LOVE_HOME" + MQTagCons.ADMIN_MQ_LOVE_HOME,
        topic = MQTopicCons.CF)
@Slf4j
public class CfLoveHomeConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<List<Integer>> {

    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;
    @Resource
    private MsgClientV2 msgClientV2;
    @Resource
    private UserInfoDelegate userInfoDelegate;
    @Autowired
    private ICfVolunteerService iCfVolunteerService;
    @Autowired
    private RaiseSuccessNoticeService raiseSuccessNoticeService;
    @Resource
    private MsgClientV2Service msgClientV2Service;

    private static final int SHUIDIBANGFUZHAN = 409;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<List<Integer>> mqMessage) {

        List<Integer> crowfundingIdList = mqMessage.getPayload();
        if (crowfundingIdList == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        try {
            return handleMessage(crowfundingIdList);
        } catch (Exception e) {
            log.warn("爱心首页更新实例信息发送给志愿者", e);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
    }

    private ConsumeStatus handleMessage(List<Integer> crowfundingIdList) {
        try{
            for (Integer crowfundingId : crowfundingIdList) {
                log.info("sendLoveHome2Volunteer_crowfundingId:{}",crowfundingId);
                CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(crowfundingId);
                if (crowdfundingInfo != null) {
                    log.info("sendLoveHome2Volunteer_crowdfundingInfo:{}",crowdfundingInfo);
                    sendLoveHome2Volunteer(crowdfundingInfo);
                }
            }
            return ConsumeStatus.CONSUME_SUCCESS;
        }catch (Exception e){
            log.error("爱心首页更新实例信息发送给志愿者出错", e);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
    }

    /**
     * 给志愿者发送患者案例上了爱心首页信息
     *
     * @param crowdfundingInfo 案例信息
     */
    private void sendLoveHome2Volunteer(CrowdfundingInfo crowdfundingInfo) {

        CrowdfundingVolunteer cfVolunteer = iCfVolunteerService.getVolunteer(crowdfundingInfo);
        com.shuidihuzhu.msg.vo.Response response = null;
        if (cfVolunteer != null) {
            //发送微信消息
            String realName = raiseSuccessNoticeService.getRealName(crowdfundingInfo.getId(),crowdfundingInfo.getUserId());
            CfVolunteerTypeEnum typeEnum = CfVolunteerTypeEnum.parse(cfVolunteer.getVolunteerType());
            switch (typeEnum) {
                case SERIOUS_ILLNESS_SALVAGE_ANGEL:
                    response = getResponseBySendMsg(realName, cfVolunteer, "1707model1",AccountThirdTypeEnum.WX_CF_DREAM.getCode());
                    break;
                case SERVE_ADMIN:
                    response = getResponseBySendMsg(realName, cfVolunteer, "1712model1",SHUIDIBANGFUZHAN);
                    break;
                default:
                    break;
            }
            log.info("LoveHome_Response:{}", JSON.toJSONString(response));
        }
    }

    /**
     * 获取发送微信信息返回结果
     * @param realName
     * @param cfVolunteer
     * @param modelNum
     * @param thirdType 公众号id
     * @return
     */
    private com.shuidihuzhu.msg.vo.Response getResponseBySendMsg(String realName, CrowdfundingVolunteer cfVolunteer, String modelNum, int thirdType) {

        com.shuidihuzhu.msg.vo.Response response;
        CfVolunteerMaterialDO cfVolunteerMaterialDO = iCfVolunteerService.getVolunteerMateri(cfVolunteer.getUniqueCode());
        log.info("LoveHome_uniqueCode:{},mobile:{},realName:{},cfVolunteerMaterialDO:{}", cfVolunteer.getUniqueCode(),cfVolunteer.getMobile(),realName,cfVolunteerMaterialDO);
        //如果material有数据，且openID不为空
        if (null != cfVolunteerMaterialDO && StringUtils.isNotBlank(cfVolunteerMaterialDO.getOpenId())) {
            response = getResponse(realName, modelNum, thirdType, cfVolunteerMaterialDO.getOpenId());
        }else{
            //兼容老用户
            CfUserVolunteerRelationDO cfUserVolunteerRelationDO = iCfVolunteerService.getAccountUserAndVolunteerRelationByPhone(cfVolunteer.getMobile());
            log.info("LoveHome_cfUserVolunteerRelationDO:{}",cfUserVolunteerRelationDO);
            response = getResponse(realName, modelNum, thirdType,cfUserVolunteerRelationDO.getOpenId());
        }
        return response;
    }

    /**
     * 获取发送消息的对应的返回值
     * @param realName
     * @param modelNum
     * @param thirdType
     * @param openId
     * @return
     */
    private com.shuidihuzhu.msg.vo.Response getResponse(String realName, String modelNum, int thirdType, String openId) {
        com.shuidihuzhu.msg.vo.Response response = null;
        UserInfoModel userInfoModelVolunteer = userInfoDelegate.getUserInfoByOpenId(openId);
        log.info("LoveHome_userInfoModelVolunteer:{}", userInfoModelVolunteer);
        if (null != userInfoModelVolunteer) {
            Map<Long, Map<Integer, String>> msgMap = Maps.newHashMap();
            HashMap<Integer, String> params = Maps.newHashMap();
            params.put(1, realName);
            msgMap.put(userInfoModelVolunteer.getUserId(), params);
            msgClientV2Service.sendWxParamsMsg(modelNum, msgMap, thirdType);
        }
        return response;
    }

}
