package com.shuidihuzhu.cf.mq.consumers;

import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.notice.RaiseSuccessNoticeService;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 案例初审通过的MSG
 * <p>
 * Created by wangsf on 18/8/04.
 */
@Service
@RocketMQListener(id = MQTagCons.ADMIN_MQ_TAG_FIRST_APPROVE_SUCCESS,
        tags = MQTagCons.ADMIN_MQ_TAG_FIRST_APPROVE_SUCCESS,
        topic = MQTopicCons.CF)
@Slf4j
public class CfCaseFirstApproveConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<CrowdfundingInfo> {

    @Resource(name = "cfRedissonHandler")
    private RedissonHandler redissonHandler;

    @Resource
    private RaiseSuccessNoticeService raiseSuccessNoticeService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CrowdfundingInfo> mqMessage) {

        printReceiveMqMessageLog(mqMessage);

        CrowdfundingInfo crowdfundingInfo = mqMessage.getPayload();
        if (crowdfundingInfo == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        String key = mqMessage.getKeys();
        String tryLock = null;
        try {
            tryLock = redissonHandler.tryLock(key, 0, 60 * 1000L);
        } catch (Exception e) {
            log.error("", e);
        }

        if (StringUtils.isBlank(tryLock)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        try {
            return handleMessage(crowdfundingInfo);
        } catch (Exception e) {
            log.warn("捐款成功补发模板消息", e);
            return ConsumeStatus.CONSUME_SUCCESS;
        } finally {
            try {
                if (StringUtils.isNotBlank(tryLock)) {
                    redissonHandler.unLock(key, tryLock);
                }
            } catch (Exception e) {
                log.info("", e);
            }
        }
    }

    @Override
    public int getOrder() {
        return 0;
    }

    /**
     * 处理初审成功消息
     *
     * @param crowdfundingInfo
     * @return
     */
    private ConsumeStatus handleMessage(CrowdfundingInfo crowdfundingInfo) {
        raiseSuccessNoticeService.sendOnApproveSuccess(crowdfundingInfo);

        return ConsumeStatus.CONSUME_SUCCESS;
    }

}


