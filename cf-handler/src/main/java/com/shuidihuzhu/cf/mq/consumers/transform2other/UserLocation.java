package com.shuidihuzhu.cf.mq.consumers.transform2other;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@AllArgsConstructor
@NoArgsConstructor
@Data
public class UserLocation {
	@J<PERSON><PERSON>ield(name = "userId")
	private long userId;
	@JSONField(name = "last_visit_time")
	private String lastVisitTime;
	@JSONField(name = "last_visit_city")
	private String lastVisitCity;
	@JSONField(name = "last_visit_country")
	private String lastVisitCountry;
	@JSONField(name = "last_visit_ip")
	private String lastVisitIp;
	@JSONField(name = "last_visit_province")
	private String lastVisitProvince;
	@JSONField(name = "last_visit_dist")
	private String lastVisitDist;
}