package com.shuidihuzhu.cf.mq.consumers;


import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingBaseInfoBackup;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.mq.common.CommonConsumerHelper;
import com.shuidihuzhu.cf.service.msg.MsgClientV2Service;
import com.shuidihuzhu.cf.service.notice.urgeraise.UrgeRaiseMsgService;
import com.shuidihuzhu.cf.service.task.CfCaseBasicSaveDelayService;
import com.shuidihuzhu.cf.util.crowdfunding.CFPushRecordFactory;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户保存基本信息后 延时到19或者21发送客服消息
 */
@Service
@RocketMQListener(id = MQTagCons.CF_CASE_BASIC_INTRA_SUPPLEMENT_CUSTOM,
        tags = MQTagCons.CF_CASE_BASIC_INTRA_SUPPLEMENT_CUSTOM,
        topic = MQTopicCons.CF)
@Slf4j
public class CfIntraDayBaseCaseCustomConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<CrowdfundingBaseInfoBackup> {

    @Resource
    private CommonConsumerHelper commonConsumerHelper;

    @Resource
    private CrowdfundingInfoBiz crowdfundingInfoBiz;

    @Autowired
    private CfCaseBasicSaveDelayService cfCaseBasicSaveDelayService;
    @Autowired
    private CFPushRecordFactory cfPushRecordFactory;
    @Autowired(required = false)
    Producer producer;
    @Resource(name = "cfRedissonHandler")
    private RedissonHandler redissonHandler;

    @Resource
    private UrgeRaiseMsgService urgeRaiseMsgService;

    @Resource
    private MsgClientV2Service msgClientV2Service;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CrowdfundingBaseInfoBackup> consumerMessage) {
        printReceiveMqMessageLog(consumerMessage);

        return commonConsumerHelper.consumeMessage(consumerMessage, this::handle, true);
    }

    private boolean handle(ConsumerMessage<CrowdfundingBaseInfoBackup> consumerMessage) {
        CrowdfundingBaseInfoBackup crowdfundingBaseInfoBackup = consumerMessage.getPayload();
        if (crowdfundingBaseInfoBackup == null) {
            log.error("CfIntraDayBaseCaseCustomConsumer data null", consumerMessage);
            return true;
        }
        long userId = crowdfundingBaseInfoBackup.getUserId();

        if (userId <= 0) {
            log.warn("userId is 0");
            return true;
        }

        List<CrowdfundingInfo> noFinishedList = crowdfundingInfoBiz.getNoFinished(userId);

        log.info("CfIntraDayBaseCaseCustomConsumer crowdfundingBaseInfoBackup:{}", crowdfundingBaseInfoBackup);
        // 若已发起筹款 则返回
        if (CollectionUtils.isNotEmpty(noFinishedList)) {
            log.info("已经发起");
            return true;
        }
        LocalDateTime localDateTime = LocalDateTime.now();
        int hour = localDateTime.getHour();
        String code = "A";
        if (hour >= 21) {
            code = "B";
        }

        String tryLock = "";
        try {
            tryLock = redissonHandler.tryLock(
                    this.cfCaseBasicSaveDelayService.getKey("CCBISC" + "_" + code, userId),
                    0, 2000L);
            if (StringUtils.isEmpty(tryLock)) {
                return true;
            }
        } catch (Exception e) {
            log.error("userId:{}, get redis lock failed", userId);
            return true;
        }
        try {
            if (cfCaseBasicSaveDelayService.isHasSend(MQTagCons.CF_CASE_BASIC_INTRA_SUPPLEMENT_CUSTOM + "_" + code,
                    userId)) {
                return true;
            }
            //发送定时消息次日的8点
            cfCaseBasicSaveDelayService.morrowDayMessage(crowdfundingBaseInfoBackup);
            this.sendMessage(userId);
            sendAppMessage(userId);
            cfCaseBasicSaveDelayService.setRedisson(MQTagCons.CF_CASE_BASIC_INTRA_SUPPLEMENT_CUSTOM + "_" + code,
                    userId);
        } catch (Exception e) {
            log.error("mq message send is error", e);
        } finally {
            try {
                if (StringUtils.isNotBlank(tryLock)) {
                    redissonHandler.unLock(this.cfCaseBasicSaveDelayService.getKey("CCBISC" + "_" + code, userId),
                            tryLock);
                }
            } catch (Exception e) {
                log.info("", e);
            }
        }
        return true;
    }

    public void sendMessage(long userId) {
        boolean preApprove = urgeRaiseMsgService.checkPreApprove(userId);
        if (!preApprove) {
            return;
        }

        String modelNum0 = "template1040";
        msgClientV2Service.sendWxMsg(modelNum0, Lists.newArrayList(userId));
    }

    public void sendAppMessage(long userId) {
        String modelNum0 = "OPT8106";
        msgClientV2Service.sendAppMsg(modelNum0, Lists.newArrayList(userId));
    }
}
