package com.shuidihuzhu.cf.mq.consumers.order;

import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoSimpleBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSimpleModel;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.service.crowdfunding.CfCaseDonatorService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 支付成功添加捐款人头像
 *
 * <AUTHOR>
 * @since 2023-03-31 2:26 下午
 **/
@Slf4j
@Service
@RocketMQListener(id = "PAY_SUCCESS_TO_UPDATE_USER_IMG",
        group = "cf-CfPaySuccessAddUserHeadImgConsumer_PAY_SUCCESS_TO_UPDATE",
        tags = MQTagCons.PAY_SUCCESS_TO_UPDATE,
        topic = MQTopicCons.CF)
public class CfPaySuccessAddUserHeadImgConsumer implements MessageListener<CrowdfundingOrder> {

    @Resource
    private CfCaseDonatorService cfCaseDonatorService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CrowdfundingOrder> mqMessage) {
        if (Objects.nonNull(mqMessage) && Objects.nonNull(mqMessage.getPayload())) {
            CrowdfundingOrder crowdfundingOrder = mqMessage.getPayload();
            CfInfoSimpleModel cfInfoSimpleModel = new CfInfoSimpleModel();
            cfInfoSimpleModel.setId(crowdfundingOrder.getCrowdfundingId());
            boolean result = cfCaseDonatorService.putUserHeadUrl(cfInfoSimpleModel, crowdfundingOrder);
            if (!result) {
                log.debug("支付成功添加捐款人头像失败, orderId: {}, userId:{}", crowdfundingOrder.getId(), crowdfundingOrder.getUserId());
            }
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
