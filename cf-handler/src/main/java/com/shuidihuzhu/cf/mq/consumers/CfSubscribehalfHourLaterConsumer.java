package com.shuidihuzhu.cf.mq.consumers;

import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.CfUserOrderCountService;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.mq.consumers.transform2other.UserLocationService;
import com.shuidihuzhu.cf.mq.payload.WxSubscribePayload;
import com.shuidihuzhu.cf.service.msg.SdAdMsgClientService;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;


/**
 * <AUTHOR>
 */
@Service
@RocketMQListener(id = MQTagCons.CF_SUBSCRIBE_HALF_HOUR_LATER,
        tags = MQTagCons.CF_SUBSCRIBE_HALF_HOUR_LATER,
        group = "cf-" + MQTagCons.CF_SUBSCRIBE_HALF_HOUR_LATER + "-group",
        topic = MQTopicCons.CF)
@Slf4j
@RefreshScope
public class CfSubscribehalfHourLaterConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<WxSubscribePayload> {

    @Resource(name = "cfRedissonHandler")
    private RedissonHandler redissonHandler;

    @Value("${subscribe.msg.nickname:亲}")
    private String defaulNickName;

    @Autowired
    private UserInfoDelegate userInfoDelegate;
    @Autowired
    private SdAdMsgClientService sdAdMsgClientService;
    @Resource
    private UserLocationService userLocationService;
    @Autowired
    private CfUserOrderCountService cfUserOrderCountService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<WxSubscribePayload> consumerMessage) {

        printReceiveMqMessageLog(consumerMessage);
        String keys = consumerMessage.getKeys();
        if (StringUtils.isBlank(keys)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        WxSubscribePayload payload = consumerMessage.getPayload();
        if (payload == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        try {
            UserInfoModel userInfoModel = this.userInfoDelegate.getUserInfoByOpenId(payload.getOpenId());
            if (userInfoModel == null) {
                log.info("no userInfoModel found");
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            long userId = userInfoModel.getUserId();

            String nickname = StringUtils.isBlank(userInfoModel.getNickname()) ? defaulNickName : StringUtils.trimToEmpty(userInfoModel.getNickname());
            int orderCount = this.cfUserOrderCountService.getOrderCount(userId);
            String count = orderCount == 0 ? "多" : String.valueOf(orderCount);
            String adPositionId = "posad15614480709565631";
            if (payload.getEventKey().startsWith("last_trade_no_") || payload.getEventKey().contains("dona")) {
                adPositionId = "posad15909846208966593";
                // check 当天是否已发送过1950
                String sentKey = "InsurancePurchaseGuide_" + userId + "_" + DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.now());
                Boolean hasSent = redissonHandler.get(sentKey, Boolean.class);
                if (hasSent != null && hasSent) {
                    return ConsumeStatus.CONSUME_SUCCESS;
                }
            }
            String userArea = userLocationService.getUserArea(userId, "本地");
            log.info("sendSubscribeHalfHourLaterMsg params. adPositionId:{}, userId:{} nickname:{} orderCount:{}", adPositionId, userId, nickname, orderCount);
            this.sdAdMsgClientService.sendAdMsg(adPositionId, userInfoModel.getUserId(), "", 0, "1", nickname, "2", userArea, "5", count);

        } catch (Exception e) {
            log.warn("subscribeHalfHourLater", e);
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
