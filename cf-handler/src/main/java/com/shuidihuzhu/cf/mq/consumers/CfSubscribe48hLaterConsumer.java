package com.shuidihuzhu.cf.mq.consumers;


import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.CfUserOrderCountService;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.mq.consumers.transform2other.UserLocationService;
import com.shuidihuzhu.cf.mq.payload.WxSubscribePayload;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.cf.service.msg.SdAdMsgClientService;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 48小时客服消息变现
 */
@Service
@RocketMQListener(id = MQTagCons.CF_SUBSCRIBE_48h_LATER,
        tags = MQTagCons.CF_SUBSCRIBE_48h_LATER,
        group = "cf-" + MQTagCons.CF_SUBSCRIBE_48h_LATER + "-group",
        topic = MQTopicCons.CF)
@Slf4j
@RefreshScope
public class CfSubscribe48hLaterConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<WxSubscribePayload> {

    private static final String REDIS_KEY_PREFIX = "cf-sub-48h-";

    @Resource(name = "cfRedissonHandler")
    private RedissonHandler redissonHandler;

    @Value("${subscribe.msg48h.send-flag:true}")
    private boolean sendMsgFlag;
    @Value("${subscribe.msg48h.expire:28800000}")
    private long expire;
    @Value("${subscribe.msg48h.nickname:亲}")
    private String defaulNickName;

    @Autowired
    private UserInfoDelegate userInfoDelegate;
    @Autowired
    private SdAdMsgClientService sdAdMsgClientService;
    @Autowired
    private ApplicationService applicationService;
    @Resource
    private UserLocationService userLocationService;
    @Autowired
    private CfUserOrderCountService cfUserOrderCountService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<WxSubscribePayload> consumerMessage) {

        printReceiveMqMessageLog(consumerMessage);
        String keys = consumerMessage.getKeys();
        if (StringUtils.isBlank(keys)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        WxSubscribePayload payload = consumerMessage.getPayload();
        if (payload == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        if (!this.sendMsgFlag) {
            log.info("sendFlag is off");
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        String key = consumerMessage.getKeys();
        String tryLock = null;
        try {
            tryLock = redissonHandler.tryLock(key, 0, 60 * 1000L);
        } catch (Exception e) {
            log.warn("subscribe48HLater", e);
        }

        if (StringUtils.isBlank(tryLock)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        try {
            UserInfoModel userInfoModel = this.userInfoDelegate.getUserInfoByOpenId(payload.getOpenId());
            if (userInfoModel == null) {
                log.info("no userInfoModel found");
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            long userId = userInfoModel.getUserId();
            String redisKey = REDIS_KEY_PREFIX + userId;
            Long lastSendTime = this.redissonHandler.get(redisKey, Long.class);
            if (lastSendTime != null) {
                //48小时内发过此消息
                log.info("has send msg to user:{}", userId);
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            // 已确认全量走广告发消息
            String nickname = StringUtils.isBlank(userInfoModel.getNickname()) ? defaulNickName : StringUtils.trimToEmpty(userInfoModel.getNickname());
            int orderCount = this.cfUserOrderCountService.getOrderCount(userId);
            String count = orderCount == 0 ? "多" : String.valueOf(orderCount);
            String adPositionId = "posad15619802749742141";
            if (payload.getEventKey().startsWith("last_trade_no_") || payload.getEventKey().contains("dona")) {
                adPositionId = "posad15434752917278904";
                // check 当天是否已发送过1950
                String sentKey = "InsurancePurchaseGuide_" + userId + "_" + DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.now());
                Boolean hasSent = redissonHandler.get(sentKey, Boolean.class);
                if (hasSent != null && hasSent) {
                    return ConsumeStatus.CONSUME_SUCCESS;
                }
            }
            String userArea = userLocationService.getUserArea(userId, "本地");
            log.info(" sendSubscribe48hLaterMsg params. adPositionId:{}, userId:{} nickname:{} orderCount:{}", adPositionId, userId, nickname, orderCount);
            this.sdAdMsgClientService.sendAdMsg(adPositionId, userInfoModel.getUserId(), "", 0, "1", nickname, "2", userArea, "5", count);

            //存到redis里
            this.redissonHandler.setEX(redisKey, System.currentTimeMillis(), this.expire);

        } catch (Exception e) {
            log.warn("subscribe48HLater", e);
            return ConsumeStatus.CONSUME_SUCCESS;
        } finally {
            try {
                if (StringUtils.isNotBlank(tryLock)) {
                    redissonHandler.unLock(key, tryLock);
                }
            } catch (Exception e) {
                log.warn("consumeMessage unLock err", e);
            }
        }

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
