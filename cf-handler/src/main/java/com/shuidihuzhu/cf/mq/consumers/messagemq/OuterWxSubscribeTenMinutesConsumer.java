package com.shuidihuzhu.cf.mq.consumers.messagemq;

import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.delegate.service.WxGroupServiceBiz;
import com.shuidihuzhu.cf.model.wx.WxEventDelayMsgPayLoad;
import com.shuidihuzhu.cf.mq.consumers.BaseConsumerPrintReceiveLog;
import com.shuidihuzhu.cf.service.msg.MsgClientV2Service;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import com.shuidihuzhu.wx.grpc.model.WxGroupInfoModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 给外围号发"关注10分钟未发起的催登记消息".
 */
@Service
@RocketMQListener(id = MQTagCons.OUTER_WX_SUBSCRIBE_TEN_MINUTES_MSG,
        tags = MQTagCons.OUTER_WX_SUBSCRIBE_TEN_MINUTES_MSG,
        group = "cf-" + MQTagCons.OUTER_WX_SUBSCRIBE_TEN_MINUTES_MSG + "-group",
        topic = MQTopicCons.CF)
@Slf4j
public class OuterWxSubscribeTenMinutesConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<WxEventDelayMsgPayLoad> {

    private final static  int NON_MAIN_MP_GROUP_ID = 13;

    @Autowired
    private UserInfoDelegate userInfoDelegate;
    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;
    @Resource
    private WxGroupServiceBiz wxGroupServiceBiz;
    @Resource
    private MsgClientV2Service msgClientV2Service;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<WxEventDelayMsgPayLoad> mqMessage) {
        if (null == mqMessage || null == mqMessage.getPayload() || mqMessage.getPayload().getUserId() <= 0) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        WxEventDelayMsgPayLoad mqMessagePayload = mqMessage.getPayload();
        long userId = mqMessagePayload.getUserId();
        int thirdType = mqMessagePayload.getThirdType();

        log.info("--OUTER_WX_SUBSCRIBE_TEN_MINUTES_MSG begin userId:{} thirdType:{}", userId, thirdType);
        List<WxGroupInfoModel> wxGroupInfoModels = wxGroupServiceBiz.getByGroupId(NON_MAIN_MP_GROUP_ID);
        if (CollectionUtils.isEmpty(wxGroupInfoModels)) {
            log.info("获取外围号组失败");
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        long hasThirdType = wxGroupInfoModels.stream().filter(item -> item.getThirdType() == thirdType).count();
        if (hasThirdType <= 0) {
            log.info("微信号{}不在外围号组中", thirdType);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        int count = crowdfundingInfoBiz.getCountByUserId(userId);
        if (count > 0) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        Map<Integer, String> params = Maps.newHashMap();
        UserInfoModel userInfoModel = userInfoDelegate.getUserInfoByUserId(userId);
        if (null != userInfoModel && StringUtils.isNotEmpty(userInfoModel.getNickname())) {
            params.put(1, userInfoModel.getNickname());
        } else {
            params.put(1, "水滴筹用户");
        }
//        String modelNum0 = "2007model1";
//        Map<Long, Map<Integer, String>> msgMap = Maps.newHashMap();
//        msgMap.put(userId, params);
//        msgClientV2Service.sendWxParamsMsg(modelNum0, msgMap, thirdType);

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
