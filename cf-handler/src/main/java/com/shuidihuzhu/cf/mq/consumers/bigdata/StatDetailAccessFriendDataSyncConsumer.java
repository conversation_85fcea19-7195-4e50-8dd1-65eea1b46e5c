package com.shuidihuzhu.cf.mq.consumers.bigdata;

import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Random;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
@RocketMQListener(id = MQTagCons.STAT_DETAIL_ACCESS_FRIEND,
        tags = MQTagCons.STAT_DETAIL_ACCESS_FRIEND,
        topic = MQTopicCons.CF)
public class StatDetailAccessFriendDataSyncConsumer implements MessageListener<String> {

    @Resource
    private Producer producer;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<String> mqMessage) {
        if (mqMessage == null || mqMessage.getPayload() == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        String payload = mqMessage.getPayload();

        Random random = new Random();
        int delayTime = random.nextInt(1800) + 1;
        Message message = Message.ofDelay(MQTopicCons.CF, MQTagCons.STAT_DELAY_DETAIL_ACCESS_FRIEND, MQTagCons.STAT_DELAY_DETAIL_ACCESS_FRIEND, payload, delayTime, TimeUnit.SECONDS);
        MessageResult result = producer.send(message);
        log.info("StatDetailAccessFriendDataSyncConsumer end result:{}", result);

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
