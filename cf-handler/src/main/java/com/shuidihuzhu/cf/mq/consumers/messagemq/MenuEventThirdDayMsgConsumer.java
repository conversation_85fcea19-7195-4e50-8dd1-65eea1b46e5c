package com.shuidihuzhu.cf.mq.consumers.messagemq;

import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.CfUserOrderCountService;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.model.crowdfunding.CfOperatingRecord;
import com.shuidihuzhu.cf.service.msg.SdAdMsgClientService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
@RocketMQListener(id = MQTagCons.MUTUAL_WX_MENU_THIRD_DAY_MSG,
        group = "cf-api-" + MQTagCons.MUTUAL_WX_MENU_THIRD_DAY_MSG + "-group",
        tags = MQTagCons.MUTUAL_WX_MENU_THIRD_DAY_MSG,
        topic = MQTopicCons.CF)
@RefreshScope
public class MenuEventThirdDayMsgConsumer implements MessageListener<CfOperatingRecord> {
    @Autowired
    private SdAdMsgClientService sdAdMsgClientService;
    @Autowired
    private CfUserOrderCountService cfUserOrderCountService;
    @Resource
    private UserInfoDelegate userInfoDelegate;
    @Value("${subscribe.msg.nickname:水滴用户}")
    private String defaulNickName;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CfOperatingRecord> mqMessage) {
        CfOperatingRecord cfOperatingRecord = mqMessage.getPayload();
        long userId = cfOperatingRecord.getUserId();
        UserInfoModel userInfoModel = userInfoDelegate.getUserInfoByUserId(userId);
        if (userInfoModel == null || StringUtils.isBlank(userInfoModel.getNickname())) {
            log.warn("菜单交互消费端)用户与菜单交互，发送第三日消息，未找到用户userId:{} ", userId);
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        int orderCount = cfUserOrderCountService.getOrderCount(userId);
        String count = orderCount == 0 ? "多" :String.valueOf(orderCount);
        String nickname = StringUtils.isBlank(userInfoModel.getNickname()) ? defaulNickName : StringUtils.trimToEmpty(userInfoModel.getNickname());

        sdAdMsgClientService.sendAdMsg("posad15535052060647612", userId, "", 0, "1", nickname, "5", count);
        return ConsumeStatus.CONSUME_SUCCESS;
    }

}
