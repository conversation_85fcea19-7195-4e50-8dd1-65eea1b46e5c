package com.shuidihuzhu.cf.mq.consumers.order;

import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.finance.model.CfDonorRefundApply;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.service.crowdfunding.CfCaseDonatorService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 单笔退款成功之后，删除redis缓存的用户头像
 *
 * <AUTHOR>
 * @since 2023-03-31 2:32 下午
 **/
@Slf4j
@Service
@RocketMQListener(id = "REFUND_SUCCESS_TO_UPDATE_USER_IMG",
        group = "cf-CfSingleRefundSuccessRemoveUserHeadConsumer_REFUND_SUCCESS_TO_UPDATE",
        tags = MQTagCons.REFUND_SUCCESS_TO_UPDATE,
        topic = MQTopicCons.CF)
public class CfSingleRefundSuccessRemoveUserHeadConsumer implements MessageListener<CfDonorRefundApply> {

    @Resource
    private CfCaseDonatorService cfCaseDonatorService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CfDonorRefundApply> mqMessage) {
        if (Objects.nonNull(mqMessage) && Objects.nonNull(mqMessage.getPayload())) {
            CfDonorRefundApply cfDonorRefundApply = mqMessage.getPayload();
            boolean result = cfCaseDonatorService.removeUserHeadUrl(cfDonorRefundApply);
            if (!result) {
                log.debug("单笔退款成功删除缓存头像失败, orderId: {}, userId: {}", cfDonorRefundApply.getOrderId(), cfDonorRefundApply.getUserId());
            }
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
