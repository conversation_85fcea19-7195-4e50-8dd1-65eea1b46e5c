package com.shuidihuzhu.cf.mq.consumers.verification;

import com.shuidihuzhu.cf.biz.crowdfunding.verification.CfConfirmBlacklistBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.mq.consumers.BaseConsumerPrintReceiveLog;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;


/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RocketMQListener(id = MQTagCons.BLACKLIST_CONFIRMED_USERS_RELEASE + "-realTime",
        tags = MQTagCons.BLACKLIST_CONFIRMED_USERS_RELEASE,
        topic = MQTopicCons.CF,
        group = MQTagCons.BLACKLIST_CONFIRMED_USERS_RELEASE + "-realTime")
public class CfBlacklistConfirmedUsersReleaseConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<Long> {

    @Autowired
    private CfConfirmBlacklistBiz cfConfirmBlacklistBiz;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<Long> mqMessage) {

        if (Objects.isNull(mqMessage) || mqMessage.getPayload() <= 0) {
            log.debug("CfVerificationCountConsumer, mqMessage.getPayload()={}", mqMessage);
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        long verifyUserId = mqMessage.getPayload();
        cfConfirmBlacklistBiz.updateValid(verifyUserId);
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
