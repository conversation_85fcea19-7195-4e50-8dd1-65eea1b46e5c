package com.shuidihuzhu.cf.mq.consumers.activity;

import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoSimpleBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.dao.activity.ActivityRedPocketNodeDAO;
import com.shuidihuzhu.cf.domain.activity.ActivityRedPocketDO;
import com.shuidihuzhu.cf.enums.activity.ActivityRedPocketStatusEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSimpleModel;
import com.shuidihuzhu.cf.mq.common.BaseMessageConsumer;
import com.shuidihuzhu.cf.service.activity.subsidy.IActivityRedPocketService;
import com.shuidihuzhu.cf.vo.activity.ActivityRedPacketNodeVO;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created by sven on 2020/4/27.
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RocketMQListener(id = MQTagCons.ACTIVITY_RED_POCKET_END,
        tags = MQTagCons.ACTIVITY_RED_POCKET_END,
        group = "cf-api" + MQTagCons.ACTIVITY_RED_POCKET_END,
        topic = MQTopicCons.CF)
public class ActivityNodeOpenConsumer extends BaseMessageConsumer<Object>
        implements MessageListener<Object> {

    @Resource
    private IActivityRedPocketService redPocketService;

    @Resource
    private Producer producer;

    @Resource
    private CrowdfundingInfoSimpleBiz simpleBiz;

    @Resource
    private ActivityRedPocketNodeDAO nodeDAO;

    @Resource(name="cfShareViewRedissonHandler")
    private RedissonHandler redissonHandler;

    @Override
    protected boolean handle(ConsumerMessage<Object> consumerMessage) {

        Object object = consumerMessage.getPayload();

        log.info("ActivityNodeOpenConsumer get msg:{}", object);

        int caseId = 0;
        long nodeId = 0L;

        if(object instanceof Integer){
            List<ActivityRedPacketNodeVO> list = redPocketService.getByCaseId((Integer) object);
            for(ActivityRedPacketNodeVO vo : list){
                if(vo.getStatus() == ActivityRedPocketStatusEnum.DOING.getCode()){
                    caseId = (Integer) object;
                    nodeId = vo.getNodeId();
                    break;
                }
            }
        }

        if(object instanceof JSONObject){
            JSONObject node= (JSONObject)object;
            caseId = node.getInteger("caseId");
            nodeId = node.getLong("id");
        }

        if(caseId == 0 || nodeId == 0){
            log.info("convert error {} {}", caseId, nodeId);
        }

        final long doingNodeId = nodeId;

        try {
            String lockId = redissonHandler.tryLock("LOCK-"+caseId+ "-"+doingNodeId,20,50);
            if(StringUtils.isEmpty(lockId)){
                return false;
            }
        } catch (Exception e) {
            log.error("error in lock {} {}", caseId, doingNodeId);
            return false;
        }

        List<ActivityRedPacketNodeVO> list = redPocketService.getByCaseId(caseId);
        ActivityRedPacketNodeVO vo = list.stream().filter(r->r.getNodeId() == doingNodeId).findFirst().get();
        if(vo.getStatus() != ActivityRedPocketStatusEnum.DOING.getCode()){
            return true;
        }


        ActivityRedPacketNodeVO next = null;
        for(ActivityRedPacketNodeVO nodeVO : list){
            if(nodeVO.getStatus() == ActivityRedPocketStatusEnum.UNDO.getCode()){
                next = nodeVO;
                break;
            }
        }

        CfInfoSimpleModel cfInfoSimpleModel = simpleBiz.getFundingInfoById(caseId);
        if(cfInfoSimpleModel == null){
            return true;
        }
        boolean isLastNode = (next== null?false:redPocketService.isLastNode(list, next));

        redPocketService.openOnePocket(caseId, vo.getNodeId(), next!=null? next.getNodeId():0L, isLastNode);

        if(next != null){
            redPocketService.sendEventCenter(cfInfoSimpleModel.getUserId(), 29, caseId, 20L, next.getTotalMoney());
        }

        return true;
    }

    @Override
    protected Logger getLogger() {
        return log;
    }
}
