package com.shuidihuzhu.cf.mq.consumers;

import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.BonusPointsMQResult;
import com.shuidihuzhu.client.common.grpc.BizType;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RocketMQListener(id = MQTagCons.BONUS_POINTS_CALLBACK,
		tags = MQTagCons.BONUS_POINTS_CALLBACK,
		topic = MQTopicCons.USER_POINTS)
public class BonusPointsCallBackConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<BonusPointsMQResult> {

	@Override
	public ConsumeStatus consumeMessage(ConsumerMessage<BonusPointsMQResult> message) {
		printReceiveMqMessageLog(message);
		if (message.getPayload().getBizType() != null && message.getPayload().getBizType().intValue() == BizType.CF_VALUE) {
			log.info("consumeMessage: {}", message.getPayload());
		}

		return ConsumeStatus.CONSUME_SUCCESS;
	}
}
