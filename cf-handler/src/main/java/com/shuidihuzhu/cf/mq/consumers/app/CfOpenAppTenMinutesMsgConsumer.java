package com.shuidihuzhu.cf.mq.consumers.app;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.dao.crowdfunding.app.AppDeviceBindDao;
import com.shuidihuzhu.cf.dao.crowdfunding.app.AppEventDao;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.appevent.AppPushEnums;
import com.shuidihuzhu.cf.model.crowdfunding.app.vo.AppDeviceBindVo;
import com.shuidihuzhu.cf.model.crowdfunding.app.vo.AppEventVo;
import com.shuidihuzhu.cf.mq.consumers.BaseConsumerPrintReceiveLog;
import com.shuidihuzhu.cf.service.msg.MsgClientV2Service;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackApiClient;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewBaseInfoDO;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019-06-24
 */
@RefreshScope
@Service
@RocketMQListener(id = MQTagCons.OPEN_APP_10_MINUTE_MSG,
        tags = MQTagCons.OPEN_APP_10_MINUTE_MSG,
        group = MQTagCons.OPEN_APP_10_MINUTE_MSG +"_cf_api_group",
        topic = MQTopicCons.CF)
@Slf4j
public class CfOpenAppTenMinutesMsgConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<String> {
    @Autowired
    private AppEventDao appEventDao;
    @Autowired
    private AppDeviceBindDao appDeviceBindDao;
    @Autowired
    private CfClewtrackApiClient cfClewtrackApiClient;
    @Resource
    private UserInfoDelegate userInfoDelegate;

    @Autowired
    private MsgClientV2Service msgClientV2Service;
    @Autowired
    private ShuidiCipher shuidiCipher;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<String> mqMessage) {
        String appUniqId = mqMessage.getPayload();
        AppDeviceBindVo appDeviceBindVo = appDeviceBindDao.getAppDeviceBindVoByAppUnqiueId(appUniqId);
        if (appDeviceBindVo != null){
            sendOpenApp10MMsg(appUniqId,appDeviceBindVo);
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private void sendOpenApp10MMsg(String appUniqId, AppDeviceBindVo appDeviceBindVo) {
        log.debug("sendOpenApp10MMsg_appUniqId:{}",appUniqId);
        AppEventVo appEventVo = appEventDao.getAppEventDo(appUniqId, AppPushEnums.EventEnum.APP_INITIATE.getCode());
        //用户没有进入发起筹款流程
        Long userId = appDeviceBindVo.getUserId();
        UserInfoModel userInfoModel = userInfoDelegate.getUserInfoByUserId(userId);
        if (userInfoModel == null || StringUtils.isBlank(userInfoModel.getCryptoMobile())){
             return;
        }
        Response<CfClewBaseInfoDO> cfClewBaseInfoDOResponse = cfClewtrackApiClient.getLatestClewbaseByMobile(shuidiCipher.decrypt(userInfoModel.getCryptoMobile()));
        log.debug("cfClewBaseInfoDOResponse:{}", JSONObject.toJSONString(cfClewBaseInfoDOResponse));
        if (appEventVo == null){
            if (cfClewBaseInfoDOResponse !=null && cfClewBaseInfoDOResponse.getCode() == 0 && cfClewBaseInfoDOResponse.getData() != null){
                CfClewBaseInfoDO cfClewBaseInfoDO = cfClewBaseInfoDOResponse.getData();
                //判断登记是否在24小时内
                if (isInner24Hours(cfClewBaseInfoDO)){
                    //在24小时内有登记
                    if (StringUtils.isBlank(cfClewBaseInfoDO.getInfoUuid())){
                        //用户没有进入发起筹款流程，用户有登记，但用户没有发起
                        log.debug("sendOpenApp10MMsg_1856");
                        msgClientV2Service.sendAppMsg("JQX2072", Lists.newArrayList(appDeviceBindVo.getUserId()));
                    }
                }else{
                    //在24小时内没有登记
                    log.debug("sendOpenApp10MMsg_1855");
                    msgClientV2Service.sendAppMsg("GJH8723", Lists.newArrayList(appDeviceBindVo.getUserId()));
                }
            }else{
                //用户没有进入发起筹款流程，且用户没有登记
                log.debug("sendOpenApp10MMsg_1855");
                msgClientV2Service.sendAppMsg("GJH8723", Lists.newArrayList(appDeviceBindVo.getUserId()));
            }
        }
    }

    private boolean isInner24Hours(CfClewBaseInfoDO cfClewBaseInfoDO) {
        Date createTime = cfClewBaseInfoDO.getCreateTime();
        Date standardTime = DateUtil.addHours(DateUtil.getCurrentTimestamp(),-24);
        if (createTime.after(standardTime)){
            return true;
        }
        return false;
    }
}
