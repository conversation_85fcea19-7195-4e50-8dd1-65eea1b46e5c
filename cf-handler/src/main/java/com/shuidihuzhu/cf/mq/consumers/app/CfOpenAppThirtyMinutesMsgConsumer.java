package com.shuidihuzhu.cf.mq.consumers.app;

import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.dao.crowdfunding.app.AppDeviceBindDao;
import com.shuidihuzhu.cf.dao.crowdfunding.app.AppEventDao;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.appevent.AppPushEnums;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.app.vo.AppDeviceBindVo;
import com.shuidihuzhu.cf.model.crowdfunding.app.vo.AppEventVo;
import com.shuidihuzhu.cf.mq.consumers.BaseConsumerPrintReceiveLog;
import com.shuidihuzhu.cf.service.msg.MsgClientV2Service;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-06-24
 */
@RefreshScope
@Service
@RocketMQListener(id = MQTagCons.OPEN_APP_30_MINUTE_MSG,
        tags = MQTagCons.OPEN_APP_30_MINUTE_MSG,
        group = MQTagCons.OPEN_APP_30_MINUTE_MSG +"_cf_api_group",
        topic = MQTopicCons.CF)
@Slf4j
public class CfOpenAppThirtyMinutesMsgConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<String> {

    @Autowired
    private AppEventDao appEventDao;
    @Autowired
    private AppDeviceBindDao appDeviceBindDao;
    @Resource
    private UserInfoDelegate userInfoDelegate;
    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;

    @Autowired
    private MsgClientV2Service msgClientV2Service;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<String> mqMessage) {
        String appUniqId = mqMessage.getPayload();
        AppDeviceBindVo appDeviceBindVo = appDeviceBindDao.getAppDeviceBindVoByAppUnqiueId(appUniqId);
        AppEventVo appEventVo = appEventDao.getAppEventDo(appUniqId, AppPushEnums.EventEnum.APP_INITIATE.getCode());
        //新用户打开APP30分钟后，用户进入发起筹款流程
        if (appDeviceBindVo != null && appEventVo !=null){
            log.debug("sendOpenApp30MMsg_appUniqId:{}",appUniqId);
            sendOpenApp30MMsg(appUniqId,appDeviceBindVo);
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private void sendOpenApp30MMsg(String appUniqId, AppDeviceBindVo appDeviceBindVo) {
        Long userId = appDeviceBindVo.getUserId();
        UserInfoModel userInfoModel = userInfoDelegate.getUserInfoByUserId(userId);
        if (userInfoModel == null){
            return;
        }
        List<CrowdfundingInfo> crowdfundingInfoList = crowdfundingInfoBiz.getCrowdfundingInfoListByUserId(userId);
        //没有发起成功
        if (CollectionUtils.isEmpty(crowdfundingInfoList)){
          AppEventVo appEventVo = appEventDao.getAppEventDo(appUniqId,AppPushEnums.EventEnum.APP_ONLINESERVICE.getCode());
          if (appEventVo == null){
              //没有进入过在线客服
              log.debug("sendOpenApp30MMsg_1857");
              msgClientV2Service.sendAppMsg("BGJ0252", Lists.newArrayList(appDeviceBindVo.getUserId()));
          }else {
              //进入过在线客服
              log.debug("sendOpenApp30MMsg_1858");
              msgClientV2Service.sendAppMsg("IEE3712", Lists.newArrayList(appDeviceBindVo.getUserId()));
          }
        }
    }
}
