package com.shuidihuzhu.cf.mq.consumers.loverank;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.cf.biz.crowdfunding.CfFundraiserManagementBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfInfoShareRecordBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfSharePromoteOrderBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingOrderBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enhancer.mq.MQHelperService;
import com.shuidihuzhu.cf.facade.loverank.UserLoveRankFace;
import com.shuidihuzhu.cf.finance.model.CfDonorRefundApply;
import com.shuidihuzhu.cf.finance.model.CfOrderRefundSuccessModel;
import com.shuidihuzhu.cf.finance.mq.FinanceMQTagCons;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoShareRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.mq.consumers.BaseConsumerPrintReceiveLog;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.data.analytics.javasdk.core.Analytics;
import com.shuidihuzhu.data.analytics.javasdk.core.UserTagTypeEnum;
import com.shuidihuzhu.data.servicelog.meta.cf.CaseOrderRefund;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Optional;

/**
 * @author: lyq
 **/
@Slf4j
@Service
@RefreshScope
@RocketMQListener(id = "RefundSuccessLoveRankConsumer",
        tags = FinanceMQTagCons.CF_ORDER_REFUND_SUCCESS,
        topic = MQTopicCons.CF,
        group = "cf-api-love-rank" + FinanceMQTagCons.CF_ORDER_REFUND_SUCCESS)
public class RefundSuccessLoveRankConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<CfOrderRefundSuccessModel> {

    @Resource(name = "cfRedissonHandler")
    private RedissonHandler cfRedissonHandler;
    @Autowired
    CrowdfundingOrderBiz crowdfundingOrderBiz;
    @Autowired
    private UserLoveRankFace userLoveRankFace;
    @Value("${apollo.new.count.remove.refund.case:100000000}")
    private long useSqlRemoveRefundCaseId;


    /**
     * 发送时机，整单退款后发送
     *
     * @param mqMessage
     * @return
     */
    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CfOrderRefundSuccessModel> mqMessage) {
        printReceiveMqMessageLog("refund success msg", mqMessage);
        CfOrderRefundSuccessModel refundSuccessModel = mqMessage.getPayload();
        if (refundSuccessModel == null) {
            log.warn("msg is null");
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        // 老消息不处理新案例
        if (refundSuccessModel.getCaseId() > useSqlRemoveRefundCaseId) {
            log.debug("爱心榜单老逻辑-案例ID: {} 不处理整体退款", refundSuccessModel.getCaseId());
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        long orderId = refundSuccessModel.getOrderId();
        //查找这个退款的转发来源
        CrowdfundingOrder crowdfundingOrder = crowdfundingOrderBiz.getById(orderId);
        if (crowdfundingOrder == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        //需要确保幂等
        String tryLock = null;
        String lockKey = "RefundSuccessLoveRankConsumer_" + orderId;
        try {
            tryLock = cfRedissonHandler.tryLock(lockKey, 0, 60 * 60 * 1000L);
            //未获取到锁
            if (StringUtils.isBlank(tryLock)) {
                log.warn("repeat refund success msg orderId:{}", orderId);
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            userLoveRankFace.refundSuccess(crowdfundingOrder);
            return ConsumeStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            log.error("refund success consume error, msg:{}", mqMessage, e);
            try {
                if (StringUtils.isNotBlank(tryLock)) {
                    cfRedissonHandler.unLock(lockKey, tryLock);
                }
            } catch (Exception ex) {
                log.error("refund consumer unlock error, orderId:{}", orderId, ex);
            }
            return ConsumeStatus.RECONSUME_LATER;
        }
    }

}
