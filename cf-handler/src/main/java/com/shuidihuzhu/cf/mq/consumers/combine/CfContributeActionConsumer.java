package com.shuidihuzhu.cf.mq.consumers.combine;


import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.biz.combine.CfContributeOrderBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.contribute.CfContributeMsgBody;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

@Service
@RefreshScope
@Slf4j
@RocketMQListener(id = MQTagCons.CF_CONTRIBUTE_ORDER_TAG,
        topic = MQTopicCons.CF,
        tags = MQTagCons.CF_CONTRIBUTE_ORDER_TAG,
        group =  "cf-api_" + MQTagCons.CF_CONTRIBUTE_ORDER_TAG)
public class CfContributeActionConsumer implements MessageListener<CfContributeMsgBody> {

    @Autowired
    private CfContributeOrderBiz contributeOrderBiz;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CfContributeMsgBody> mqMessage) {
        log.info("收到捐赠动作消息.msg:{}", mqMessage);

        if (mqMessage == null || mqMessage.getPayload() == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        contributeOrderBiz.handleContributeAction(mqMessage.getPayload());

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
