package com.shuidihuzhu.cf.mq.consumers.messagemq;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.wx.Cf111WxEventRecordBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingBaseInfoBackup;
import com.shuidihuzhu.cf.mq.common.CommonConsumerHelper;
import com.shuidihuzhu.cf.mq.consumers.BaseConsumerPrintReceiveLog;
import com.shuidihuzhu.cf.service.msg.MsgClientV2Service;
import com.shuidihuzhu.cf.service.notice.urgeraise.UrgeRaiseMsgService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 用户保存基本信息后，立即发送
 */
@Service
@RocketMQListener(id = MQTagCons.CF_SNAPSHOT_SAVE_SUCESS_RIGHT_NOW,
        tags = MQTagCons.CF_SNAPSHOT_SAVE_SUCESS_RIGHT_NOW,
        topic = MQTopicCons.CF,
        group = MQTagCons.CF_SNAPSHOT_SAVE_SUCESS_RIGHT_NOW)
@RefreshScope
@Slf4j
public class CfCaseBasicSaveConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<CrowdfundingBaseInfoBackup> {
    @Resource
    private CommonConsumerHelper commonConsumerHelper;

    @Resource
    private UrgeRaiseMsgService urgeRaiseMsgService;
    @Resource
    private MsgClientV2Service msgClientV2Service;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CrowdfundingBaseInfoBackup> consumerMessage) {
        printReceiveMqMessageLog(consumerMessage);
        return commonConsumerHelper.consumeMessage(consumerMessage, this::handle, true);
    }

    private boolean handle(ConsumerMessage<CrowdfundingBaseInfoBackup> consumerMessage) {
        CrowdfundingBaseInfoBackup v = consumerMessage.getPayload();
        if (v == null) {
            log.error("data null", consumerMessage);
            return true;
        }
        long userId = v.getUserId();


        boolean preApprove = urgeRaiseMsgService.checkPreApprove(userId);
        if (!preApprove) {
            return true;
        }

        String modelNum0 = "draftSaveMsg1313";
        msgClientV2Service.sendWxMsg(modelNum0, Lists.newArrayList(userId));

        return true;
    }
}
