package com.shuidihuzhu.cf.mq.consumers.mask;

import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.service.mask.ImageMaskProcessService;
import com.shuidihuzhu.cf.service.mask.impl.ImageMaskProcessServiceImpl;
import com.shuidihuzhu.client.model.ImageMaskInform;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Description: 图片掩码通知消费者
 * @Author: panghairui
 * @Date: 2022/9/8 2:14 下午
 */
@Slf4j
@Service
@RocketMQListener(id = MQTagCons.CF_IMAGE_MASK_TO_AI,
        topic = MQTopicCons.CF,
        tags = MQTagCons.CF_IMAGE_MASK_TO_AI,
        group = MQTagCons.CF_IMAGE_MASK_TO_AI)
public class ImageMaskInformConsumer implements MessageListener<ImageMaskInform> {

    @Resource
    private ImageMaskProcessServiceImpl imageMaskProcessService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<ImageMaskInform> mqMessage) {

        log.info("ImageMaskInformConsumer consumeMessage begin mqMessage:{}", mqMessage);
        ImageMaskInform imageMaskInform = mqMessage.getPayload();
        if (Objects.isNull(imageMaskInform)) {
            log.info("ImageMaskInformConsumer imageMaskInform is null");
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        // 调用AI接口进行掩码
        imageMaskProcessService.processImageToAIMask(imageMaskInform);

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
