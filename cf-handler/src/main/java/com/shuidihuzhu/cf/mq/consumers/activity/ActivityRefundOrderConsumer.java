package com.shuidihuzhu.cf.mq.consumers.activity;

import com.shuidihuzhu.cf.activity.feign.CaseCountFeignClient;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enhancer.mq.BaseMessageConsumer;
import com.shuidihuzhu.cf.finance.model.CfOrderRefundSuccessModel;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RocketMQListener(id = MQTagCons.CF_ACTIVITY_REFUND_SUCCESS,
        tags = MQTagCons.CF_ACTIVITY_REFUND_SUCCESS,
        group = "cf-api" + MQTagCons.CF_ACTIVITY_REFUND_SUCCESS,
        topic = MQTopicCons.CF)
public class ActivityRefundOrderConsumer
        extends BaseMessageConsumer<CfOrderRefundSuccessModel>
        implements MessageListener<CfOrderRefundSuccessModel> {

    @Autowired
    private CaseCountFeignClient caseCountFeignClient;

    @Override
    protected boolean handle(ConsumerMessage<CfOrderRefundSuccessModel> consumerMessage) {
        CfOrderRefundSuccessModel model = consumerMessage.getPayload();
        if (model == null) {
            log.error("PaySuccessSubsidyConsumer order null");
            return true;
        }
        int caseId = model.getCaseId();
        long userId = model.getUserId();
        return caseCountFeignClient.refundOne(caseId, userId, model.getOrderId()).isSuccess();
    }

    @Override
    protected Logger getLogger() {
        return log;
    }
}
