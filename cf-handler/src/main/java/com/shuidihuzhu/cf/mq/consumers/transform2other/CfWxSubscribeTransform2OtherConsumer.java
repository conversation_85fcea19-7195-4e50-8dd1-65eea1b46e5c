package com.shuidihuzhu.cf.mq.consumers.transform2other;

import com.shuidihuzhu.account.model.UserThirdModel;
import com.shuidihuzhu.cf.biz.crowdfunding.CfUserOrderCountService;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.delegate.UserThirdDelegate;
import com.shuidihuzhu.cf.delegate.WxSubscribeEventDelegate;
import com.shuidihuzhu.cf.mq.common.CommonConsumerHelper;
import com.shuidihuzhu.cf.mq.payload.WxSubscribePayload;
import com.shuidihuzhu.cf.service.msg.SdAdMsgClientService;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@Service
@RocketMQListener(id = MQTagCons.CF_WX_SUBSCRIBE_TRANSFORM_TO_OTHER,
        tags = MQTagCons.CF_WX_SUBSCRIBE_TRANSFORM_TO_OTHER,
        group = "cf-" + MQTagCons.CF_WX_SUBSCRIBE_TRANSFORM_TO_OTHER + "-group",
        topic = MQTopicCons.CF)
@Slf4j
@RefreshScope
public class CfWxSubscribeTransform2OtherConsumer implements MessageListener<WxSubscribePayload> {

    @Autowired
    protected WxSubscribeEventDelegate wxSubscribeEventDelegate;
    @Autowired
    SdAdMsgClientService sdAdMsgClientService;
    @Resource
    private CommonConsumerHelper commonConsumerHelper;
    @Resource
    private UserThirdDelegate userThirdDelegate;
    @Resource
    private UserLocationService userLocationService;
    @Autowired
    private CfUserOrderCountService cfUserOrderCountService;
    @Resource(name = "cfRedissonHandler")
    private RedissonHandler redissonHandler;

    @Value("${subscribe.msg.nickname:亲}")
    private String defaulNickName;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<WxSubscribePayload> consumerMessage) {

        return commonConsumerHelper.consumeMessage(consumerMessage,
                CfWxSubscribeTransform2OtherConsumer.this::handle
                , true);
    }

    private boolean handle(ConsumerMessage<WxSubscribePayload> consumerMessage) {
        WxSubscribePayload payload = consumerMessage.getPayload();
        String openId = payload.getOpenId();

        UserThirdModel userThirdModel = userThirdDelegate.getThirdModelWithOpenId(openId);
        if (userThirdModel == null) {
            log.warn("payload: {} userThirdModel null", payload);
            return true;
        }
        long userId = userThirdModel.getUserId();

        pushAdMsg(userId, userThirdModel, payload.getEventKey());

        return true;
    }

    private void pushAdMsg(long userId, UserThirdModel userThirdModel, String eventKey) {
        String adPositionId = "posad15614480709565631";
        if (eventKey.startsWith("last_trade_no_") || eventKey.contains("dona")) {
            adPositionId = "posad15434750117273825";
            // check 当天是否已发送过1950
            String key = "InsurancePurchaseGuide_" + userId + "_" + DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDate.now());
            Boolean hasSent = redissonHandler.get(key, Boolean.class);
            if (hasSent != null && hasSent) {
                return;
            }
        }
        String userArea = userLocationService.getUserArea(userId, "本地");
        String nickname = StringUtils.trimToEmpty(userThirdModel.getNickname()) == "" ? defaulNickName : StringUtils.trimToEmpty(userThirdModel.getNickname());

        int orderCount = this.cfUserOrderCountService.getOrderCount(userId);
        String count = orderCount == 0 ? "多" : String.valueOf(orderCount);
        log.info(" sendSubscribe24hLaterMsg params. adPositionId:{} userId:{} nickname:{} userArea",
                adPositionId, userId, nickname, userArea);
        this.sdAdMsgClientService.sendAdMsg(adPositionId, userId, "", 0,
                "1", nickname, "2", userArea, "5", count);
    }
}
