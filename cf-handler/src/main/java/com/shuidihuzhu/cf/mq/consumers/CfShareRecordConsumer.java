package com.shuidihuzhu.cf.mq.consumers;


import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.event.CfSatDateEvent;
import com.shuidihuzhu.cf.model.crowdfunding.message.CfShareRecord;
import com.shuidihuzhu.cf.service.EventPublishService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@RocketMQListener(id = MQTagCons.CF_SHARE_SUCCESS_MSG,
        group = "cf-share-group",
        tags = MQTagCons.CF_SHARE_SUCCESS_MSG,
        topic = MQTopicCons.CF)
@Slf4j
public class CfShareRecordConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<CfShareRecord> {

    @Autowired
    private EventPublishService publishService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CfShareRecord> consumerMessage) {
        printReceiveMqMessageLog(consumerMessage);

        try {
            CfShareRecord cfShareRecord = consumerMessage.getPayload();
            publishService.publish(new CfSatDateEvent(this,cfShareRecord));
            return ConsumeStatus.CONSUME_SUCCESS;
        }catch (Exception e){
            log.error("CfShareRecordConsumer error consumerMessage={}",consumerMessage,e);
        }

        return ConsumeStatus.RECONSUME_LATER;
    }
}
