package com.shuidihuzhu.cf.mq.consumers.messagemq;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.client.baseservice.ad.v1.AdClient;
import com.shuidihuzhu.client.baseservice.ad.v1.model.Response;
import com.shuidihuzhu.client.baseservice.ad.v1.model.SdAdMessage;
import com.shuidihuzhu.client.baseservice.ad.v1.model.SdAdMessageVO;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @time 2019/9/27 下午2:14
 * @desc rocketmq默认最多重试16次 https://help.aliyun.com/document_detail/43490.html?spm=a2c4g.11186623.6.555.42d5e406OzE9Uy
 */
@Slf4j
@Service
@RocketMQListener(id = MQTagCons.AD_CALL_FAIL_TRYABLE_MQ,
        group = "cf-api-" + MQTagCons.AD_CALL_FAIL_TRYABLE_MQ + "-group",
        tags = MQTagCons.AD_CALL_FAIL_TRYABLE_MQ,
        topic = MQTopicCons.CF)
public class AdFailTryableConsumer implements MessageListener<SdAdMessage> {

    @Resource
    private AdClient adClient;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<SdAdMessage> mqMessage) {

        if(Objects.isNull(mqMessage) || Objects.isNull(mqMessage.getPayload())){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        //重试16次后放弃
        int tryableSize = mqMessage.getReconsumeTimes();
        if(tryableSize > 16){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        SdAdMessage adMessage = mqMessage.getPayload();

        log.info("AdFailTryableConsumer keys:{},adPosition:{},tryableSize:{}", mqMessage.getKeys(), adMessage.getAdPositionId(), tryableSize);

        Response<List<SdAdMessageVO>> response = adClient.getMessage(adMessage);

        if (Objects.isNull(response)){
            return ConsumeStatus.RECONSUME_LATER;
        }

        if(ErrorCode.SUCCESS.getCode() == response.getCode()){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        log.info("AdFailTryableConsumer tryable fail keys:{},result:{}", mqMessage.getKeys(), JSON.toJSONString(response));

        //实际上rocketmq默认也是最多重试16次
        //二次开发的mq重试多少次？
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
