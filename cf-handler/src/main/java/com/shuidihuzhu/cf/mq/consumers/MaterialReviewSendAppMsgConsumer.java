package com.shuidihuzhu.cf.mq.consumers;

import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.service.msg.MsgClientV2Service;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;


/**
 * <AUTHOR>
 * @time 2019/5/23 下午3:19
 * @desc
 */
@Slf4j
@Service
@RocketMQListener(id = MQTagCons.MATERIAL_REVIEW_SEND_APP_MSG,
        group = "cf-api-" + MQTagCons.MATERIAL_REVIEW_SEND_APP_MSG + "-group",
        tags = MQTagCons.MATERIAL_REVIEW_SEND_APP_MSG,
        topic = MQTopicCons.CF)
public class MaterialReviewSendAppMsgConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<CrowdfundingOrder> {
    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;

    @Autowired
    private MsgClientV2Service msgClientV2Service;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CrowdfundingOrder> mqMessage) {
        printReceiveMqMessageLog(mqMessage);

        CrowdfundingOrder payload = mqMessage.getPayload();

        if (payload == null || StringUtils.isEmpty(payload.getInfoId())) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoFromSlave(payload.getInfoId());
        if (crowdfundingInfo == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        //材审通过
        if (crowdfundingInfo.getStatus().value() == CrowdfundingStatus.CROWDFUNDING_STATED.value()) {
            log.info("材料审核已经通过 infoUuid:{}", payload.getInfoId());
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        Map<Long, Map<Integer, String>> appMsgMap = Maps.newHashMap();

        Map<Integer, String> paramMap = Maps.newHashMap();
        paramMap.put(1, DateUtil.getCurrentDateTimeStr());
        paramMap.put(2, crowdfundingInfo.getInfoId());

        appMsgMap.put(crowdfundingInfo.getUserId(), paramMap);
        msgClientV2Service.sendAppParamsMsg("IUG6731", appMsgMap);

        return ConsumeStatus.CONSUME_SUCCESS;
    }

}
