package com.shuidihuzhu.cf.mq.consumers.activity;

import com.shuidihuzhu.cf.activity.feign.CfActivityFeignClient;
import com.shuidihuzhu.cf.activity.model.ActivityDetail;
import com.shuidihuzhu.cf.activity.mq.CaseStatusChangeVo;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.mq.common.BaseMessageConsumer;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.activity.IActivityDonatorRedPocketService;
import com.shuidihuzhu.cf.service.activity.subsidy.IActivityRedPocketService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * {@link com.shuidihuzhu.cf.mq.consumers.activity.ActivityNodeConsumer}
 * 这个消息的延迟消息
 * 解决查询活动没查到（主从延迟导致）
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RocketMQListener(id = "DONATE_COOPERATE_CASE_DELAY",
        tags = "DONATE_COOPERATE_CASE_DELAY",
        group = "cf-api" + "DONATE_COOPERATE_CASE_DELAY",
        topic = MQTopicCons.CF)
public class ActivityNodeDelayConsumer extends BaseMessageConsumer<CaseStatusChangeVo>
        implements MessageListener<CaseStatusChangeVo> {

    @Resource
    private IActivityRedPocketService redPocketService;
    @Resource
    private IActivityDonatorRedPocketService donatorRedPocketService;
    @Resource
    private CfActivityFeignClient activityFeignClient;
    @Override
    protected boolean handle(ConsumerMessage<CaseStatusChangeVo> consumerMessage) {

        if (consumerMessage == null || consumerMessage.getPayload() == null || consumerMessage.getPayload().getSourceStatus() != null) {
            return true;
        }

        int caseId = consumerMessage.getPayload().getCaseId();

        long activityId = consumerMessage.getPayload().getActivityId();

        RpcResult<ActivityDetail> rpcResult = activityFeignClient.getActivityForCaseIdActivityId(caseId, activityId);

        log.info("ActivityNodeDelayConsumer getActivityForRaiser {} {}", caseId, rpcResult);

        if (rpcResult == null || rpcResult.getData() == null) {
            return true;
        }

        if (!rpcResult.getData().isShowOnTaskPage()) {
            log.info("ActivityNodeDelayConsumer not show on task {}", caseId);
            return true;
        }

        int pocketType = rpcResult.getData().getPocketType();

        switch (pocketType) {
            case 0:
                log.info("默认不创建 {}", caseId);
                break;
            case 1:
                OpResult<Void> opResult = redPocketService.createPockets(caseId);
                log.info("redPocketService createPockets {} {}", consumerMessage.getPayload().getCaseId(), opResult);
                break;
            case 2:
                donatorRedPocketService.create(caseId);
                log.info("donatorRedPocketService createPockets {}", consumerMessage.getPayload().getCaseId());
            default:
                break;
        }

        return true;
    }

    @Override
    protected Logger getLogger() {
        return log;
    }
}
