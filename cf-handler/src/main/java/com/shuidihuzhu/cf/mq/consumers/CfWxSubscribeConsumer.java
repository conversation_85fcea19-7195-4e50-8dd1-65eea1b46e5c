package com.shuidihuzhu.cf.mq.consumers;


import com.alibaba.fastjson.JSON;
import com.shuidi.weixin.common.util.StringUtils;
import com.shuidihuzhu.account.model.service.OpenIdUserIdModel;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.WxConstants;
import com.shuidihuzhu.cf.constants.crowdfunding.DSTokenInfo;
import com.shuidihuzhu.cf.delegate.SimpleUserAccountDelegate;
import com.shuidihuzhu.cf.event.CfWxSubscribeEvent;
import com.shuidihuzhu.cf.mq.payload.WxSubscribePayload;
import com.shuidihuzhu.cf.mq.producer.CommonMessageHelperService;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.cf.service.wx.WxSubscribeEventService;
import com.shuidihuzhu.client.account.v1.accountservice.OpenIdUserIdResponse;
import com.shuidihuzhu.data.analytics.javasdk.core.Analytics;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.*;
import com.shuidihuzhu.wx.enums.AccountThirdTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Calendar;
import java.util.HashMap;
import java.util.Map;

@Service
@RocketMQListener(id = MQTagCons.CF_WX_SUBSCRIBE,
        tags = MQTagCons.CF_WX_SUBSCRIBE,
        group = "cf-" + MQTagCons.CF_WX_SUBSCRIBE + "-group",
        topic = MQTopicCons.CF)
@Slf4j
@RefreshScope
public class CfWxSubscribeConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<WxSubscribePayload> {

    private final static String CF_LAST_SUBSCRIBE_TIME_NUM = "cf_last_subscribe_time_num";

    private final static int ENDHOUR = 17;

    @Autowired
    private SimpleUserAccountDelegate simpleUserAccountDelegate;

    @Autowired
    private CommonMessageHelperService commonMessageHelperService;

    @Autowired
    private Analytics analytics;

    @Autowired
    private ApplicationService applicationService;

    @Resource
    private WxSubscribeEventService wxSubscribeEventService;

    @Autowired(required = false)
    private Producer producer;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<WxSubscribePayload> consumerMessage) {

        //发一条48小时消息
        WxSubscribePayload payload = consumerMessage.getPayload();

        //24/48h处理ad消息以及0.5h/1h处理ad消息
        wxSubscribeEventService.sendAdMsg(payload);

        //发送48小时订阅的用户信息
        this.sendUser48hSubscribeTag(payload);

        //触发定时向已关注但未发起筹款的用户发送催发的消息--当天晚上召回消息
        sendSubscribeUnRaisePersonMessageToday(payload);
        // 触发定时向已关注但未发起筹款的用户发送催发的消息
        sendSubscribeUnRaisePersonMessage(payload);
        //第3天召回消息 -- 触发定时向已关注但未发起筹款的用户发送催发的消息
        sendSubscribeUnRaisePersonMessageThreeDay(payload);
        //主动关注十分钟后给用户发消息
        cfWxPushForAdviser(payload);
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private CfWxSubscribeEvent map(ConsumerMessage<WxSubscribePayload> consumerMessage) {
        WxSubscribePayload payload = consumerMessage.getPayload();
        return new CfWxSubscribeEvent(CfWxSubscribeConsumer.this, payload.getOpenId(),
                payload.getUserThirdType(), payload.getEventKey());
    }

    private void sendUser48hSubscribeTag(WxSubscribePayload payload) {

        int userThirdType = payload.getUserThirdType();

        if (WxConstants.MAJOR.contains(userThirdType)) {
            Calendar cal = Calendar.getInstance();
            String currentHour = String.valueOf(cal.get(Calendar.HOUR_OF_DAY));
            OpenIdUserIdModel openIdUserIdResponse = simpleUserAccountDelegate.getUserIdByOpenId(payload.getOpenId());
            if(openIdUserIdResponse != null){
                long userId = openIdUserIdResponse.getUserId();
                Map<String, Object> properties = new HashMap<>();
                properties.put(CF_LAST_SUBSCRIBE_TIME_NUM, currentHour);
                analytics.profileSet(DSTokenInfo.NEW_TOKEN, String.valueOf(userId), DSTokenInfo.BIZ, properties);
                log.info("DSTokenInfo.NEW_TOKEN: {},userId: {},DSTokenInfo.BIZ: {},properties {}", DSTokenInfo.NEW_TOKEN, String.valueOf(userId), DSTokenInfo.BIZ, properties);
            }

        }
    }

    /**
     * 主动关注十分钟后发消息
     * @param payload
     */
    private void cfWxPushForAdviser(WxSubscribePayload payload){
        long userId = getUserIdByOpendId(payload, WxConstants.FUNDRAISER_THIRD_TYPE);
        if (userId <= 0) {
            return;
        }
        Message message = new Message<>(MQTopicCons.CF,
                MQTagCons.SEND_SUBSCRIBE_TEN_TIME_MSG,
                MQTagCons.SEND_SUBSCRIBE_TEN_TIME_MSG + "_" + payload.getOpenId() + "_" + System.currentTimeMillis(),
                payload, DelayLevel.M10);
        MessageResult messageResult = producer.send(message);
        log.info("主动关注十分钟后发消息 message:{} messageResult:{}", message, messageResult);
    }

    /**
     * 第二天催登记消息
     * @param payload
     */
    private void sendSubscribeUnRaisePersonMessage(WxSubscribePayload payload) {

        long userId = getUserIdByOpendId(payload, WxConstants.FUNDRAISER_THIRD_TYPE);
        if (userId <= 0) {
            return;
        }

        DateTime now = new DateTime();
        long expectTime = 0;
        //第二天10点左右发送
        if (applicationService.isProduction()) {
            expectTime = now.plusDays(1).withHourOfDay(10).withMinuteOfHour(RandomUtils.nextInt(0, 30)).getMillis();
        } else {
            expectTime = now.plusDays(0).plusHours(0).plusMinutes(2).getMillis();
        }
        Message message = Message.ofSchedule(MQTopicCons.CF,
                MQTagCons.SEND_SUBSCRIBE_UN_RAISE_PERSON_MSG,
                MQTagCons.SEND_SUBSCRIBE_UN_RAISE_PERSON_MSG + "_" + payload.getOpenId() + "_" + System.currentTimeMillis(), payload, expectTime / 1000);
        MessageResult messageResult = producer.send(message);
        log.info("sendSubscribeUnRaisePersonMessage_payload:{} message:{} messageResult:{}", JSON.toJSONString(payload), message, messageResult);
    }

    /**
     * 当天晚上19点召回消息
     * @param payload
     */
    private void sendSubscribeUnRaisePersonMessageToday(WxSubscribePayload payload) {
        long userId = getUserIdByOpendId(payload, WxConstants.FUNDRAISER_THIRD_TYPE);
        if (userId <= 0) {
            return;
        }

        LocalDateTime now = LocalDateTime.now();
        if (now.getHour() < ENDHOUR) {
            long expectTime = 0;
            if (applicationService.isProduction()) {
                expectTime = new DateTime().plusDays(0).withHourOfDay(19).withMinuteOfHour(RandomUtils.nextInt(0, 30)).getMillis();
            } else {
                expectTime = new DateTime().plusDays(0).plusHours(0).plusMinutes(2).getMillis();
            }
            //关注时间在0点-17点
            Message message = Message.ofSchedule(MQTopicCons.CF,
                    MQTagCons.SEND_SUBSCRIBE_UN_RAISE_PERSON_TODAY_MSG,
                    MQTagCons.SEND_SUBSCRIBE_UN_RAISE_PERSON_TODAY_MSG + "_" + payload.getOpenId() + "_" + System.currentTimeMillis(),
                    payload, expectTime / 1000);
            MessageResult messageResult = producer.send(message);
            log.info("sendSubscribeUnRaisePersonMessageToday_payload:{} message:{} messageResult:{}", JSON.toJSONString(payload), message, messageResult);
        }
    }

    /**
     * 第3天召回消息
     * @param payload
     */
    private void sendSubscribeUnRaisePersonMessageThreeDay(WxSubscribePayload payload) {
        long userId = getUserIdByOpendId(payload, WxConstants.FUNDRAISER_THIRD_TYPE);
        if (userId <= 0) {
            return;
        }

        long expectTime = 0;
        //第二天10点左右发送
        if (applicationService.isProduction()) {
            expectTime = new DateTime().plusDays(2).withHourOfDay(10).withMinuteOfHour(RandomUtils.nextInt(0, 30)).getMillis();
        } else {
            expectTime = new DateTime().plusDays(0).plusHours(0).plusMinutes(2).getMillis();
        }
        //关注时间在0点-17点
        Message message = Message.ofSchedule(MQTopicCons.CF,
                MQTagCons.SEND_SUBSCRIBE_UN_RAISE_PERSON_THREEDAY_MSG,
                MQTagCons.SEND_SUBSCRIBE_UN_RAISE_PERSON_THREEDAY_MSG + "_" + payload.getOpenId() + "_" + System.currentTimeMillis(),
                payload, expectTime / 1000);
        MessageResult messageResult = producer.send(message);
        log.info("sendSubscribeUnRaisePersonMessageToday_payload:{} message:{} messageResult:{}", JSON.toJSONString(payload), message, messageResult);
    }

    /**
     * 根据 openId 获取 userId
     * @param payload
     * @param userThirdType
     * @return
     */
    private long getUserIdByOpendId(WxSubscribePayload payload, int userThirdType) {
        long userId = -1;
        if (payload == null) {
            log.info("payload is null");
            return userId;
        }
        if (payload.getUserThirdType() != userThirdType){
            log.info("关注的公众号不是水滴筹,关注的公众号公众号:{}",payload.getUserThirdType());
            return userId;
        }
        String openId = payload.getOpenId();
        if (StringUtils.isEmpty(openId)) {
            log.info("openId is null");
            return userId;
        }
        // 根据 openId 获取 userId
        OpenIdUserIdModel openIdUserIdResponse = simpleUserAccountDelegate.getUserIdByOpenId(openId);
        if (openIdUserIdResponse == null) {
            log.info("OpenIdUserIdResponse is null. openId:{}", openId);
            return userId;
        }
        return openIdUserIdResponse.getUserId();
    }
}
