package com.shuidihuzhu.cf.mq.consumers;


import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;

import com.shuidihuzhu.cf.model.dailysign.DailySignScoreRecord;
import com.shuidihuzhu.cf.service.crowdfunding.dailysign.DailySignService;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;

import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;

import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 *
 * 每日签到成功
 *
 */
@Service
@Slf4j
@RocketMQListener(id = MQTagCons.CF_DAILY_SIGNIN_SUCCESS,
		tags = MQTagCons.CF_DAILY_SIGNIN_SUCCESS,
		topic = MQTopicCons.CF,
		group = "Cf-ConsumerGroup_CF_DAILY_SIGNIN_SUCCESS")
public class CfDailySignInSuccessConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<DailySignScoreRecord> {

	@Autowired
	private DailySignService dailySignService;

	@Resource(name = "cfRedissonHandler")
	private RedissonHandler redissonHandler;

	/**
	 * 签到成功后需要做两件事：通过积分服务增加积分
	 * @param consumerMessage
	 * @return
	 */
	@Override
	public ConsumeStatus consumeMessage(ConsumerMessage<DailySignScoreRecord> consumerMessage) {

		printReceiveMqMessageLog(consumerMessage);

		DailySignScoreRecord record = consumerMessage.getPayload();
		if(record == null) {
			return ConsumeStatus.CONSUME_SUCCESS;
		}

		String key = "daily-sign-" + record.getSignId();
		String tryLock = null;
		try {
			tryLock = redissonHandler.tryLock(key, 0, 60 * 1000L);
		} catch (InterruptedException e) {
			log.error("tryLock error.", e);
		}

		if(StringUtils.isBlank(tryLock)) {
			log.info("dailysign，tryLock is null. {}", tryLock);
			return ConsumeStatus.RECONSUME_LATER;
		}

		try {
			//TODO: 获取积分
			int score = this.dailySignService.increasePoints(record);
			if(score > 0) {
				return ConsumeStatus.CONSUME_SUCCESS;
			}

		} catch (Exception e) {
			log.error("dailysign error. ", e);
		} finally {
			try {
				if (StringUtils.isNotBlank(tryLock)) {
					redissonHandler.unLock(key, tryLock);
				}
			} catch (Exception e) {
				log.info("", e);
			}
		}

		return ConsumeStatus.RECONSUME_LATER;

	}
}
