package com.shuidihuzhu.cf.mq.consumers.loverank;

import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.delegate.activity.ActivitySubsidyUseDelegate;
import com.shuidihuzhu.cf.facade.loverank.UserLoveRankFace;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.mq.common.BaseMessageConsumer;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RocketMQListener(id = "PaySuccessLoveRankConsumer",
        tags = MQTagCons.CF_DONATION_SUCCESS_SUBSIDY,
        group = "cf-api-love-rank" + MQTagCons.CF_DONATION_SUCCESS_SUBSIDY,
        topic = MQTopicCons.CF)
public class PaySuccessLoveRankConsumer
        extends BaseMessageConsumer<CrowdfundingOrder>
        implements MessageListener<CrowdfundingOrder> {

    @Autowired
    private UserLoveRankFace userLoveRankFace;

    @Override
    protected boolean handle(ConsumerMessage<CrowdfundingOrder> consumerMessage) {
        CrowdfundingOrder order = consumerMessage.getPayload();
        if (order == null) {
            log.error("PaySuccessSubsidyConsumer order null");
            return true;
        }
        return userLoveRankFace.onPaySuccess(order);
    }

    @Override
    protected Logger getLogger() {
        return log;
    }
}
