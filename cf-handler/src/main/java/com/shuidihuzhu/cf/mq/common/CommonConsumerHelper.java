package com.shuidihuzhu.cf.mq.common;

import com.shuidihuzhu.cf.service.EventPublishService;
import com.shuidihuzhu.cf.util.MQLogUtils;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEvent;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2018-07-16  15:30
 */
@Service
@Slf4j
public class CommonConsumerHelper {


    @Autowired
    private EventPublishService publishService;

    /**
     * @param consumerMessage
     * @param handler
     * @param discardable     是否可以在异常的时候丢弃
     * @param <T>
     * @return
     *
     * 使用handler 类名作为tag打印 不推荐
     * @see #consumeMessage(ConsumerMessage, Handler, boolean, Logger)
     *
     */
    @Deprecated
    public <T> ConsumeStatus consumeMessage(ConsumerMessage<T> consumerMessage, Handler<T> handler, boolean discardable) {
        MQLogUtils.logWithSelf(handler, consumerMessage, null);
        boolean success = false;
        try {
            success = handler.handle(consumerMessage);
        } catch (Exception e) {
            MQLogUtils.logErrorWithSelf(handler, consumerMessage, e, null);
            // 如果可以丢弃 则在异常的时候丢弃
            if (discardable) {
                success = true;
            }
        }
        return success ? ConsumeStatus.CONSUME_SUCCESS : ConsumeStatus.RECONSUME_LATER;
    }

    /**
     *
     * @param consumerMessage
     * @param handler
     * @param discardable 异常时是否可以忽略 不重试
     * @param logger
     * @param <T> 消息payload
     * @return
     *
     * 使用外部传入logger打印
     */
    public <T> ConsumeStatus consumeMessage(ConsumerMessage<T> consumerMessage, Handler<T> handler,
                                            boolean discardable, Logger logger) {
        MQLogUtils.logWithLogger(logger, consumerMessage, null);
        boolean success = false;
        try {
            success = handler.handle(consumerMessage);
        } catch (Exception e) {
            MQLogUtils.logErrorWithLogger(logger, consumerMessage, e, null);
            // 如果可以丢弃 则在异常的时候丢弃
            if (discardable) {
                success = true;
            }
        }
        return success ? ConsumeStatus.CONSUME_SUCCESS : ConsumeStatus.RECONSUME_LATER;
    }

    /**
     * 接到mq同时发送springEvent
     * @param consumerMessage
     * @param mapper
     * @param discardable
     * @param <T>
     * @param <E>
     * @return
     */
    public <T, E extends ApplicationEvent> ConsumeStatus consumeMessageWithPublishSpringEvent(
            ConsumerMessage<T> consumerMessage,
            Mapper<E, T> mapper,
            boolean discardable) {
        return consumeMessage(
                consumerMessage,
                getHandlerFromApplicationEventMapper(mapper),
                discardable);
    }

    /**
     * 转换springEvent方法
     * @param mapper
     * @param <T> consumerMessage model
     * @param <E> springEvent model
     * @return
     */
    private <T, E extends ApplicationEvent> Handler<T> getHandlerFromApplicationEventMapper(Mapper<E, T> mapper) {
        return consumerMessage -> {
            publishService.publish(mapper.map(consumerMessage));
            return true;
        };
    }

    public interface Mapper<E extends ApplicationEvent, T> {
        E map(ConsumerMessage<T> consumerMessage);
    }

    public interface Handler<T> {
        /**
         * 处理消息函数
         *
         * @param consumerMessage
         * @return
         */
        boolean handle(ConsumerMessage<T> consumerMessage);
    }

}
