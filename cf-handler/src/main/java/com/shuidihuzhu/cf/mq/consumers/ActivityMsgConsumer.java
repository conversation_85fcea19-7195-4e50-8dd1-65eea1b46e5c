package com.shuidihuzhu.cf.mq.consumers;

import com.shuidihuzhu.cf.activity.constants.ActivityMQConstants;
import com.shuidihuzhu.cf.activity.model.ActivityMsg;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @time 2019/5/23 下午3:19
 * @desc
 */
@Slf4j
@Service
@RocketMQListener(id = ActivityMQConstants.CF_ACTIVITY_MSG_TAG,
        tags = ActivityMQConstants.CF_ACTIVITY_MSG_TAG,
        topic = MQTopicCons.CF,
        group = ActivityMQConstants.CF_ACTIVITY_MSG_TAG + "_OSS_PICTURE")
public class ActivityMsgConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<ActivityMsg> {

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<ActivityMsg> mqMessage) {
        if (Objects.isNull(mqMessage) || Objects.isNull(mqMessage.getPayload())) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }

}
