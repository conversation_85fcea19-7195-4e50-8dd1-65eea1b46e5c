package com.shuidihuzhu.cf.mq.consumers;


import com.shuidihuzhu.cf.facade.risk.CfCaseRiskFacade;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.mq.common.CommonConsumerHelper;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@RocketMQListener(id = MQTagCons.CF_RAISE_CHECK_RISK,
        tags = MQTagCons.CF_RAISE_CHECK_RISK,
        topic = MQTopicCons.CF)
@Slf4j
public class CfRaiseRiskCheckConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<String> {

    @Resource
    private CommonConsumerHelper commonConsumerHelper;

    @Resource
    private CfCaseRiskFacade cfCaseRiskFacade;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<String> consumerMessage) {
        printReceiveMqMessageLog(consumerMessage);
        return commonConsumerHelper.consumeMessage(consumerMessage,
                CfRaiseRiskCheckConsumer.this::handle
                , false);
    }

    private boolean handle(ConsumerMessage<String> consumerMessage) {
        String infoUuid = consumerMessage.getPayload();
        OpResult opResult = cfCaseRiskFacade.onCaseFirstApproveHasDelay72(infoUuid);
        return opResult.isSuccess();
    }
}
