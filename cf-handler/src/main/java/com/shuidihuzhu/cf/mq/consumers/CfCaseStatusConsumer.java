package com.shuidihuzhu.cf.mq.consumers;

import com.shuidihuzhu.cf.biz.crowdfunding.toufang.ICfToufangInviteService;
import com.shuidihuzhu.cf.delegate.WxSubscribeEventDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.UserLabelAlterEnum;
import com.shuidihuzhu.cf.facade.risk.CfCaseRiskFacade;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enums.crowdfunding.CfBaseStatus;
import com.shuidihuzhu.cf.model.crowdfunding.message.CfStatusChangeRecord;
import com.shuidihuzhu.cf.mq.producer.impl.MQProdecer;
import com.shuidihuzhu.cf.service.crowdfunding.CrowdfundingInfoService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.DelayLevel;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 * @DATE 2018/7/16
 */
@Service
@RocketMQListener(id = MQTagCons.CF_STATUS_CHANGE_MSG,
        group = "cf-status-group",
        tags = MQTagCons.CF_STATUS_CHANGE_MSG,
        topic = MQTopicCons.CF)
@Slf4j
public class CfCaseStatusConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<CfStatusChangeRecord> {

    @Autowired
    private MQProdecer mqProdecer;

    @Resource
    private CfCaseRiskFacade cfCaseRiskFacade;

    @Resource
    private CrowdfundingInfoService crowdfundingInfoService;

    @Autowired
    private ICfToufangInviteService cfToufangInviteService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CfStatusChangeRecord> consumerMessage) {

        printReceiveMqMessageLog(consumerMessage);

        CfStatusChangeRecord record = consumerMessage.getPayload();

        if (record == null){
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        String caseUuId = record.getCaseUuId();
        int status = record.getStatus();

        try {
            // 案例结束
            if (status == CfBaseStatus.cf_end.getCode()) {
                cfCaseRiskFacade.onCaseEnd(caseUuId);
                mqProdecer.sendUserLabelAlterMq((int) record.getCaseId(), record.getUserId(), UserLabelAlterEnum.CASE_END, DelayLevel.S10);

//                sendTreeMsg(record);
            }
            //如果案例是新发起的
            if (status == CfBaseStatus.cf_doing.getCode()){
                crowdfundingInfoService.deleteSnapshot(record.getUserId());
                mqProdecer.sendUserLabelAlterMq((int) record.getCaseId(), record.getUserId(), UserLabelAlterEnum.DELETE_DRAFT, DelayLevel.S10);
                sendWxPyqStatCallback(record.getUserId());
            }
        } catch (Exception e) {
            log.error("record: {}, error: {}", record, e);
        }

        log.info("status change record={}",record);
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private void sendWxPyqStatCallback(long userId) {
        try{
            cfToufangInviteService.callbackWxPyqCfDoingLog(userId);
        }catch (Exception e){
            log.error("sendWxPyqStatCallback exception:{}",userId);
        }
    }
}
