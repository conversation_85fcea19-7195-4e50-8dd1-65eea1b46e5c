package com.shuidihuzhu.cf.mq.consumers.usertransfer;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.account.model.UserTransferData;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.biz.crowdfunding.verification.CrowdFundingVerificationBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.dao.crowdfunding.CfUserStatDao;
import com.shuidihuzhu.cf.delegate.ICFFinanceFeignDelegate;
import com.shuidihuzhu.cf.delegate.ICfGrowthtoolDelegate;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.CfPropertyTransferEnum;
import com.shuidihuzhu.cf.enums.user.UserStatStatusEnum;
import com.shuidihuzhu.cf.model.contribute.CfContributeOrder;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.mq.consumers.BaseConsumerPrintReceiveLog;
import com.shuidihuzhu.cf.service.combineorder.CfContributeOrderService;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.*;

import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RocketMQListener(id = "CfOrderTransferMQListner", tags = MQTagCons.ACCOUNT_TRANSFER, topic = MQTopicCons.HZ_DATA_TRANSFER)
public class CfOrderTransferMQListner extends BaseConsumerPrintReceiveLog implements MessageListener<UserTransferData> {

    @Autowired
    private UserInfoDelegate userInfoDelegate;
    @Autowired
    private CrowdfundingOrderBiz crowdfundingOrderBiz;
    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private CfUserStatBiz cfUserStatBiz;
    @Autowired
    private CfOrderTransferHistoryBiz orderTransferHistoryBiz;
    @Autowired
    private ICfGrowthtoolDelegate cfGrowthtoolDelegate;
    @Autowired
    private ICFFinanceFeignDelegate icfFinanceFeignDelegate;
    @Autowired
    private CfInfoBlessingBiz blessingBiz;
    @Autowired
    private CfFirstApproveBiz firstApproveBiz;
    @Autowired
    private CrowdFundingVerificationBiz verificationBiz;
    @Autowired
    private CfUserStatDao cfUserStatDao;
    @Autowired
    private CfContributeOrderService contributeOrderService;
    @Autowired(required = false)
    private Producer producer;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<UserTransferData> mqMessage) {
        UserTransferData userTransferData = mqMessage.getPayload();
        if (null == userTransferData) {
            log.error("--usertransferdata fail. message is null");
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        transferCfOrder(userTransferData);
        long toUserId = userTransferData.getToUserId();
        MessageResult send = producer.send(new Message(MQTopicCons.HZ_DATA_TRANSFER, MQTopicCons.DEDALY_HZ_DATA_TRANSFER,
                MQTopicCons.DEDALY_HZ_DATA_TRANSFER + "_" + toUserId, userTransferData, DelayLevel.M3));
        log.info("usertransferdata success. info{}，data:{}", JSON.toJSONString(userTransferData),send);
        return ConsumeStatus.CONSUME_SUCCESS;

    }


    public int transferCfOrder(UserTransferData userTransferData) {
        if (null == userTransferData) {
            return 0;
        }
        long fromUserId = userTransferData.getFromUserId();
        long toUserId = userTransferData.getToUserId();
        // 不处理<1数据转移
        if (fromUserId < 1 || toUserId < 1) {
            return 0;
        }

        boolean needTransfer = needTransfer(fromUserId, toUserId);
        if (needTransfer) {
            doTransfer(fromUserId, toUserId);

        }
        return 0;
    }

    public void doTransfer(long fromUserId, long toUserId) {
        // 异步上报大数据
        transferHistoryToDs(fromUserId, toUserId);
        // 转移订单
        transferOrder(fromUserId, toUserId);
        // 转移捐赠平台的订单
        transferContributeOrder(fromUserId, toUserId);

        // 转移案例,20211011sea后台换发起人手机号收口
        crowdfundingInfoBiz.transferCf(fromUserId, toUserId, 0);
        // 转移用户统计信息
        transferCfUserStat(fromUserId, toUserId);
        // 转移证实
        transferVerification(fromUserId, toUserId);
        // 转移微信事件...

        // 转移BD关联数据
        cfGrowthtoolDelegate.transferCfVolunteer(fromUserId,toUserId);

        // 2020-11-19新增
        // 加油记录
        transferBlessing(fromUserId, toUserId);
    }

    private void transferVerification(long fromUserId, long toUserId) {

        try {
            List<CrowdFundingVerification> verifications = verificationBiz.queryByVerifyUserId(fromUserId);

            List<CfPropertyTransferHistory> historyList = Lists.newArrayList();
            verifications.forEach(item -> {
                log.info("转移证实, fromUserId:{}\ttoUserId:{}\tverification:{}", fromUserId, toUserId, JSON.toJSONString(item));
                verificationBiz.updateVerifyUserId(item.getId(), toUserId);
                CfPropertyTransferHistory history = new CfPropertyTransferHistory(
                        item.getVerifyUserId(),
                        toUserId,
                        item.getId(),
                        CfPropertyTransferEnum.USER_STAT_VERIFICATION
                );
                historyList.add(history);
            });
            orderTransferHistoryBiz.addBatch(historyList);
        }  catch (Exception e) {
            log.error("转移证实失败", e);
        }
    }

    private void transferBlessing(long fromUserId, long toUserId) {

        try {
            List<CfInfoBlessing> blessings = blessingBiz.selectBlessByUserId(fromUserId);
            if (CollectionUtils.isEmpty(blessings)) {
                log.info("当前用户没有加油数据,不做转移 fromUserId:{}", fromUserId);
                return;
            }
            List<Integer> ids = blessings.stream().map(CfInfoBlessing::getId).collect(Collectors.toList());
            blessingBiz.updateUserIdByIds(ids, toUserId);
            List<CfPropertyTransferHistory> historyList = Lists.newArrayList();
            ids.forEach(id -> {
                historyList.add(new CfPropertyTransferHistory(fromUserId, toUserId, id, CfPropertyTransferEnum.USER_BLESSING_REAL));
            });
            orderTransferHistoryBiz.addBatch(historyList);
        } catch (Exception e) {
            log.error("转移加油数据异常", e);
        }
    }


    private void transferCfUserStat(long fromUserId, long toUserId) {
        List<CfUserStat> fromUserStats= cfUserStatBiz.getByUserIds(Sets.newHashSet(fromUserId));
        List<CfUserStat> toUserStats = this.cfUserStatBiz.getByUserIds(Sets.newHashSet(toUserId));
        // toUserId : CfUserStat
        if (CollectionUtils.isEmpty(fromUserStats)){
            return;
        }
        CfUserStat fromStat = fromUserStats.get(0);
        CfUserStat toUserStat = null;
        if (!CollectionUtils.isEmpty(toUserStats)){
            toUserStat = toUserStats.get(0);
        }
        List<CfPropertyTransferHistory> transferHistories = orderTransferHistoryBiz.get(
                fromUserId,
                toUserId,
                fromStat.getId(),
                CfPropertyTransferEnum.USER_STAT
        );
        //不等于空则资产进行转移
        if(!CollectionUtils.isEmpty(transferHistories)){
            return;
        }
        if (toUserStat != null) {
            toUserStat.setAmount(toUserStat.getAmount() + fromStat.getAmount());
            toUserStat.setDonationCount(toUserStat.getDonationCount() + fromStat.getDonationCount());
            toUserStat.setScore(toUserStat.getScore() + fromStat.getScore());
            toUserStat.setBlessingCount(toUserStat.getBlessingCount() + fromStat.getBlessingCount());
            toUserStat.setVerifyUserCount(toUserStat.getVerifyUserCount() + fromStat.getVerifyUserCount());
            cfUserStatBiz.updateBatch(Lists.newArrayList(toUserStat));
        } else {
            toUserStat = new CfUserStat();
            BeanUtils.copyProperties(fromStat,toUserStat);
            toUserStat.setUserId(toUserId);
            toUserStat.setInitStatus(UserStatStatusEnum.INITED.getType());
            cfUserStatDao.insertContainStatus(toUserStat);

        }
        CfPropertyTransferHistory history = new CfPropertyTransferHistory(
                fromStat.getUserId(),
                toUserId,
                fromStat.getId(),
                CfPropertyTransferEnum.USER_STAT
        );
        orderTransferHistoryBiz.addBatch(Lists.newArrayList(history));
    }

    // 订单资产转移
    private void transferOrder(long fromUserId, long toUserId) {
        List<CrowdfundingOrder> orders = crowdfundingOrderBiz.getAllPayByUserIdsFromSharding(Sets.newHashSet(fromUserId));
        if(CollectionUtils.isEmpty(orders)){
            return;
        }

        List<CfPropertyTransferHistory> historyList = Lists.newArrayList();
        orders.forEach(order ->{
            // 记录转移历史
            CfPropertyTransferHistory history = new CfPropertyTransferHistory(
                    order.getUserId(),
                    toUserId,
                    order.getId(),
                    CfPropertyTransferEnum.ORDER
            );
            historyList.add(history);
            order.setUserId(toUserId);
        });
        log.info("转移订单, fromUserId:{}\ttoUserId:{}\t orders:{}", fromUserId, toUserId, JSON.toJSON(orders));
        this.crowdfundingOrderBiz.updateUserIdBatch(orders,fromUserId);
        orderTransferHistoryBiz.addBatch(historyList);
        // 资金系统内转移资产
        icfFinanceFeignDelegate.transferOrder(orders, fromUserId);
    }

    private void transferContributeOrder(long fromUserId, long toUserId) {
        // 捐赠平台的单转移
        List<CfContributeOrder> contributeList = contributeOrderService.selectByUserIds(Lists.newArrayList(fromUserId), null);
        if (CollectionUtils.isEmpty(contributeList)) {
            return;
        }

        List<CfPropertyTransferHistory> historyList = Lists.newArrayList();
        List<Long> ids = Lists.newArrayList();
        for (CfContributeOrder contributeOrder : contributeList) {
            CfPropertyTransferHistory history = new CfPropertyTransferHistory(
                    contributeOrder.getUserId(),
                    toUserId,
                    contributeOrder.getId(),
                    CfPropertyTransferEnum.ORDER
            );
            historyList.add(history);
            ids.add(contributeOrder.getId());
        }

        log.info("转移订单, fromUserId:{}\ttoUserId:{}\t orders:{}", fromUserId, toUserId, JSON.toJSON(historyList));
        orderTransferHistoryBiz.addBatch(historyList);
        contributeOrderService.updateUserByIds(ids, toUserId);
    }

    private static final String FROM_USE_ID = "from_user_id";
    private static final String TO_USER_ID = "to_user_id";
    private static final String CREATE_TIME = "create_time";

    // 上报大数据
    @Async
    public void transferHistoryToDs(long fromUserId, long toUserId) {
        Map<String, Object> ext = new HashMap<>();
        ext.put(FROM_USE_ID, fromUserId);
        ext.put(TO_USER_ID, toUserId);
        ext.put(CREATE_TIME, DateUtil.getCurrentDateStr()); // 创建时间
        try {
//            analytics.track(String.valueOf(-1024), false, "cf", "user_transfer", ext);
            log.info("资产转移上报成功 case:{} ", JSON.toJSONString(ext));
        } catch (Exception e) {
            log.error("资产转移上报失败 case:{} 上报异常", JSON.toJSONString(ext), e);
            return;
        }
    }

    /**
     * 校验是否需要资产转移
     */
    private boolean needTransfer(long fromUserId, long toUserId) {
        // 不处理小于1的数据
        if (fromUserId < 1 || toUserId < 1) {
            return false;
        }
        // from_user_id 没有手机号且没有微信才做资产转移。
        UserInfoModel userInfoModel = userInfoDelegate.getUserInfoByUserId(fromUserId);
        if (userInfoModel != null && !Strings.isBlank(userInfoModel.getCryptoMobile())) {
            return false;
        }
        return true;
    }



}
