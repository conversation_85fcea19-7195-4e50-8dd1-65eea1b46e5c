package com.shuidihuzhu.cf.mq.consumers.topList;

import com.shuidihuzhu.cf.biz.topList.CfTopListSummaryBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.topList.CfTopListConstant;
import com.shuidihuzhu.cf.mq.model.InnerUserInfoMessage;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
@RefreshScope
@RocketMQListener(id = MQTagCons.CASE_USER_RANK_LIST_MSG,
        tags = MQTagCons.CASE_USER_RANK_LIST_MSG,
        topic = MQTopicCons.CF_LESS_QUEUE_TOPIC,
        group = "cf-api_" + MQTagCons.CASE_USER_RANK_LIST_MSG)
public class CfTopListUserDataChangeConsumer  implements MessageListener<InnerUserInfoMessage> {

    @Autowired
    private CfTopListSummaryBiz summaryBiz;
    @Resource(name = "cfRedissonHandler")
    private RedissonHandler redissonHandler;
    @Value("${apollo.case.user.rank.consumer.on:1}")
    private int caseUserRankListOn = 1;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<InnerUserInfoMessage> mqMessage) {

        log.info("收到用户贡献数据变更消息:{}", mqMessage);

        if (caseUserRankListOn == 0) {
            log.info("开关已开，不消费");
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        if (mqMessage == null || mqMessage.getPayload() == null) {
            log.info("当前消费的数据为空 直接返回");
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        int caseId = mqMessage.getPayload().getCaseId();
        long userId = mqMessage.getPayload().getUserId();
        String identifier = "";

        String lockName = CfTopListConstant.USER_DATA_CHANGE_LOCK_NAME + caseId;
        try {
            identifier = redissonHandler.tryLock(lockName, 5 * 1000);

            if (StringUtils.isBlank(identifier)) {
                log.info("榜单数据变更没有获取到锁直接返回. caseId:{} userId:{}", caseId, userId);
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            summaryBiz.createOrFlushTopListByChange(caseId, userId);
        } catch (Throwable e) {
            log.error("消息消费异常 caseId:{} userId:{}", caseId, userId, e);
        } finally {
            try {
                if (StringUtils.isNotBlank(identifier)) {
                    redissonHandler.unLock(lockName, identifier);
                }
            } catch (Exception e) {
                log.warn("toplist consumeMessage unLock caseId:{} userId:{} err", caseId, userId, e);
            }
        }

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
