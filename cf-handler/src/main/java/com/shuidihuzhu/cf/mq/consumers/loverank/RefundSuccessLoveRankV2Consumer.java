package com.shuidihuzhu.cf.mq.consumers.loverank;

import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingOrderBiz;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.facade.loverank.UserLoveRankFace;
import com.shuidihuzhu.cf.finance.model.CfDonorRefundApply;
import com.shuidihuzhu.cf.finance.mq.FinanceMQTagCons;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.mq.consumers.BaseConsumerPrintReceiveLog;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 只处理单笔退款
 *
 * <AUTHOR>
 **/
@Slf4j
@Service
@RefreshScope
@RocketMQListener(id = "RefundSuccessLoveRankConsumerV2",
        tags = FinanceMQTagCons.CF_DONOR_REFUND_SUCCESS_MSG,
        topic = MQTopicCons.CF,
        group = "cf-api-love-rank-v2" + FinanceMQTagCons.CF_DONOR_REFUND_SUCCESS_MSG)
public class RefundSuccessLoveRankV2Consumer extends BaseConsumerPrintReceiveLog implements MessageListener<CfDonorRefundApply> {

    @Resource(name = "cfRedissonHandler")
    private RedissonHandler cfRedissonHandler;
    @Autowired
    CrowdfundingOrderBiz crowdfundingOrderBiz;

    @Autowired
    private UserLoveRankFace userLoveRankFace;

    @Value("${apollo.new.count.remove.refund.case:100000000}")
    private long useSqlRemoveRefundCaseId;


    /**
     * 发送时机，整单退款后发送
     *
     * @param mqMessage
     * @return
     */
    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CfDonorRefundApply> mqMessage) {
        printReceiveMqMessageLog("refund success msg", mqMessage);
        CfDonorRefundApply refundSuccessModel = mqMessage.getPayload();
        if (refundSuccessModel == null) {
            log.warn("msg is null");
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        int infoId = refundSuccessModel.getInfoId();
        // 老案例不处理单笔退款
        if (infoId <= useSqlRemoveRefundCaseId) {
            log.debug("爱心榜单单笔退款逻辑-案例ID: {} 不处理单笔退款", infoId);
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        long orderId = refundSuccessModel.getOrderId();
        //查找这个退款的转发来源
        CrowdfundingOrder crowdfundingOrder = crowdfundingOrderBiz.getById(orderId);
        if (crowdfundingOrder == null) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        //需要确保幂等
        String tryLock = null;
        String lockKey = "RefundSuccessLoveRankConsumerV2_" + orderId;
        try {
            tryLock = cfRedissonHandler.tryLock(lockKey, 0, 60 * 60 * 1000L);
            //未获取到锁
            if (StringUtils.isBlank(tryLock)) {
                log.warn("repeat refund success msg orderId:{}", orderId);
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            userLoveRankFace.refundSuccess(crowdfundingOrder);
            return ConsumeStatus.CONSUME_SUCCESS;
        } catch (Exception e) {
            log.error("refund success consume error, msg:{}", mqMessage, e);
            try {
                if (StringUtils.isNotBlank(tryLock)) {
                    cfRedissonHandler.unLock(lockKey, tryLock);
                }
            } catch (Exception ex) {
                log.error("refund consumer unlock error, orderId:{}", orderId, ex);
            }
            return ConsumeStatus.RECONSUME_LATER;
        }
    }

}
