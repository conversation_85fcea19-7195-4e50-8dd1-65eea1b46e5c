package com.shuidihuzhu.cf.mq.consumers;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.IOssPictureService;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment;
import com.shuidihuzhu.cf.model.crowdfunding.OssPictureAttrDO;
import com.shuidihuzhu.cf.service.IOssToCosTransService;
import com.shuidihuzhu.cf.service.httpclient.CosPictureAttrHttpClinetService;
import com.shuidihuzhu.cf.service.httpclient.OssPictureAttrHttpClinetService;
import com.shuidihuzhu.cf.service.impl.OssToCosTransServiceImpl;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import com.shuidihuzhu.pf.common.v2.enums.ImageDomainEnum;
import com.shuidihuzhu.pf.common.v2.util.ImageUrlUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @time 2019/5/23 下午3:19
 * @desc
 */
@Slf4j
@Service
@RocketMQListener(id = MQTagCons.CF_ATTACHMENT_RISK_ATTR_STAT,
        tags = MQTagCons.CF_ATTACHMENT_RISK_ATTR_STAT,
        topic = MQTopicCons.CF,
        group = MQTagCons.CF_ATTACHMENT_RISK_ATTR_STAT + "_OSS_PICTURE")
public class AttachmentRiskStatInfoConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<List<CrowdfundingAttachment>> {

    @Autowired
    private IOssPictureService ossPictureService;

    @Autowired
    private OssPictureAttrHttpClinetService ossPictureAttrHttpClinetService;
    @Autowired
    private CosPictureAttrHttpClinetService cosPictureAttrHttpClinetService;
    @Autowired
    private Producer producer;
    @Autowired
    private IOssToCosTransService ossToCosTransService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<List<CrowdfundingAttachment>> mqMessage) {

        List<CrowdfundingAttachment> attachments = mqMessage.getPayload();

        if(CollectionUtils.isEmpty(attachments)){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        log.info("attachment risk consumer size:{}", attachments.size());

        List<OssPictureAttrDO> attrDOs = Lists.newArrayList();
        for (CrowdfundingAttachment attachment : attachments){
            if(Objects.isNull(attachment)){
                continue;
            }

            String url = attachment.getUrl();
            int id = attachment.getId();
            log.info("queryPicAttrExecute request id:{} url:{}", id, url);
            String domain = ImageUrlUtil.getDomain(url);
            String cosDomain = ImageDomainEnum.getCosDomain(domain);
            if (StringUtils.isNotBlank(cosDomain)) {
                String ossJson = ossPictureAttrHttpClinetService.queryPicAttrExecute(id, url + "?x-oss-process=image/info");
                Map<String, Object> kvMap = ossPictureAttrHttpClinetService.parse(ossJson);
                OssPictureAttrDO attrDO = ossPictureAttrHttpClinetService.vauleOf(kvMap, attachment);
                if (Objects.nonNull(attrDO)) {
                    attrDOs.add(attrDO);
                }
            }else {
                String exifStr = cosPictureAttrHttpClinetService.queryPicAttrExecute(id, url + "!cf_mtr_1080_nw" + "?exif");
                Map<String, Object> kvMap = cosPictureAttrHttpClinetService.parseExif(exifStr);
                if (MapUtils.isEmpty(kvMap)){
                    continue;
                }
                String infoStr = cosPictureAttrHttpClinetService.queryPicAttrExecute(id, url + "!cf_mtr_1080_nw" + "?imageInfo");
                cosPictureAttrHttpClinetService.parseInfo(kvMap, infoStr);
                OssPictureAttrDO attrDO = cosPictureAttrHttpClinetService.valueOf(kvMap, attachment);
                if (Objects.nonNull(attrDO)) {
                    attrDOs.add(attrDO);
                }
            }
        }

        List<OssPictureAttrDO> extAttrDos = getExtraOssPic(attrDOs,  attachments);
        if (CollectionUtils.isNotEmpty(extAttrDos)) {
            log.info("需要插入额外的数据 param:{}", extAttrDos);
            attrDOs.addAll(extAttrDos);
        }
        ossPictureService.batchInsert(attrDOs);

        sendMarkMsg(attachments);
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private List<OssPictureAttrDO> getExtraOssPic(List<OssPictureAttrDO> attrDOs, List<CrowdfundingAttachment> attachments) {
        List<OssPictureAttrDO> extraAttrList = Lists.newArrayList();
        for (CrowdfundingAttachment attachment : attachments) {
            boolean found = false;
            for (OssPictureAttrDO attrDO : attrDOs) {
                if (Objects.equals(attachment.getId(), attrDO.getAttaId())) {
                    found = true;
                    break;
                }
            }

            if (!found) {
                extraAttrList.add(OssPictureAttrDO.buildFromAttachImg(attachment));
            }
        }

        return extraAttrList;
    }

    private void sendMarkMsg(List<CrowdfundingAttachment> attachments) {

        Message msg = new Message(MQTopicCons.CF, MQTagCons.CF_ATTACHMENT_MARK_ATTR_STAT,
                MQTagCons.CF_ATTACHMENT_MARK_ATTR_STAT + "_" + System.currentTimeMillis(), attachments);

        MessageResult msgResult = producer.send(msg);

        log.info("图片各种属性标签发送 msg:{} result:{}", msg, msgResult);
    }
}
