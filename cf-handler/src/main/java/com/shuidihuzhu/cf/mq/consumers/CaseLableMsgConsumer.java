package com.shuidihuzhu.cf.mq.consumers;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.biz.crowdfunding.CaseLabelBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enums.crowdfunding.CaseLableEnum;
import com.shuidihuzhu.cf.mq.payload.CaseLabelPayload;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@RocketMQListener(id = MQTagCons.CF_CASE_LABEL,
        tags = MQTagCons.CF_CASE_LABEL,
        topic = MQTopicCons.CF)
@Slf4j
public class CaseLableMsgConsumer implements MessageListener<String> {

    @Autowired
    private CaseLabelBiz caseLabelBiz;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<String> mqMessage) {
        int reconsumeTimes = mqMessage.getReconsumeTimes();
        if (reconsumeTimes > 1) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        try {
            String payload = mqMessage.getPayload();
            CaseLabelPayload caseLabelPayload = JSON.parseObject(payload, CaseLabelPayload.class);
            log.info("begin consumer message:{}", caseLabelPayload);
            int result = caseLabelBiz.insert(caseLabelPayload.getCaseId(), caseLabelPayload.getActiveProfile(), CaseLableEnum.RAISE_ENV);
            if (result > 0) {
                log.info("consumer message success,message:{}", caseLabelPayload);
                return ConsumeStatus.CONSUME_SUCCESS;
            }
        } catch (Exception e) {
            log.error("consumer message fail: ", e);
        }
        return ConsumeStatus.RECONSUME_LATER;
    }
}
