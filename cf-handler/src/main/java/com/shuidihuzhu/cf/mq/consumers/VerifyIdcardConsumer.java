package com.shuidihuzhu.cf.mq.consumers;

import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.account.verify.client.menu.IdCardVerifyResultEnum;
import com.shuidihuzhu.account.verify.client.model.VerifyIdcardVO;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.IdcardVerifyStatus;
import com.shuidihuzhu.cf.model.crowdfunding.UserRealInfo;
import com.shuidihuzhu.cf.service.huzhugrpc.HzUserRealInfoService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@RocketMQListener(id = MQTagCons.IDCARD_VERIFIED,
		topic = MQTopicCons.VERIFY_ID_CARD,
		tags = MQTagCons.IDCARD_VERIFIED)
public class VerifyIdcardConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<VerifyIdcardVO> {
	private static final Logger LOGGER = LoggerFactory.getLogger(VerifyIdcardConsumer.class);
	private static final int CF_MQ_BIZ_TYPE = 2;

	private static final String CF_SOURCE = "publictity";

	@Resource
	private HzUserRealInfoService hzUserRealInfoService;
	@Resource
    private UserInfoDelegate userInfoDelegate;

	public VerifyIdcardConsumer() {

	}

	public boolean onMessage(VerifyIdcardVO userIdcardVerifyVo) {
		LOGGER.info("VerifyIdcardHandler onMessage userIdcardVerifyVo:{}", JSONObject.toJSONString(userIdcardVerifyVo));
		Integer orderId;
		String ext = userIdcardVerifyVo.getExt();
		if (StringUtils.isNotEmpty(ext) && ext.contains(CF_SOURCE)){
			String[] exts = ext.split("_");
			orderId = Integer.parseInt(exts[0]);
		}else {
			orderId = StringUtils.isNumeric(userIdcardVerifyVo.getExt())? Integer.parseInt(userIdcardVerifyVo.getExt()) : null;
		}

		if (orderId == null || orderId <= 0) {
			LOGGER.error("orderId:[{}] invalid!", orderId);
			return true;
		}
		UserRealInfo userRealInfo = this.hzUserRealInfoService.getById(orderId);
		LOGGER.info("VerifyIdcardHandler onMessage userRealInfo:{}", userRealInfo);
		if (userRealInfo == null) {
			LOGGER.error("userRealInfo: invalid!");
			return true;
		}
		if (!userRealInfo.getCryptoIdCard().equals(userIdcardVerifyVo.getIdCard())
		    || !userRealInfo.getName().equals(userIdcardVerifyVo.getName())) {
			LOGGER.error("userRealInfo.content:[{}] invalid!", userRealInfo);
			return true;
		}
		IdcardVerifyStatus idcardVerifyStatus = IdcardVerifyStatus.fromCode(userRealInfo.getIdcardVerifyStatus());
		if (idcardVerifyStatus == IdcardVerifyStatus.HANDLE_SUCCESS
		    || idcardVerifyStatus == IdcardVerifyStatus.HANDLE_FAILED) {
			return true;
		}
		try {
			return update(userRealInfo, userIdcardVerifyVo);
		} catch (Exception e) {
			LOGGER.error("", e);
			return false;
		}
	}

	private boolean update(UserRealInfo userRealInfo, VerifyIdcardVO userIdcardVerifyVo) {
        IdCardVerifyResultEnum verifyResult = userIdcardVerifyVo.getCode();
        long userId = userRealInfo.getUserId();
		if (verifyResult == IdCardVerifyResultEnum.MATCH) {
			this.hzUserRealInfoService.updateIdcardVerifyStatus(userRealInfo.getId(), IdcardVerifyStatus.HANDLE_SUCCESS, "",
			                                              "");
            UserInfoModel userInfoByUserId = userInfoDelegate.getUserInfoByUserId(userId);
			if (StringUtils.isBlank(userInfoByUserId.getCryptoIdCard())) {
                userInfoDelegate.updateRealNameAndCryptoIdCard(userId,
                        userIdcardVerifyVo.getName(), userIdcardVerifyVo.getIdCard());
            }
        } else {
			this.hzUserRealInfoService.updateIdcardVerifyStatus(userRealInfo.getId(), IdcardVerifyStatus.HANDLE_FAILED, "",
			                                              "");
		}
		return true;
	}

	@Override
	public ConsumeStatus consumeMessage(ConsumerMessage<VerifyIdcardVO> mqMessage) {

		printReceiveMqMessageLog(mqMessage);

		VerifyIdcardVO verifyIdcardVO = mqMessage.getPayload();
		if (verifyIdcardVO.getBizType() == CF_MQ_BIZ_TYPE) {
			onMessage(verifyIdcardVO);
		}
		return ConsumeStatus.CONSUME_SUCCESS;
	}
}
