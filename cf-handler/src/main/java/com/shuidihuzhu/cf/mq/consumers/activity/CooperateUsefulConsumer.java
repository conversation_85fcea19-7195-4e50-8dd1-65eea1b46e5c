package com.shuidihuzhu.cf.mq.consumers.activity;

import com.shuidihuzhu.cf.activity.model.DonateCooperateFeeVO;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.mq.consumers.BaseConsumerPrintReceiveLog;
import com.shuidihuzhu.cf.service.activity.subsidy.ActivityKeyAreaService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by dongcf on 2020/4/2
 * 配转有效的消费
 */
@Service
@Slf4j
@RocketMQListener(id = "CF_COOPERATE_USEFUL_CONSUMER", topic = MQTopicCons.CF,
        tags = MQTagCons.BILL_USE_TAG,
        group ="CF-API_ConsumerGroup_CF_COOPERATE_USEFUL_CONSUMER")
public class CooperateUsefulConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<DonateCooperateFeeVO> {

    @Autowired
    private ActivityKeyAreaService activityKeyAreaService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<DonateCooperateFeeVO> mqMessage) {
        printReceiveMqMessageLog("cooperate useful message", mqMessage);

        DonateCooperateFeeVO feeVO = mqMessage.getPayload();
//        ActivityVenueStatusVo statusVo = venueService.getVenueStatusByCooperateId(feeVO.getActivityId());
//        if(statusVo == null || !statusVo.inActivity()){
//            return ConsumeStatus.CONSUME_SUCCESS;
//        }
//        this.activityVenueCaseBiz.updateCooperateDonateByCaseId((int)feeVO.getMoney(),(int)statusVo.getActivityId(),feeVO.getCaseId());
        try {
            activityKeyAreaService.onSubsidyCallback(feeVO);
        } catch (Exception e) {
            log.error("activityKeyAreaService onSubsidyCallback fail caseId:{}", feeVO.getCaseId(), e);
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
