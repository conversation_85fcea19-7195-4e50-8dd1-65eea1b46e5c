package com.shuidihuzhu.cf.mq.consumers;

import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.message.MulitMpPushBiz;
import com.shuidihuzhu.cf.clinet.event.center.enums.UserOperationTypeEnum;
import com.shuidihuzhu.cf.clinet.event.center.model.CfUserEvent;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.delegate.UserThirdDelegate;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.param.CrowdFundingVerificationParam;
import com.shuidihuzhu.cf.service.huzhugrpc.HzOrderService;
import com.shuidihuzhu.cf.service.msg.MsgClientV2Service;
import com.shuidihuzhu.cf.util.TextUtil;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.*;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * Author: Alvin Tian
 * Date: 2017/11/9 20:33
 */
@Service
@RocketMQListener(id = MQTagCons.CF_VERIFICATION_SUCCESS,
        tags = MQTagCons.CF_VERIFICATION_SUCCESS,
        topic = MQTopicCons.CF,
        group = "Cf-ConsumerGroup_CF_VERIFICATION_SUCCESS")
@Slf4j
@RefreshScope
public class CfVerificationSuccessConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<CrowdFundingVerificationParam> {

    private static final Logger LOGGER = LoggerFactory.getLogger(CfVerificationSuccessConsumer.class);
    private static final String EVENT_CENTER_SWITCH_ON = "1";
    @Autowired
    MulitMpPushBiz mulitMpPushBiz;
    @Autowired
    UserThirdDelegate userThirdDelegate;
    @Autowired
    private UserInfoDelegate userInfoDelegate;
    @Resource(name = "cfRedissonHandler")
    private RedissonHandler redissonHandler;
    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;
    @Resource
    private HzOrderService hzOrderService;
    @Autowired(required = false)
    private Producer producer;
    @Resource
    private MeterRegistry meterRegistry;
    @Value("${apollo.event-center.213-317-switch:0}")
    private String eventCenterSwitch;
    @Resource
    private MsgClientV2Service msgClientV2Service;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CrowdFundingVerificationParam> mqMessage) {
        printReceiveMqMessageLog(mqMessage);

        LOGGER.info("证实回调 {}", mqMessage);

        CrowdFundingVerificationParam param = mqMessage.getPayload();

        String key = mqMessage.getKeys();
        String tryLock = null;
        try {
            tryLock = redissonHandler.tryLock(key, 0, 60 * 1000L);
        } catch (Exception e) {
            LOGGER.error("", e);
        }

        if (StringUtils.isBlank(tryLock)) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }


        try {
            long userId = param.getVerifyUserId();
            LOGGER.info("推送后发送证实消息... userId: {}", userId);

            CrowdfundingInfo cfInfo = crowdfundingInfoBiz.getFundingInfo(param.getInfoUuid());
            if (null == cfInfo || StringUtils.isEmpty(cfInfo.getTitle())) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            //发送用户事件
            if (EVENT_CENTER_SWITCH_ON.equals(eventCenterSwitch)) {
                sendUserEvent(userId, cfInfo);
                return ConsumeStatus.CONSUME_SUCCESS;
            }

            String msgTitle = cfInfo.getTitle();
            if (msgTitle.length() >= 25) {
                msgTitle = msgTitle.substring(0, 25);
            }

            String nickname = "亲";
            UserInfoModel userInfoModel = userInfoDelegate.getUserInfoByUserId(userId);
            if (userInfoModel != null) {
                nickname = StringUtils.defaultIfBlank(userInfoModel.getNickname(), "亲");
            }


            if (hasOrder(userId)) {
                String modelNum0 = "template317";
                Map<Long, Map<Integer, String>> msgMap = Maps.newHashMap();
                HashMap<Integer, String> params = Maps.newHashMap();
                params.put(2, TextUtil.limitComment(msgTitle));
                params.put(3, cfInfo.getInfoId());
                msgMap.put(userId, params);

                msgClientV2Service.sendWxParamsMsg(modelNum0, msgMap);

                Tags of = Tags.of("subBizType", "317", "modelNum", "template317");
                Counter.builder("CF_VERIFICATION_SUCCESS").tags(of).register(meterRegistry).increment();
                return ConsumeStatus.CONSUME_SUCCESS;
            } else {
                String infoUuid = param.getInfoUuid();
                    send213(userId, infoUuid, msgTitle, nickname);
                Tags of = Tags.of("subBizType", "213", "modelNum", "template213");
                Counter.builder("CF_VERIFICATION_SUCCESS").tags(of).register(meterRegistry).increment();
            }
        } catch (Exception e) {
            LOGGER.error("发送证实消息失败...", e);
        } finally {
            try {
                if (StringUtils.isNotBlank(tryLock)) {
                    redissonHandler.unLock(key, tryLock);
                }
            } catch (Exception e) {
                log.warn("consumeMessage unLock err", e);
            }
        }

        return ConsumeStatus.CONSUME_SUCCESS;
    }

    public void send213(long userId, String infoUuid, String msgTitle, String nickname) {
        String modelNum0 = "template213";
        Map<Long, Map<Integer, String>> msgMap = Maps.newHashMap();
        HashMap<Integer, String> params = Maps.newHashMap();
        params.put(1, nickname);
        params.put(2, msgTitle);
        params.put(3, infoUuid);
        msgMap.put(userId, params);

        msgClientV2Service.sendWxParamsMsg(modelNum0, msgMap);
    }

    private boolean hasOrder(long userId) {
        return hzOrderService.isHZMember(userId, false);
    }

    private void sendUserEvent(long userId, CrowdfundingInfo cfInfo) {
        int caseId = cfInfo.getId();
        try {
            CfUserEvent cfUserEvent = new CfUserEvent();
            cfUserEvent.setUserId(userId);
            cfUserEvent.setCaseId(caseId);
            cfUserEvent.setType(UserOperationTypeEnum.CASE_VERIFY.getCode());
            cfUserEvent.getDataMap().put("infoUuid", cfInfo.getInfoId());

            producer.send(
                    new Message<>(MQTopicCons.CF,
                            MQTagCons.USER_EVENT_FOR_EVENT_CENTER,
                            MQTagCons.USER_EVENT_FOR_EVENT_CENTER + "_" + caseId,
                            cfUserEvent, DelayLevel.S1));
        } catch (Exception e) {
            log.error("sendUserEvent error. userId:{}, caseId:{}", userId, caseId, e);
        }
    }

}
