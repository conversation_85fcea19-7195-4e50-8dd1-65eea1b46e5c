package com.shuidihuzhu.cf.mq.consumers;

import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingRelationType;
import com.shuidihuzhu.cf.enums.crowdfunding.HonestPersonChangeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.crowdfunding.RepeatRaiserPayeeTraceService;
import com.shuidihuzhu.client.cf.api.model.DishonestPayload;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @time 2019/8/4 下午4:31
 * @desc 发起人或者收款人重复时埋点上报
 */
@Slf4j
@Service
@RocketMQListener(id = MQTagCons.CF_DISHONEST_VALIDATE_MQ + "_REPEAT_RAISER_PAYEE_TRACE",
        tags = MQTagCons.CF_DISHONEST_VALIDATE_MQ,
        topic = MQTopicCons.CF,
        group = MQTagCons.CF_DISHONEST_VALIDATE_MQ + "_REPEAT_RAISER_PAYEE_TRACE")
public class RepeatRaiserPayeeTraceConsumer implements MessageListener<DishonestPayload> {

    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;

    @Autowired
    private RepeatRaiserPayeeTraceService repeatRaiserPayeeTraceService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<DishonestPayload> mqMessage) {

        if(Objects.isNull(mqMessage) || Objects.isNull(mqMessage.getPayload())){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        DishonestPayload payload = mqMessage.getPayload();
        int changeType = payload.getChangeType();
        HonestPersonChangeEnum changeEnum = HonestPersonChangeEnum.parse(changeType);

        String infoUuid = payload.getInfoUuid();
        CrowdfundingInfo cfInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);

        if(Objects.isNull(cfInfo) || (changeEnum != HonestPersonChangeEnum.RAISER && changeEnum != HonestPersonChangeEnum.PAYEE)){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        CrowdfundingRelationType relationType = cfInfo.getRelationType();
        boolean hospital = (relationType == CrowdfundingRelationType.LOCATION_HOSPITAL_ACCOUNT || relationType == CrowdfundingRelationType.charitable_organization) ? true : false;
        if(hospital && changeEnum == HonestPersonChangeEnum.PAYEE){
            return ConsumeStatus.CONSUME_SUCCESS;
        }

        if(changeEnum == HonestPersonChangeEnum.RAISER){
            repeatRaiserPayeeTraceService.repeatRaiserTrace(cfInfo);
        }

        if(changeEnum == HonestPersonChangeEnum.PAYEE){
            repeatRaiserPayeeTraceService.repeatPayeeTrace(cfInfo);
        }

        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
