package com.shuidihuzhu.cf.mq.consumers;

import com.shuidi.weixin.common.bean.result.WxMediaUploadResult;
import com.shuidi.weixin.mp.api.WxMpService;
import com.shuidihuzhu.account.model.UserThirdModel;
import com.shuidihuzhu.cf.biz.wx.WxCustomRecordBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.crowdfunding.message.CfActivityTreeMsg;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.cf.util.GetDateUtils;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.frame.client.api.mina.MinaSummaryClient;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import com.shuidihuzhu.msg.enums.RecordConstant;
import com.shuidihuzhu.msg.model.MiniProgramPage;
import com.shuidihuzhu.msg.model.WxCustomRecord;
import com.shuidihuzhu.wx.biz.ShuidiWxService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Date;

/**
 * 种树二期引流消息
 * Created by dongcf on 2019/4/2
 */
@Slf4j
@Service
@RocketMQListener(id = MQTagCons.MINA_PLANT_TREES_V2,
        group = "cf-api-" + MQTagCons.MINA_PLANT_TREES_V2 + "-group",
        tags = MQTagCons.MINA_PLANT_TREES_V2,
        topic = MQTopicCons.CF)
@RefreshScope
public class CfMinaPlantTreesMsgConsumer extends BaseConsumerPrintReceiveLog implements MessageListener<CfActivityTreeMsg> {

    private static final String BUSINESS_INFO_ACTIVITY = "CrowdFundingPlantTreesMsgPush-";
    private static final int sub_biz_type = 1658;
    private static final String HAS_SEND_1658_COUNT = "HAS_SEND_1658_COUNT";
    @Resource(name = "cf2RedissonHandler")
    private RedissonHandler cf2RedissonHandler;
    @Autowired
    protected ShuidiWxService shuidiWxService;
    @Autowired
    private WxCustomRecordBiz wxCustomRecordBiz;
    @Autowired
    private ApplicationService applicationService;
    @Autowired
    private MinaSummaryClient minaSummaryClient;

    @Value("${1658-msg-controller.can-send:1}")
    private boolean canSend1658;
    @Value("${1658-msg-controller.how-many-send:100000}")
    private int howManySend;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<CfActivityTreeMsg> mqMessage) {
        printReceiveMqMessageLog(mqMessage);
        CfActivityTreeMsg cfActivityTreeMsg = mqMessage.getPayload();
        UserThirdModel userThirdModel = cfActivityTreeMsg.getUserThirdModel();
        if(null == userThirdModel) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        long userId = userThirdModel.getUserId();
        boolean isJoin;
        try {
            isJoin = minaSummaryClient.isJoin(userId);
        } catch (Exception e) {
            log.error("", e);
            isJoin = true;
        }
        if(isJoin) {
            if(applicationService.isProduction()) {
                return ConsumeStatus.CONSUME_SUCCESS;
            }
            log.debug("（111活动消息推送接收端)userId:{}不发送消息，已领取树", userId);
        }
        Integer hasSendCount = cf2RedissonHandler.get(HAS_SEND_1658_COUNT, Integer.class);
        if(!canSend1658 || -1 != howManySend && null != hasSendCount && hasSendCount >= howManySend) {
            return ConsumeStatus.CONSUME_SUCCESS;
        }
        WxCustomRecord wxCustomRecord = this.createMiniCustomRecord(userThirdModel, sub_biz_type, "wx_msg_pf_sb1658");
        if(null != wxCustomRecord) {
            log.info("（111活动消息推送接收端)发送种树客服消息:{}", wxCustomRecord);
            //wxCustomRecordBiz.saveWxCustomRecord这个调用消息系统的api已下线,如再使用此消息请使用MsgClientV2来发送消息
//            wxCustomRecordBiz.saveWxCustomRecord(Collections.singletonList(wxCustomRecord), getBusinessInfo());
            cf2RedissonHandler.incr(HAS_SEND_1658_COUNT, GetDateUtils.getSecondsNextEarlyMorning());
        }
        return ConsumeStatus.CONSUME_SUCCESS;
    }

    private WxCustomRecord createMiniCustomRecord(UserThirdModel userThirdModel, int subBizType, String channel) {
        if(userThirdModel == null) {
            return null;
        }
        String title = "感谢您的善心帮助，送您一棵福气树";

        String appId = "wx77dc31fd329db7c1";
        String path = "pages/index/index?channel=" + channel;
        String picUrl = "http://wx-miniapp.oss-cn-beijing.aliyuncs.com/summary/msg.png";
        String thumbMediaId = getThumbMediaId(picUrl, userThirdModel.getThirdType());
        if(StringUtils.isBlank(thumbMediaId)){
            return null;
        }
        log.info("thumbMediaId:{}", thumbMediaId);
        MiniProgramPage miniProgramPage = new MiniProgramPage(title, appId, path, thumbMediaId);
        return WxCustomRecord.buildMiniProgramPage(userThirdModel.getUserId(), userThirdModel.getOpenId(), RecordConstant.MSG_TYPE_USER,
                RecordConstant.BIZ_TYPE_AIXINCHOU, subBizType, miniProgramPage).buildUserThirdType(userThirdModel.getThirdType());
    }

    private static String getBusinessInfo() {
        return BUSINESS_INFO_ACTIVITY + DateUtil.getYearMonthDayStr(new Date());
    }

    private String getThumbMediaId(String picUrl, int thirdType) {
        String key = "newThumbMediaId_" +sub_biz_type+"_"+ thirdType;
        String mediaId = cf2RedissonHandler.get(key, String.class);
        if(StringUtils.isNotBlank(mediaId)) {
            return mediaId;
        }
        try {
            URL url = new URL(picUrl);
            HttpURLConnection urlConnection = (HttpURLConnection) url.openConnection();
            urlConnection.setRequestMethod("GET");
            urlConnection.setConnectTimeout(5 * 1000);
            InputStream inStream = urlConnection.getInputStream();
            WxMpService wxMpService = shuidiWxService.getWxService(thirdType);
            WxMediaUploadResult wxMediaUploadResult = wxMpService.mediaUpload("image", "png", inStream);
            mediaId = wxMediaUploadResult.getMediaId();
            cf2RedissonHandler.setEX(key, mediaId, 2 * 24 * 60 * 60 * 1000L);
            return mediaId;
        } catch (Exception e) {
            log.error("get mediaId error", e);
            return "";
        }
    }
}
