package com.shuidihuzhu.cf.mq.consumers;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.PropertyFilter;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class BaseConsumerPrintReceiveLog {

    public <T> void printReceiveMqMessageLog(ConsumerMessage<T> mqMessage) {
        printReceiveMqMessageLog("",mqMessage);
    }

    public <T> void printReceiveMqMessageLog(String simpleDesc,ConsumerMessage<T> mqMessage) {
        if(mqMessage == null){
            log.error(this.getClass().getSimpleName()+" " + simpleDesc+" receive empty mqMessage");
            return ;
        }

        PropertyFilter filter = new PropertyFilter() {
            public boolean apply(Object source, String name, Object value) {
                if ("bornHostBytes".equals(name) || "storeHostBytes".equals(name)) {
                    return false;
                }
                return true;
            }
        };

        log.debug(this.getClass().getSimpleName()+ " " + simpleDesc+" receive topicName:{} ,tags:{}  msg:{}",mqMessage.getTopic(),mqMessage.getTags(), JSON.toJSONString(mqMessage, filter));
        return ;
    }

}
