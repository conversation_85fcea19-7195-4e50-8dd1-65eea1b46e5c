package com.shuidihuzhu.cf.mq.consumers.order;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.mq.payload.OrderAddPayload;
import com.shuidihuzhu.cf.service.EventPublishService;
import com.shuidihuzhu.infra.starter.rocketmq.annotation.RocketMQListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumeStatus;
import com.shuidihuzhu.infra.starter.rocketmq.core.ConsumerMessage;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 支付预下单成功消息
 *
 * <AUTHOR>
 * @since 2025/1/7
 */
@Slf4j
@Service
@RocketMQListener(id = "cf-api" + MQTagCons.CF_DONATION_ORDER_ADD_MSG,
        group = "cf-api" + MQTagCons.CF_DONATION_ORDER_ADD_MSG + "-group",
        tags = MQTagCons.CF_DONATION_ORDER_ADD_MSG,
        topic = MQTopicCons.CF)
public class CfOrderAddFromFinanceTocConsumer implements MessageListener<OrderAddPayload> {

    @Resource
    private EventPublishService eventPublishService;

    @Override
    public ConsumeStatus consumeMessage(ConsumerMessage<OrderAddPayload> mqMessage) {
        OrderAddPayload orderAddPayload = mqMessage.getPayload();
        log.debug("CF_DONATION_ORDER_ADD_MSG payload: {}", JSON.toJSONString(orderAddPayload));
        eventPublishService.sendOrderAddEvent(this, orderAddPayload.getCaseId()
                , orderAddPayload.getUserId(), orderAddPayload.getOrderId(), orderAddPayload.getChannel());
        return ConsumeStatus.CONSUME_SUCCESS;
    }
}
