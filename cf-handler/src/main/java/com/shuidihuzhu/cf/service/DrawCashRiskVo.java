package com.shuidihuzhu.cf.service;

import com.shuidihuzhu.cf.domain.CfCaseRiskDO;
import com.shuidihuzhu.client.cf.risk.model.result.CfRiskDrawCashDO;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 * @time 2019/3/19 下午7:53
 * @desc
 */
public class DrawCashRiskVo {
    public static CfCaseRiskDO convert(CfRiskDrawCashDO drawCashDO){
        if(null == drawCashDO){
            return null;
        }

        CfCaseRiskDO cfCaseRiskDO = new CfCaseRiskDO();

        BeanUtils.copyProperties(drawCashDO, cfCaseRiskDO);

        return cfCaseRiskDO;
    }
}
