package com.shuidihuzhu.cf.service;

import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.constants.CaseRiskConstants;
import com.shuidihuzhu.cf.domain.CfCaseRiskDO;
import com.shuidihuzhu.cf.enums.crowdfunding.risk.CaseRiskPassedEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.crowdfunding.CrowdfundingApproveService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @time 2019/3/19 下午7:46
 * @desc
 */
@Slf4j
@Service
public class DrawCashRiskApprovePushService {

    @Resource
    private CrowdfundingApproveService crowdfundingApproveService;

    @Resource
    private CrowdfundingInfoBiz crowdfundingInfoBiz;


    public void sendInfoPassedNotice(String infoUuid, CrowdfundingInfo info) {
        if (info == null) {
            info = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        }
        if (info == null) {
            log.error("{}", infoUuid);
            return;
        }
        crowdfundingApproveService.pushNotice(info);
    }


    public void touchSendNotice(CfCaseRiskDO v, CrowdfundingInfo info) {
        // 风控检查通过 直接发消息通知通过
        if (!CaseRiskPassedEnum.isPassed(v.getPassed())) {
            return;
        }
        // 材料审核没过 则不发消息
        if (v.getCaseInfoPassed() != CaseRiskConstants.CaseInfo.PASSED) {
            return;
        }
        sendInfoPassedNotice(v.getInfoUuid(), info);
    }

    public void onInfoRiskPassed(CfCaseRiskDO v) {

        touchSendNotice(v, null);
    }

}
