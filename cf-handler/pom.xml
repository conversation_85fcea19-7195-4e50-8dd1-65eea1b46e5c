<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>cf-api-parent</artifactId>
        <groupId>com.shuidihuzhu.cf</groupId>
        <version>3.6.656-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>cf-handler</artifactId>
  <version>3.6.656-SNAPSHOT</version>
    <packaging>jar</packaging>


    <properties>
        <maven.source.skip>false</maven.source.skip>
    </properties>


    <dependencies>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-biz</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>xstream</artifactId>
                    <groupId>com.thoughtworks.xstream</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.msg</groupId>
            <artifactId>msg-rpc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.risk-control</groupId>
            <artifactId>rc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.sdb</groupId>
            <artifactId>sdb-order-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-event-center-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-api-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.dataservice</groupId>
            <artifactId>dataservice-client</artifactId>
        </dependency>

    </dependencies>
</project>
