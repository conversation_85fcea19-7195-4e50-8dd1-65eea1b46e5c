<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<packaging>jar</packaging>
	<artifactId>cf-model</artifactId>
  <version>3.6.656-SNAPSHOT</version>

	<parent>
		<groupId>com.shuidihuzhu.cf</groupId>
		<artifactId>cf-api-parent</artifactId>
		<version>3.6.656-SNAPSHOT</version>
	</parent>

	<properties>
		<maven.source.skip>false</maven.source.skip>
	</properties>

	<dependencies>

		<dependency>
			<groupId>com.shuidihuzhu.common</groupId>
			<artifactId>shuidi-wx-provider</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>xstream</artifactId>
					<groupId>com.thoughtworks.xstream</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.shuidihuzhu.pay</groupId>
			<artifactId>pay-rpc-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.shuidihuzhu.account</groupId>
			<artifactId>account-grpc-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.shuidihuzhu.account</groupId>
			<artifactId>verify-client</artifactId>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
		</dependency>
		<dependency>
			<groupId>commons-lang</groupId>
			<artifactId>commons-lang</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-collections4</artifactId>
		</dependency>
		<dependency>
			<groupId>commons-collections</groupId>
			<artifactId>commons-collections</artifactId>
		</dependency>
		<dependency>
			<groupId>javax.validation</groupId>
			<artifactId>validation-api</artifactId>
		</dependency>
		<dependency>
			<groupId>org.hibernate.validator</groupId>
			<artifactId>hibernate-validator</artifactId>
		</dependency>
		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-annotations</artifactId>
		</dependency>
		<dependency>
			<groupId>com.github.binarywang</groupId>
			<artifactId>weixin-java-mp</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>xstream</artifactId>
					<groupId>com.thoughtworks.xstream</groupId>
				</exclusion>
			</exclusions>
		</dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
        </dependency>
		<dependency>
			<groupId>com.shuidihuzhu.cf</groupId>
			<artifactId>cf-client</artifactId>
		</dependency>
        <dependency>
            <groupId>com.shuidihuzhu.common</groupId>
            <artifactId>web-core-v2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.baseservice</groupId>
            <artifactId>baseservice-client</artifactId>
        </dependency>
		<dependency>
			<groupId>com.shuidihuzhu.cf</groupId>
			<artifactId>cf-finance-common-model</artifactId>
		</dependency>

		<dependency>
			<groupId>com.shuidihuzhu.cf</groupId>
			<artifactId>cf-ugc-client</artifactId>
			<exclusions>
				<exclusion>
					<groupId>com.shuidihuzhu.cf</groupId>
					<artifactId>cf-model</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.shuidihuzhu.cf</groupId>
			<artifactId>cf-client-base</artifactId>
		</dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-material-client</artifactId>
			<exclusions>
				<exclusion>
					<groupId>com.shuidihuzhu.cf</groupId>
					<artifactId>cf-model</artifactId>
				</exclusion>
			</exclusions>
        </dependency>

		<dependency>
			<groupId>com.shuidihuzhu.cf-risk</groupId>
			<artifactId>cf-risk-rpc-client</artifactId>
		</dependency>

		<dependency>
			<groupId>com.shuidihuzhu.cf</groupId>
			<artifactId>cf-activity-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.shuidihuzhu.msg</groupId>
			<artifactId>msg-rpc-client</artifactId>
		</dependency>

	</dependencies>
</project>
