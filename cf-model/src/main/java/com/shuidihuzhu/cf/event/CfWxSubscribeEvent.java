package com.shuidihuzhu.cf.event;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@EqualsAndHashCode
public class CfWxSubscribeEvent extends ApplicationEvent {

    private static final long serialVersionUID = -6294693869794079384L;

    private String openId ;

    private int userThirdType;

    private String eventKey;

    public CfWxSubscribeEvent(Object source, String openId, int userThirdType, String eventKey) {
        super(source);
        this.openId = openId;
        this.userThirdType = userThirdType;
        this.eventKey = eventKey;
    }
}
