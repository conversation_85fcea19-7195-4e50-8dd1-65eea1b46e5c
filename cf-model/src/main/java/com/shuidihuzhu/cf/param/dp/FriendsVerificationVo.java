package com.shuidihuzhu.cf.param.dp;

import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/16  8:35 下午
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FriendsVerificationVo {
    private int friendsVerificationCount;

    private int doctorVerificationCount;

    private int sickFriendsVerificationCount;

    private int otherCount;

    @ApiModelProperty("证实人总数")
    private int count;
    private int countFromHospital;

    private Integer anchorId;
    private Integer size;
    private boolean hasNext;
    private Integer totalCount;

    private List<FriendVerification> list = Lists.newArrayList();

}
