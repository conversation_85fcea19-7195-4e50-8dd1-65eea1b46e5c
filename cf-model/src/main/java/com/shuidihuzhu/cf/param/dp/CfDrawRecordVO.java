package com.shuidihuzhu.cf.param.dp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@ApiModel("提现成功数据")
@Data
public class CfDrawRecordVO {

    @ApiModelProperty("案例uuid")
    private String infoUuid;

    @ApiModelProperty("提现记录唯一id")
    private String traceNo;

    @ApiModelProperty("申请提现金额，单位分，含手续费")
    private int drawCashAmount;

    @ApiModelProperty("提现时间")
    private long actionTime;

}
