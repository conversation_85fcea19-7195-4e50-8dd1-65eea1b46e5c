package com.shuidihuzhu.cf.param.dp;

import com.shuidihuzhu.client.baseservice.pay.model.PayResultV2;
import com.shuidihuzhu.common.web.enums.Platform;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/3/16  11:19 上午
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OrderResultNewVo {
    private String tradeNo;
    private double amt;
    private String body;
    private String openId;
    private PayResultV2 payInfo;
    private Platform payType;

    @ApiModelProperty("订单唯一id(非自增)")
    private String orderCode;
}
