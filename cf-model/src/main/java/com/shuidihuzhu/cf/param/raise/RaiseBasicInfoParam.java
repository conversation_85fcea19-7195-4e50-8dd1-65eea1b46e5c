package com.shuidihuzhu.cf.param.raise;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.InitialAuditItem;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("基础信息")
public class RaiseBasicInfoParam {

    /**
     * {@link com.shuidihuzhu.cf.enums.crowdfunding.BaseInfoTemplateConst.CfBaseInfoRelationshipEnum}
     */
    @ApiModelProperty("为谁筹款")
    private int userRelationTypeForC; //本人与患者的关系

    @ApiModelProperty("所患疾病名称")
    private String diseaseName;

    @ApiModelProperty("发起者的姓名")
    private String selfRealName;

    @ApiModelProperty("发起者的手机号")
    private String selfMobile;

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    @ApiModelProperty("验证码")
    private String verifyCode;

    @ApiModelProperty("材料的状态")
    private InitialAuditItem.MaterialStatus materialStatus;

    @ApiModelProperty("驳回详情")
    private Map<Integer, List<String>> rejectDetail;

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    @ApiModelProperty("发送验证码时使用的key")
    private String key;

    @ApiModelProperty("第一页的患者姓名")
    private String raisePatientName;

    @ApiModelProperty("第一页的患者身份证号")
    private String raisePatientIdCard;

    @ApiModelProperty("第一页的患者出生证")
    private String raisePatientBornCard;
    /**
     * {@link com.shuidihuzhu.cf.enums.crowdfunding.UserIdentityType}
     */
    @ApiModelProperty("患者证件类型{1:身份证, 2:出生证}")
    private int raisePatientIdType;

}
