package com.shuidihuzhu.cf.param.raise;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.constants.crowdfunding.RaiseCons;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("发起参数")
public class RaiseCaseParam {

    @JsonIgnore
    private long userId;

    @JsonIgnore
    private long bundleId;

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    @ApiModelProperty(" pageFlag传值 basicInfo(基础信息) titleImage(求助说明) firstApprove(医疗证明) assertInfo(经济状况)")
    private String pageFlag = "";

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    @ApiModelProperty("渠道信息")
    private String channel = "";

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    @ApiModelProperty("草稿版本")
    private int cfVersion;

    @JsonIgnore
    private String clientIp;

    @ApiModelProperty("基础信息")
    private RaiseBasicInfoParam basicInfoParam;

    @ApiModelProperty("求助说明")
    private RaiseTitleImageParam titleImageParam;

    @ApiModelProperty("医疗证明")
    private RaiseMedicalInfoParam medicalInfoParam;

    @ApiModelProperty("经济状况")
    private RaiseAssetInfoParam assetInfoParam;

    @ApiModelProperty("页面填写情况")
    private Map<String, Boolean> pageFillInDetail = Maps.newHashMap();



}
