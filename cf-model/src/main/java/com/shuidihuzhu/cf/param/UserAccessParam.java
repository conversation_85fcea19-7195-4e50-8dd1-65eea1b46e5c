package com.shuidihuzhu.cf.param;

import com.alibaba.fastjson.support.spring.annotation.FastJsonView;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.shuidihuzhu.account.model.UserInfoModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by sven on 18/8/7.
 *
 * <AUTHOR>
 */
@Data
@ApiModel("用户访问参数")
public class UserAccessParam implements Serializable {

    @ApiModelProperty(value = "用户基本信息，返回值不给出")
    @JsonIgnore
    private UserInfoModel userInfoModel;

    @ApiModelProperty(value = "用户真实ip")
    private String clientIp;

    @ApiModelProperty("访问渠道")
    private String channel;

    //当前访问时间
    private long currentTime = System.currentTimeMillis();

    @ApiModelProperty("selftag")
    private String selfTag;

    @ApiModelProperty("省名称")
    private String provinceName;

    @ApiModelProperty("城市名称")
    private String cityName;

    @ApiModelProperty("223000,表示当天的晚上10:30，HHmmss按照这个序列化的时间")
    private int timeOfDay;

    @ApiModelProperty("20190111,表示2019年1月11日，yyyyMMdd按照这个序列化的时间")
    private int dayDate;
}
