package com.shuidihuzhu.cf.param.raise;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("发起参数")
public class RaiseControlParam {

    @ApiModelProperty("发起版本")
    private int cfVersion;

    @ApiModelProperty("前端标识")
    private String selfTag;

    @ApiModelProperty("渠道")
    private String channel;

    private Integer platform;

    private Integer userThirdType;

    @ApiModelProperty("发起草稿箱id  不存在为0")
    private Long preId;

    @ApiModelProperty("代录入id标识")
    private String reportLinkInfo;

    @ApiModelProperty("案例版本信息")
    private Integer planId;

}
