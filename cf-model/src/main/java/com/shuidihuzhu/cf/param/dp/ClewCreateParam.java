package com.shuidihuzhu.cf.param.dp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.lang.Nullable;

/**
 * <AUTHOR>
 */
@ApiModel("线索创建参数")
@Data
public class ClewCreateParam {

    @ApiModelProperty("加密的手机号")
    private String cryptoMobile;

    @ApiModelProperty("姓名")
    private String name;

    @Nullable
    @ApiModelProperty("加密的身份证号")
    private String cryptoIdCard;

    @ApiModelProperty("省")
    private String province;

    @ApiModelProperty("市")
    private String city;

    @ApiModelProperty("区")
    private String district;

    @ApiModelProperty("就诊医院名称")
    private String hospitalName;

    @ApiModelProperty("疾病名")
    private String diseaseName;

    @ApiModelProperty("备注")
    private String remark;

}
