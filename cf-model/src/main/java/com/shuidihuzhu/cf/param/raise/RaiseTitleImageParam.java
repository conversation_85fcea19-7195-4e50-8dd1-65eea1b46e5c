package com.shuidihuzhu.cf.param.raise;

import com.shuidihuzhu.cf.enums.crowdfunding.CfContentTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTemplateRecord;
import com.shuidihuzhu.cf.vo.questionnaire.TemplateTitleAndContentVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("求助说明")
public class RaiseTitleImageParam {

    @ApiModelProperty("目标金额(单位:分)")
    private Integer targetAmount;
    @ApiModelProperty("标题")
    private String title;
    @ApiModelProperty("内容")
    private String content;
    @ApiModelProperty("图片")
    private List<String> attachments;


    private CfContentTypeEnum contentType;

    @ApiModelProperty("用户自动生成文案选择记录")
    private CfBaseInfoTemplateRecord cfBaseInfoTemplateRecord;

    @ApiModelProperty("记录是否是智能发起 0不是 1是 ")
    private int useTemplateRaise;

    @ApiModelProperty("用户的智能模版的参数")
    private TemplateTitleAndContentVo templateParam;

    @ApiModelProperty("案例标题授权开关 0 关 1 开")
    private int caseTitleAuthorizationSwitch;

}
