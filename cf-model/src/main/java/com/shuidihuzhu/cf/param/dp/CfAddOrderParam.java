package com.shuidihuzhu.cf.param.dp;

import com.shuidihuzhu.client.baseservice.pay.enums.PayType;
import com.shuidihuzhu.client.baseservice.pay.enums.PayTypeNewEnum;
import com.shuidihuzhu.common.web.enums.Platform;
import com.shuidihuzhu.wx.enums.AccountThirdTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 创建订单参数
 *
 * <AUTHOR>
 * @since 2022-03-15 10:52 上午
 **/
@Data
@ApiModel("创建订单参数")
public class CfAddOrderParam {

    @ApiModelProperty("案例唯一标识")
    private String infoUuid;

    @ApiModelProperty("捐款人ID")
    private String encryptUserId;

    @ApiModelProperty("金额")
    private double amt;

    @ApiModelProperty("备注")
    private String comment;

    /**
     * @see Platform
     */
    @ApiModelProperty("平台")
    private String platform;

    @ApiModelProperty("来源")
    private String source;

    @ApiModelProperty("随机数+时间戳")
    private String selfTag;

    @ApiModelProperty("是否匿名")
    private boolean anonymous;

    @ApiModelProperty("水滴公众号分流")
    private String splitFlowUuid;

    @ApiModelProperty("分享ID")
    private String shareSourceId;

    /**
     * {@link PayTypeNewEnum}
     * {@link PayType}
     */
    @ApiModelProperty("支付渠道")
    private int payType;

    @ApiModelProperty("是否使用捐助金")
    private boolean useSubsidy;

    @ApiModelProperty("几度好友")
    private int shareDv = -97;

    @ApiModelProperty("活动主会场id")
    private int activityId;

    @ApiModelProperty("小善日分会场id")
    private String subActivityId;

    @ApiModelProperty("祝福卡片id")
    private int blessingCardId;

    @ApiModelProperty("众人帮活动id")
    private String bonfireUuid;

}
