package com.shuidihuzhu.cf.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class CaseOrderQueryParam {
    private Long id;
    // 这个字段需要有非0值
    private int crowdfundingId;
    private Integer amount;
    private String comment;
    private Date ctimeStart;
    private Date ctimeEnd;
    private int pageNum;
    private int pageSize;
    private Long userId;

    @ApiModelProperty("金额区间开始")
    private int amountStart;
    @ApiModelProperty("金额区间结束")
    private int amountEnd;
}
