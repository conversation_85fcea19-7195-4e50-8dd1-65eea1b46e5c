package com.shuidihuzhu.cf.param.raise;

import com.shuidihuzhu.cf.client.material.model.CfBasicLivingGuardModel;
import com.shuidihuzhu.cf.vo.initialaudit.CfPropertyInsuranceVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("资产信息")
public class RaiseAssetInfoParam {

    @ApiModelProperty("初审增加的增信相关信息")
    private CfPropertyInsuranceVO propertyInsuranceParam;

    @ApiModelProperty("低保贫困信息")
    private CfBasicLivingGuardModel livingGuardParam;

}
