package com.shuidihuzhu.cf.param.raise;

import com.shuidihuzhu.cf.client.material.model.medication.UserMedicineView;
import com.shuidihuzhu.cf.enums.raise.MedicalImageTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("医疗证明")
public class RaiseMedicalInfoParam {

    @ApiModelProperty("发起者的姓名")
    private String selfRealName;
    @ApiModelProperty("发起者的身份证号")
    private String selfIdCard;

    @ApiModelProperty("患者的姓名")
    private String patientRealName;
    @ApiModelProperty("患者的身份证号")
    private String patientIdCard;
    @ApiModelProperty("患者的出生证")
    private String patientBornCard;

    @ApiModelProperty("患者是否有身份证号")
    private int patientHasIdCard;
    /**
     * {@link com.shuidihuzhu.cf.enums.crowdfunding.UserIdentityType}
     */
    @ApiModelProperty("患者证件类型{1:身份证, 2:出生证}")
    private int patientIdType;


    //    private UserRelTypeEnum relType; //本人与患者的关系
    @ApiModelProperty("医疗证明图片")
    private String preAuditImageUrl;

    /**
     * {@link MedicalImageTypeEnum}
     */
    @ApiModelProperty("医疗证明种类")
    private int imageUrlType;

    /**
     * {@link com.shuidihuzhu.cf.enums.crowdfunding.BaseInfoTemplateConst.CfBaseInfoRelationshipEnum}
     */
    @ApiModelProperty("为谁筹款")
    private int userRelationTypeForC; //本人与患者的关系

    @ApiModelProperty("患者的药物治疗情况")
    private UserMedicineView medicineView;


}
