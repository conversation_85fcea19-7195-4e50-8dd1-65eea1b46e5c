package com.shuidihuzhu.cf.param.dp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2022/3/16  8:35 下午
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FriendVerification {

    @ApiModelProperty("加密证实id")
    private String infoId;

    @ApiModelProperty("真实姓名")
    private String userName;

    @ApiModelProperty("证实描述")
    private String description;

    @ApiModelProperty("微信昵称")
    private String nickName;

    @ApiModelProperty("微信头像")
    private String headImgUrl = "";

    @ApiModelProperty("证实好友实名情况")
    private int idcardVerifyStatus;

    @ApiModelProperty("证实时间")
    private Timestamp createTime;

}
