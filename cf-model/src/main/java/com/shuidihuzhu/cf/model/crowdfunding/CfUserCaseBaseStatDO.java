package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @time 2019/2/20 下午8:04
 * @desc
 */
@Data
public class CfUserCaseBaseStatDO {
    private Integer id;

    private Long userId;

    private Integer caseId;

    private Integer shareBroughtAmount;

    private Integer shareFirstAmount;

    private Integer shareBroughtCount;

    private Integer donateAmount;

    private Integer shareCount;

    private Date createTime;

    private Date updateTime;

    private int isDelete;

    public CfUserCaseBaseStatDO() {
        shareBroughtAmount = 0;
        shareFirstAmount = 0;
        donateAmount = 0;
        shareCount = 0;
    }
}
