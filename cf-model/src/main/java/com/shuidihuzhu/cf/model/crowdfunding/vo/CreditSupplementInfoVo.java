package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.shuidihuzhu.cf.model.crowdfunding.CfCreditSupplement;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@ApiModel
public class CreditSupplementInfoVo {

    private List<CfCreditSupplement> creditSupplements;
    private Map<Integer, List<String>> rejectDetails;
}
