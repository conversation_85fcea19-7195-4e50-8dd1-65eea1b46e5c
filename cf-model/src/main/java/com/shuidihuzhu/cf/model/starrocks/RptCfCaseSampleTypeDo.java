package com.shuidihuzhu.cf.model.starrocks;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/12/18  14:40
 */
@Data
public class RptCfCaseSampleTypeDo {
    @ApiModelProperty("案例id")
    private Long infoId;
    @ApiModelProperty("相似案例id")
    private Long sampleTypeInfoId;
    @ApiModelProperty("类型,满足兜底则为2，不是兜底的为1")
    private Integer typeFlag;
    @ApiModelProperty("总筹款金额,单位：元")
    private Double donateAmt;
    @ApiModelProperty("总捐款次数")
    private Long donateAnt;
    @ApiModelProperty("总转发次数")
    private Long shareCnt;
    @ApiModelProperty("筹款人总转发次数")
    private Long ckrShareCnt;
    @ApiModelProperty("日期")
    private Date dt;
}
