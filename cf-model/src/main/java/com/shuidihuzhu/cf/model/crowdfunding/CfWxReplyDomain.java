package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @author: cuikexiang
 * @date: 2020/12/25
 */
@Data
@NoArgsConstructor
public class CfWxReplyDomain {
    private String keyWorld;

    private String replyContent;

    private Integer needEqual;

    private Integer groupId;

    @NotNull
    private Integer pageNo;

    @NotNull
    private Integer pageSize;

    private Integer valid;

    @NotBlank
    private String orderByClause;
}
