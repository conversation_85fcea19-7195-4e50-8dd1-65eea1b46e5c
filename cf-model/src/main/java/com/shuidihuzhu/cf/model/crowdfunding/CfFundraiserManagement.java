package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.Data;

import java.util.Map;

/**
 *
 * <AUTHOR>
 */
@Data
public class CfFundraiserManagement {
    private long id;
    private String date;
    private String infoUuid;
    /**
     * 当天筹款金额
     */
    private long fundraisingAmount;
    /**
     * 筹款人转发的次数
     */
    private int shareCount;
    /**
     * 一度好友转发的人数
     */
    private long sharePeople;

    /**
     * 显示最近七天的柱状图  k:日期 v:当日筹款金额
     */
    private Map<String,Long> histogramMap;

    /**
     * 最近7天有没有捐款 true是最近7天没有捐款
     */
    private boolean noRecentDonations;

    /**
     * 案例结束 累计好友转发人数 totalSharePeople
     *
     * 案例结束 累计帮助次数 totalHelps
     */
    private Map<String,Integer> caseEndMap;

    /**
     * 判断案例是否结束 true案例结束
     */
    private boolean caseEnd;

    /**
     * 动态任务话术
     */
    private String result;

    /**
     * 每日筹款人的获捐次数
     */
    private long payNum;

}
