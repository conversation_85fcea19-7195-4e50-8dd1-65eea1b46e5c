package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class UserPointsHistory {
	private Long id;

	private Long userId;

	private Date dateKey;

	private Integer bizType;

	private Integer userThirdType;

	private Integer points;

	private Integer action;
	private String outTradeNo;

	private Date createTime;

	private Date updateTime;

	private Boolean isDelete;

	private String channel;

	private String remark;
	private String tradeNo;

	private Integer status;

	private Integer type;

	private String errorRemark;

	private Integer currentPoints = -1;
}