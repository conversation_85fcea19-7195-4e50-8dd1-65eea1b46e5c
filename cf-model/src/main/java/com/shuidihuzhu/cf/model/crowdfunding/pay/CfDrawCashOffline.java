package com.shuidihuzhu.cf.model.crowdfunding.pay;

import java.util.Date;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/1/28.
 */
public class CfDrawCashOffline {
    private long id;
    private int infoId;
    private String infoUuid;
    private String reason;
    private Date createTime;
    private Date updateTime;

    public CfDrawCashOffline(){

    }

    public CfDrawCashOffline(int infoId,
                             String infoUuid,
                             String reason,
                             Date createTime,
                             Date updateTime) {
        this.infoId = infoId;
        this.infoUuid = infoUuid;
        this.reason = reason;
        this.createTime = createTime;
        this.updateTime = updateTime;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public int getInfoId() {
        return infoId;
    }

    public void setInfoId(int infoId) {
        this.infoId = infoId;
    }

    public String getInfoUuid() {
        return infoUuid;
    }

    public void setInfoUuid(String infoUuid) {
        this.infoUuid = infoUuid;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
