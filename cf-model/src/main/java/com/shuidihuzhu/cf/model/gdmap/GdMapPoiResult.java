package com.shuidihuzhu.cf.model.gdmap;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;

@Data
public class GdMapPoiResult extends GdMapBaseResult{

    private JSONArray pois;

    public List<GdMapPoiModel> getPoiModels(){
        if(pois==null || pois.size()==0){
            return Lists.newArrayList();
        }
        List<GdMapPoiModel> retList = Lists.newArrayList();
        for(int i=0;i<pois.size();i++){
            GdMapPoiModel gdMapPoiModel = JSON.parseObject(JSON.toJSONString(pois.get(i)),GdMapPoiModel.class);
            if(gdMapPoiModel==null) continue;
            retList.add(gdMapPoiModel);
        }
        return retList;
    }

}
