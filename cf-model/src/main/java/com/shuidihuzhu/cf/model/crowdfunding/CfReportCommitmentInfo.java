package com.shuidihuzhu.cf.model.crowdfunding;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-02-12 15:25
 **/
@Data
@ApiModel(value = "举报承诺书")
public class CfReportCommitmentInfo {
    @ApiModelProperty(value = "id")
    private long id;
    @ApiModelProperty(value = "增信表id")
    private long incrTrustId;
    @ApiModelProperty(value = "疾病名称")
    private String illnessName;
    @ApiModelProperty(value = "治疗医院")
    private String hospital;
    @ApiModelProperty(value = "科室名称或治疗地点")
    private String detail;
    @ApiModelProperty(value = "治疗方式")
    private String treatment;
    @ApiModelProperty(value = "承诺人名称")
    private String userName;
    @ApiModelProperty(value = "填写时间")
    private String addDate;
    @ApiModelProperty(value = "患者姓名")
    private String patientName;
}
