package com.shuidihuzhu.cf.model.crowdfunding;

import java.sql.Timestamp;

public class CrowdfundingInfoStatus {

	private Long id;
	private Integer caseId;
	private String infoUuid;
	private Integer type;
	private Integer status;
	private Timestamp dateCreated;
	private Timestamp lastModified;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getInfoUuid() {
		return infoUuid;
	}

	public void setInfoUuid(String infoUuid) {
		this.infoUuid = infoUuid;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Timestamp getDateCreated() {
		return dateCreated;
	}

	public void setDateCreated(Timestamp dateCreated) {
		this.dateCreated = dateCreated;
	}

	public Timestamp getLastModified() {
		return lastModified;
	}

	public void setLastModified(Timestamp lastModified) {
		this.lastModified = lastModified;
	}

	public Integer getCaseId() {
		return caseId;
	}

	public void setCaseId(Integer caseId) {
		this.caseId = caseId;
	}

	@Override
	public String toString() {
		return "CrowdfundingInfoStatus{" +
				"id=" + id +
				", caseId=" + caseId +
				", infoUuid='" + infoUuid + '\'' +
				", type=" + type +
				", status=" + status +
				", dateCreated=" + dateCreated +
				", lastModified=" + lastModified +
				'}';
	}
}
