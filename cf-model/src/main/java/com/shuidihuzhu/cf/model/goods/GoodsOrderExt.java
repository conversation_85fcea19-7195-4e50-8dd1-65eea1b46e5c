package com.shuidihuzhu.cf.model.goods;

import com.shuidihuzhu.common.web.model.AbstractModel;
import com.shuidihuzhu.common.web.model.UserAddress;

import java.sql.Timestamp;

public class GoodsOrderExt extends AbstractModel {

	private static final long serialVersionUID = -1472604659965782093L;

	private long id;
	private String infoUuid;
	private long orderId;
	private long gearId;
	private int province;
	private int city;
	private int county;
	private String address;
	private String name;
	private String mobile;
	private String email;
	private int goodsCount; //订单中回报的个数
	private String shippingCode = "";
	private String shippingCompany = "";
	private Timestamp dateCreated;
	private Timestamp lastModified;
	private int orderStatus;

    public String getEncryptAddress() {
        return encryptAddress;
    }

    public void setEncryptAddress(String encryptAddress) {
        this.encryptAddress = encryptAddress;
    }

    public String getEncryptName() {
        return encryptName;
    }

    public void setEncryptName(String encryptName) {
        this.encryptName = encryptName;
    }

    public String getEncryptMobile() {
        return encryptMobile;
    }

    public void setEncryptMobile(String encryptMobile) {
        this.encryptMobile = encryptMobile;
    }

    public String getEncryptEmail() {
        return encryptEmail;
    }

    public void setEncryptEmail(String encryptEmail) {
        this.encryptEmail = encryptEmail;
    }

    private String encryptAddress;
	private String encryptName;
	private String encryptMobile;
	private String encryptEmail;



	public GoodsOrderExt() {
		address = "";
		name = "";
		mobile = "";
		email = "";
	}

	public void setByUserAddress(UserAddress userAddress) {
		name = userAddress.getName();
		mobile = userAddress.getMobile();
		province = userAddress.getProvince();
		city = userAddress.getCity();
		county = userAddress.getCounty();
		address = userAddress.getAddress();
		email = userAddress.getEmail();
	}

	public String getInfoUuid() {
		return infoUuid;
	}

	public void setInfoUuid(String infoUuid) {
		this.infoUuid = infoUuid;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getOrderId() {
		return orderId;
	}

	public void setOrderId(long orderId) {
		this.orderId = orderId;
	}

	public int getProvince() {
		return province;
	}

	public void setProvince(int province) {
		this.province = province;
	}

	public int getCity() {
		return city;
	}

	public void setCity(int city) {
		this.city = city;
	}

	public int getCounty() {
		return county;
	}

	public void setCounty(int county) {
		this.county = county;
	}

	public long getGearId() {
		return gearId;
	}

	public void setGearId(long gearId) {
		this.gearId = gearId;
	}

	public Timestamp getDateCreated() {
		return dateCreated;
	}

	public void setDateCreated(Timestamp dateCreated) {
		this.dateCreated = dateCreated;
	}

	public Timestamp getLastModified() {
		return lastModified;
	}

	public void setLastModified(Timestamp lastModified) {
		this.lastModified = lastModified;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public int getGoodsCount() {
		return goodsCount;
	}

	public void setGoodsCount(int goodsCount) {
		this.goodsCount = goodsCount;
	}

	public String getShippingCode() {
		return shippingCode;
	}

	public void setShippingCode(String shippingCode) {
		this.shippingCode = shippingCode;
	}

	public String getShippingCompany() {
		return shippingCompany;
	}

	public void setShippingCompany(String shippingCompany) {
		this.shippingCompany = shippingCompany;
	}

	public int getOrderStatus() {
		return orderStatus;
	}

	public void setOrderStatus(int orderStatus) {
		this.orderStatus = orderStatus;
	}


}
