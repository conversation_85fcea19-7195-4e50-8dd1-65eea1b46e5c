package com.shuidihuzhu.cf.model.getfundinginfo;

import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingTreatment;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingAttachmentVo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/3/7  14:13
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CrowdfundingTreatmentInfoParam {
    private Map<AttachmentTypeEnum, List<CrowdfundingAttachmentVo>> attachmentMap;
    private CrowdfundingTreatment treatment;
    private CrowdfundingInfo crowdfundingInfo;
    private CfFirsApproveMaterial cfFirsApproveMaterial;
    private long userId;
    private  Map<String, List<String>> materialValueMap;





    private String medicalTreatmentPhoto;
    private List<String> attachmentList;
    private boolean specialReport;
}
