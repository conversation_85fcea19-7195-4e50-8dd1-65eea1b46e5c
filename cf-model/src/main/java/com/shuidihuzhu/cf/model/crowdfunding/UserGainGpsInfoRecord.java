package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: wangpeng
 * @Date: 2020/12/31 19:15
 * @Description:
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserGainGpsInfoRecord implements Serializable {
    private static final long serialVersionUID = -8501606802364333374L;
    /**
     * 主键Id
     */
    private long id;
    /**
     * 用户Id
     */
    private long userId;
    /**
     * 收集节点
     */
    private int gainNode;
    /**
     * 收集经度
     */
    private String gpsLongitude;
    /**
     * 收集经度
     */
    private String gpsLatitude;
    /**
     * 收集时间
     */
    private Date gpsTime;
}
