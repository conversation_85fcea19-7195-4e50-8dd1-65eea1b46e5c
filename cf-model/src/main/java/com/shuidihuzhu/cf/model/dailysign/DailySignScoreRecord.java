package com.shuidihuzhu.cf.model.dailysign;

import lombok.Data;

import java.sql.Timestamp;

/**
 * Created by wangsf on 18/4/27.
 */
@Data
public class DailySignScoreRecord {

	private long id;

	private long userId;

	private long signId;

	private boolean confirmed;

	private String channel = "";

	private int userThirdType;

	private int points;

	private String remark = "";

	private String outTradeNo = "";

	private String tradeNo = "";

	private int action;

}
