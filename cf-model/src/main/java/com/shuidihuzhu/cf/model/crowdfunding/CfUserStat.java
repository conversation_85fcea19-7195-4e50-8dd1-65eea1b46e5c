package com.shuidihuzhu.cf.model.crowdfunding;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.sql.Timestamp;

public class CfUserStat {
	private int id;
	private long userId;
	private int score;
	private int shareCount;
	private int donationCount;
	private int verifyUserCount;
	private int commentCount;
	private int blessingCount;
	private int amount;
	private Timestamp createTime;
	private Timestamp updateTime;
	private int isDelete;
	private boolean hadFollowed;
	private int initStatus;

	public int getInitStatus() {
		return initStatus;
	}

	public void setInitStatus(int initStatus) {
		this.initStatus = initStatus;
	}

	public boolean isHadFollowed() {
		return hadFollowed;
	}

	public void setHadFollowed(boolean hadFollowed) {
		this.hadFollowed = hadFollowed;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}

	public int getScore() {
		return score;
	}

	public void setScore(int score) {
		this.score = score;
	}

	public int getShareCount() {
		return shareCount;
	}

	public void setShareCount(int shareCount) {
		this.shareCount = shareCount;
	}

	public int getDonationCount() {
		return donationCount;
	}

	public void setDonationCount(int donationCount) {
		this.donationCount = donationCount;
	}

	public int getVerifyUserCount() {
		return verifyUserCount;
	}

	public void setVerifyUserCount(int verifyUserCount) {
		this.verifyUserCount = verifyUserCount;
	}

	public int getCommentCount() {
		return commentCount;
	}

	public void setCommentCount(int commentCount) {
		this.commentCount = commentCount;
	}

	public int getBlessingCount() {
		return blessingCount;
	}

	public void setBlessingCount(int blessingCount) {
		this.blessingCount = blessingCount;
	}

	public int getAmount() {
		return amount;
	}

	public void setAmount(int amount) {
		this.amount = amount;
	}

	public Timestamp getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Timestamp createTime) {
		this.createTime = createTime;
	}

	public Timestamp getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Timestamp updateTime) {
		this.updateTime = updateTime;
	}

	public int getIsDelete() {
		return isDelete;
	}

	public void setIsDelete(int isDelete) {
		this.isDelete = isDelete;
	}

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
	}
}
