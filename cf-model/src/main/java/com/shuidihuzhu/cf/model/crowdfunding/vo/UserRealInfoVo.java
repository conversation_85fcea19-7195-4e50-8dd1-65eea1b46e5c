package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.shuidihuzhu.cf.model.crowdfunding.UserRealInfo;
import lombok.Data;

/**
 * @Author: lianghongchao
 * @Date: 2018/9/20 01:13
 */
@Data
public class UserRealInfoVo extends UserRealInfo {
    private String infoUuid;

    public UserRealInfoVo() {
    }

    public UserRealInfoVo(UserRealInfo userRealInfo) {
        super();
        super.setId(userRealInfo.getId());
        super.setUserId(userRealInfo.getUserId());
        super.setOrderId(userRealInfo.getOrderId());
        super.setName(userRealInfo.getName());
        super.setIdCard(userRealInfo.getIdCard());
        super.setCryptoIdCard(userRealInfo.getCryptoIdCard());
        super.setIdcardVerifyStatus(userRealInfo.getIdcardVerifyStatus());
        super.setResultCode(userRealInfo.getResultCode());
        super.setResultMsg(userRealInfo.getResultMsg());
        super.setDateCreated(userRealInfo.getDateCreated());
        super.setLastModified(userRealInfo.getLastModified());
    }
}
