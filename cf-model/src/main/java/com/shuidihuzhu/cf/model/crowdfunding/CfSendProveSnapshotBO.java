package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.vo.CfReportAddTrustDisposeVo;
import lombok.Data;

import java.util.List;

/**
 * @author: l<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-09-21 15:07
 **/
@Data
public class CfSendProveSnapshotBO {
    /**
     * 补充证明
     */
    private CfSendProve cfSendProve;
    /**
     * 处理动作
     */
    private List<CfReportAddTrustDisposeVo> cfReportAddTrustDisposeVos;
    /**
     * 发送的模板
     */
    private List<CfSendProveTemplate> cfSendProveTemplates;

    /**
     * 记录快照时的审核状态
     */
    private int auditStatus;


}
