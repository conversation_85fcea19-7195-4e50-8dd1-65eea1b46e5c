package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.common.web.model.AbstractModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.util.Date;

/**
 * @author: wanghui
 * @time: 2019/3/11 11:13 AM
 * @description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CrowdfundingOrderShardingModel extends AbstractModel {

	private static final long serialVersionUID = -1L;
    /**
     * //'订单id,与主表id保持一致'
     */
	private Long id;
    /**
     * //'案例id'
     */
    private Long crowdfundingId;

    /**
     * //'用户id'
     */
    private Long userId;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
