package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.shuidihuzhu.account.model.AccountPlatform;
import com.shuidihuzhu.account.model.UserInfoModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@ApiModel
public class UserBaseInfVo {
    private UserVo crowdfundingUserInfo;

    @ApiModelProperty("写入筹款人的话")
    private List<String> wordList = new ArrayList<>();

    //需求: https://wiki.shuiditech.com/pages/viewpage.action?pageId=*********
    @ApiModelProperty("支付金额分组")
    private int payGroup;

    @Data
    public static class UserVo {
        private String nickname = "";
        private String headImgUrl = "";
        private String realName = "";
        /** @deprecated */
        @Deprecated
        private AccountPlatform platform;
        private int platformCode;
        private String channel = "";
        private long createTime;
        private String cipherUserId;

        public static UserVo getInstance(UserInfoModel userInfoModel) {
            if (userInfoModel == null) {
                return null;
            }
            UserVo vo = new UserVo();
            vo.setNickname(userInfoModel.getNickname());
            vo.setHeadImgUrl(userInfoModel.getHeadImgUrl());
            vo.setRealName(userInfoModel.getRealName());
            vo.setPlatform(userInfoModel.getPlatform());
            vo.setPlatformCode(userInfoModel.getPlatformCode());
            vo.setChannel(userInfoModel.getChannel());
            vo.setCreateTime(userInfoModel.getCreateTime());
            vo.setCipherUserId(userInfoModel.getCipherUserId());
            return vo;
        }
    }
}
