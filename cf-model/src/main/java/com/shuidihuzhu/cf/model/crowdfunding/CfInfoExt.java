package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.CfVersion;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum;
import com.shuidihuzhu.common.web.model.AbstractModel;
import lombok.extern.slf4j.Slf4j;

import java.sql.Timestamp;
import java.util.Date;

@Slf4j
public class CfInfoExt extends AbstractModel {

	/**
	 * 为DEFAULT表示不参照这次的needCaseList来判断，还按照之前的逻辑来判断
	 */
	public static final String DEFAULT_APPROVE_CASE_CODE_LIST = "DEFAULT";

	private static final long serialVersionUID = 8331463202306589079L;

	private Long id;
	private String infoUuid;
	private String selfTag;
	private String productName;
	private int fromType;
	private String fromDetail;
	// 退款状态
	private int refundStatus;
	// 结束状态
	private int finishStatus;
	// 打款状态
	private int transferStatus;
	private Timestamp refundEndTime;
	private Timestamp dateCreated;
	private Timestamp lastModified;
	// 支付类型
	private int payType;
	// 第三方来源
	private int userThirdType;
	// 注册手机号
	private String cryptoRegisterMobile;
	// 志愿者唯一code
	private String volunteerUniqueCode;
	// 筹款案例的发起人
	private String clientIp = "";
	// 主channel
	private String primaryChannel = "";
	//是否免手续费
	private int noHandlingFee;

	/**
	 * 案例初审状态，只有新案例才有。
	 * @see {@link com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum}
	 */
	private int firstApproveStatus = FirstApproveStatusEnum.DEFAULT.getCode();

	/**
	 * 案例初审通过时间
	 */
	private Timestamp firstApproveTime;

	/**
	 * 需要通过审核的材料列表
	 * 如果是default还按照以前的逻辑来。
	 * 如果有，有几项就要审核几项材料
	 *
	 * 比如此项为：「1,2,3,4,5」就代表了需要验证CrowdfundingInfoDataStatusTypeEnum中的1，2，3，4，5项目
     *
	 * @see {@link com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoDataStatusTypeEnum}
	 */
	private String needCaseList = "DEFAULT";

	/**
	 * 案例版本号
	 * @see  com.shuidihuzhu.cf.enums.crowdfunding.CfVersion
	 */
	private int cfVersion = CfVersion.default_0.getCode();

	//草稿箱id
	private long preId;
	/**
	 * bd 是否关注此案例 0:未关注 1:已关注
	 */
	private int bdFollowed;

	/**
	 * 运营后台填写得结束案例展示文案
	 */
	private String finishStr;

	/**
	 * 对应筹款基本信息id
	 */
	private int caseId;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getInfoUuid() {
		return infoUuid;
	}

	public void setInfoUuid(String infoUuid) {
		this.infoUuid = infoUuid;
	}

	public String getSelfTag() {
		return selfTag;
	}

	public void setSelfTag(String selfTag) {
		this.selfTag = selfTag;
	}

	public Timestamp getDateCreated() {
		return dateCreated;
	}

	public void setDateCreated(Timestamp dateCreated) {
		this.dateCreated = dateCreated;
	}

	public Timestamp getLastModified() {
		return lastModified;
	}

	public void setLastModified(Timestamp lastModified) {
		this.lastModified = lastModified;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public int getFromType() {
		return fromType;
	}

	public void setFromType(int fromType) {
		this.fromType = fromType;
	}

	public String getFromDetail() {
		return fromDetail;
	}

	public void setFromDetail(String fromDetail) {
		this.fromDetail = fromDetail;
	}

	public int getRefundStatus() {
		return refundStatus;
	}

	public void setRefundStatus(int refundStatus) {
		this.refundStatus = refundStatus;
	}

	public int getFinishStatus() {
		return finishStatus;
	}

	public void setFinishStatus(int finishStatus) {
		this.finishStatus = finishStatus;
	}

	public int getTransferStatus() {
		return transferStatus;
	}

	public void setTransferStatus(int transferStatus) {
		this.transferStatus = transferStatus;
	}

	public Date getRefundEndTime() {
		return refundEndTime;
	}

	public void setRefundEndTime(Timestamp refundEndTime) {
		this.refundEndTime = refundEndTime;
	}

	public int getPayType() {
		return payType;
	}

	public void setPayType(int payType) {
		this.payType = payType;
	}

	public int getUserThirdType() {
		return userThirdType;
	}

	public void setUserThirdType(int userThirdType) {
		this.userThirdType = userThirdType;
	}

	public void setCryptoRegisterMobile(String cryptoRegisterMobile) {
		this.cryptoRegisterMobile = cryptoRegisterMobile;
	}

	public String getCryptoRegisterMobile() {
		return cryptoRegisterMobile;
	}

	public String getVolunteerUniqueCode() {
		return volunteerUniqueCode;
	}

	public void setVolunteerUniqueCode(String volunteerUniqueCode) {
		this.volunteerUniqueCode = volunteerUniqueCode;
	}

	public int getFirstApproveStatus() {
		return firstApproveStatus;
	}

	public void setFirstApproveStatus(int firstApproveStatus) {
		this.firstApproveStatus = firstApproveStatus;
	}

	public Timestamp getFirstApproveTime() {
		return firstApproveTime;
	}

	public void setFirstApproveTime(Timestamp firstApproveTime) {
		this.firstApproveTime = firstApproveTime;
	}

	public String getClientIp() {
		return clientIp;
	}

	public void setClientIp(String clientIp) {
		this.clientIp = clientIp;
	}

	public String getNeedCaseList() {
		return needCaseList;
	}

	public void setNeedCaseList(String needCaseList) {
		this.needCaseList = needCaseList;
	}

	public String getPrimaryChannel() {
		return primaryChannel;
	}

	public void setPrimaryChannel(String primaryChannel) {
		this.primaryChannel = primaryChannel;
	}

	public int getCfVersion() {
		return cfVersion;
	}

	public void setCfVersion(int cfVersion) {
		this.cfVersion = cfVersion;
	}


	public long getPreId() {
		return preId;
	}

	public void setPreId(long preId) {
		this.preId = preId;
	}

	public int getBdFollowed() {
		return bdFollowed;
	}

	public void setBdFollowed(int bdFollowed) {
		this.bdFollowed = bdFollowed;
	}


	public String getFinishStr() {
		return finishStr;
	}

	public void setFinishStr(String finishStr) {
		this.finishStr = finishStr;
	}

	public int getCaseId() {
		return caseId;
	}

	public void setCaseId(int caseId) {
		this.caseId = caseId;
	}

	public int getNoHandlingFee() {
		return noHandlingFee;
	}

	public void setNoHandlingFee(int noHandlingFee) {
		this.noHandlingFee = noHandlingFee;
	}

}
