package com.shuidihuzhu.cf.model.crowdfunding.loverank;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */

@Data
public class LoveRankSimpleVo implements Serializable {


    @ApiModelProperty("用户昵称头像信息")
    List<UserDonateInfoForC> userDonateList;

    @ApiModelProperty("捐款好友个数")
    private int friendCount;

    @ApiModelProperty("爱心人士个数")
    private int lovePepoleCount;

    @ApiModelProperty("登录用户捐款金额，单位为分")
    private int donateAmount;

    @ApiModelProperty("登录用户分享次数")
    private int shareCount;

}
