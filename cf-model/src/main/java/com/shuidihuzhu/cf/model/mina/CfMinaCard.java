package com.shuidihuzhu.cf.model.mina;

/**
 * Created by dongcf on 2017/12/18
 */
public class CfMinaCard {

    private Integer id;

    private String appId;

    private String title;

    private String titleImgUrl;//列表页图片

    private String titleBigImgUrl;//详情页大图

    private String receiverImgUrl;//领取人结果页卡片图片

    private String unsentImgUrl;//送礼人未赠出结果页图片

    private String alreadyImgUrl;//送礼人赠出结果页图片

    private String shareImgUrl;//发送到群、个人截图

    private Integer cardType;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitleImgUrl() {
        return titleImgUrl;
    }

    public void setTitleImgUrl(String titleImgUrl) {
        this.titleImgUrl = titleImgUrl;
    }

    public String getTitleBigImgUrl() {
        return titleBigImgUrl;
    }


    public void setTitleBigImgUrl(String titleBigImgUrl) {
        this.titleBigImgUrl = titleBigImgUrl;
    }

    public String getReceiverImgUrl() {
        return receiverImgUrl;
    }

    public void setReceiverImgUrl(String receiverImgUrl) {
        this.receiverImgUrl = receiverImgUrl;
    }

    public String getUnsentImgUrl() {
        return unsentImgUrl;
    }

    public void setUnsentImgUrl(String unsentImgUrl) {
        this.unsentImgUrl = unsentImgUrl;
    }

    public String getAlreadyImgUrl() {
        return alreadyImgUrl;
    }

    public void setAlreadyImgUrl(String alreadyImgUrl) {
        this.alreadyImgUrl = alreadyImgUrl;
    }

    public String getShareImgUrl() {
        return shareImgUrl;
    }

    public void setShareImgUrl(String shareImgUrl) {
        this.shareImgUrl = shareImgUrl;
    }

    public Integer getCardType() {
        return cardType;
    }

    public void setCardType(Integer cardType) {
        this.cardType = cardType;
    }
}
