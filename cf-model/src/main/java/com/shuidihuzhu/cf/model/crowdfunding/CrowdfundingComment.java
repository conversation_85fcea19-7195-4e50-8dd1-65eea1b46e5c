package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingCommentType;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * Created by roy on 16/7/18.
 */
@EqualsAndHashCode
public class CrowdfundingComment {
    /**
     * @link source CrowdfundingCommentType
     */
    //not null
    private Long id;
    private Integer crowdfundingId;
    //required not null
    private Long parentId;

    //required not null
    private long userId;
    //required
    private Long commentId;
    //required not null
    private String content;
    //not null
    private boolean isDeleted;
    //not null
    private Date createTime;
    //required not null
    private Integer type;

    private Integer userThirdId;

    private Integer userThirdType;

    private int commentSource;

    public CrowdfundingComment() {
    }

    public CrowdfundingComment(Long parentId,Long commentId,long userId, Integer userThirdId,Integer userThirdType, String content, CrowdfundingCommentType type) {
        this.parentId = parentId;
        this.userId = userId;
        this.commentId = commentId;
        this.content = content;
        this.userThirdId = userThirdId;
        this.userThirdType = userThirdType;
        setType(type);
    }

    public Integer getUserThirdType() {
        return userThirdType;
    }

    public void setUserThirdType(Integer userThirdType) {
        this.userThirdType = userThirdType;
    }

    public void setIsDeleted(boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Integer getUserThirdId() {
        return userThirdId;
    }

    public void setUserThirdId(Integer userThirdId) {
        this.userThirdId = userThirdId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

//    public Integer getParentId() {
//        return parentId;
//    }
//
//    public void setParentId(Integer parentId) {
//        this.parentId = parentId;
//    }


    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public Long getCommentId() {
        return commentId;
    }

    public void setCommentId(Long commentId) {
        this.commentId = commentId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public boolean isDeleted() {
        return isDeleted;
    }

    public void setDeleted(boolean deleted) {
        isDeleted = deleted;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
    public void setType(CrowdfundingCommentType type) {
        this.type = type.value();
    }

    public Integer getCrowdfundingId() {
        return crowdfundingId;
    }

    public void setCrowdfundingId(Integer crowdfundingId) {
        this.crowdfundingId = crowdfundingId;
    }

    @Override
    public String toString() {
        return "CrowdfundingComment{" +
                "id=" + id +
                ", crowdfundingId=" + crowdfundingId +
                ", parentId=" + parentId +
                ", userId=" + userId +
                ", commentId=" + commentId +
                ", content='" + content + '\'' +
                ", isDeleted=" + isDeleted +
                ", createTime=" + createTime +
                ", commentSource=" + commentSource +
                '}';
    }


    public int getCommentSource() {
        return commentSource;
    }

    public void setCommentSource(int commentSource) {
        this.commentSource = commentSource;
    }
}
