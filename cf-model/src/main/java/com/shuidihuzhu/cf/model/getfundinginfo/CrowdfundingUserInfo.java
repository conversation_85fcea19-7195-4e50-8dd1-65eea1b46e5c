package com.shuidihuzhu.cf.model.getfundinginfo;

import com.shuidihuzhu.account.model.AccountPlatform;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2023/3/13  15:23
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CrowdfundingUserInfo {
    private long userId;
    private String cryptoUserId = "";
    private String nickname = "";
    private String headImgUrl = "";
    private String mobile = "";
    private String cryptoMobile = "";
    private String realName = "";
    private String idCard = "";
    private String cryptoIdCard = "";
    private AccountPlatform platform;
    private String channel = "";
    private long createTime;
    private String email = "";
    private String cipherUserId;
    private String shuidiAppCode;
}
