package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.CfPropertyTransferEnum;

import java.util.Date;

/**
 * Created by chao on 2017/10/13.
 */
public class CfPropertyTransferHistory {
	private long id;
	private long fromUserId;
	private long toUserId;
	private long bizId;
	private int bizType;
	private String bizTableName;
	private Date createTime;

	public CfPropertyTransferHistory() {

	}
	public CfPropertyTransferHistory(long fromUserId, long toUserId, long bizId, CfPropertyTransferEnum bizType) {
		this.fromUserId = fromUserId;
		this.toUserId = toUserId;
		this.bizId = bizId;
		this.bizTableName = bizType.getTableName();
		this.bizType = bizType.getCode();
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getFromUserId() {
		return fromUserId;
	}

	public void setFromUserId(long fromUserId) {
		this.fromUserId = fromUserId;
	}

	public long getToUserId() {
		return toUserId;
	}

	public void setToUserId(long toUserId) {
		this.toUserId = toUserId;
	}

	public long getBizId() {
		return bizId;
	}

	public void setBizId(long bizId) {
		this.bizId = bizId;
	}

	public int getBizType() {
		return bizType;
	}

	public void setBizType(int bizType) {
		this.bizType = bizType;
	}

	public String getBizTableName() {
		return bizTableName;
	}

	public void setBizTableName(String bizTableName) {
		this.bizTableName = bizTableName;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
}
