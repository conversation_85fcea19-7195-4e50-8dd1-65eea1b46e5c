package com.shuidihuzhu.cf.model.dailyreport;

/**
 * Created by chao on 2017/11/9.
 */
public class CfDailyReportSummary {
	private long id;
	private int infoId;
	private long userId;
	private String infoUuid;
	private String nickname;
	private String title;
	private String dateStr;
	private int totalAmount;
	private int currentAmount;
	private int todayAmount;
	private int todayShareCount;
	private int todayShareAmount;
	private int moneyInOneShare;
	private int messageStatus;

	public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}

	public String getInfoUuid() {
		return infoUuid;
	}

	public void setInfoUuid(String infoUuid) {
		this.infoUuid = infoUuid;
	}

	public String getNickname() {
		return nickname;
	}

	public void setNickname(String nickname) {
		this.nickname = nickname;
	}

	public int getTodayShareAmount() {
		return todayShareAmount;
	}

	public void setTodayShareAmount(int todayShareAmount) {
		this.todayShareAmount = todayShareAmount;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public int getInfoId() {
		return infoId;
	}

	public void setInfoId(int infoId) {
		this.infoId = infoId;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getDateStr() {
		return dateStr;
	}

	public void setDateStr(String dateStr) {
		this.dateStr = dateStr;
	}

	public int getTotalAmount() {
		return totalAmount;
	}

	public void setTotalAmount(int totalAmount) {
		this.totalAmount = totalAmount;
	}

	public int getCurrentAmount() {
		return currentAmount;
	}

	public void setCurrentAmount(int currentAmount) {
		this.currentAmount = currentAmount;
	}

	public int getTodayAmount() {
		return todayAmount;
	}

	public void setTodayAmount(int todayAmount) {
		this.todayAmount = todayAmount;
	}

	public int getTodayShareCount() {
		return todayShareCount;
	}

	public void setTodayShareCount(int todayShareCount) {
		this.todayShareCount = todayShareCount;
	}

	public int getMoneyInOneShare() {
		return moneyInOneShare;
	}

	public void setMoneyInOneShare(int moneyInOneShare) {
		this.moneyInOneShare = moneyInOneShare;
	}

	public int getMessageStatus() {
		return messageStatus;
	}

	public void setMessageStatus(int messageStatus) {
		this.messageStatus = messageStatus;
	}

}
