package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.shuidihuzhu.cf.model.crowdfunding.DiseaseLabel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("支付成功后给用户返回的信息")
public class CfOrderPaySuccessPageVO {

    @ApiModelProperty("用户填写的疾病信息，可能为空")
    private String diseaseName;

    @ApiModelProperty("疾病归一后的的疾病信息，可能为空或者为未知")
    private String diseaseNorm;

    @ApiModelProperty("区分案例服务渠道")
    private int serviceTag;

    @ApiModelProperty("疾病-险种")
    private DiseaseLabel diseaseLabel;

    @ApiModelProperty("疾病名称-广告使用")
    private String adDiseaseName;

}
