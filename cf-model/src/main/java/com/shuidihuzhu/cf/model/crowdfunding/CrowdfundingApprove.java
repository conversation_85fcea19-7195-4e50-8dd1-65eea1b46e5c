package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class CrowdfundingApprove implements Serializable {

    private Integer id;

    private Integer crowdfundingId;

    /**
     * @see AdminApproveSourceTypeEnum#getValue()
     */
    private int sourceType;

    private long workOrderId;

    private long oprid;

    private Date oprtime;

    private Integer status;

    private String comment;

    /**
     * 运营评论时的组织快照
     */
    private String organization;

    private static final long serialVersionUID = 1L;

}