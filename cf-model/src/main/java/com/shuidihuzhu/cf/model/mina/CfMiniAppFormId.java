package com.shuidihuzhu.cf.model.mina;

/**
 * Created by wangsf on 17/11/22.
 */
public class CfMiniAppFormId {

	private int id;
	private String appId;
	private String version;
	private String formId;
	private long userId;
	private String selfTag; //用户未登录的情况下用来标记用户
	private int elementCode; //用来标记是哪个id被点击
	private boolean hasSentMsg = false; //该formId是否被使用过
	private String button;

	public CfMiniAppFormId() {

	}

	public CfMiniAppFormId(String appId, String version, String formId,long userId,
	                       String selfTag, int elementCode) {
		this.appId = appId;
		this.version = version;
		this.formId = formId;
		this.userId = userId;
		this.selfTag = selfTag;
		this.elementCode = elementCode;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getFormId() {
		return formId;
	}

	public void setFormId(String formId) {
		this.formId = formId;
	}

	public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}

	public String getSelfTag() {
		return selfTag;
	}

	public void setSelfTag(String selfTag) {
		this.selfTag = selfTag;
	}

	public int getElementCode() {
		return elementCode;
	}

	public void setElementCode(int elementCode) {
		this.elementCode = elementCode;
	}

	public boolean isHasSentMsg() {
		return hasSentMsg;
	}

	public void setHasSentMsg(boolean hasSentMsg) {
		this.hasSentMsg = hasSentMsg;
	}

	public String getButton() {
		return button;
	}

	public void setButton(String button) {
		this.button = button;
	}
}
