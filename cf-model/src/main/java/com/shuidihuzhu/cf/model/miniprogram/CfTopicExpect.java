package com.shuidihuzhu.cf.model.miniprogram;

public class CfTopicExpect {
    private long id;
    private int keywordId;
    private long userId;
    private int status;
    private int isDelete;
    private int phaseId;

    public CfTopicExpect() {
    }

    public CfTopicExpect(int keywordId,long userId, int status, int phaseId) {
        this.keywordId = keywordId;
        this.userId = userId;
        this.status = status;
        this.phaseId = phaseId;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public int getKeywordId() {
        return keywordId;
    }

    public void setKeywordId(int keywordId) {
        this.keywordId = keywordId;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(int isDelete) {
        this.isDelete = isDelete;
    }

    public int getPhaseId() {
        return phaseId;
    }

    public void setPhaseId(int phaseId) {
        this.phaseId = phaseId;
    }

    @Override
    public String toString() {
        return "CfTopicExpect{" +
                "id=" + id +
                ", keywordId=" + keywordId +
                ", userId=" + userId +
                ", status=" + status +
                ", isDelete=" + isDelete +
                ", phaseId=" + phaseId +
                '}';
    }
}
