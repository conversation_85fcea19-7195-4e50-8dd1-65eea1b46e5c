package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.shuidihuzhu.cf.model.crowdfunding.CfHospital;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
public class HospitalInfo {
    private Pagination pagination;

    private List<CfHospital> cfHospitals=new ArrayList<>();

    @Data
    @NoArgsConstructor
    public static class Pagination{
        private int current;

        private int total;

        private int pageSize;

    }
}
