package com.shuidihuzhu.cf.model.contribute;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author：liuchangjun
 * @Date：2021/11/11
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("打赏下单所需信息")
public class ContributeAddOrderDTO {

    @ApiModelProperty("金额：单位元")
    private double amt;

    private String platform;

    private String source;

    private String selfTag;


    private Boolean anonymous;


    private Integer userThirdType;

    private Integer payType =0;

    @ApiModelProperty("捐献渠道")
    private String channel;

}
