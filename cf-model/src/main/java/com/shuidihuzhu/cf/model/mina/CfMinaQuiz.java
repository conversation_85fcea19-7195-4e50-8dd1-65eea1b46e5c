package com.shuidihuzhu.cf.model.mina;

import java.util.Date;

/**
 * Created by dongcf on 2017/12/12
 * @Updated By Wangsf on 2018/01/11 增加'新'标识，以及排序字段
 *  @Updated By dongcf on 2018/01/24 增加'显示类型'标识
 */
public class CfMinaQuiz {

    private int id;

    private String appId;

    private String title;

    private String titleImgUrl;

    private String shareTitle;

    private String shareImgUrl;

    private int times;

    private int sort;

    private boolean isNew;

    private int showType;

    private int quizType;

    private Date releaseTime;

    private int isDelete;

    private String resultDefaultShareImgUrl;

    private String resultDefaultImgUrl;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitleImgUrl() {
        return titleImgUrl;
    }

    public void setTitleImgUrl(String titleImgUrl) {
        this.titleImgUrl = titleImgUrl;
    }


    public String getShareTitle() {
        return shareTitle;
    }

    public void setShareTitle(String shareTitle) {
        this.shareTitle = shareTitle;
    }

    public String getShareImgUrl() {
        return shareImgUrl;
    }

    public void setShareImgUrl(String shareImgUrl) {
        this.shareImgUrl = shareImgUrl;
    }

    public int getTimes() {
        return times;
    }

    public void setTimes(int times) {
        this.times = times;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public boolean isNew() {
        return isNew;
    }

    public void setNew(boolean aNew) {
        isNew = aNew;
    }

    public int getShowType() {
        return showType;
    }

    public void setShowType(int showType) {
        this.showType = showType;
    }

    public int getQuizType() {
        return quizType;
    }

    public void setQuizType(int quizType) {
        this.quizType = quizType;
    }

    public Date getReleaseTime() {
        return releaseTime;
    }

    public void setReleaseTime(Date releaseTime) {
        this.releaseTime = releaseTime;
    }

    public int getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(int isDelete) {
        this.isDelete = isDelete;
    }

    public String getResultDefaultShareImgUrl() {
        return resultDefaultShareImgUrl;
    }

    public void setResultDefaultShareImgUrl(String resultDefaultShareImgUrl) {
        this.resultDefaultShareImgUrl = resultDefaultShareImgUrl;
    }

    public String getResultDefaultImgUrl() {
        return resultDefaultImgUrl;
    }

    public void setResultDefaultImgUrl(String resultDefaultImgUrl) {
        this.resultDefaultImgUrl = resultDefaultImgUrl;
    }

    @Override
    public String toString() {
        return "CfMinaQuiz{" +
                "id=" + id +
                ", appId='" + appId + '\'' +
                ", title='" + title + '\'' +
                ", titleImgUrl='" + titleImgUrl + '\'' +
                ", shareTitle='" + shareTitle + '\'' +
                ", shareImgUrl='" + shareImgUrl + '\'' +
                ", times=" + times +
                ", sort=" + sort +
                ", isNew=" + isNew +
                ", showType=" + showType +
                ", quizType=" + quizType +
                ", resultDefaultShareImgUrl=" + resultDefaultShareImgUrl +
                ", resultDefaultImgUrl=" + resultDefaultImgUrl +
                '}';
    }
}
