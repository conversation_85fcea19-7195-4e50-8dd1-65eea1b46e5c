package com.shuidihuzhu.cf.model.crowdfunding;

import io.swagger.annotations.ApiModelProperty;

import java.sql.Timestamp;

/**
 * Created by ahrievil on 2017/4/11.
 */
public class CfHospital {
    private int id;
    private String name;
    private String province;
    private String city;
    private Timestamp createTime;
    private Timestamp updateTime;
    @ApiModelProperty("医院编码")
    private String hospitalCode;


    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public Timestamp getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }

    public String getHospitalCode() {
        return hospitalCode;
    }

    public void setHospitalCode(String hospitalCode) {
        this.hospitalCode = hospitalCode;
    }
}
