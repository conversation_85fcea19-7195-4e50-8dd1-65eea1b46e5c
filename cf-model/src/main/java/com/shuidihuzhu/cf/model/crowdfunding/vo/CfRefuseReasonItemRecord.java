package com.shuidihuzhu.cf.model.crowdfunding.vo;

import org.apache.commons.lang3.builder.ToStringBuilder;

import java.util.Map;

/**
 * Created by Ahrievil on 2017/7/31
 */
public class CfRefuseReasonItemRecord {
    private int itemIds;
    private Map<Integer, String> reason;

    public CfRefuseReasonItemRecord() {
    }

    public CfRefuseReasonItemRecord(int itemIds, Map<Integer, String> reason) {
        this.itemIds = itemIds;
        this.reason = reason;
    }

    public int getItemIds() {
        return itemIds;
    }

    public void setItemIds(int itemIds) {
        this.itemIds = itemIds;
    }

    public Map<Integer, String> getReason() {
        return reason;
    }

    public void setReason(Map<Integer, String> reason) {
        this.reason = reason;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
