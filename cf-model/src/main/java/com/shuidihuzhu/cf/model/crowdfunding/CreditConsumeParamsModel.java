package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CreditConsumeParamsModel {

	private long uid;
	private String sUid;//加密Uid，用户在兑吧的唯一标识
	private Integer credits=0;//消耗积分数
	private String orderNum="";//兑吧订单号
	private String description="";
	private String itemCode="";//商品编码，非必须参数
	private String type="";//类型：QB,Phonebill,Alipay,Coupon  所有类型不区分大小写
	private Integer facePrice=0;//面值，分为单位
	private Integer actualPrice=0;//实际扣款，分为单位
	private String ip="";//用户兑换时使用的ip地址，有可能为空
	private String params="";//参数，根据不同的type，有不同的含义，参见在线文档
	private String timestamp = "";
	private String waitAudit = "";
	private String appKey = "";
}
