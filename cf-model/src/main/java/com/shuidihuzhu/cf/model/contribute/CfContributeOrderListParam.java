package com.shuidihuzhu.cf.model.contribute;

import lombok.Data;

/**
 * 资助订单查询列表参数
 *
 * <AUTHOR>
 * @since 2021-09-13 9:54 下午
 **/
@Data
public class CfContributeOrderListParam {

    /**
     * 付款时间开始, 默认空
     */
    private String callbackStartTime;

    /**
     * 付款时间结束, 默认空
     */
    private String callbackEndTime;

    /**
     * 资助方式 默认-1
     */
    private Integer contributeType;

    /**
     * 订单状态 默认-1
     */
    private Integer orderStatus;

    /**
     * 案例id 默认-1
     */
    private Integer caseId;

    /**
     * 关联捐款订单id
     */
    private Long donateOrderId;

    /**
     * 金额 默认-1
     */
    private String payAmountStr;

    /**
     * 默认10
     */
    private Integer pageSize;

    /**
     * 默认 空
     */
    private String pageType;

    /**
     * 默认 0
     */
    private Long id;
    /**
     * 资助三方订单号
     */
    private String thirdPayUid;

}
