package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

/**
 * @author: wanghui
 * @time: 2018/10/31 10:49 AM
 * @description:
 * @param:  * @param null :
 * @return:  * @return : null
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class CrowdfundingInfoTag implements Serializable {
	// id
	private Integer id;
	// 案例info_uuid
	private String infoId;

	// 标识 以,分割
	private String tags;

	private Date createTime;

	private Date updateTIme;
	// 是否删除
	private Integer isDelete;
}
