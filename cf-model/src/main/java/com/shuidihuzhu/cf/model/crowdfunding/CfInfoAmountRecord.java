package com.shuidihuzhu.cf.model.crowdfunding;

import java.sql.Timestamp;

public class CfInfoAmountRecord {

	private Long id;
	private String infoUuid;
	private Long beforeAmount;
	private Long changeAmount;
	private Long afterAmount;
	private Integer directionType;
	private Integer bizType;
	private String payUid;
	private Timestamp dateCreated;
	private Timestamp lastModified;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getInfoUuid() {
		return infoUuid;
	}

	public void setInfoUuid(String infoUuid) {
		this.infoUuid = infoUuid;
	}

	public Long getBeforeAmount() {
		return beforeAmount;
	}

	public void setBeforeAmount(Long beforeAmount) {
		this.beforeAmount = beforeAmount;
	}

	public Long getChangeAmount() {
		return changeAmount;
	}

	public void setChangeAmount(Long changeAmount) {
		this.changeAmount = changeAmount;
	}

	public Long getAfterAmount() {
		return afterAmount;
	}

	public void setAfterAmount(Long afterAmount) {
		this.afterAmount = afterAmount;
	}

	public Integer getDirectionType() {
		return directionType;
	}

	public void setDirectionType(Integer directionType) {
		this.directionType = directionType;
	}

	public Integer getBizType() {
		return bizType;
	}

	public void setBizType(Integer bizType) {
		this.bizType = bizType;
	}

	public String getPayUid() {
		return payUid;
	}

	public void setPayUid(String payUid) {
		this.payUid = payUid;
	}

	public Timestamp getDateCreated() {
		return dateCreated;
	}

	public void setDateCreated(Timestamp dateCreated) {
		this.dateCreated = dateCreated;
	}

	public Timestamp getLastModified() {
		return lastModified;
	}

	public void setLastModified(Timestamp lastModified) {
		this.lastModified = lastModified;
	}

}
