package com.shuidihuzhu.cf.model.topList;

import com.google.common.collect.Maps;
import lombok.Getter;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.util.Comparator;
import java.util.Map;

public class CfTopListConstant {

    // 筹款人 感谢、邀请
    public static final int THANK = 1;
    public static final int INVITE_SHARE = 2;
    public static final int HAS_PRIZED_ALL = 3;
    public static final int HAS_PRIZED_VERIFICATE = 4;


    public static final int CAN_PRIZE = 100;

    // 榜单数据个数
    public static final int TOP_LIST_SIZE = 20;

    public static final Map<Integer, Comparator> TOP_LIST_COMPARATOR = Maps.newHashMap();
    static {

        TOP_LIST_COMPARATOR.put(TopListType.SHARE_TOP_LIST_TYPE.getCode(), new CfTopListSummary.ShareUserCaseInfoComparator());
        TOP_LIST_COMPARATOR.put(TopListType.DONATE_TOP_LIST_TYPE.getCode(), new CfTopListSummary.DonateUserCaseComparator());
    }

    public static final Map<Integer, Comparator> TOP_LIST_DONOR_COMPARATOR = Maps.newHashMap();
    static {

        TOP_LIST_DONOR_COMPARATOR.put(TopListType.SHARE_TOP_LIST_TYPE.getCode(), new CfTopListSummary.ShareDonorComparator());
        TOP_LIST_DONOR_COMPARATOR.put(TopListType.DONATE_TOP_LIST_TYPE.getCode(), new CfTopListSummary.DonateDonorComparator());
    }



    public static final int CAN_OPERATE = 0;
    public static final int NOT_OPERATE = 1;

    public static final int NOT_ACTIVITY = 1;
    public static final int ACTIVITY_CAN_PRIZE = 2;
    public static final int ACTIVITY_HAS_PRIZED = 3;
    public static final int ACTIVITY_NOT_PRIZE = 4;


    // 初审通过多少天后 可以领奖
    public static final int PRIZE_TIME_AFTER_INITIAL_PASS = 4;
    // 可以领奖的整点时间
    public static final int CAN_PRIZE_TIME_HOUR = 12;

    public static final int TOTAL_PRIZE_LIMIT = 3;

    public static final DateTimeFormatter MM_DD_FMT = DateTimeFormat.forPattern("MM-dd");

    // 捐款人是否在榜单
    public static final int NOT_IN_RANK = 0;
    public static final int IN_RANK = 1;

    public static final String TOP_LIST_INVITE_BUSINESSINFO = "topListInvite";

    // 榜单分流的实验key
    public static final String TOP_LIST_EAGLE_VALUE = "activityList111";


    public static final int TOP_LIST_THANKS_COMMENT_SOURCE = 1;

    public static final String TOP_LIST_232_MODEL_NUM = "templateCkrReply232-List";
    public static final String TOP_LILST_1352_MODEL_NUM = "template1352-List";

    // 榜单的类型
    @Getter
    public enum TopListType {
        NO_TOP_LIST(0, "无榜单"),
        SHARE_TOP_LIST_TYPE(1, "分享-带来点击榜"),
        DONATE_TOP_LIST_TYPE(2, "贡献-本人贡献+转发带来贡献榜"),
        ;

        private int code;
        private String desc;

        TopListType(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        static public TopListType valueOfCode(int code) {

            TopListType[] values = TopListType.values();
            for (TopListType type : values) {
                if (type.getCode() == code) {
                    return type;
                }
            }

            throw new RuntimeException("TopListType valueOfCode code错误 code: " + code);
        }
    }

    // 榜单的范围
    @Getter
    public enum TopListScope {
        DEFAULT(0, ""),
        SCOPE_ALL_LIST(1, "全部榜单"),
        SCOPE_FRIEND_LIST(2, "好友榜单"),
        ;

        TopListScope(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        private int code;
        private String desc;

        static public TopListScope valueOfCode(int code) {

            TopListScope[] values = TopListScope.values();
            for (TopListScope type : values) {
                if (type.getCode() == code) {
                    return type;
                }
            }

            throw new RuntimeException("TopListScope valueOfCode code错误 code: " + code);
        }
    }


    public static final String USER_DATA_CHANGE_LOCK_NAME = "cfTopListChangeLock";


    public static final int ALL_SHARE_RECORD = 1;
    public static final int FRIEND_SHARE_RECORD = 2;
    public static final int ALL_DONATE_RECORD = 3;
    public static final int FRIEND_DONATE_RECORD = 4;


}
