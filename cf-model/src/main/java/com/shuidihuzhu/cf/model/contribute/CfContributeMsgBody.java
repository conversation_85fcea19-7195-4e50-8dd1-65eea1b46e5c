package com.shuidihuzhu.cf.model.contribute;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CfContributeMsgBody {

    private String payUid;

    private int action;

    @Getter
    public enum OrderAction {

        PRE_PAY(1, "预支付"),

        PAY_SUCCESS(5, "支付成功"),

        REFUND_APPLY(10, "发起退款"),

        REFUND_SUCCESS(15, "退款成功"),

        REFUND_FAILED(20, "退款失败"),


        ;

        private int code;
        private String msg;

        OrderAction(int code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        public static OrderAction valueOfCode(int code) {

            for (OrderAction action : OrderAction.values()) {
                if (action.getCode() == code) {
                    return action;
                }
            }

            return null;
        }
    }
}
