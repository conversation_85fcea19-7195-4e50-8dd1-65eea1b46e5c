package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.common.web.model.AbstractModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.sql.Timestamp;
import java.util.Date;

/**
 * Created by lgj on 16/6/21.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CrowdfundingPayRecordShardingModel extends AbstractModel {
	private static final long serialVersionUID = 3142190598947320481L;

	private Integer id;

    private String payUid;

    private Long crowdfundingOrderId;

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}
}
