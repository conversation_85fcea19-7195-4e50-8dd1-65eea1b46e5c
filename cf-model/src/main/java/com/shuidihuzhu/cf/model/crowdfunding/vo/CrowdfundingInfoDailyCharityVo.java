package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * Author: <PERSON>
 * Date: 2017/6/4 15:14
 */
public class CrowdfundingInfoDailyCharityVo {

	private String tag;
	private int donationCount;
	private double targetAmount;
	private double amount;
	private String title;
	private String content;
	private long userId;
	private String userHeadImgUrl;
	private String userNickName;
	private Integer type;
	private String infoUuid;
	private long createTime;

	public String getInfoUuid() {
		return infoUuid;
	}

	public void setInfoUuid(String infoUuid) {
		this.infoUuid = infoUuid;
	}

	public String getTag() {
		return tag;
	}

	public void setTag(String tag) {
		this.tag = tag;
	}

	public int getDonationCount() {
		return donationCount;
	}

	public void setDonationCount(int donationCount) {
		this.donationCount = donationCount;
	}

	public double getTargetAmount() {
		return targetAmount;
	}

	public void setTargetAmount(double targetAmount) {
		this.targetAmount = targetAmount;
	}

	public double getAmount() {
		return amount;
	}

	public void setAmount(double amount) {
		this.amount = amount;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}

	public String getUserHeadImgUrl() {
		return userHeadImgUrl;
	}

	public void setUserHeadImgUrl(String userHeadImgUrl) {
		this.userHeadImgUrl = userHeadImgUrl;
	}

	public String getUserNickName() {
		return userNickName;
	}

	public void setUserNickName(String userNickName) {
		this.userNickName = userNickName;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public List<CrowdfundingAttachmentVo> getCrowdfundingAttachmentList() {
		return crowdfundingAttachmentList;
	}

	public void setCrowdfundingAttachmentList(
			List<CrowdfundingAttachmentVo> crowdfundingAttachmentList) {
		this.crowdfundingAttachmentList = crowdfundingAttachmentList;
	}

	private List<CrowdfundingAttachmentVo> crowdfundingAttachmentList = Lists.newArrayList();

	public void setCreateTime(long createTime) {
		this.createTime = createTime;
	}

	public long getCreateTime() {
		return createTime;
	}
}
