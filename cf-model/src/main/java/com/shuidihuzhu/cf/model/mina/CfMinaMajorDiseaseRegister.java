package com.shuidihuzhu.cf.model.mina;

import java.sql.Timestamp;

/**
 * Created by Ahrievil on 2017/8/11
 */
public class CfMinaMajorDiseaseRegister {
    private long id;
    private int bizId;
    private String openId;
    private String formId;
    private int status;
    private Timestamp notifyTime;
    private int isDelete;
    private Timestamp createTime;
    private Timestamp updateTime;

    public CfMinaMajorDiseaseRegister() {
    }

    public CfMinaMajorDiseaseRegister(int bizId, String openId, String formId) {
        this.bizId = bizId;
        this.openId = openId;
        this.formId = formId;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public int getBizId() {
        return bizId;
    }

    public void setBizId(int bizId) {
        this.bizId = bizId;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getFormId() {
        return formId;
    }

    public void setFormId(String formId) {
        this.formId = formId;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public Timestamp getNotifyTime() {
        return notifyTime;
    }

    public void setNotifyTime(Timestamp notifyTime) {
        this.notifyTime = notifyTime;
    }

    public int getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(int isDelete) {
        this.isDelete = isDelete;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public Timestamp getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }
}
