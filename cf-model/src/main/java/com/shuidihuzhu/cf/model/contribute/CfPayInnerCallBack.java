package com.shuidihuzhu.cf.model.contribute;

import com.shuidihuzhu.cf.enums.combine.CombineCommonEnum;
import com.shuidihuzhu.client.baseservice.pay.model.PayInnerCallBack;
import com.shuidihuzhu.client.baseservice.pay.model.v3.combine.callback.CombineInnerCallBack;
import com.shuidihuzhu.client.baseservice.pay.model.v3.combine.callback.CombineInnerSub;
import com.shuidihuzhu.common.web.util.MoneyUtil;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Data
public class CfPayInnerCallBack {

    private CombineCommonEnum.CombineOrderType orderType;

    // 业务方订单号
    private String orderId;
    // 支付系统订单号
    private String payUid;

    // 支付成功时间
    private Timestamp businessTime;
    // 真实支付金额
    private int amountInFen;

    private int bizType;

    /**
     * 手续费
     */
    private int feeAmountInFen;
    /**
     * 手续费费率
     */
    private String feePercent;



    public static CfPayInnerCallBack convertFromPayInner(PayInnerCallBack innerCallBack) {

        if (innerCallBack == null) {
            return null;
        }
        CfPayInnerCallBack callBack = new CfPayInnerCallBack();
        callBack.setOrderId(innerCallBack.getOrderId());
        callBack.setPayUid(innerCallBack.getPayUid());

        callBack.setBusinessTime(innerCallBack.getBusinessTime());
        callBack.setAmountInFen(innerCallBack.getAmountInFen());
        callBack.setBizType(innerCallBack.getBizType());
        callBack.setFeeAmountInFen(innerCallBack.getFee());
        callBack.setFeePercent(Optional.ofNullable(innerCallBack.getRate()).map(BigDecimal::toString).orElse("0.006"));
        callBack.setOrderType(CombineCommonEnum.CombineOrderType.CASE_DONATE);
        return callBack;
    }

    public static CfPayInnerCallBack convertFrom(CombineInnerCallBack innerCallBack,
                                                 CombineInnerSub innerSub,
                                                 CombineCommonEnum.CombineOrderType orderType) {
        if (innerCallBack == null || innerSub == null) {
            return null;
        }

        CfPayInnerCallBack callBack = new CfPayInnerCallBack();


        callBack.setOrderId(innerSub.getSubOrderId());
        callBack.setPayUid(innerSub.getSubPayUid());
        callBack.setBusinessTime(innerSub.getCallbackTime() != null ? innerSub.getCallbackTime() : new Timestamp(System.currentTimeMillis()));

        callBack.setAmountInFen(innerSub.getRealPayAmount());
        callBack.setBizType(innerSub.getBizType());

        callBack.setFeeAmountInFen(innerSub.getFee());
        callBack.setFeePercent(Optional.ofNullable(innerSub.getRate()).map(BigDecimal::toString).orElse("0.006"));
        callBack.setOrderType(orderType);
        return callBack;
    }

}
