package com.shuidihuzhu.cf.model.bigdata;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/12/26  21:54
 */
@Data
public class CfCaseFriendShareDo {
    @ApiModelProperty("案例id")
    private Long infoId;
    @ApiModelProperty("好友用户id")
    private Long friendUserId;
    @ApiModelProperty("转发带来的捐单量")
    private Long shareDonateAnt;
    @ApiModelProperty("转发带来的捐单金额,单位：元")
    private BigDecimal shareDonateAmt;
    @ApiModelProperty("日期")
    private String dt;
}
