package com.shuidihuzhu.cf.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * @package: com.shuidihuzhu.cf.model
 * @Author: l<PERSON>jiaw<PERSON>
 * @Date: 2019-09-09  14:53
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class CfInfoSimpleModelV2 {
    /**
     * 筹款ID,自增长
     */
    private int id;
    /**
     * UUID,前端引用时使用(避免暴露顺序ID,防止被猜中)
     */
    private String infoId;
    /**
     * 发起筹款人用户ID,发起筹款人必须通过水滴账号验证
     */
    private long userId = -1;
    /**
     * 筹款金额,单位:分
     */
    private int targetAmount;
    /**
     * 已筹金额,单位:分
     */
    private int amount;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 筹款开始时间
     */
    private Date beginTime;
    /**
     * 筹款结束时间
     */
    private Date endTime;
}
