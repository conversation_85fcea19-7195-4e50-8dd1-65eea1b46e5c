package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.shuidihuzhu.cf.vo.CfMiniProgramModel;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@ApiModel
public class MiniProgramVo {
    private Integer size;
    private boolean hasNext;
    private Integer anchorId;
    private List<CfMiniProgramModel> list = new ArrayList<>();
}
