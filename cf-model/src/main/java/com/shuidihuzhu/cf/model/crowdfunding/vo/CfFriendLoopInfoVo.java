package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.shuidihuzhu.common.web.util.MoneyUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(value = "好友捐款数据")
public class CfFriendLoopInfoVo {
    @ApiModelProperty(value = "好友昵称")
    private String nickname;
    @ApiModelProperty(value = "头像链接")
    private String headImgUrl;
    /**
     * 这个amount 实际返回的是元
     */
    @ApiModelProperty(value = "累计捐款金额--元")
    private double amount;
    @ApiModelProperty(value = "累计分享次数")
    private int shared;
    @ApiModelProperty(value = "0：捐款且转发 1：仅捐款 2：仅转发 3：默认，可以忽略展示")
    private int type;
    @ApiModelProperty(value = "最新的捐款或者转发时间")
    private Date updateTime;
    @ApiModelProperty(value = "累计捐款金额-分")
    private int amountFen;
    /**
     * 对该案例捐款次数
     */
    @ApiModelProperty(value = "累计捐款次数")
    private int amountCount;

    /**
     * 需要将amountFen转为元
     *
     * @return
     */
    public double getAmount() {
        if (amountFen > 0) {
            this.amount = Double.parseDouble(MoneyUtil.buildBalanceV2(this.amountFen));
        }
        return amount;
    }

    public void setAmount(double amount) {
        this.amount = amount;
    }

}
