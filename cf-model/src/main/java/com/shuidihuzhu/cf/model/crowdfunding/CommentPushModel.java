package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.wx.enums.AccountThirdTypeEnum;

public class CommentPushModel {

	public String commentUserOpenId;
	public Integer crowdfundingId;
	public AccountThirdTypeEnum userThirdType = AccountThirdTypeEnum.WX_SD;
	public CrowdfundingComment comment;
	public String firstValue;
	public String username;
	public String time;
	public String content;
	public String remarkContent = "感谢您的给力帮助！\n求一次转发，让更多的人帮助%s！";
	public String url;
	public String firstValueColor = "";
	public String remarkColor = "";

	public CrowdfundingInfoView crowdfundingInfoView;
	public CrowdfundingAuthor crowdfundingAuthor;
	public long authorUserId;
}