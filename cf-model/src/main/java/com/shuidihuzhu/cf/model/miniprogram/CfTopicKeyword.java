package com.shuidihuzhu.cf.model.miniprogram;

public class CfTopicKeyword {
    private int id;
    private String keyword;
    private int isDelete;

    public CfTopicKeyword() {
    }

    public CfTopicKeyword(String keyword) {
        this.keyword = keyword;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public int getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(int isDelete) {
        this.isDelete = isDelete;
    }

    @Override
    public String toString() {
        return "CfTopicKeyword{" +
                "id=" + id +
                ", keyword='" + keyword + '\'' +
                ", isDelete=" + isDelete +
                '}';
    }
}
