package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.account.compatible.model.HasUserId;
import com.shuidihuzhu.account.compatible.model.HasUserInfo;
import com.shuidihuzhu.cf.vo.GoodsOrderExtVo;
import com.shuidihuzhu.common.web.model.AbstractModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;

import java.util.Date;
import java.util.List;

/**
 * Created by lgj on 16/6/22.
 */
public class CrowdfundingUserView extends AbstractModel implements HasUserId, HasUserInfo {

    private static final long serialVersionUID = -6193023505650984802L;

    private Long id;//为订单id orderId

    /**
     * 根据id生成的加密id，防止泄露
     */
    private String targetId; //根据id生成的加密id

    /**
     * 是否禁止评论
     */
    private boolean disableComment;

    private long userId;

    private Integer userThirdId;

    private String nickname;

    private String headImgUrl;

    private Double amt;

    private String comment;

    private List<CrowdfundingCommentView> comments;

    private Integer time;

    private boolean isShare;

    private boolean anonymous;

    private GoodsOrderExtVo goodsOrderExtVo;

    private int score;
    //是否关注   true 关注点赞
    private boolean follow;

    @ApiModelProperty("补贴费用count")
    private double subsidyCount;

    @ApiModelProperty("补贴费用单位")
    private String subsidyUnitName;

    @ApiModelProperty("补贴费用和订单金额和 单位元，因为amt单位是元")
    private double totalAmount;

    @ApiModelProperty("好友关系")
    private String ship;

    @ApiModelProperty("支付时间")
    private Date payTime;


    public void resetId(){
        Long id = getId();
        if (id != null && id >= 0L){
            setId(-1L);
        }
        if (CollectionUtils.isNotEmpty(getComments())){
            getComments().forEach(v -> v.resetReplyId(v));
        }
    }

    @Getter @Setter
    @ApiModelProperty("祝福卡片文案")
    private String blessingCardText;

    @Getter @Setter
    @ApiModelProperty("祝福卡片图片")
    private String blessingCardImage;

    public CrowdfundingUserView() {
    }

    public boolean isAnonymous() {
        return anonymous;
    }

    public void setAnonymous(boolean anonymous) {
        this.anonymous = anonymous;
    }

    public CrowdfundingUserView(CrowdfundingUser cu) {
        this.id = cu.getOrderId() == null ? 0L : cu.getOrderId();
        this.userId = cu.getUserId();
        this.userThirdId = cu.getUserThirdId();
        this.nickname = cu.getNickname();
        this.headImgUrl = cu.getHeadImgUrl();
        this.amt = cu.getAmt();
        this.comment = cu.getComment();
        this.time = cu.getTime();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTargetId() {
        return targetId;
    }

    public void setTargetId(String targetId) {
        this.targetId = targetId;
    }

    public double getSubsidyCount() {
        return subsidyCount;
    }

    public void setSubsidyCount(double subsidyCount) {
        this.subsidyCount = subsidyCount;
    }

    public String getSubsidyUnitName() {
        return subsidyUnitName;
    }

    public void setSubsidyUnitName(String subsidyUnitName) {
        this.subsidyUnitName = subsidyUnitName;
    }

    public List<CrowdfundingCommentView> getComments() {
        return comments;
    }

    public void setComments(List<CrowdfundingCommentView> comments) {
        this.comments = comments;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public Integer getUserThirdId() {
        return userThirdId;
    }

    public void setUserThirdId(Integer userThirdId) {
        this.userThirdId = userThirdId;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getHeadImgUrl() {
        return headImgUrl;
    }

    public void setHeadImgUrl(String headImgUrl) {
        this.headImgUrl = headImgUrl;
    }

    public Double getAmt() {
        return amt;
    }

    public void setAmt(Double amt) {
        this.amt = amt;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public Integer getTime() {
        return time;
    }

    public void setTime(Integer time) {
        this.time = time;
    }

    public boolean getIsShare() {
        return isShare;
    }

    public void setIsShare(boolean isShare) {
        this.isShare = isShare;
    }

    public GoodsOrderExtVo getGoodsOrderExtVo() {
        return goodsOrderExtVo;
    }

    public void setGoodsOrderExtVo(GoodsOrderExtVo goodsOrderExtVo) {
        this.goodsOrderExtVo = goodsOrderExtVo;
    }

    public void setShare(boolean isShare) {
        this.isShare = isShare;
    }

    public int getScore() {
        return score;
    }

    public void setScore(int score) {
        this.score = score;
    }


    public boolean isFollow() {
        return follow;
    }

    public void setFollow(boolean follow) {
        this.follow = follow;
    }

    public boolean isDisableComment() {
        return disableComment;
    }

    public void setDisableComment(boolean disableComment) {
        this.disableComment = disableComment;
    }

    public double getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(double totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getShip() {
        return ship;
    }

    public void setShip(String ship) {
        this.ship = ship;
    }

    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }
}
