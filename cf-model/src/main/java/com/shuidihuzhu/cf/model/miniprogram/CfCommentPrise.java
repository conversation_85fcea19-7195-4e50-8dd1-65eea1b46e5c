package com.shuidihuzhu.cf.model.miniprogram;

public class CfCommentPrise {
    private long id;
    private int topicId;
    private long userId;
    private long commentId;
    private int status;
    private int isDelete;

    public CfCommentPrise() {
    }

    public CfCommentPrise(int topicId,long userId, long commentId, int status) {
        this.topicId = topicId;
        this.userId = userId;
        this.commentId = commentId;
        this.status = status;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public int getTopicId() {
        return topicId;
    }

    public void setTopicId(int topicId) {
        this.topicId = topicId;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public long getCommentId() {
        return commentId;
    }

    public void setCommentId(long commentId) {
        this.commentId = commentId;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(int isDelete) {
        this.isDelete = isDelete;
    }

    @Override
    public String toString() {
        return "CfCommentPrise{" +
                "id=" + id +
                ", topicId=" + topicId +
                ", userId=" + userId +
                ", commentId=" + commentId +
                ", status=" + status +
                ", isDelete=" + isDelete +
                '}';
    }
}
