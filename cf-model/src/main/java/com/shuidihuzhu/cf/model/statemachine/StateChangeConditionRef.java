package com.shuidihuzhu.cf.model.statemachine;

import com.shuidihuzhu.common.web.model.AbstractModel;

/**
 * Created by <PERSON><PERSON> on 2017/9/12.
 */
public class StateChangeConditionRef extends AbstractModel {
	private static final long serialVersionUID = 1L;
	
	private int stateChangeId;
	private int conditionId;
	private int Conditionorder;

	public StateChangeConditionRef() {

	}

	public StateChangeConditionRef(int stateChangeId, int conditionId, int conditionorder) {
		this.stateChangeId = stateChangeId;
		this.conditionId = conditionId;
		Conditionorder = conditionorder;
	}

	public int getStateChangeId() {
		return stateChangeId;
	}

	public void setStateChangeId(int stateChangeId) {
		this.stateChangeId = stateChangeId;
	}

	public int getConditionId() {
		return conditionId;
	}

	public void setConditionId(int conditionId) {
		this.conditionId = conditionId;
	}

	public int getConditionorder() {
		return Conditionorder;
	}

	public void setConditionorder(int conditionorder) {
		Conditionorder = conditionorder;
	}
}
