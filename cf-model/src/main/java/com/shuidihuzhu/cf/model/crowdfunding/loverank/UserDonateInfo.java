package com.shuidihuzhu.cf.model.crowdfunding.loverank;

import groovy.transform.EqualsAndHashCode;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode
public class UserDonateInfo{

    @ApiModelProperty("昵称")
    private String nickName;
    @ApiModelProperty("头像")
    private String headImg;
    @ApiModelProperty("捐款金额")
    private int amount;
    @ApiModelProperty("转发次数")
    private int shareCount;
    @ApiModelProperty("点赞次数")
    private int likeCount;
    @ApiModelProperty("是否匿名")
    private boolean anonymous;
    @ApiModelProperty("加密userId")
    private String cipherUserId;
    @ApiModelProperty("userId 后端用 返回前置空")
    private long userId;
    @ApiModelProperty("是否点过赞")
    private boolean like = false;
}