package com.shuidihuzhu.cf.model.crowdfunding.material.credit;

import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.domain.material.credit.CrowdfundingAuthorPropertyDO;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfCreditSupplementVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by sven on 18/11/15.
 *
 * <AUTHOR>
 */
@ApiModel("案例相关的财产信息")
@Data
public class CreditSupplementModel {

    @ApiModelProperty("案例id")
    private String infoUuid;

    @ApiModelProperty(value = "是否有社保  -1,未知，用户没有填写，0，没有，1有",required = true)
    private Integer healthInsurance = -1;

    @ApiModelProperty(value = "是否有商业保险，参数同healthInsurance", required = true)
    private Integer commercialInsurance = -1;

    @ApiModelProperty("是否有低保")
    private Integer livingSecurity = -1;

    @ApiModelProperty("低保情况说明")
    private String livingSecurityText = "";

    @ApiModelProperty("是否有政府补助")
    private Integer govRelief = -1;

    @ApiModelProperty("政府补助情况说明")
    private String govReliefText = "";

    @ApiModelProperty("家庭年收入,单位为元")
    private Integer homeIncome = -1;

    @ApiModelProperty("家庭金融财产价值,单位为元")
    private Integer homeStock = -1;

    @ApiModelProperty(value = "车房相关信息",required = true)
    private List<CfCreditSupplementVo> creditSupplements = Lists.newArrayList();

    @ApiModelProperty("驳回理由相关")
    private Map<Integer, List<String>> rejectDetails;

    public void setPropertyDO(CrowdfundingAuthorPropertyDO crowdfundingAuthorPropertyDO){
        Preconditions.checkNotNull(crowdfundingAuthorPropertyDO);
        setCommercialInsurance(crowdfundingAuthorPropertyDO.getCommercialInsurance());
        setHealthInsurance(crowdfundingAuthorPropertyDO.getHealthInsurance());

        setGovRelief(crowdfundingAuthorPropertyDO.getGovRelief());
        setGovReliefText(crowdfundingAuthorPropertyDO.getGovReliefText());
        setLivingSecurity(crowdfundingAuthorPropertyDO.getLivingSecurity());
        setLivingSecurityText(crowdfundingAuthorPropertyDO.getLivingSecurityText());

        setHomeIncome(crowdfundingAuthorPropertyDO.getHomeIncome());
        setHomeStock(crowdfundingAuthorPropertyDO.getHomeStock());
    }
    
    public void setCrowdfundingAuthor(int healthInsurance, int commercialInsurance) {
        setHealthInsurance(healthInsurance);
        setCommercialInsurance(commercialInsurance);
    }
}
