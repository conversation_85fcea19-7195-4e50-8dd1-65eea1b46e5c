package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.UserIdentityType;
import com.shuidihuzhu.cf.model.crowdfunding.CfCreditSupplement;
import com.shuidihuzhu.cf.vo.v5.CfMaterialAuditListView;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
public class CrowdfundingInfoPatientVo extends CrowdfundingInfoParentVo {

	private String name;
	private UserIdentityType idType;
	private String idCard;
	private String idCardPhoto;
	private String onlyIdCardPhoto;//身份证单独照片
	private int healthInsurance;
	private int commercialInsurance;
	private List<CfCreditSupplement> properties;
	private Map<Integer, List<String>> rejectDetails;
	//
	private Map<Integer, Set<String>> rejects;
	//
	private List<CfMaterialAuditListView.ModifySuggest> suggests;

	/**
	 * 该信息状态，参见 {@See CrowdfundingInfoStatusEnum}
	 */
	private int status;

	/**
	 * 关系，参见 {@See CrowdfundingRelationType}
	 */
	private UserRelTypeEnum relation = UserRelTypeEnum.NOTHING;


	/**
	 * 人脸识别结果  0 默认  10 失败   20 成功
	 */
	private int faceIdResult;

	/**
	 * 人脸识别图片
	 */
	private String faceVideo;

	/**
	 * 没有身份证合照原因
	 */
	private int noIdCardPhotoReason;

	/**
	 * 上传其他身份证件 0：否; 1：是
	 */
	private int otherIdPhoto;


	public boolean faceIdResultPass(){
		return getFaceIdResult() == 20;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}

}
