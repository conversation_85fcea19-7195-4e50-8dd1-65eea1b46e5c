package com.shuidihuzhu.cf.model.contribute;

import com.shuidihuzhu.cf.enums.combine.CombineCommonEnum;
import com.shuidihuzhu.common.web.util.IdGenUtil;
import lombok.Data;

@Data
public class CfCombineOrderManage {

    private String parentPayUid;
    private String parentThirdPayUid;

    private String subPayUid;
    private String subThirdPayUid;
    /**
     * @see CombineCommonEnum.CombineOrderType
     */
    private int orderType;

    /**
     * @see CombineCommonEnum.CombineOrderBizType
     */
    private int bizType;

    /** 多案例合并支付记录 **/
    private int caseId;

    private long orderId;
    // 是否主订单
    private int main;



    public static String buildParentUid() {
        return IdGenUtil.tradeNo();
    }

    public static String buildSubPayUid() {
        return IdGenUtil.tradeNo();
    }
}
