package com.shuidihuzhu.cf.model.crowdfunding;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @time 2019/2/19 下午8:27
 * @desc
 */
@Data
@ApiModel("用户自身基本信息")
public class CfUserBaseInfo {
    @ApiModelProperty("访问用户昵称")
    private String nickName;
    @ApiModelProperty("筹款人昵称")
    private String crowdfundingNickName;
    @ApiModelProperty("转发人昵称")
    private String shareNickName;
    @ApiModelProperty("访问用户头像url")
    private String imgUrl;
    @ApiModelProperty("筹款人头像url")
    private String crowdfundingImgUrl;
    @ApiModelProperty("转发人头像url")
    private String shareImgUrl;
}
