package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/12/9  3:20 下午
 */
@Data
public class CfMsgTestRecord {
    /**
     * 鹰眼组别
     */
    private String eyeGroup;
    /**
     * 捐款人userId
     */
    private long donorUser;
    /**
     * 捐款人捐款的案例
     */
    private long donorCase;
    /**
     * 捐款人的昵称不为空的一度好友userId
     */
    private long donorFirstFriendUser;
    /**
     * 捐款人与一度好友的亲密度
     */
    private int bothIntimacy;
    /**
     * 一度好友是否捐款过当前案例 0:没有，1:有
     */
    private int firstFriendHelp;
    /**
     * 一度好友是否转发过当前案例 0:没有，1:有
     */
    private int firstFriendShare;
    /**
     * 一度好友30天内是否有捐款过非当前案例 0:没有，1:有
     */
    private int firstFriendElseHelp;
}
