package com.shuidihuzhu.cf.model.crowdfunding;

import java.util.Date;

/**
 * Created by wangsf on 17/7/31.
 */
public class CfOrderForWithdrawedCase {

	private long id;
	private long userId;
	private int orderId;
	private int infoId;
	private int anonymous;
	private Date orderTime;

	public CfOrderForWithdrawedCase() {

	}

	public CfOrderForWithdrawedCase(long userId, int orderId, int infoId) {
		this.userId = userId;
		this.orderId = orderId;
		this.infoId = infoId;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}

	public int getOrderId() {
		return orderId;
	}

	public void setOrderId(int orderId) {
		this.orderId = orderId;
	}

	public int getInfoId() {
		return infoId;
	}

	public void setInfoId(int infoId) {
		this.infoId = infoId;
	}

	public int getAnonymous() {
		return anonymous;
	}

	public void setAnonymous(int anonymous) {
		this.anonymous = anonymous;
	}

	public Date getOrderTime() {
		return orderTime;
	}

	public void setOrderTime(Date orderTime) {
		this.orderTime = orderTime;
	}
}
