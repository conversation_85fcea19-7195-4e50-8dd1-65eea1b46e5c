package com.shuidihuzhu.cf.model.crowdfunding;

import java.util.Date;

/**
 * Author: <PERSON>
 * Date: 2017/2/16 14:23
 */
public class CfAdviserService {

	private Integer id;
	private String openId;
	private boolean signNearTime;
	private Date dateCreated;
	private Date lastModified;
	private String mobile;
	private String lastEventKey;
	private int wxMpType;

	private long clientIp;

	private String primaryChannel;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getOpenId() {
		return openId;
	}

	public void setOpenId(String openId) {
		this.openId = openId;
	}

	public boolean isSignNearTime() {
		return signNearTime;
	}

	public void setSignNearTime(boolean signNearTime) {
		this.signNearTime = signNearTime;
	}

	public Date getDateCreated() {
		return dateCreated;
	}

	public void setDateCreated(Date dateCreated) {
		this.dateCreated = dateCreated;
	}

	public Date getLastModified() {
		return lastModified;
	}

	public void setLastModified(Date lastModified) {
		this.lastModified = lastModified;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String moblie) {
		this.mobile = moblie;
	}

	public String getLastEventKey() {
		return lastEventKey;
	}

	public void setLastEventKey(String lastEventKey) {
		this.lastEventKey = lastEventKey;
	}

	public Date getLastFollowTime() {
		return lastFollowTime;
	}

	public void setLastFollowTime(Date lastFollowTime) {
		this.lastFollowTime = lastFollowTime;
	}

	public int getWxMpType() {
		return wxMpType;
	}

	public void setWxMpType(int wxMpType) {
		this.wxMpType = wxMpType;
	}

	public CfAdviserService() {

	}

	private Date lastFollowTime;
	private long userId;
	private String channel;
	private String helpType;

	public String getChannel() {
		return channel;
	}

	public void setChannel(String channel) {
		this.channel = channel;
	}

	public String getHelpType() {
		return helpType;
	}

	public void setHelpType(String helpType) {
		this.helpType = helpType;
	}

	public long getClientIp() {
		return clientIp;
	}

	public void setClientIp(long clientIp) {
		this.clientIp = clientIp;
	}

	public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}

	public String getPrimaryChannel() {
		return primaryChannel;
	}

	public void setPrimaryChannel(String primaryChannel) {
		this.primaryChannel = primaryChannel;
	}
}
