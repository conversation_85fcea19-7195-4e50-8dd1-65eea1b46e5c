package com.shuidihuzhu.cf.model.clewtrack;

import lombok.Getter;

/**
 * @author: wanghui
 * @create: 2019/6/21 2:59 PM
 */
public enum ScanQRCodeNoticeEnum {
    FOLLOW_OFFICIAL_ACCOUNT("followOfficialAccount","用户关注水滴筹等公众号"),
    USER_SCAN_AFTER_LAUNCH("userScanAfterLaunch","用户扫描筹款顾问二维码后发起"),
    ;
    @Getter
    private String type;
    @Getter
    private String desc;

    ScanQRCodeNoticeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }
    public static ScanQRCodeNoticeEnum parse(String type){
        for (ScanQRCodeNoticeEnum e : ScanQRCodeNoticeEnum.values()) {
            if (e.type.equals(type)) {
                return e;
            }
        }
        return null;
    }
}
