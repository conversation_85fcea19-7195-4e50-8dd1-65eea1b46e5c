package com.shuidihuzhu.cf.model.crowdfunding.vo;

import lombok.Data;

import java.util.List;

/**
 * @author: subing
 * @create: 2020-04-18
 **/
@Data
public class CfReportAddTrustDisposeVo {
    /**
     * 分类id
     */
    private long id;
    private String actionClassify;
    private List<String> disposeAction;
    private List<CfReportDisposeActionInfo> disposeActionInfos;

    @Data
    public class CfReportDisposeActionInfo {
        /**
         * id
         */
        private long id;
        /**
         * 处理动作
         */
        private String disposeAction;
        /**
         * 是否支持待录入
         */
        private boolean isHelp;
        /**
         * 有无模板
         */
        private boolean hasTemplate;
    }
}
