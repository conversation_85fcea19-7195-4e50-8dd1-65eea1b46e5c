package com.shuidihuzhu.cf.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.http.util.VersionInfo;

import java.util.List;

/**
 * @Author：liuchangjun
 * @Date：2021/9/28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserSatisfactionInformationModel {

    private String informationName;

    private String description;

    private List<VersionInfo> versionInfos;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public
    class VersionInfo{

        private Integer testType;

        private List<Integer> results;
    }

}
