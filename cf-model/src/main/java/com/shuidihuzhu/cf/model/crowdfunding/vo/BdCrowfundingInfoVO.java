package com.shuidihuzhu.cf.model.crowdfunding.vo;

import lombok.Data;

@Data
public class BdCrowfundingInfoVO {
    /**
     * 案例infoId
     */
    private String infoUuid;
    /**
     * 案例标题
     */
    private String cfTitle;
    /**
     * 跳转到案例详情页的url
     */
    private String cfInfoDetailUrl;
    /**
     * 案例状态描述
     */
    private String cfBaseStatusDesc;
    /**
     * 案例状态
     */
    private Integer cfBaseStatus;
    /**
     * 发起电话
     */
    private String cfPhone;
    /**
     * 材料审核状态码
     */
    private Integer cfMaterialApplyStatus;
    /**
     * 材料审核状态描述
     */
    private String cfMaterialApplyStatusDesc;
    /**
     * 已筹金额(单位元)
     */
    private Float amount;
    /**
     * 案例发起时间
     */
    private String cfCreateTime;
    /**
     * 关注状态,0: bd未关注案例,1:bd 已关注案例
     */
    private Integer followStatus;
    /**
     * 患者姓名
     */
    private String cfPatientName;
    /**
     * 案例发起方式：BD引导发起，兼职发起，拎包发起，用户自己发起
     */
    private String caseMethod;
    /**
     * 案例来源渠道：扫楼、转介绍、物料案例、其他
     */
    private String caseSource;
    /**
     * 该用户是否首次众筹：是、否、不确定
     */
    private String firstRaise;
    /**
     * 案例负责人
     */
    private String volunteerName;
    /**
     * 医院名称
     */
    private String hospitalName;
    /**
     * 科室名称
     */
    private String departmentName;
    /**
     * 疾病名称
     */
    private String diseaseName;

}
