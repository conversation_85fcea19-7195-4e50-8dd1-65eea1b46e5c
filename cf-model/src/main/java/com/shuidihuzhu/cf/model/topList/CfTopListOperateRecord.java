package com.shuidihuzhu.cf.model.topList;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

@Data
public class CfTopListOperateRecord {

    private long id;

    private int caseId;

    private long userId;

    /** @see CfTopListConstant
     *
     */
    private int operateType;

    private String operateDetail;

    private String operateTime;

    private Date createTime;

    public CfTopListSummaryUserView.PrizeResult parsePrizeResult() {
        if (StringUtils.isBlank(operateDetail)) {
            return null;
        }

        return JSON.parseObject(operateDetail, CfTopListSummaryUserView.PrizeResult.class);
    }
}
