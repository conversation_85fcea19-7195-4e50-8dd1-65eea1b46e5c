package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.cf.enums.SplitFlowPay;
import lombok.ToString;

import java.util.Date;

@ToString
public class CfPaySplitFlowRecord {

	private long id;
	private String uuid;
	private long userId;
	private String infoUuid;
	private int authType;
	private String appId;
	private int userThirdType;
	private int rule;
	private int isDelete;
	private Date createTime;
	private Date updateTime;
	private String mobileType;
	private String province;
	private String city;

	public CfPaySplitFlowRecord() {
	}

	public CfPaySplitFlowRecord(String uuid, long userId, String infoUuid, int authType, String appId, int userThirdType,
								SplitFlowPay.Rule rule, String mobileType, String province, String city) {
		this.uuid = uuid;
		this.userId = userId;
		this.infoUuid = infoUuid;
		this.authType = authType;
		this.appId = appId;
		this.userThirdType = userThirdType;
		this.rule = rule.getCode();
		this.mobileType = mobileType;
		this.province = province;
		this.city = city;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getUuid() {
		return uuid;
	}

	public void setUuid(String uuid) {
		this.uuid = uuid;
	}

	public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}

	public String getInfoUuid() {
		return infoUuid;
	}

	public void setInfoUuid(String infoUuid) {
		this.infoUuid = infoUuid;
	}

	public int getAuthType() {
		return authType;
	}

	public void setAuthType(int authType) {
		this.authType = authType;
	}

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	public int getUserThirdType() {
		return userThirdType;
	}

	public void setUserThirdType(int userThirdType) {
		this.userThirdType = userThirdType;
	}

	public int getIsDelete() {
		return isDelete;
	}

	public void setIsDelete(int isDelete) {
		this.isDelete = isDelete;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public int getRule() {
		return rule;
	}

	public void setRule(int rule) {
		this.rule = rule;
	}

	public String getMobileType() {
		return mobileType;
	}

	public void setMobileType(String mobileType) {
		this.mobileType = mobileType;
	}

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

}
