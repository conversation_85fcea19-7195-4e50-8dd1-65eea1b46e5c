package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.Data;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 * @time 2018/12/10 下午2:11
 * @desc
 */
@Data
public class CfTemplateBaseInfoConvert {
    private float amount;
    private String name;
    private int relationship;
    private String authorName;
    private String diseaseName;
    private String hospitalName;
    //非必要词
    private Integer age;
    private String hometown;
    private String disasterDay;
    private Long cost;
    private int fp;

    //患者家庭成员:AuthorFamilyMemberEnum
    //例如: 1,2,5 用英文逗号分隔
    //选填:如果没填家庭成员，传null或空字符串
    private String familyMember;

    //患者职业:AuthorOccupationEnum
    //选填:如果没填患者职业，则为0
    private int authorOccupation;

    //是否有医疗产生的欠款:有欠款传金额,单位元
    //选填:如果没填医疗欠款,传0
    private float medicalArrears;

    private int source;

    // 新增适用场景 0 表示C端 1表示微信1v1金牌服务
    private int useScene;

    /**
     * 确诊前症状
     */
    private String symptomsBeforeDiagnosis;
    /**
     * 初诊疾病
     */
    private String firstDisease;
    /**
     * 初诊医院
     */
    private String firstHospital;
    /**
     * 后续花费
     */
    private int followCost;
    /**
     * 患者家庭欠款
     */
    private int homeDebt;
    /**
     * 目前治疗方式
     */
    private String nowTreatment;
    /**
     * 后期治疗方式
     */
    private String laterTreatment;

    /**
     * 模版id
     */
    private Integer templateId;


    public static CfTemplateBaseInfo convertToBaseInfo(CfTemplateBaseInfoConvert source) {
        if (source == null) {
            return null;
        }

        CfTemplateBaseInfo target = new CfTemplateBaseInfo();
        BeanUtils.copyProperties(source, target, "amount");
        target.setAmount(Math.round(source.getAmount()));

        return target;
    }

}
