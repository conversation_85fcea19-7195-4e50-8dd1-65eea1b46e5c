package com.shuidihuzhu.cf.model.crowdfunding.app.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.shuidihuzhu.cf.model.crowdfunding.app.AppNews;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.util.Objects;

/**
 * Created by wangsf on 18/2/2.
 */
public class AppNewsVo {

    private String title = "";
    private String description = "";
    private String createTime = "";
    private String picUrl = "";
    private String url = "";
    @JsonIgnore
    private Integer sort;

    public AppNewsVo() {
    }

    public AppNewsVo(AppNews appNews) {
        if (appNews != null) {
            this.title = appNews.getTitle();
            this.description = appNews.getDescription();
            this.createTime = DateFormatUtils.format(appNews.getCreateTime().getTime(), "yyyy-MM-dd");
            this.picUrl = appNews.getPicUrl();
            this.url = appNews.getUrl();
            this.sort = appNews.getSort();
        }
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getPicUrl() {
        return picUrl;
    }

    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AppNewsVo appNewsVo = (AppNewsVo) o;
        return Objects.equals(title, appNewsVo.title) &&
                Objects.equals(description, appNewsVo.description) &&
                Objects.equals(createTime, appNewsVo.createTime) &&
                Objects.equals(picUrl, appNewsVo.picUrl) &&
                Objects.equals(url, appNewsVo.url) &&
                Objects.equals(sort, appNewsVo.sort);
    }

    @Override
    public int hashCode() {
        return Objects.hash(title, description, createTime, picUrl, url, sort);
    }
}
