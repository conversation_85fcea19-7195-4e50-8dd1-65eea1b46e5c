package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.UserIdentityType;
import lombok.Data;

/**
 *
 * 案例发起人信息
 *
 * Created by wangsf on 18/11/15.
 */
@Data
public class CfSuggestPatientInfoVo {

	/**
	 * 前置审核状态 {@See FirstApproveStatusEnum}
	 */
	private int firstApproveStatus = FirstApproveStatusEnum.DEFAULT.getCode();

	/**
	 * 关系
	 */
	private UserRelTypeEnum relationType;

	/**
	 * 发起人的信息
	 */
	private String raiserIdCard;

	/**
	 * 发起人真实姓名
	 */
	private String raiserRealName;

	/**
	 * 发起人身份证是否校验过
	 */
	private boolean raiserIdCardVerified;

	/**
	 * 发起人id类型
	 */
	private UserIdentityType raiserIdType;

	/**
	 * 患者证件类型
	 */
	private UserIdentityType patientIdType;

	/**
	 * 患者证件号
	 */
	private String patientIdCard;

	/**
	 * 患者真实姓名
	 */
	private String patientRealName;

	/**
	 * 患者证件是否验证过
	 */
	private boolean patientIdCardVerified;

	/**
	 * 新增-患者证件类型，未避免原接口调用，兼容字段
	 */
	private UserIdentityType patientIdTypeAuthor;

	/**
	 * 新增-患者证件号，未避免原接口调用，兼容字段
	 */
	private String patientIdCardAuthor;

	/**
	 * 新增-患者真实姓名，未避免原接口调用，兼容字段
	 */
	private String patientRealNameAuthor;

	/**
	 * 新增-患者证件是否验证过，未避免原接口调用，兼容字段
	 */
	private boolean patientIdCardVerifiedAuthor;
}
