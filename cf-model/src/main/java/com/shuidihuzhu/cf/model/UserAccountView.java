package com.shuidihuzhu.cf.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.shuidihuzhu.account.compatible.model.HasUserInfo;
import com.shuidihuzhu.common.web.model.AbstractModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * Created by chao on 16/11/20.
 */
@Data
public class UserAccountView {

    @JsonIgnore
	private long userId = 0;

	private String nickname = "";

	private String headImgUrl = "";

	private Date createTime = new Date();

	@ApiModelProperty("pr详情页中评论中筹款人昵称使用求助人姓名")
	private boolean prFlag;
}

