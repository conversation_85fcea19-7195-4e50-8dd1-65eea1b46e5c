package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import lombok.Data;

import java.sql.Timestamp;

@Data
public class CrowdfundingInfoVo extends CrowdfundingInfo {

	private Integer operation;
	private Timestamp auditCommitTime;
	private Integer refuseCount;
	private Integer userRefuseCount;
	private Integer callCount;
	private Integer callStatus;
	private Integer operatorId;
	private String cryptoRegisterMobile;

	private boolean isCaseRepeat;
	private boolean isSecondRaise;
}
