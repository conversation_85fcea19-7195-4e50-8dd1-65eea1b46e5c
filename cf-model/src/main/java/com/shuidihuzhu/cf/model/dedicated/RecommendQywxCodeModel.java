package com.shuidihuzhu.cf.model.dedicated;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: fengxuan
 * @create 2022-05-27 17:17
 **/
@Data
public class RecommendQywxCodeModel {

    @ApiModelProperty("0:正常返回二维码;1:已生成线索,但未分配,2分钟提醒;2:没有生成线索或者生成无效线索,2小时提醒")
    private int status;

    @ApiModelProperty("企业微信二维码")
    private String qywxCode;

    @ApiModelProperty("微信号")
    private String wxNo;
}
