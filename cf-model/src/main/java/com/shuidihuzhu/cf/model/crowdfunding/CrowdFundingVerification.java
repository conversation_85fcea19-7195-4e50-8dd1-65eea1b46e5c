package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.common.web.model.AbstractModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/10/18.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CrowdFundingVerification extends AbstractModel {

    private Integer id;

    /***
     * 筹款案例ID
     */
    private String crowdFundingInfoId;

    /***
     * 受助人
     */
    private long patientUserId;

    /***
     * 证实人
     */
    private long verifyUserId;

    /***
     * 证实人 openId
     */
    private String openId;

    /***
     * 受助人, 证实人的关系
     */
    private Integer relationShip;

    private int type;

    private String userName;

    private String description;

    private Integer valid;

    private String selfTag;

    /**
     * 证实人所在省份code
     */
    private String provinceCode;

    /**
     * 证实人所在医院名称
     */
    private String hospitalName;

    /**
     * 证实人图片列表
     */
    private String medicalImageList;

    /***
     * 创建时间
     */
    private Timestamp createTime;

    /***
     * 创建时间
     */
    private Timestamp operateTime;

	private String mobile;
	private String encryptMobile;

    private String shareSourceId;

    /**
     * 志愿者标签
     */
    private boolean showLabel;

    /**
     * 真实性、按关系排序的规则
     */
    private int relationshipSort;
}
