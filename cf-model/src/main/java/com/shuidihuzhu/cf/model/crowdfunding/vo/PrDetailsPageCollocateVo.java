package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.shuidihuzhu.client.cf.admin.model.PrMediaCoverageExt;
import com.shuidihuzhu.client.cf.admin.model.PrWaterDroppower;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class PrDetailsPageCollocateVo {
    private long id;

    @ApiModelProperty("案例id")
    private int caseId;

    @ApiModelProperty("案例标题")
    private String caseTitle;

    @ApiModelProperty("求助人头像")
    private String handImgUrl;

    @ApiModelProperty("求助人姓名")
    private String helpName;

    @ApiModelProperty("求助人故事")
    private String helpStory;

    @ApiModelProperty("品牌模式 0是普通模式,1是品牌模式")
    private int brandType;

    @ApiModelProperty("媒体播报")
    private PrMediaCoverageExt prMediaCoverageExt;

    @ApiModelProperty("水滴助力")
    private PrWaterDroppower prWaterDroppower;

    @ApiModelProperty("爱心首页帮筹")
    private long loveHomeAmount;

    @ApiModelProperty("爱心补贴")
    private long loveSubsidyAmount;

    @ApiModelProperty("是否有参加补贴活动")
    private boolean onSubsidy;

}
