package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.Data;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/4/3.
 */
@Data
public class PatientQuestionnaire {
    private int id;
    private String identity;
    private String relation;
    private String illness;
    private String forTreatment;
    private String forSurgery;
    private String surgeryName;
    private String surgeryStatus;
    private String isWaitZygosity;
    private String zygosityObject;
    private String surgeryNameTwo;
    private String surgeryStatusTwo;
    private String chemotherapyStage;
    private String dialysisFrequency;
    private String radiotherapyStatus;
    private String useDrug;
    private String nowStatus;
    private String observeSite;
    private String notOptimistic;
    private String afterRecovery;
    private String nowStatusReplenish;
    private String elseCureReplenish;
    private String surgeryNameThree;
    private String cureResult;
    private String isExceptionalCase;
    private String anAccident;
    private String mentalState;
    private String memoryStatus;
    private String consciousnessStatus;
    private String obstacle;
    private String emotion;
    private String sequela;
    private String futurePlan;
    private String cureWay;
    private String futureSurgeryName;
    private String needChemotherapyCount;
    private String drugTherapy;
    private String text;
    private String wxName;
    private String gender;
    private String state;
    private String province;
    private String city;
    private String openId;
}
