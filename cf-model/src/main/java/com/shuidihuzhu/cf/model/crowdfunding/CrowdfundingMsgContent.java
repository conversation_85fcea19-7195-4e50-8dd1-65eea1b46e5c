package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingMsgContentEnum;

import java.sql.Timestamp;
public class CrowdfundingMsgContent {

	private Integer id;
	private String key;
	private String content;
	private Timestamp dateCreated;
	private Timestamp lastModified;
	private String name;
	private int type= CrowdfundingMsgContentEnum.Type.OPERATOR.getCode();


	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
    public int getType() {
        return type;
    }
    public void setType(int type) {
        this.type = type;
    }
    public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public Timestamp getDateCreated() {
		return dateCreated;
	}

	public void setDateCreated(Timestamp dateCreated) {
		this.dateCreated = dateCreated;
	}

	public Timestamp getLastModified() {
		return lastModified;
	}

	public void setLastModified(Timestamp lastModified) {
		this.lastModified = lastModified;
	}

}
