package com.shuidihuzhu.cf.model.crowdfunding;

import org.apache.commons.lang3.builder.ToStringBuilder;

/**
 * Author: <PERSON>
 * Date: 2017/4/1 14:59
 */
public class CfInfoStat {
	public CfInfoStat() {
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getDonationCount() {
		return donationCount;
	}

	public void setDonationCount(Integer donationCount) {
		this.donationCount = donationCount;
	}

	public Integer getVerifyUserCount() {
		return verifyUserCount;
	}

	public void setVerifyUserCount(Integer verifyUserCount) {
		this.verifyUserCount = verifyUserCount;
	}

	public Integer getCommentCount() {
		return commentCount;
	}

	public void setCommentCount(Integer commentCount) {
		this.commentCount = commentCount;
	}

	public Integer getAmount() {
		return amount;
	}

	public void setAmount(Integer amount) {
		this.amount = amount;
	}

	private Integer id;

	public Integer getShareCount() {
		return shareCount;
	}

	public void setShareCount(Integer shareCount) {
		this.shareCount = shareCount;
	}

	public Integer getRefundCount() {
		return refundCount;
	}

	public void setRefundCount(Integer refundCount) {
		this.refundCount = refundCount;
	}

	public Integer getDonatorCount() {
		return donatorCount;
	}

	public void setDonatorCount(Integer donatorCount) {
		this.donatorCount = donatorCount;
	}

	private Integer shareCount;
	private Integer donationCount;
	private Integer verifyUserCount;
	private Integer commentCount;
	private Integer amount;
	private Integer refundCount;
	private Integer donatorCount;


	public Integer getVerifyFriendCount() {
		return verifyFriendCount;
	}

	public void setVerifyFriendCount(Integer verifyFriendCount) {
		this.verifyFriendCount = verifyFriendCount;
	}

	public Integer getVerifyHospitalCount() {
		return verifyHospitalCount;
	}

	public void setVerifyHospitalCount(Integer verifyHospitalCount) {
		this.verifyHospitalCount = verifyHospitalCount;
	}

	private Integer verifyFriendCount;
	private Integer verifyHospitalCount;
	private Integer blessingCount;

	public Integer getBlessingCount() {
		return blessingCount;
	}

	public CfInfoStat setBlessingCount(Integer blessingCount) {
		this.blessingCount = blessingCount;
		return this;
	}

	public static CfInfoStat getDefault(int id) {
		CfInfoStat cfInfoStat = new CfInfoStat();
		cfInfoStat.setAmount(0);
		cfInfoStat.setCommentCount(0);
		cfInfoStat.setDonationCount(0);
		cfInfoStat.setShareCount(0);
		cfInfoStat.setVerifyUserCount(0);
		cfInfoStat.setId(id);
		cfInfoStat.setVerifyFriendCount(0);
		cfInfoStat.setVerifyHospitalCount(0);
		cfInfoStat.setBlessingCount(0);
		return cfInfoStat;
	}

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}
}
