package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.InitialAuditItem;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingAttachmentVo;
import com.shuidihuzhu.cf.vo.CrowdfundingAuthorVoV2;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 众筹信息视图类V2版本
 * 相比V1版本，删除了敏感字段：payeeBankCard、payeeBankName、payeeIdCard、payeeMobile、payeeName
 * 并使用CrowdfundingAuthorVoV2代替CrowdfundingAuthorVo
 */
@Data
public class CrowdfundingInfoViewV2 {

    private String infoId;

    private String userHeadImgUrl;

    private String userNickName;

    private String relation;

    private CrowdfundingRelationType relationType;

    private CrowdfundingChannelTypeEnum channelType;

    // 删除了敏感字段：payeeBankCard、payeeBankName、payeeIdCard、payeeMobile、payeeName

    private String applicantName;

    private String title;

    private String use;

    private String titleImg;

    private String content;

    private String from;

    private double targetAmount;

    private double amount;

    private int donationCount;

    private Date createTime;

    private Date beginTime;

    private Date endTime;

    private int status;

    private int remainedDays;

    private Boolean rechargeAble;

    private CrowdfundingAuthorVoV2 crowdfundingAuthor;

    private List<CrowdfundingAttachmentVo> crowdfundingAttachmentList;

    private CrowdfundingTreatment crowdfundingTreatment;

    private int shareCount; //总转发分享次数

    /**
     * 筹款数据状态
     * 参见 {@link CrowdfundingInfoDataStatusEnum}
     */
    private int dataStatus;

    private FirstApproveStatusEnum firstApproveStatus;

    private boolean hasFinished;

    /**
     * 筹款状态
     * 参见 {@link CrowdfundingInfoOldStatusEnum}
     */
    private int infoStatus;
    
    /**
     * 筹款类型
     * 参见 {@link CrowdfundingType}
     */
    private Integer type;

    /**
     * 拒绝状态
     * {@link CFRefuseType1StatusEnum}
     */
    private int rejectType;

    private int originatorType;

    private boolean baseInfoRejected;

    private boolean extInfoRejected;

    private int displayStatus;

    @ApiModelProperty("筹款材料数据验证是否通过")
    private boolean raiseInfoRiskPassed;

    @ApiModelProperty("资金信息情况补充状态")
    private int infoCapitalCompensationStatus;

    @ApiModelProperty("是否允许发起人主动结束案例")
    private boolean raiserFinish;

    /**
     * {@link PropertyInsuranceCaseType}
     */
    @ApiModelProperty("案例材料的类型")
    private int caseRaiseMaterialType;

    @ApiModelProperty("单独流程的增信材料的状态")
    private InitialAuditItem.MaterialStatus materialStatus;

    @ApiModelProperty("是否可以提现")
    private boolean canCash;

    @ApiModelProperty("我的筹款-案例的状态标签。")
    private int caseTagStatus;

    @ApiModelProperty("筹款人管理分流")
    private boolean fundraiserManagementShunt;

    @ApiModelProperty("风险案例标识 true：风险案例")
    private boolean caseEndFlag;
} 