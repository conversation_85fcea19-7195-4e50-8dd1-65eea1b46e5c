package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingSeekHelpInfoFragmentVo;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;


/**
 * Created by ch<PERSON> on 2017/8/6.
 */
public class CfStatSeekHelpInfoPoint {
	private long id;
	private String selfTag;
	private int amount;
	private String title;
	private String fromPath;
	private String toPath;
	private int urlNo;
	private int type;
	private Date opTime;
	private String infoUuid;
	private String openId;
	private String channel;
	private String grayMode;
	private String mobile;
	private String appUniqueId;
	private String appVersion;
	private String appSubVersion;
	private long userId;
	private String patientIntro;
	private String disease;
	private String income;
	private String diagnoseIntro;
	private String cost;
	private String wantToSay;
	private int newStage;
	private String defaultContentType;
	private String content;
	private int contentType;
	private Date createTime;
	private Date updateTime;

	public void cutTooLongContent() {
		int maxLength = CrowdfundingSeekHelpInfoFragmentVo.MAX_LENGTH;
		int maxLengthPatientIntro = CrowdfundingSeekHelpInfoFragmentVo.MAX_LENGTH_PATIENT_INTRO;
		if (!StringUtils.isEmpty(patientIntro) && patientIntro.length() > maxLengthPatientIntro) {
			this.setPatientIntro(this.patientIntro.substring(0, maxLengthPatientIntro));
		}
		if (!StringUtils.isEmpty(disease) && disease.length() > maxLengthPatientIntro) {
			this.setDisease(this.disease.substring(0, maxLengthPatientIntro));
		}
		if (!StringUtils.isEmpty(diagnoseIntro) && diagnoseIntro.length() > maxLength) {
			this.setDiagnoseIntro(this.diagnoseIntro.substring(0, maxLength));
		}
		if (!StringUtils.isEmpty(wantToSay) && wantToSay.length() > maxLength) {
			this.setWantToSay(this.wantToSay.substring(0, maxLength));
		}
	}

	public int getContentType() {
		return contentType;
	}

	public void setContentType(int contentType) {
		this.contentType = contentType;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public int getNewStage() {
		return newStage;
	}

	public void setNewStage(int newStage) {
		this.newStage = newStage;
	}

	public String getDefaultContentType() {
		return defaultContentType;
	}

	public void setDefaultContentType(String defaultContentType) {
		this.defaultContentType = defaultContentType;
	}

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getSelfTag() {
		return selfTag;
	}

	public void setSelfTag(String selfTag) {
		this.selfTag = selfTag;
	}

	public int getAmount() {
		return amount;
	}

	public void setAmount(int amount) {
		this.amount = amount;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getFromPath() {
		return fromPath;
	}

	public void setFromPath(String fromPath) {
		this.fromPath = fromPath;
	}

	public String getToPath() {
		return toPath;
	}

	public void setToPath(String toPath) {
		this.toPath = toPath;
	}

	public int getUrlNo() {
		return urlNo;
	}

	public void setUrlNo(int urlNo) {
		this.urlNo = urlNo;
	}

	public Date getOpTime() {
		return opTime;
	}

	public void setOpTime(Date opTime) {
		this.opTime = opTime;
	}

	public String getInfoUuid() {
		return infoUuid;
	}

	public void setInfoUuid(String infoUuid) {
		this.infoUuid = infoUuid;
	}

	public String getOpenId() {
		return openId;
	}

	public void setOpenId(String openId) {
		this.openId = openId;
	}

	public String getChannel() {
		return channel;
	}

	public void setChannel(String channel) {
		this.channel = channel;
	}

	public String getGrayMode() {
		return grayMode;
	}

	public void setGrayMode(String grayMode) {
		this.grayMode = grayMode;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getAppUniqueId() {
		return appUniqueId;
	}

	public void setAppUniqueId(String appUniqueId) {
		this.appUniqueId = appUniqueId;
	}

	public String getAppVersion() {
		return appVersion;
	}

	public void setAppVersion(String appVersion) {
		this.appVersion = appVersion;
	}

	public String getAppSubVersion() {
		return appSubVersion;
	}

	public void setAppSubVersion(String appSubVersion) {
		this.appSubVersion = appSubVersion;
	}

	public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}

	public String getPatientIntro() {
		return patientIntro;
	}

	public void setPatientIntro(String patientIntro) {
		this.patientIntro = patientIntro;
	}

	public String getDisease() {
		return disease;
	}

	public void setDisease(String disease) {
		this.disease = disease;
	}

	public String getIncome() {
		return income;
	}

	public void setIncome(String income) {
		this.income = income;
	}

	public String getDiagnoseIntro() {
		return diagnoseIntro;
	}

	public void setDiagnoseIntro(String diagnoseIntro) {
		this.diagnoseIntro = diagnoseIntro;
	}

	public String getCost() {
		return cost;
	}

	public void setCost(String cost) {
		this.cost = cost;
	}

	public String getWantToSay() {
		return wantToSay;
	}

	public void setWantToSay(String wantToSay) {
		this.wantToSay = wantToSay;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
}
