package com.shuidihuzhu.cf.model.crowdfunding.message;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

@Data
@ApiModel
public class CfShareRecord implements Serializable {

    @ApiModelProperty("案例id")
    private long caseId;

    @ApiModelProperty("案例分享次数")
    private int shareCount;

    @ApiModelProperty("案例结束时间")
    private Timestamp finishTime;

    @ApiModelProperty("案例开始时间")
    private Timestamp caseCreateTime;

    @ApiModelProperty("分享用户id")
    private long userId;


}
