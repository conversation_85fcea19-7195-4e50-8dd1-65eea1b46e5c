package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * Author: <PERSON>
 * Date: 2017/4/1 16:44
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class CfInfoShareRecord {
	//0：未知，1：朋友圈，2：朋友，3:QQ,4:QQ空间
	private int toWxSource;
	private long id;
	private long userId;
	private long userSourceId;
	private String uuid = "";
	private int infoId;
	private int wxSource;
	private String source = "";
	private int score;
	private String channel = "";
	private String shareId = "";
	private String shareSourceId = "";
	private Date dateCreated;
	/**
	 * 支付id
	 */
	private String tradeNo;
	/**
	 * 发起分享的页面路径
	 */
	private String pathName;
}
