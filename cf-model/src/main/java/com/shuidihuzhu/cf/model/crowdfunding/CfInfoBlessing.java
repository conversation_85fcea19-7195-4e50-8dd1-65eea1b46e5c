package com.shuidihuzhu.cf.model.crowdfunding;

import java.util.Date;

public class CfInfoBlessing {
    private Integer id;

    private String selfTag;

    private long userId;

    private String infoUuid;

    private Boolean blessing;

    private Boolean valid;

    private Date createTime;

    private Date lastModified;
    private int score;

    public CfInfoBlessing(Integer id, String selfTag, Long userId, String infoUuid, Boolean blessing, Boolean valid, Date createTime, Date lastModified) {
        this.id = id;
        this.selfTag = selfTag;
        this.userId = userId;
        this.infoUuid = infoUuid;
        this.blessing = blessing;
        this.valid = valid;
        this.createTime = createTime;
        this.lastModified = lastModified;
    }

    public CfInfoBlessing() {
        super();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getSelfTag() {
        return selfTag;
    }

    public void setSelfTag(String selfTag) {
        this.selfTag = selfTag == null ? null : selfTag.trim();
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public String getInfoUuid() {
        return infoUuid;
    }

    public void setInfoUuid(String infoUuid) {
        this.infoUuid = infoUuid == null ? null : infoUuid.trim();
    }

    public Boolean getBlessing() {
        return blessing;
    }

    public void setBlessing(Boolean blessing) {
        this.blessing = blessing;
    }

    public Boolean getValid() {
        return valid;
    }

    public void setValid(Boolean valid) {
        this.valid = valid;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getLastModified() {
        return lastModified;
    }

    public void setLastModified(Date lastModified) {
        this.lastModified = lastModified;
    }

    public int getScore() {
        return score;
    }

    public void setScore(int score) {
        this.score = score;
    }
}