package com.shuidihuzhu.cf.model.eagle;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * cf_case_info_flow_record 案例分流实验记录表
 * <AUTHOR>
@Data
public class CfCaseInfoFlowRecordDO implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 规则: caseId_分流key
     */
    private String caseIdKey;

    /**
     * 类型规则: 1 鹰眼逻辑插入 2 活动规则插入
     */
    private Integer datatype;

    /**
     * 是否删除0存在1删除
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}