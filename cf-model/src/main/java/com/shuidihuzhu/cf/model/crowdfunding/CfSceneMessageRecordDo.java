package com.shuidihuzhu.cf.model.crowdfunding;

import com.alibaba.fastjson.annotation.JSONField;
import com.shuidihuzhu.cf.enums.crowdfunding.SceneMessageEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;

/**
 * Author   : roy
 * Date     : 2018-05-07  16:17
 * Describe :
 * <p>
 * backup @JSONField(serialize = false)
 *
 * <AUTHOR>
 */
@Data
@ApiModel("场景消息记录")
public class CfSceneMessageRecordDo {
    private long id;
    private long userId;
    /**
     * 是否浏览过消息
     */
    private int visited;
    /**
     * 是否发送过回复消息
     */
    private int hasSend;
    private String channel;
    private int wxMpType;

    @ApiModelProperty("场景Id")
    private SceneMessageEnum sceneId;

    @JSONField(serialize = false)
    private Timestamp createTime;

    @JSONField(serialize = false)
    private Timestamp updateTime;

}
