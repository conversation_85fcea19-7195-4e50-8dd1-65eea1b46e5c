package com.shuidihuzhu.cf.model.dailyreport;

/**
 * Created by chao on 2017/11/9.
 */
public class CfDailyReportList {
	private long id;
	private String dateStr;
	private long userId;
	private int infoId;
	private String infoUuid;
	private int shareCount;
	private String headImgUrl;
	private String nickname;
	private int selfMoneyAmount;
	private int shareDonateAmount;
	private int shareDonateCount;
	private int totalDonateAmount;

	public int getShareCount() {
		return shareCount;
	}

	public void setShareCount(int shareCount) {
		this.shareCount = shareCount;
	}

	public String getHeadImgUrl() {
		return headImgUrl;
	}

	public void setHeadImgUrl(String headImgUrl) {
		this.headImgUrl = headImgUrl;
	}

	public String getNickname() {
		return nickname;
	}

	public void setNickname(String nickname) {
		this.nickname = nickname;
	}

	public String getInfoUuid() {
		return infoUuid;
	}

	public void setInfoUuid(String infoUuid) {
		this.infoUuid = infoUuid;
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getDateStr() {
		return dateStr;
	}

	public void setDateStr(String dateStr) {
		this.dateStr = dateStr;
	}

	public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}

	public int getInfoId() {
		return infoId;
	}

	public void setInfoId(int infoId) {
		this.infoId = infoId;
	}

	public int getSelfMoneyAmount() {
		return selfMoneyAmount;
	}

	public void setSelfMoneyAmount(int selfMoneyAmount) {
		this.selfMoneyAmount = selfMoneyAmount;
	}

	public int getShareDonateAmount() {
		return shareDonateAmount;
	}

	public void setShareDonateAmount(int shareDonateAmount) {
		this.shareDonateAmount = shareDonateAmount;
	}

	public int getShareDonateCount() {
		return shareDonateCount;
	}

	public void setShareDonateCount(int shareDonateCount) {
		this.shareDonateCount = shareDonateCount;
	}

	public int getTotalDonateAmount() {
		return totalDonateAmount;
	}

	public void setTotalDonateAmount(int totalDonateAmount) {
		this.totalDonateAmount = totalDonateAmount;
	}
}
