package com.shuidihuzhu.cf.model.crowdfunding.param;

import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.client.ugc.model.view.RiskWordResult;
import com.shuidihuzhu.cf.client.ugc.model.view.RiskWordResultV2;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * @Author: wangpeng
 * @Date: 2021/4/6 20:28
 * @Description:
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SendHitNoticeToAlarmParam {
    @NotNull
    private UserInfoModel userInfoModel;
    private RiskWordResultV2 hitResult;
    private String caseTitle;
    private String caseContent;
    /**
     * 触发时机
     */
    private String desc;
    private String diseaseName;
    /**
     * 拦截位置
     */
    private String location;
}
