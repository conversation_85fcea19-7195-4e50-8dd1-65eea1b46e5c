package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * Created by wangsf on 18/7/28.
 */
@Data
@ToString
public class CfSuspectedCaseInfo {

	private int id;

	/**
	 * 被举报案例发起人昵称
	 */
	private String nickname = "";

	/**
	 * 被举报案例发起人手机号
	 */
	private String mobile = "";
	private String encryptMobile = "";
	private NumberMaskVo mobileMask;

	/**
	 * 被举报案例title
	 */
	private String title = "";

	/**
	 * 案例uuid
	 */
	private String infoUuid = "";

	/**
	 * 失信原因
	 */
	private String reason = "";

	/**
	 * 失信时间
	 */
	private Date faithlessDate;

	/**
	 * 失信人身份证号
	 */
	private String idNumber = "";
	private NumberMaskVo idNumberMask;

}
