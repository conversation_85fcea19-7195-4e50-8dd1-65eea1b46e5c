package com.shuidihuzhu.cf.model.crowdfunding;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel
public class CfPageGlobalConfig {

    private long id;

    @ApiModelProperty("页面跳转地址")
    private String jumperUrl;
    @ApiModelProperty("页面跳转地址名称")
    private String jumperName = "";
    @ApiModelProperty("页面跳转地址描述")
    private String jumperDesc = "";
    @ApiModelProperty("页面跳转地址类型")
    private int globalType;
    @ApiModelProperty("页面跳转效果")
    private int effectShow;
    @ApiModelProperty("页面跳转地址测试比例，总和必须是100")
    private int percent;
    @ApiModelProperty("创建时间")
    private Date createTime;
    @ApiModelProperty("更新时间")
    private Date updateTime;

}
