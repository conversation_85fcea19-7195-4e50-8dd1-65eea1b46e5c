package com.shuidihuzhu.cf.model.loverank;

import com.alibaba.fastjson.JSONArray;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * user_love_rank_like_stat
 * <AUTHOR>
@Data
public class UserLoveRankLikeStat implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 用户userId
     */
    private Long userId;

    /**
     * 案例id
     */
    private Integer caseId;

    /**
     * 是否匿名，1匿名，0非匿名
     */
    private Integer anonymous;

    /**
     * 点赞统计
     */
    private Integer likeCount;

    /**
     * 查看当前user_id转发案例用户userId集合
     */
    private String viewUserIds;



    private static final long serialVersionUID = 1L;
}