package com.shuidihuzhu.cf.model.risk.verify;

import com.shuidihuzhu.cf.enums.risk.UgcTypeEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2018-11-15  16:19
 */
@NoArgsConstructor
@Data
public class RiskUgcVerifyModel {

    private long id;

    private long caseId;

    private UgcTypeEnum ugcType;

    private long ugcId;

    private String description;

    public RiskUgcVerifyModel(long caseId, UgcTypeEnum ugcType, long ugcId, String description) {
        this.caseId = caseId;
        this.ugcType = ugcType;
        this.ugcId = ugcId;
        this.description = description;
    }
}
