package com.shuidihuzhu.cf.model.crowdfunding.message;

import com.shuidihuzhu.msg.vo.rpc.MsgRecord;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 对应“228项目进展通知消息定时发送”需求
 *
 * <AUTHOR>
 * @date 2019/05/30
 */
@Data
@AllArgsConstructor
public class CfProgressDelayPushMsg {
    /**
     * 案例主键
     */
    private int caseId;
    /**
     * 对应 msg-api 的 model
     */
    private MsgRecord msgRecord;
    /**
     * 收到发送进展通知的时间
     */
    private long progressTime;
    /**
     * 预定推送时间
     */
    private long sendTime;
}
