package com.shuidihuzhu.cf.model.river;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public enum RiverRejectLocationEnum {

    /**
     * 默认值
     */
    DEFAULT(0, "默认"),

    HOUSE(21, "房产信息"),
    CAR(22, "车产信息"),
    HOME_INCOME(23, "家庭年收入"),
    HOME_STOCK(24, "家庭金融资产"),
    HOME_DEBT(25, "负债情况"),
    MEDICAL_INSURANCE(26, "医保情况"),
    LIFE_INSURANCE(27, "人身险"),
    PROPERTY_INSURANCE(28, "财产险"),
    GOV_RELIEF(29, "政府医疗救助情况"),
    DI_BAO(31, "低保情况"),
    PIN_KUN_HU(32, "建档立卡贫困户"),
    PATIENT_FAMILY_DI_BAO(53, "患者核心家庭经济情况-低保信息"),
    ;


    @Getter
    private final int value;

    /**
     * name必须为cf_refuse_reason_item.content
     */
    @Getter
    private final String name;

    RiverRejectLocationEnum(int v, String name) {
        this.value = v;
        this.name = name;
    }

    private static final Map<String, RiverRejectLocationEnum> LOCATION_NAME_MAP =
            Arrays.stream(RiverRejectLocationEnum.values())
                    .collect(Collectors.toMap(RiverRejectLocationEnum::getName, Function.identity()));

    /**
     * 根据cf_refuse_reason_item.content
     * @param name
     * @return
     */
    public static RiverRejectLocationEnum getByName(String name){
        return LOCATION_NAME_MAP.get(name);
    }

    public static RiverRejectLocationEnum parse(int v){
        for (RiverRejectLocationEnum e : RiverRejectLocationEnum.values()) {
            if (e.getValue() == v) {
                return e;
            }
        }
        throw new IllegalArgumentException("can not find RiverRejectLocationEnum code: " + v);
    }

}
