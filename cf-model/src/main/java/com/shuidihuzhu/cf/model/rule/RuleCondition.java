package com.shuidihuzhu.cf.model.rule;

import com.shuidihuzhu.cf.enums.rule.RuleNodeTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by sven on 18/8/7.
 *
 * <AUTHOR>
 */
@Data
public class RuleCondition implements Serializable {

    private long id;

    /**
     * 所属的集合id
     */
    private long collectionId;

    /**
     * 字段名称，支持匹配
     */
    private String fieldName;

    /**
     * 操作符号
     * 含义参见 {@link com.shuidihuzhu.cf.enums.rule.RuleOperatorTypeEnum}
     */
    private int operator;

    /**
     * 基本值
     */
    private String targetValue;

    /**
     * 当value过大的时候，明显已经不适合放在单条语句中了，
     */
    private String externDataSource;

    /**
     * 优先级
     */
    private int priority;

    /**
     * 建议的结果
     */
    private String result;

    /**
     * 节点的类型，默认都是叶子节点
     */
    private RuleNodeTypeEnum nodeTypeEnum;

    /**
     * 如果nodeTypeEnum的值是BRANCH，那么此处就是下一个条件的地址
     */
    private long sourceNodeId;

    private Date createTime;
    private Date updateTime;
}
