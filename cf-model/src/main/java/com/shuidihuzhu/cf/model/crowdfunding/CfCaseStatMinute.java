package com.shuidihuzhu.cf.model.crowdfunding;

import org.apache.commons.lang3.builder.ToStringBuilder;

import java.sql.Timestamp;

/**
 * Created by ahrievil on 2017/5/21.
 */
public class CfCaseStatMinute {
    private int id;
    private int caseCount;
    private int orderCount;
    private int sumAmount;
    private int verificationCount;
    private int commentCount;
    private int progressCount;
    private int realInfoCount;
    private Timestamp createTime;
    private Timestamp lastModified;

    private int shareCount;
    private int subscribeCount;
    private int orderSubscribeCount;

    public int getShareCount() {
        return shareCount;
    }

    public void setShareCount(int shareCount) {
        this.shareCount = shareCount;
    }

    public int getSubscribeCount() {
        return subscribeCount;
    }

    public void setSubscribeCount(int subscribeCount) {
        this.subscribeCount = subscribeCount;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getCaseCount() {
        return caseCount;
    }

    public void setCaseCount(int caseCount) {
        this.caseCount = caseCount;
    }

    public int getOrderCount() {
        return orderCount;
    }

    public void setOrderCount(int orderCount) {
        this.orderCount = orderCount;
    }

    public int getSumAmount() {
        return sumAmount;
    }

    public void setSumAmount(int sumAmount) {
        this.sumAmount = sumAmount;
    }

    public int getVerificationCount() {
        return verificationCount;
    }

    public void setVerificationCount(int verificationCount) {
        this.verificationCount = verificationCount;
    }

    public int getCommentCount() {
        return commentCount;
    }

    public void setCommentCount(int commentCount) {
        this.commentCount = commentCount;
    }

    public int getProgressCount() {
        return progressCount;
    }

    public void setProgressCount(int progressCount) {
        this.progressCount = progressCount;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public Timestamp getLastModified() {
        return lastModified;
    }

    public void setLastModified(Timestamp lastModified) {
        this.lastModified = lastModified;
    }

    public int getRealInfoCount() {
        return realInfoCount;
    }

    public void setRealInfoCount(int realInfoCount) {
        this.realInfoCount = realInfoCount;
    }

    public int getOrderSubscribeCount() {
        return orderSubscribeCount;
    }

    public void setOrderSubscribeCount(int orderSubscribeCount) {
        this.orderSubscribeCount = orderSubscribeCount;
    }


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
