package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * @Description:
 * @Author: panghair<PERSON>
 * @Date: 2025/4/18 14:45
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CrowdfundingAttachmentDTO {

    private int id;
    private int parentId;
    private AttachmentTypeEnum type;
    private String url;
    private int sequence;
    private Timestamp createTime;
    private Timestamp lastModified;
    private int isDelete;

}
