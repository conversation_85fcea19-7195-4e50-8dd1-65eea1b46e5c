package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.SubTreatmentTypeEnum;
import io.swagger.annotations.ApiModelProperty;

/**
 * Created by w<PERSON><PERSON><PERSON> on 7/5/16.
 * Modified by <PERSON> on 2016-09-12 for V3
 * 筹款治疗信息表
 * 理论上与 {@link CrowdfundingInfo} 是 1:1 关系
 * 所以,只会使用到 crowdfundingId 来做查询(应该将其当做主键来看待)
 */
public class CrowdfundingTreatment {
	/**
	 * 自增长ID
	 */
	private int id;
	/**
	 * 筹款ID
	 * 参照 {@link CrowdfundingInfo#id}
	 */
	private int crowdfundingId;
	/**
	 * 疾病名称
	 */
	private String diseaseName;
	/**
	 * 医院所在地
	 * V3版本不再需要
	 */
	private String location;
	/**
	 * 就诊医院名称
	 */
	private String hospitalName;
	/**
	 * 确诊医院名称
	 */
	private String diagnoseHospitalName;
	/**
	 * 就诊医院id
	 */
	private int hospitalId;
    /**
     * 确诊医院id
     */
	private int diagnoseHospitalId;
	/**
	 * 就诊医院所在城市id
	 */
	private int hospitalCityId;
	/**
	 * 确诊医院所在城市id
	 */
	private int diagnoseHospitalCityId;
    /**
     * 诊断材料子类型
     * {@link SubTreatmentTypeEnum}
     */
    private Integer subAttachmentType;


	@ApiModelProperty("医院编码")
	private String hospitalCode;
    
	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public int getCrowdfundingId() {
		return crowdfundingId;
	}

	public void setCrowdfundingId(int crowdfundingId) {
		this.crowdfundingId = crowdfundingId;
	}

	public String getDiseaseName() {
		return diseaseName;
	}

	public void setDiseaseName(String diseaseName) {
		this.diseaseName = diseaseName;
	}

	public String getLocation() {
		return location;
	}

	public void setLocation(String location) {
		this.location = location;
	}

	public String getHospitalName() {
		return hospitalName;
	}

	public void setHospitalName(String hospitalName) {
		this.hospitalName = hospitalName;
	}

	public String getDiagnoseHospitalName() {
		return diagnoseHospitalName;
	}

	public void setDiagnoseHospitalName(String diagnoseHospitalName) {
		this.diagnoseHospitalName = diagnoseHospitalName;
	}

    public int getHospitalId() {
        return hospitalId;
    }

    public void setHospitalId(int hospitalId) {
        this.hospitalId = hospitalId;
    }

    public int getDiagnoseHospitalId() {
        return diagnoseHospitalId;
    }

    public void setDiagnoseHospitalId(int diagnoseHospitalId) {
        this.diagnoseHospitalId = diagnoseHospitalId;
    }

	public int getHospitalCityId() {
		return hospitalCityId;
	}

	public void setHospitalCityId(int hospitalCityId) {
		this.hospitalCityId = hospitalCityId;
	}

	public int getDiagnoseHospitalCityId() {
		return diagnoseHospitalCityId;
	}

	public void setDiagnoseHospitalCityId(int diagnoseHospitalCityId) {
		this.diagnoseHospitalCityId = diagnoseHospitalCityId;
	}

	public Integer getSubAttachmentType() {
        return subAttachmentType;
    }

    public void setSubAttachmentType(Integer subAttachmentType) {
        this.subAttachmentType = subAttachmentType;
    }


	public String getHospitalCode() {
		return hospitalCode;
	}

	public void setHospitalCode(String hospitalCode) {
		this.hospitalCode = hospitalCode;
	}
}
