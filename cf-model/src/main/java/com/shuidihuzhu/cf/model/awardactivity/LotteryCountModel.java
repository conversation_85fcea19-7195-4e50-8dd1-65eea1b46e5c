package com.shuidihuzhu.cf.model.awardactivity;

import com.shuidihuzhu.common.web.model.AbstractModel;

import java.util.Date;

/**
 * Created by ch<PERSON> on 16/11/25.
 */
public class LotteryCountModel extends AbstractModel {
	int id;
	long userId;
	int activityId;
	int count;
	Date dateCreated;
	Date lastModified;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}

	public int getActivityId() {
		return activityId;
	}

	public void setActivityId(int activityId) {
		this.activityId = activityId;
	}

	public int getCount() {
		return count;
	}

	public void setCount(int count) {
		this.count = count;
	}

	public Date getDateCreated() {
		return dateCreated;
	}

	public void setDateCreated(Date dateCreated) {
		this.dateCreated = dateCreated;
	}

	public Date getLastModified() {
		return lastModified;
	}

	public void setLastModified(Date lastModified) {
		this.lastModified = lastModified;
	}
}
