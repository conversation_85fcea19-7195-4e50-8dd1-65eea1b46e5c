package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingTreatment;
import com.shuidihuzhu.cf.model.getfundinginfo.CrowdfundingTreatmentInfoParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Author: <PERSON>
 * Date: 16/9/12 20:27
 */
@ApiModel("诊断证明")
@Data
public class CrowdfundingTreatmentInfo implements Serializable {

	@ApiModelProperty("疾病名称")
	private String diseaseName;

	@ApiModelProperty("医院名称")
	private String hospitalName;

	@ApiModelProperty("诊断医院")
	private String diagnoseHospitalName;

	@ApiModelProperty("参见SubTreatmentTypeEnum")
	private Integer subAttachmentType;

	@ApiModelProperty("诊断证明照片")
	private String verifyPhoto;

	@ApiModelProperty("案例发起时，医疗证明材料图片")
	private String medicalTreatmentPhoto;

	@ApiModelProperty("所有照片")
	private List<String> attachments;

	public CrowdfundingTreatmentInfo() {
	}

	public CrowdfundingTreatmentInfo(CrowdfundingTreatment treatment,
	                                 CrowdfundingAttachmentVo verifyAttachmentVo,
	                                 List<CrowdfundingAttachmentVo> attachmentVos) {
		if(treatment != null){
			diseaseName = treatment.getDiseaseName();
			hospitalName = treatment.getHospitalName();
			diagnoseHospitalName = treatment.getDiagnoseHospitalName();
            subAttachmentType = treatment.getSubAttachmentType();
		}
		if (verifyAttachmentVo != null) {
			verifyPhoto = verifyAttachmentVo.getUrl();
		}
		attachments = new ArrayList<>();
		if (!CollectionUtils.isEmpty(attachmentVos)) {
			for (CrowdfundingAttachmentVo attachmentVo : attachmentVos) {
				attachments.add(attachmentVo.getUrl());
			}
		}
	}

	public CrowdfundingTreatmentInfo(CrowdfundingTreatmentInfoParam param) {

		this.medicalTreatmentPhoto = param.getMedicalTreatmentPhoto();
		this.attachments = param.getAttachmentList();
		this.verifyPhoto = StringUtils.EMPTY;

		CrowdfundingTreatment treatment = param.getTreatment();
		if (treatment != null) {
			this.diseaseName = treatment.getDiseaseName();
			this.hospitalName = treatment.getHospitalName();
			this.diagnoseHospitalName = treatment.getDiagnoseHospitalName();
			this.subAttachmentType = treatment.getSubAttachmentType();
		}

	}

}
