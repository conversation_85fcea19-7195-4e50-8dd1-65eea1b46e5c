package com.shuidihuzhu.cf.model.crowdfunding.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/24  6:01 下午
 */
@Data
public class NewDetailVo {
    @ApiModelProperty("案例详情页版本 0：老版详情页 1：新版详情页 2：pr详情页")
    private String caseDetailsPageVersion;

    @ApiModelProperty("案例详情页头图")
    private String headPicture;

    @ApiModelProperty("案例详情页透明水滴")
    private String carouselText;

    @ApiModelProperty("案例详情页标签")
    private List<String> caseLabel;

    @ApiModelProperty("案例详情页患者性别 1：男 2：女 0：未知")
    private int patientSex;

    public NewDetailVo() {

    }

    public NewDetailVo(String caseDetailsPageVersion) {
        this.caseDetailsPageVersion = caseDetailsPageVersion;
    }
}
