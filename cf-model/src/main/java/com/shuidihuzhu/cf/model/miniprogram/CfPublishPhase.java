package com.shuidihuzhu.cf.model.miniprogram;

import java.sql.Timestamp;

public class CfPublishPhase {
    private int id;
    private int keywordId;
    private int phaseId;
    private int publishStatus;
    private Timestamp publishTime;
    private int isDelete;

    public CfPublishPhase() {
    }

    public CfPublishPhase(int keywordId, int phaseId, Timestamp publishTime) {
        this.keywordId = keywordId;
        this.phaseId = phaseId;
        this.publishTime = publishTime;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getKeywordId() {
        return keywordId;
    }

    public void setKeywordId(int keywordId) {
        this.keywordId = keywordId;
    }

    public int getPhaseId() {
        return phaseId;
    }

    public void setPhaseId(int phaseId) {
        this.phaseId = phaseId;
    }

    public int getPublishStatus() {
        return publishStatus;
    }

    public void setPublishStatus(int publishStatus) {
        this.publishStatus = publishStatus;
    }

    public Timestamp getPublishTime() {
        return publishTime;
    }

    public void setPublishTime(Timestamp publishTime) {
        this.publishTime = publishTime;
    }

    public int getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(int isDelete) {
        this.isDelete = isDelete;
    }

    @Override
    public String toString() {
        return "CfPublicPhase{" +
                "id=" + id +
                ", keywordId=" + keywordId +
                ", phaseId=" + phaseId +
                ", publishStatus=" + publishStatus +
                ", publishTime=" + publishTime +
                ", isDelete=" + isDelete +
                '}';
    }
}
