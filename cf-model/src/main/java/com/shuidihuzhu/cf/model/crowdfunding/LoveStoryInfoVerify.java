package com.shuidihuzhu.cf.model.crowdfunding;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class LoveStoryInfoVerify {

        @ApiModelProperty("患者姓名")
        private String name;

        @ApiModelProperty("患者献爱心次数")
        private int cnt;

        @ApiModelProperty("患者标签(按选择数量排列,标签id+标签文案+标签选择次数)")
        private List<LabelInfo> label;

        @ApiModelProperty("帮助者回复(文案+日期)")
        private List<ReplyInfo> reply;

}

