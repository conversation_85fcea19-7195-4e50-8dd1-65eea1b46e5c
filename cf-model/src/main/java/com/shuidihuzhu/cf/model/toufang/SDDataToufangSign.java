package com.shuidihuzhu.cf.model.toufang;

import lombok.Data;

/**
 * Created by wangsf on 18/8/9.
 */
@Data
public class SDDataToufangSign {

	/**
	 * {“ext”:"{\
	 * "data\":{
	 * "\site_id\": \"355820\",
	 * \"page_id\": \"365610\",
	 * \"data\":[
	 * {
	 * \"label\": \"姓名\",
	 * \"id\": \"52200093244\",
	 * \"value\": \"黎先\"},
	 * { \"label\": \"电话\",
	 * \"id\": \"***********\",
	 * \"value\": \"***********\"},
	 * {\"label\": \"邮箱\",
	 * \"id\": \"***********\",
	 * \"value\": \"<EMAIL>\"},
	 * {\"label\": \"地区\",
	 * \"id\": \"***********\",
	 * \"value\": \"北京\"}]
	 * }
	 * }",
	 * "biz":"cf",
	 * "mobile":"***********"}
	 */

	private String mobile;
	private String biz;

	private String channel;
	private String match; //关键词模式
	private String keyword; //关键词跟踪参数
	private String semwp; //关键词类型
	private String account; //账户名称
	private String kwid; //keywordId
	private String creative; //creative
	private String eAdposition;
	private String source;

	private String ext;
	private String siteId;
	private String primaryChannel;
	private String toufangSource;

	private String cryptoMobile;

}