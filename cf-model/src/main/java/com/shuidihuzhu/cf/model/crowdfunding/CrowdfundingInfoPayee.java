package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import lombok.Data;

import java.sql.Timestamp;

@Data
public class CrowdfundingInfoPayee {

	private Long id;
	private String infoUuid;
	private Integer relationType;
	private String name;
	private String idCard;
	private Integer idType;
	private String mobile;
	private String bankName;
	private String bankBranchName;
	private String bankCard;
	private Timestamp dateCreated;
	private Timestamp lastModified;
	private int caseId;
	//近亲属关系  @sea RelativesTypeEnum
	private int relativesType;
	//紧急联系人
	private String emergency;
	private String emergencyPhone;
	// 紧急联系人手机号（后台用）
	private NumberMaskVo emergencyPhoneMask;

	/**
	 * 人脸识别结果  0 默认  10 失败   20 成功
	 */
	private int faceIdResult;

	/**
	 * 上传其他身份证件 0：否; 1：是
	 */
	private int otherIdPhoto;
}
