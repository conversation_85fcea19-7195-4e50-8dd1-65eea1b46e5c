package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.CfBillingConstants;

import java.util.Date;

/**
 * Created by niejiangnan on 2017/8/30.
 */
public class CfDuiZhangBilling {
    private long id;
    private long batchId;
    //从业务订单表发起对账
    private Date billingTime;
    private String orderId="";
    private String payUid;
    private int orderAmount;
    private Date orderTime;
    //从第三方订单发起对账
    private Date thirdBillingTime;
    private String thirdOrderId=""; // 400开头的订单号
    private String thirdPayUid;
    private int thirdOrderAmount;
    private Date thirdOrderTime;
    //对账状态  ：1.已对上 2 对不上 3 手动标记对上
    private int billingStatus;
    //对不上的原因 1.自己订单库没有  2.第三方订单库没有 3.金额不对
    private int billingFailReason;
    //手动标记成功原因 ：1.测试数据
    private int manualMarkReason;
    //对账核对状态：1.自己已经核对  2.第三方已核对  3.两边都核对
    private int billingCheckStatus;
    private Date createTime;
    private Date updateTime;

    public CfDuiZhangBilling(long batchId,
                             Date billingTime,
                             String orderId,
                             String payUid,
                             int orderAmount,
                             Date orderTime,
                             Date thirdBillingTime,
                             String thirdOrderId,
                             String thirdPayUid,
                             int thirdOrderAmount,
                             Date thirdOrderTime,
                             CfBillingConstants.CfBillingStatus billingStatus,
                             CfBillingConstants.BillingFailReason billingFailReason,
                             CfBillingConstants.CfManualMarkReason manualMarkReason,
                             CfBillingConstants.BillingCheckStatus billingCheckStatus) {
        this.batchId = batchId;
        this.billingTime = billingTime;
        this.orderId = orderId;
        this.payUid = payUid;
        this.thirdPayUid = thirdPayUid;
        this.orderAmount = orderAmount;
        this.orderTime = orderTime;
        this.thirdBillingTime = thirdBillingTime;
        this.thirdOrderId = thirdOrderId;
        this.thirdOrderAmount = thirdOrderAmount;
        this.thirdOrderTime = thirdOrderTime;
        this.billingStatus = billingStatus.getCode();
        this.billingFailReason = billingFailReason.getCode();
        this.manualMarkReason = manualMarkReason.getCode();
        this.billingCheckStatus = billingCheckStatus.getCode();
    }

    public CfDuiZhangBilling() {
    }

    public String getPayUid() {
        return payUid;
    }

    public void setPayUid(String payUid) {
        this.payUid = payUid;
    }

    public String getThirdPayUid() {
        return thirdPayUid;
    }

    public void setThirdPayUid(String thirdPayUid) {
        this.thirdPayUid = thirdPayUid;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getBatchId() {
        return batchId;
    }

    public void setBatchId(long batchId) {
        this.batchId = batchId;
    }

    public Date getBillingTime() {
        return billingTime;
    }

    public void setBillingTime(Date billingTime) {
        this.billingTime = billingTime;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public int getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(int orderAmount) {
        this.orderAmount = orderAmount;
    }

    public Date getOrderTime() {
        return orderTime;
    }

    public void setOrderTime(Date orderTime) {
        this.orderTime = orderTime;
    }

    public Date getThirdBillingTime() {
        return thirdBillingTime;
    }

    public void setThirdBillingTime(Date thirdBillingTime) {
        this.thirdBillingTime = thirdBillingTime;
    }

    public String getThirdOrderId() {
        return thirdOrderId;
    }

    public void setThirdOrderId(String thirdOrderId) {
        this.thirdOrderId = thirdOrderId;
    }

    public int getThirdOrderAmount() {
        return thirdOrderAmount;
    }

    public void setThirdOrderAmount(int thirdOrderAmount) {
        this.thirdOrderAmount = thirdOrderAmount;
    }

    public Date getThirdOrderTime() {
        return thirdOrderTime;
    }

    public void setThirdOrderTime(Date thirdOrderTime) {
        this.thirdOrderTime = thirdOrderTime;
    }

    public int getBillingStatus() {
        return billingStatus;
    }

    public void setBillingStatus(int billingStatus) {
        this.billingStatus = billingStatus;
    }

    public int getBillingFailReason() {
        return billingFailReason;
    }

    public void setBillingFailReason(int billingFailReason) {
        this.billingFailReason = billingFailReason;
    }

    public int getManualMarkReason() {
        return manualMarkReason;
    }

    public void setManualMarkReason(int manualMarkReason) {
        this.manualMarkReason = manualMarkReason;
    }

    public int getBillingCheckStatus() {
        return billingCheckStatus;
    }

    public void setBillingCheckStatus(int billingCheckStatus) {
        this.billingCheckStatus = billingCheckStatus;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
