package com.shuidihuzhu.cf.model.crowdfunding;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 *Author：梁洪超
 *Date： 2017/10/23
 */

public class CfContribute implements Serializable, Comparable<CfContribute> {

	private static final long serialVersionUID = 3316697879229492481L;
	private long userId;
	private int infoId;             //案例ID
	private long donation;          //个人捐款
	private long trantsmitAccount;  //分享带来的捐款
	private long sumAccount ;       //个人捐款加分享代来的捐款
	private int counts ;            //转发次数
	private int interact ;          //互动次数
	private String nickname ;       //昵称
	private String headImgUrl;      //头像地址
	private Date donationDate = new Date(**********);     //注意 null 异常
	private int id ;
	private int isDelete;
	private Date createTime;
	private Date updateTime;

	public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}

	public int getInfoId() {
		return infoId;
	}

	public void setInfoId(int infoId) {
		this.infoId = infoId;
	}

	public long getDonation() {
		return donation;
	}

	public void setDonation(long donation) {
		this.donation = donation;
	}

	public long getTrantsmitAccount() {
		return trantsmitAccount;
	}

	public void setTrantsmitAccount(long trantsmitAccount) {
		this.trantsmitAccount = trantsmitAccount;
	}

	public long getSumAccount() {
		return sumAccount;
	}

	public void setSumAccount(long sumAccount) {
		this.sumAccount = sumAccount;
	}

	public int getCounts() {
		return counts;
	}

	public void setCounts(int counts) {
		this.counts = counts;
	}

	public String getNickname() {
		return nickname;
	}

	public void setNickname(String nickname) {
		this.nickname = nickname;
	}

	public String getHeadImgUrl() {
		return headImgUrl;
	}

	public void setHeadImgUrl(String headImgUrl) {
		this.headImgUrl = headImgUrl;
	}

	public Date getDonationDate() {
		return donationDate;
	}

	public void setDonationDate(Date donationDate) {
		this.donationDate = donationDate;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public int getInteract() {
		return interact;
	}

	public void setInteract(int interact) {
		this.interact = interact;
	}

	public int getIsDelete() {
		return isDelete;
	}

	public void setIsDelete(int isDelete) {
		this.isDelete = isDelete;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	@Override
	public int compareTo(CfContribute o) {
		if(o == null ){
			return -1 ;
		}
		if (o.getNickname() == null || o.getHeadImgUrl() == null || o.getDonationDate() == null){
			return -1 ;
		}
		if(this.sumAccount != o.getSumAccount()){
			return (int) ((o.sumAccount - this.getSumAccount()) % 1000000 );
		}
		if(this.donationDate != o.getDonationDate()){
			return o.donationDate.compareTo(this.donationDate);
		}
		return  0 ;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o) { return true; }
		if (!(o instanceof CfContribute)) { return false; }
		CfContribute that = (CfContribute) o;
		return getUserId() == that.getUserId() &&
		       getInfoId() == that.getInfoId();
	}

	@Override
	public int hashCode() {
		return Objects.hash(getUserId(), getInfoId());
	}

	@Override
	public String toString() {
		return "CfContribute{" +
		       "userId=" + userId +
		       ", infoId=" + infoId +
		       ", donation=" + donation +
		       ", trantsmitAccount=" + trantsmitAccount +
		       ", sumAccount=" + sumAccount +
		       ", counts=" + counts +
		       ", interact=" + interact +
		       ", nickname='" + nickname + '\'' +
		       ", headImgUrl='" + headImgUrl + '\'' +
		       ", donationDate=" + donationDate +
		       '}';
	}

	public void desensitization() {
		this.userId = -1;
		this.infoId = -1;
	}
}

