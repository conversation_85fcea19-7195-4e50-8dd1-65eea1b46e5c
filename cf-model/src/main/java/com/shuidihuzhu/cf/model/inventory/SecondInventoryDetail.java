package com.shuidihuzhu.cf.model.inventory;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/1/11 7:03 下午
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SecondInventoryDetail {

    private Long userId;

    /**
     * 取数据策略
     */
    private String rule;

    /**
     * 二级清单标识
     */
    private String identification;

    /**
     * 二级清单描述
     */
    private String describe;

    /**
     * 使用目的
     */
    private String purpose;

    /**
     * 使用场景
     */
    private String scene;

    /**
     * 收集情况
     */
    private String situation;

    /**
     * 信息内容
     */
    private String content;

    /**
     * 查看详情跳转的链接
     */
    private String routeUrl;

}
