package com.shuidihuzhu.cf.model.publictrust;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * cf_public_trust_promotion_statistics
 * <AUTHOR>
@Data
public class CfPublicTrustPromotionStatisticsDO implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 对外展示id
     */
    private String infoId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 是否点击 1是0否
     */
    private Integer isClick;

    /**
     * 业务类型 0未知 1公众信任
     */
    private Integer type;

    /**
     * 是否删除0存在1删除
     */
    private Integer isDelete;

    /**
     * 是否可用 1是0否
     */
    private Integer isAvailable;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}