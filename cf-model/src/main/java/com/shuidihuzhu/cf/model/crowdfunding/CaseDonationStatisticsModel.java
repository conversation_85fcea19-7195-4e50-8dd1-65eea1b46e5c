package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @author: wanghui
 * @create: 2019/4/1 4:55 PM
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class CaseDonationStatisticsModel {
    /**
     * 案例id
     */
    private Integer crowdfundingId;
    /**
     * 捐款次数
     */
    private Long count;
    /**
     * 捐款金额
     */
    private BigDecimal totalAmount;
}
