package com.shuidihuzhu.cf.model.crowdfunding.loverank;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("好友和全国榜单")
public class LoveRankeInfoVo implements Serializable {


    @ApiModelProperty("当前筹款金额，单位为分")
    private int amount;

    @ApiModelProperty("好友筹款金额，单位为分 ")
    private int friendsAmount;

    @ApiModelProperty("爱心人士筹款金额，单位为分")
    private int lovePepoleAmount;

    @ApiModelProperty("是否上榜,true 在榜单，false 不在榜单")
    private boolean onRank;

    @ApiModelProperty("false未捐转，true至少有其中之一")
    private boolean donateShare;

    @ApiModelProperty("登录用户捐款金额，单位为分")
    private int donateAmount;

    @ApiModelProperty("登录用户分享次数")
    private int shareCount;

    @ApiModelProperty("上榜排名")
    private int rank;

    @ApiModelProperty("排行榜列表")
    private List<UserDonateInfoForC> userDonateList;

}
