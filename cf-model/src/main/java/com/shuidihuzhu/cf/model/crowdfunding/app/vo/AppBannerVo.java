package com.shuidihuzhu.cf.model.crowdfunding.app.vo;

import com.shuidihuzhu.cf.model.crowdfunding.app.AppBanner;

/**
 * Created by wangsf on 18/2/1.
 */
public class AppBannerVo {

	private String picUrl;
	private String url;
	private String desc;

	public String getPicUrl() {
		return picUrl;
	}

	public void setPicUrl(String picUrl) {
		this.picUrl = picUrl;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getDesc() {
		return desc;
	}

	public void setDesc(String desc) {
		this.desc = desc;
	}

	public AppBannerVo() {
	}

	public AppBannerVo(AppBanner appBanner) {
		this.picUrl = appBanner.getPicUrl();
		this.url = appBanner.getUrl();
		this.desc = appBanner.getDescription();
	}
}
