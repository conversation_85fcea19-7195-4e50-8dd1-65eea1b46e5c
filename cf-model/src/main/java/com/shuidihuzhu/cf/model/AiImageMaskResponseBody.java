package com.shuidihuzhu.cf.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: pan<PERSON><PERSON><PERSON>
 * @Date: 2022/9/8 6:35 下午
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiImageMaskResponseBody {

    private String imageId;

    private int status;

    private String urlMosaic;

    private int bizType;

    private long bizId;

}
