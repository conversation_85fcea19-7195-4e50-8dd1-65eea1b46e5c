package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.Data;
import java.util.Date;

/**
 * <AUTHOR>
 * @time 2019/3/21 下午3:09
 * @desc
 */
@Data
public class CfUserCaseLabelDO {
    private Long id;
    //筹款用户id
    private Long userId;
    //案例id
    private Integer caseId;
    //该案例是否结束
    private Boolean end;
    //该案例发起时间
    private Date raiseTime;
    //该案例结束时间
    private Date endTime;

    private String channel;
    //该案例发起渠道 参考CaseRaiseChannelEnum
    private Integer raiseChannel;
    //该案例是否走前置审核
    private Boolean firstApprove;
    //该案例是否通过前置审核 0:不走前置审核 10:审核中 20:前置审核未通过 30:前置审核通过
    private Integer approveStatus;

    //该案例前置审核通过时间
    private Date approveTime;

    //该案例前置审核驳回次数
    private Integer rejectCount;

    private Date createTime;

    private Date updateTime;

    private Integer delete;
}
