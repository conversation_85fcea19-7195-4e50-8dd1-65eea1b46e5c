package com.shuidihuzhu.cf.model.crowdfunding;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingIdCaseStatusEnum;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by sven on 18/8/15.
 *
 * 作为材料审核的一项内容存在。对应于 {@link com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoDataStatusTypeEnum#ID_VERIFY}
 *
 * <AUTHOR>
 */
@Data
@ApiModel
public class CrowdfundingIdCase implements Serializable {

    public static CrowdfundingIdCase build(int caseId, String name, String identify, String encryptIdentify){
        CrowdfundingIdCase idCase = new CrowdfundingIdCase();
        idCase.setCaseId(caseId);
        idCase.setName(name);
        idCase.setCryptoIdCard(encryptIdentify);
        idCase.setStatus(CrowdfundingIdCaseStatusEnum.VERIFYING.getCode());
        return idCase;
    }

    @JsonIgnore
    private long id;

    @JsonIgnore
    private int caseId;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("加密后的身份证号")
    private String cryptoIdCard;

    @ApiModelProperty("非加密的身份证号")
    private String idCardVo;

    /**
     * 参见 {@link com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingIdCaseStatusEnum}
     */
    @ApiModelProperty("审核状态，10审核中，20审核失败，30审核通过，40白名单通过")
    private int status;

    @JsonIgnore
    private Date createTime;

    @JsonIgnore
    private Date updateTime;
}
