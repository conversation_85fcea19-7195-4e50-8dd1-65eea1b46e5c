package com.shuidihuzhu.cf.model.crowdfunding;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @time 2019/2/19 下午8:36
 * @desc
 */
@Data
@ApiModel("用户和案例的基本信息")
public class CfUserStatInfo {
    @ApiModelProperty("总捐款案例数")
    private int donateCaseCount;
    @ApiModelProperty("总捐款案例数加1")
    private int donateCaseCountPlusOne;
    @ApiModelProperty("总捐款次数")
    private int totalDonateCount;
    @ApiModelProperty("该用户总捐款金额")
    private long donateMoney;
    @ApiModelProperty("总转发案例数")
    private int shareCaseCount;
    @ApiModelProperty("总转发案例数加1")
    private int shareCaseCountPlusOne;
    @ApiModelProperty("总转发次数")
    private int totalShareCount;
    @ApiModelProperty("用户转发当前案例带来的总捐款金额（他人捐款）单位：分")
    private int shareBroughtMoney;
    @ApiModelProperty("用户转发当前案例带来的第一笔捐款金额（他人捐款）单位：分")
    private int shareFirstMoney;
    @ApiModelProperty("用户对当前案例的捐款金额 单位：分")
    private int currentDonateMoney;
    @ApiModelProperty("用户对当前案例的转发次数")
    private int currentShareCount;
    @ApiModelProperty("转发当前案例带来的捐款人数")
    private int shareBroughtCount;
}
