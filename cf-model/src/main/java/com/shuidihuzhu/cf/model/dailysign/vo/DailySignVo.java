package com.shuidihuzhu.cf.model.dailysign.vo;

import com.shuidihuzhu.cf.model.dailysign.DailySign;
import lombok.Data;

import java.sql.Timestamp;

/**
 * Created by wangsf on 18/4/26.
 */
@Data
public class DailySignVo {

	private long createTime = 0L;

	private int points;

	public DailySignVo() {

	}

	public DailySignVo(DailySign dailySign) {
		if(dailySign != null) {
			if (dailySign.getCreateTime() != null) {
				this.createTime = dailySign.getCreateTime().getTime();
			}

			this.points = dailySign.getPoints();
		}
	}

}
