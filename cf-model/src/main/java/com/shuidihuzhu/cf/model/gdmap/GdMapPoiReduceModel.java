package com.shuidihuzhu.cf.model.gdmap;

import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
public class GdMapPoiReduceModel {
    /**
     * 名称
     */
    private String name;
    /**
     * 兴趣点类型
     */
    private String type;
    /**
     * 兴趣点类型编码
     */
    private String typecode;
//    /**
//     * 地址
//     */
//    private String address;
    /**
     * 经纬度 格式：X,Y
     */
    private String location;
    /**
     * 离中心点距离
     */
    private String distance;
}
