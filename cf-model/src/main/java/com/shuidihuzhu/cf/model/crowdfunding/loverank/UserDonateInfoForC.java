package com.shuidihuzhu.cf.model.crowdfunding.loverank;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class UserDonateInfoForC {
    @ApiModelProperty("昵称")
    private String nickName;
    @ApiModelProperty("头像")
    private String headImg;
    @ApiModelProperty("捐款金额")
    private int amount;
    @ApiModelProperty("转发次数")
    private int shareCount;
    @ApiModelProperty("点赞次数")
    private int likeCount;
    @ApiModelProperty("是否匿名")
    private boolean anonymous;
    @ApiModelProperty("是否点过赞")
    private boolean like = false;
    @ApiModelProperty("排名")
    private int rankIndex;
    @ApiModelProperty("加密userId 用于api/cf/v5/love-rank/click-like的入参")
    private String cipherUserId;
}
