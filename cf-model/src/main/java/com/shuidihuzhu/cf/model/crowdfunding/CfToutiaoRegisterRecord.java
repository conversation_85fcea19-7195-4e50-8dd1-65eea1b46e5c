package com.shuidihuzhu.cf.model.crowdfunding;

import java.sql.Date;

/**
 * Created by wangsf on 18/3/11.
 */
public class CfToutiaoRegisterRecord {

	private Long id;
	private String dayKey;
	private String mobile;
	private String channel;
	private String source;
	private String match;
	private String keyword;
	private String semwp;
	private String account;
	private String kwid;
	private String eAdposition;
	private String creative;
	private String siteId;
	private String adId;
	private String submitId;
	private String cryptoMobile;

	private String type;
	private String relation;
	private String help;
	private String disease;
	private String infoUuid;
	private String note;
	private boolean isInviter;
	private String inviterMobile;


	private Date createTime;

	public String geteAdposition() {
		return eAdposition;
	}

	public void seteAdposition(String eAdposition) {
		this.eAdposition = eAdposition;
	}

	public String getSiteId() {
		return siteId;
	}

	public void setSiteId(String siteId) {
		this.siteId = siteId;
	}

	public String getAdId() {
		return adId;
	}

	public void setAdId(String adId) {
		this.adId = adId;
	}

	public String getSubmitId() {
		return submitId;
	}

	public void setSubmitId(String submitId) {
		this.submitId = submitId;
	}

	public String getCryptoMobile() {
		return cryptoMobile;
	}

	public void setCryptoMobile(String cryptoMobile) {
		this.cryptoMobile = cryptoMobile;
	}

	/**
	 * @return the id
	 */
	public Long getId() {
		return id;
	}

	/**
	 * @param id the id to set
	 */
	public void setId(Long id) {
		this.id = id;
	}


	/**
	 * @return the mobile
	 */
	public String getMobile() {
		return mobile;
	}

	/**
	 * @param mobile the mobile to set
	 */
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	/**
	 * @return the channel
	 */
	public String getChannel() {
		return channel;
	}

	/**
	 * @param channel the channel to set
	 */
	public void setChannel(String channel) {
		this.channel = channel;
	}

	/**
	 * @return the source
	 */
	public String getSource() {
		return source;
	}

	/**
	 * @param source the source to set
	 */
	public void setSource(String source) {
		this.source = source;
	}

	/**
	 * @return the match
	 */
	public String getMatch() {
		return match;
	}

	/**
	 * @param match the match to set
	 */
	public void setMatch(String match) {
		this.match = match;
	}

	/**
	 * @return the keyword
	 */
	public String getKeyword() {
		return keyword;
	}

	/**
	 * @param keyword the keyword to set
	 */
	public void setKeyword(String keyword) {
		this.keyword = keyword;
	}

	/**
	 * @return the semwp
	 */
	public String getSemwp() {
		return semwp;
	}

	/**
	 * @param semwp the semwp to set
	 */
	public void setSemwp(String semwp) {
		this.semwp = semwp;
	}

	/**
	 * @return the account
	 */
	public String getAccount() {
		return account;
	}

	/**
	 * @param account the account to set
	 */
	public void setAccount(String account) {
		this.account = account;
	}

	/**
	 * @return the kwid
	 */
	public String getKwid() {
		return kwid;
	}

	/**
	 * @param kwid the kwid to set
	 */
	public void setKwid(String kwid) {
		this.kwid = kwid;
	}

	/**
	 * @param e_adposition the e_adposition to set
	 */
	public void setEAdposition(String e_adposition) {
		this.eAdposition = e_adposition;
	}

	/**
	 * @return the creative
	 */
	public String getCreative() {
		return creative;
	}

	/**
	 * @param creative the creative to set
	 */
	public void setCreative(String creative) {
		this.creative = creative;
	}

	/**
	 * @return the create_time
	 */
	public Date getCreateTime() {
		return createTime;
	}

	/**
	 * @param create_time the create_time to set
	 */
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getDayKey() {
		return dayKey;
	}

	public void setDayKey(String dayKey) {
		this.dayKey = dayKey;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getRelation() {
		return relation;
	}

	public void setRelation(String relation) {
		this.relation = relation;
	}

	public String getHelp() {
		return help;
	}

	public void setHelp(String help) {
		this.help = help;
	}

	public String getDisease() {
		return disease;
	}

	public void setDisease(String disease) {
		this.disease = disease;
	}

	public String getInfoUuid() {
		return infoUuid;
	}

	public void setInfoUuid(String infoUuid) {
		this.infoUuid = infoUuid;
	}

	public String getNote() {
		return note;
	}

	public void setNote(String note) {
		this.note = note;
	}

	public boolean isInviter() {
		return isInviter;
	}

	public void setInviter(boolean inviter) {
		isInviter = inviter;
	}

	public String getInviterMobile() {
		return inviterMobile;
	}

	public void setInviterMobile(String inviterMobile) {
		this.inviterMobile = inviterMobile;
	}
}
