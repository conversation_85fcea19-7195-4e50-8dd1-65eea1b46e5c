package com.shuidihuzhu.cf.model.crowdfunding;

import java.util.Date;

/**
 * Created by roy on 16/7/15.
 */
public class CrowdfundingPushHistory {

    private Integer id;
    private Integer crowdfundingId;
    private long userId;
    private String openId;
    private String nickname;
    private String payeeName;
    private String crowdfundingAuthorName;
    private String crowdfundingTitle;
    private String crowdfundingInfoId;
    private Integer amount;
    private Integer targetAmount;
    private String url;
    private String wxMessageTempleId;
    private Date createTime;

    public CrowdfundingPushHistory() {
    }

    public CrowdfundingPushHistory(Integer crowdfundingId,long userId) {
        this.crowdfundingId = crowdfundingId;
        this.userId = userId;
    }

    public CrowdfundingPushHistory(Integer crowdfundingId,long userId, String openId, String nickname, String crowdfundingAuthorName, String crowdfundingTitle, String crowdfundingInfoId, Integer amount, Integer targetAmount, String url) {
        this.crowdfundingId = crowdfundingId;
        this.userId = userId;
        this.openId = openId;
        this.nickname = nickname;
        this.crowdfundingAuthorName = crowdfundingAuthorName;
        this.crowdfundingTitle = crowdfundingTitle;
        this.crowdfundingInfoId = crowdfundingInfoId;
        this.amount = amount;
        this.targetAmount = targetAmount;
        this.url = url;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getCrowdfundingAuthorName() {
        return crowdfundingAuthorName;
    }

    public void setCrowdfundingAuthorName(String crowdfundingAuthorName) {
        this.crowdfundingAuthorName = crowdfundingAuthorName;
    }

    public String getCrowdfundingTitle() {
        return crowdfundingTitle;
    }

    public void setCrowdfundingTitle(String crowdfundingTitle) {
        this.crowdfundingTitle = crowdfundingTitle;
    }

    public String getCrowdfundingInfoId() {
        return crowdfundingInfoId;
    }

    public void setCrowdfundingInfoId(String crowdfundingInfoId) {
        this.crowdfundingInfoId = crowdfundingInfoId;
    }

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public Integer getTargetAmount() {
        return targetAmount;
    }

    public void setTargetAmount(Integer targetAmount) {
        this.targetAmount = targetAmount;
    }

    public String getUrl() {
        return url;
    }
    public void setUrl(String url) {
        this.url = url;
    }
    public Date getCreateTime() {
        return createTime;
    }
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    public Integer getId() {
        return id;
    }
    public void setId(Integer id) {
        this.id = id;
    }
    public Integer getCrowdfundingId() {
        return crowdfundingId;
    }
    public void setCrowdfundingId(Integer crowdfundingId) {
        this.crowdfundingId = crowdfundingId;
    }
    public long getUserId() {
        return userId;
    }
    public void setUserId(long userId) {
        this.userId = userId;
    }
    public String getWxMessageTempleId() {
        return wxMessageTempleId;
    }
    public void setWxMessageTempleId(String wxMessageTempleId) {
        this.wxMessageTempleId = wxMessageTempleId;
    }
	public String getPayeeName() {
		return payeeName;
	}
	public void setPayeeName(String payeeName) {
		this.payeeName = payeeName;
	}
    
}
