package com.shuidihuzhu.cf.model.dedicated;

import com.google.common.collect.Maps;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;

@Data
public class CfVolunteerMaterialModel {
    /**
     * 志愿者名字
     */
    private String volunteerName;
    /**
     * 一级职称id
     */
    private Integer firstProfessionalId;
    /**
     * 一级职称
     */
    private String firstProfessionalTitle;
    /**
     * 二级职称id
     */
    private Integer secondProfessionalId;
    /**
     * 二级职称名称
     */
    private String secondProfessionalTitle;

    /**
     * 医院名称
     */
    private String hospitalName;
    /**
     * 一级科室id
     */
    private Integer firstDepartmentId;
    /**
     * 一级科室
     */
    private String firstDepartment;
    /**
     * 二级科室id
     */
    private Integer secondDepartmentId;
    /**
     * 二级科室
     */
    private String secondDepartment;
    /**
     * 个人照片url
     */
    private List<String> personalPictureUrls;
    /**
     * 个人照片url map
     */
    private Map<String,String> personalPictureUrlsMap;
    /**
     * 认证照片map
     */
    private Map<String,String> verifyPictureUrlsMap;
    /**
     * 认证照片
     */
    private List<String> verifyPictureUrls;
    /**
     * 省份id
     */
    private Integer provinceId;
    /**
     * 城市id
     */
    private Integer cityId;
    /**
     * 下级城市id
     */
    private Integer childCityId;
    /**
     * 医院id
     */
    private Integer hospitalId;

    public void convertToPersonalPictureUrlsMap(){
        if(CollectionUtils.isNotEmpty(personalPictureUrls)){
            personalPictureUrlsMap = Maps.newHashMap();
            int i=1;
            for(String url:personalPictureUrls){
                personalPictureUrlsMap.put("pic"+i,url);
                i++;
            }
        }
    }

    public void convertToVerifyPictureUrlsMap(){
        if(CollectionUtils.isNotEmpty(verifyPictureUrls)){
            verifyPictureUrlsMap = Maps.newHashMap();
            int i=1;
            for(String url:verifyPictureUrls){
                verifyPictureUrlsMap.put("pic"+i,url);
                i++;
            }
        }
    }

}
