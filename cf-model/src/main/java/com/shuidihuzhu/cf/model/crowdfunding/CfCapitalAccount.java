package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.CfCapitalAccountEnum;
import com.shuidihuzhu.common.web.model.AbstractModel;
import com.shuidihuzhu.common.web.util.MoneyUtil;

import java.math.BigDecimal;
import java.util.Date;

public class CfCapitalAccount extends AbstractModel {

	private static final long serialVersionUID = -1L;

	private long id;
	private int caseId;
	private String infoUuid;
	private int payNum;
	private long payAmount;
	private int singleRefundNum;
	private long singleRefundAmount;
	private int allRefundNum;
	private long allRefundAmount;
	private int drawCashNum;
	private long drawCashAmount;
	/**
	 * 打款的服务费 打款成功累加
	 */
	private int serviceAmount;

	private int drawManualNum;
	private long drawManualAmount;
	/**
	 * 返还次数
	 */
	private int backNum;
	/**
	 * 返还金额含退票含服务费
	 */
	private long backAmount;

    /**
     * '后台打款账内预扣款笔数'
     */
    private int pretreatmentDrawNum;
    /**
     * '后台打款账内预扣款金额'
     */
    private int pretreatmentDrawAmount;

	private long surplusAmount;
    /**
     * 打款时累计产生的微信费用
     */
	private int feeAmount;

	/**
	 * 捐赠金额
	 */
	private long donateAmount;
	/**
	 * 捐赠手续费
	 */
	private int donateFeeAmount;



	/**
	 * 服务费规则
	 */
	private int serviceRuleId;
	/**
	 * 是否免除服务费
	 * {@link CfCapitalAccountEnum.RemoveServiceRule}
	 */
	private int removeServiceRule;

	/**
	 * 免除服务费比例配置ID
	 */
	private long removeServiceId;
	/**
	 * 退回服务费比例配置ID
	 */
	private long subsidyServiceId;

	private int isDelete;
	private Date createTime;
	private Date updateTime;


	
	public int getDonationNum() {
		return new Long(this.payNum - this.singleRefundNum).intValue();
	}
	
	public int getDonationAmountInFen() {
		return new Long(this.payAmount - this.singleRefundAmount).intValue();
	}

	public BigDecimal getDonationAmountInYuan() {
		return MoneyUtil.divide("" + getDonationAmountInFen(), "100", 2, BigDecimal.ROUND_HALF_UP);
	}

    /**
     * 有效金额--案例余额
     * @return
     */
	public int getFundsAmountInFen() {
	    if (backAmount != 0) {
	        return Long.valueOf(surplusAmount).intValue();
        }
		return new Long(this.payAmount - this.allRefundAmount - this.singleRefundAmount).intValue();
	}

    /**
     * 实际筹款金额
     * @return
     */
    public int getFundsAmountInFenToSea() {
        return new Long(this.payAmount - this.allRefundAmount - this.singleRefundAmount).intValue();
    }
	
	public BigDecimal getFundsAmountInYuan() {
		return MoneyUtil.divide("" + getFundsAmountInFenToSea(), "100", 2, BigDecimal.ROUND_HALF_UP);
	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public int getCaseId() {
		return caseId;
	}

	public void setCaseId(int caseId) {
		this.caseId = caseId;
	}

	public String getInfoUuid() {
		return infoUuid;
	}

	public void setInfoUuid(String infoUuid) {
		this.infoUuid = infoUuid;
	}

	public int getPayNum() {
		return payNum;
	}

	public void setPayNum(int payNum) {
		this.payNum = payNum;
	}

	public long getPayAmount() {
		return payAmount;
	}

	public void setPayAmount(long payAmount) {
		this.payAmount = payAmount;
	}

	public int getSingleRefundNum() {
		return singleRefundNum;
	}

	public void setSingleRefundNum(int singleRefundNum) {
		this.singleRefundNum = singleRefundNum;
	}

	public long getSingleRefundAmount() {
		return singleRefundAmount;
	}

	public void setSingleRefundAmount(long singleRefundAmount) {
		this.singleRefundAmount = singleRefundAmount;
	}

	public int getAllRefundNum() {
		return allRefundNum;
	}

	public void setAllRefundNum(int allRefundNum) {
		this.allRefundNum = allRefundNum;
	}

	public long getAllRefundAmount() {
		return allRefundAmount;
	}

	public void setAllRefundAmount(long allRefundAmount) {
		this.allRefundAmount = allRefundAmount;
	}

	public int getDrawCashNum() {
		return drawCashNum;
	}

	public void setDrawCashNum(int drawCashNum) {
		this.drawCashNum = drawCashNum;
	}

	public long getDrawCashAmount() {
		return drawCashAmount;
	}

	public void setDrawCashAmount(long drawCashAmount) {
		this.drawCashAmount = drawCashAmount;
	}

	public long getSurplusAmount() {
		return surplusAmount;
	}

	public void setSurplusAmount(long surplusAmount) {
		this.surplusAmount = surplusAmount;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public int getIsDelete() {
		return isDelete;
	}

	public void setIsDelete(int isDelete) {
		this.isDelete = isDelete;
	}

	public int getDrawManualNum() {
		return drawManualNum;
	}

	public void setDrawManualNum(int drawManualNum) {
		this.drawManualNum = drawManualNum;
	}

	public long getDrawManualAmount() {
		return drawManualAmount;
	}

	public void setDrawManualAmount(long drawManualAmount) {
		this.drawManualAmount = drawManualAmount;
	}

	public int getBackNum() {
		return backNum;
	}

	public void setBackNum(int backNum) {
		this.backNum = backNum;
	}

	public long getBackAmount() {
		return backAmount;
	}

	public void setBackAmount(long backAmount) {
		this.backAmount = backAmount;
	}

    public void setPretreatmentDrawNum(int pretreatmentDrawNum) {
        this.pretreatmentDrawNum = pretreatmentDrawNum;
    }

    public void setPretreatmentDrawAmount(int pretreatmentDrawAmount) {
        this.pretreatmentDrawAmount = pretreatmentDrawAmount;
    }

    public int getPretreatmentDrawNum() {
        return pretreatmentDrawNum;
    }

    public int getPretreatmentDrawAmount() {
        return pretreatmentDrawAmount;
    }

    public int getFeeAmount() {
        return feeAmount;
    }

    public void setFeeAmount(int feeAmount) {
        this.feeAmount = feeAmount;
    }

	public long getDonateAmount() {
		return donateAmount;
	}

	public void setDonateAmount(long donateAmount) {
		this.donateAmount = donateAmount;
	}

	public int getDonateFeeAmount() {
		return donateFeeAmount;
	}

	public void setDonateFeeAmount(int donateFeeAmount) {
		this.donateFeeAmount = donateFeeAmount;
	}


	public int getServiceAmount() {
		return serviceAmount;
	}

	public void setServiceAmount(int serviceAmount) {
		this.serviceAmount = serviceAmount;
	}

	public int getServiceRuleId() {
		return serviceRuleId;
	}

	public void setServiceRuleId(int serviceRuleId) {
		this.serviceRuleId = serviceRuleId;
	}


	public int getRemoveServiceRule() {
		return removeServiceRule;
	}

	public void setRemoveServiceRule(int removeServiceRule) {
		this.removeServiceRule = removeServiceRule;
	}

	public long getRemoveServiceId() {
		return removeServiceId;
	}

	public void setRemoveServiceId(long removeServiceId) {
		this.removeServiceId = removeServiceId;
	}

	public long getSubsidyServiceId() {
		return subsidyServiceId;
	}

	public void setSubsidyServiceId(long subsidyServiceId) {
		this.subsidyServiceId = subsidyServiceId;
	}

	/**
	 * 案例是否收取平台服务费
	 *
	 * @return -
	 */
	public boolean needServiceCharge() {
    	return this.getServiceRuleId() > 0 &&
			    (this.getRemoveServiceRule() != CfCapitalAccountEnum.RemoveServiceRule.REMOVE.getCode() || this.getRemoveServiceId() > 0);
	}
}
