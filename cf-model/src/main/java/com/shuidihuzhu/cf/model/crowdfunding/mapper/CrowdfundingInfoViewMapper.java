package com.shuidihuzhu.cf.model.crowdfunding.mapper;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoView;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoViewV2;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingTreatment;
import com.shuidihuzhu.cf.vo.CrowdfundingAuthorVo;
import com.shuidihuzhu.cf.vo.CrowdfundingAuthorVoV2;
import org.mapstruct.*;

import java.util.List;

/**
 * 众筹信息视图转换映射器
 * 使用MapStruct完成CrowdfundingInfoView到CrowdfundingInfoViewV2的转换
 */
@Mapper(componentModel = "spring")
public interface CrowdfundingInfoViewMapper {
    
    /**
     * 将CrowdfundingInfoView转换为CrowdfundingInfoViewV2
     * - 忽略敏感字段：payeeBankCard、payeeBankName、payeeIdCard、payeeMobile、payeeName
     * - 转换crowdfundingAuthor为CrowdfundingAuthorVoV2
     * - 处理treatment相关字段
     */
    @Mapping(target = "crowdfundingAuthor", source = "crowdfundingAuthor", qualifiedByName = "toCrowdfundingAuthorVoV2")
    CrowdfundingInfoViewV2 toV2(CrowdfundingInfoView source);
    
    /**
     * 将CrowdfundingInfoView列表转换为CrowdfundingInfoViewV2列表
     */
    List<CrowdfundingInfoViewV2> toV2List(List<CrowdfundingInfoView> source);
    
    /**
     * 将CrowdfundingAuthorVo转换为CrowdfundingAuthorVoV2
     * 删除敏感字段：cryptoIdCard、cryptoPhone、idCard、idType、mail、phone、qq
     */
    @Named("toCrowdfundingAuthorVoV2")
    CrowdfundingAuthorVoV2 toCrowdfundingAuthorVoV2(CrowdfundingAuthorVo source);
    
    /**
     * 处理CrowdfundingTreatment对象的转换
     * 设置id和crowdfundingId为-1
     */
    @AfterMapping
    default void handleTreatment(CrowdfundingInfoView source, @MappingTarget CrowdfundingInfoViewV2 target) {
        if (source.getCrowdfundingTreatment() != null) {
            CrowdfundingTreatment treatment = source.getCrowdfundingTreatment();
            treatment.setId(-1);
            treatment.setCrowdfundingId(-1);
        }
    }
} 