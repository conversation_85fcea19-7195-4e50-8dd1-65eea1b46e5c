package com.shuidihuzhu.cf.model.crowdfunding;

/**
 * Author: <PERSON>
 * Date: 2017/2/16 14:23
 */
public class CfTouFangSign {

	private Integer id;
	private String openId;
	private String mobile;
	private String channel;
	private String cryptoMobile;
	private String dayKey;
	private String channelType; //渠道类型,如"sem","wyyck","feed" {@see CfToufangChannelType}


	private String relation; //为谁筹款
	private String help; //需要什么帮助
	private String disease; //什么病

	//投放相关词
	private String source; //媒体来源
	private String match; //关键词模式
	private String keyword; //关键词跟踪参数
	private String semwp; //关键词类型
	private String account; //账户名称
	private String kwid; //keywordId
	private String creative; //creative
	private String eAdposition;

	private long clientIp;

	private String referer;
	private int isAuto = 1;

	private String primaryChannel;

	public Integer getIsAuto() {
		return isAuto;
	}

	public void setIsAuto(Integer isAuto) {
		this.isAuto = isAuto;
	}

	public String getDayKey() {
		return dayKey;
	}

	public void setDayKey(String dayKey) {
		this.dayKey = dayKey;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getOpenId() {
		return openId;
	}

	public void setOpenId(String openId) {
		this.openId = openId;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getCryptoMobile() {
		return cryptoMobile;
	}

	public void setCryptoMobile(String cryptoMobile) {
		this.cryptoMobile = cryptoMobile;
	}

	public String getChannel() {
		return channel;
	}

	public void setChannel(String channel) {
		this.channel = channel;
	}

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	public String getMatch() {
		return match;
	}

	public void setMatch(String match) {
		this.match = match;
	}

	public String getKeyword() {
		return keyword;
	}

	public void setKeyword(String keyword) {
		this.keyword = keyword;
	}

	public String getSemwp() {
		return semwp;
	}

	public void setSemwp(String semwp) {
		this.semwp = semwp;
	}

	public String getAccount() {
		return account;
	}

	public void setAccount(String account) {
		this.account = account;
	}

	public String getKwid() {
		return kwid;
	}

	public void setKwid(String kwid) {
		this.kwid = kwid;
	}

	public String getCreative() {
		return creative;
	}

	public void setCreative(String creative) {
		this.creative = creative;
	}

	public String geteAdposition() {
		return eAdposition;
	}

	public void seteAdposition(String eAdposition) {
		this.eAdposition = eAdposition;
	}

	public String getReferer() {
		return referer;
	}

	public void setReferer(String referer) {
		this.referer = referer;
	}

	public String getChannelType() {
		return channelType;
	}

	public void setChannelType(String channelType) {
		this.channelType = channelType;
	}

	public CfTouFangSign() {

	}

	public void setIsAuto(int isAuto) {
		this.isAuto = isAuto;
	}

	public String getRelation() {
		return relation;
	}

	public void setRelation(String relation) {
		this.relation = relation;
	}

	public String getHelp() {
		return help;
	}

	public void setHelp(String help) {
		this.help = help;
	}

	public String getDisease() {
		return disease;
	}

	public void setDisease(String disease) {
		this.disease = disease;
	}

	public long getClientIp() {
		return clientIp;
	}

	public void setClientIp(long clientIp) {
		this.clientIp = clientIp;
	}

	public String getPrimaryChannel() {
		return primaryChannel;
	}

	public void setPrimaryChannel(String primaryChannel) {
		this.primaryChannel = primaryChannel;
	}

	@Override
	public String toString() {
		return "CfTouFangSign{" +
				"id=" + id +
				", openId='" + openId + '\'' +
				", mobile='" + mobile + '\'' +
				", channel='" + channel + '\'' +
				", cryptoMobile='" + cryptoMobile + '\'' +
				", dayKey='" + dayKey + '\'' +
				", channelType='" + channelType + '\'' +
				", relation='" + relation + '\'' +
				", help='" + help + '\'' +
				", disease='" + disease + '\'' +
				", source='" + source + '\'' +
				", match='" + match + '\'' +
				", keyword='" + keyword + '\'' +
				", semwp='" + semwp + '\'' +
				", account='" + account + '\'' +
				", kwid='" + kwid + '\'' +
				", creative='" + creative + '\'' +
				", eAdposition='" + eAdposition + '\'' +
				", clientIp=" + clientIp +
				", referer='" + referer + '\'' +
				", isAuto=" + isAuto +
				", primaryChannel='" + primaryChannel + '\'' +
				", mediaId='" + mediaId + '\'' +
				'}';
	}
	//媒体信息
	private String mediaId;

	public String getMediaId() {
		return mediaId;
	}

	public void setMediaId(String mediaId) {
		this.mediaId = mediaId;
	}
}
