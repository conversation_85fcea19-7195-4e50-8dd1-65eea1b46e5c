package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.cf.mq.model.UserCaseInfoMessage;
import com.shuidihuzhu.common.web.util.MoneyUtil;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.RoundingMode;
import java.util.Date;

/**
 * @author: fengxuan
 * @create 2019-09-30 11:18
 **/
@Data
@NoArgsConstructor
public class CfUserCaseInfoDO {
    /**
     * amount:捐款金额,分,包含匿名捐款且未扣减退款
     * shareCount:转发次数
     * shareContributeAmount:转发带来的一级捐款金额
     * shareContributeDonator:转发带来的一级捐款人数
     * shareContributeFollower:转发带来的一级转发人数
     * shareContributeView:转发带来的一级访问人数
     */

    private int id;

    private int caseId;

    private long userId;

    private int amount;

    private int anonymousAmount;

    private int anonymousRefund;

    private int refund;

    private int shareCount;

    private Date shareContributeViewTime;

    private Date shareContributeDonateTime;

    private int shareContributeAmount;

    private int shareAnonymousAmount;

    private int shareAnonymousRefund;

    private int shareRefund;

    private int shareContributeDonator;

    private int shareContributeFollower;

    private int shareContributeView;

    private Date createTime;

    private Date updateTime;

    private int delete;

    /**
     * call this method after check data
     */
    public CfUserCaseInfoDO create(UserCaseInfoMessage message, long userId) {
        this.userId = userId;
        this.caseId = message.getCaseId();
        if (message.getAmount() != null) {
            this.amount = (int) MoneyUtil.changeYuanStrToFen(String.valueOf(message.getAmount()), RoundingMode.HALF_UP);
        }
        if (message.getAnonymousAmount() != null) {
            this.anonymousAmount = (int) MoneyUtil.changeYuanStrToFen(String.valueOf(message.getAnonymousAmount()), RoundingMode.HALF_UP);
        }
        if (message.getAnonymousRefund() != null) {
            this.anonymousRefund = (int) MoneyUtil.changeYuanStrToFen(String.valueOf(message.getAnonymousRefund()), RoundingMode.HALF_UP);
        }
        if (message.getRefund() != null) {
            this.refund = (int) MoneyUtil.changeYuanStrToFen(String.valueOf(message.getRefund()), RoundingMode.HALF_UP);
        }
        if (message.getShareCount() != null) {
            this.shareCount = message.getShareCount();
        }
        if (message.getShareContributeAmount() != null) {
            this.shareContributeAmount = (int) MoneyUtil.changeYuanStrToFen(String.valueOf(message.getShareContributeAmount()), RoundingMode.HALF_UP);
        }
        if (message.getShareAnonymousAmount() != null) {
            this.shareAnonymousAmount = (int) MoneyUtil.changeYuanStrToFen(String.valueOf(message.getShareAnonymousAmount()), RoundingMode.HALF_UP);
        }
        if (message.getShareAnonymousRefund() != null) {
            this.shareAnonymousRefund = (int) MoneyUtil.changeYuanStrToFen(String.valueOf(message.getShareAnonymousRefund()), RoundingMode.HALF_UP);
        }
        if (message.getShareRefund() != null) {
            this.shareRefund = (int) MoneyUtil.changeYuanStrToFen(String.valueOf(message.getShareRefund()), RoundingMode.HALF_UP);
        }
        if (message.getShareContributeViewTime() != null) {
            this.shareContributeViewTime = new Date(message.getShareContributeViewTime());
        }
        if (message.getShareContributeDonateTime() != null) {
            this.shareContributeDonateTime = new Date(message.getShareContributeDonateTime());
        }
        if (message.getShareContributeDonator() != null) {
            this.shareContributeDonator = message.getShareContributeDonator();
        }
        if (message.getShareContributeFollower() != null) {
            this.shareContributeFollower = message.getShareContributeFollower();
        }
        if (message.getShareContributeView() != null) {
            this.shareContributeView = message.getShareContributeView();
        }
        return this;
    }

}
