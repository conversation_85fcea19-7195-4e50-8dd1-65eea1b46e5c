package com.shuidihuzhu.cf.model;

import com.shuidihuzhu.cf.model.crowdfunding.CheckResult;
import lombok.Data;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class UserReportVO {
    private int type;//0官方公示1举报详情2代录入(筹款进展)
    private String title;
    private String reportName;
    private Date reportTime;
    private String reportType;
    private String reportDescribe;
    //举报进展
    private String reportProgress;
    private List<CheckResult> checkResults;
    // 案例筹款人的昵称
    private String nickName;
    // 案例筹款人的头像
    private String headImgUrl;
}
