package com.shuidihuzhu.cf.model;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * @author: <PERSON>
 * @date: 2018/3/13 15:55
 */
@Data
public class ConsumeBonusPoint {

	@NotNull
	@Min(0)
	private long userId;
	@NotNull
	@Min(0)
	private Integer bizType;
	@NotNull
	@Min(0)
	private Integer userThirdType;
	@NotNull
	@Min(0)
	private Integer points;
	@NotNull
	@Min(0)
	private Integer action;
	@NotBlank
	@Size(max = 40)
	private String outTradeNo;
	private String channel;
	private String remark;

}
