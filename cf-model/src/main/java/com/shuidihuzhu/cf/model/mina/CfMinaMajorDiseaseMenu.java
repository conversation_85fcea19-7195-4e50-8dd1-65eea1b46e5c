package com.shuidihuzhu.cf.model.mina;

import java.sql.Timestamp;

/**
 * Created by ahrievil on 2017/7/5.
 */
public class CfMinaMajorDiseaseMenu {
    private int id;
    private int diseaseId;
    private String menuName;
    private int menuOrder;
    private String menuImgUrl;
    private String describe;
    private Timestamp dateCreated;
    private Timestamp lastModified;

    public CfMinaMajorDiseaseMenu() {
    }

    public CfMinaMajorDiseaseMenu(int diseaseId, String menuName, int menuOrder, String menuImgUrl, String describe) {
        this.diseaseId = diseaseId;
        this.menuName = menuName;
        this.menuOrder = menuOrder;
        this.menuImgUrl = menuImgUrl;
        this.describe = describe;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getDiseaseId() {
        return diseaseId;
    }

    public void setDiseaseId(int diseaseId) {
        this.diseaseId = diseaseId;
    }

    public String getMenuName() {
        return menuName;
    }

    public void setMenuName(String menuName) {
        this.menuName = menuName;
    }

    public int getMenuOrder() {
        return menuOrder;
    }

    public void setMenuOrder(int menuOrder) {
        this.menuOrder = menuOrder;
    }

    public String getMenuImgUrl() {
        return menuImgUrl;
    }

    public void setMenuImgUrl(String menuImgUrl) {
        this.menuImgUrl = menuImgUrl;
    }

    public String getDescribe() {
        return describe;
    }

    public void setDescribe(String describe) {
        this.describe = describe;
    }

    public Timestamp getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(Timestamp dateCreated) {
        this.dateCreated = dateCreated;
    }

    public Timestamp getLastModified() {
        return lastModified;
    }

    public void setLastModified(Timestamp lastModified) {
        this.lastModified = lastModified;
    }
}
