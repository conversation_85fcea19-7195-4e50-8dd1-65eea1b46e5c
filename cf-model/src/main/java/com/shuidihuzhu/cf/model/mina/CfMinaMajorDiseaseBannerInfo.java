package com.shuidihuzhu.cf.model.mina;

import java.sql.Timestamp;

/**
 * Created by ahrievil on 2017/7/5.
 */
public class CfMinaMajorDiseaseBannerInfo {
    private int id;
    private int diseaseId;
    private String bannerImgUrl;
    private int bannerOrder;
    private String bannerText;
    private Timestamp dateCreated;
    private Timestamp lastModified;

    public CfMinaMajorDiseaseBannerInfo() {
    }

    public CfMinaMajorDiseaseBannerInfo(int diseaseId, String bannerImgUrl, int bannerOrder, String bannerText) {
        this.diseaseId = diseaseId;
        this.bannerImgUrl = bannerImgUrl;
        this.bannerOrder = bannerOrder;
        this.bannerText = bannerText;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getDiseaseId() {
        return diseaseId;
    }

    public void setDiseaseId(int diseaseId) {
        this.diseaseId = diseaseId;
    }

    public String getBannerImgUrl() {
        return bannerImgUrl;
    }

    public void setBannerImgUrl(String bannerImgUrl) {
        this.bannerImgUrl = bannerImgUrl;
    }

    public int getBannerOrder() {
        return bannerOrder;
    }

    public void setBannerOrder(int bannerOrder) {
        this.bannerOrder = bannerOrder;
    }

    public String getBannerText() {
        return bannerText;
    }

    public void setBannerText(String bannerText) {
        this.bannerText = bannerText;
    }

    public Timestamp getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(Timestamp dateCreated) {
        this.dateCreated = dateCreated;
    }

    public Timestamp getLastModified() {
        return lastModified;
    }

    public void setLastModified(Timestamp lastModified) {
        this.lastModified = lastModified;
    }
}
