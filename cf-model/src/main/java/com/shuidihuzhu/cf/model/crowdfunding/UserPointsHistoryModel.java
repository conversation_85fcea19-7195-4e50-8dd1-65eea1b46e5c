package com.shuidihuzhu.cf.model.crowdfunding;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.shuidihuzhu.cf.enums.crowdfunding.BonusPointAction;
import com.shuidihuzhu.cf.enums.crowdfunding.PointsStatus;
import com.shuidihuzhu.cf.enums.crowdfunding.UserPoinstHistoryType;
import com.shuidihuzhu.client.common.grpc.BizType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Author: liujiawei
 * @Date: 2018/8/7  13:46
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class UserPointsHistoryModel {
    @JsonIgnore
    private Long userId;

    private BizType bizType;

    private Integer userThirdType;

    private Integer points;

    private String action;

    private String outTradeNo;

    private Date createTime;

    private String channel;

    private String tradeNo;

    private PointsStatus status;

    private String description;

    private UserPoinstHistoryType type;

}
