package com.shuidihuzhu.cf.model.crowdfunding;


import com.shuidihuzhu.cf.enums.crowdfunding.CfBillingConstants;

import java.util.Date;

/**
 * Created by niejiangnan on 2017/8/30.
 */
public class CfDuiZhangBillingMail {
    private long id;
    private String orderId="";
    private Date orderTime;
    private int orderAmount;
    private String thirdOrderId="";
    private Date thirdOrderTime;
    private int thirdOrderAmount;
    private int thirdOrderExistStatus;
    private int OrderExistStatus;
    private int billingFailReason;
    private long batchId;
    private Date createTime;
    private Date updateTime;

    public CfDuiZhangBillingMail(long id, String orderId, Date orderTime, int orderAmount, String thirdOrderId, Date thirdOrderTime, int thirdOrderAmount, int thirdOrderExistStatus, int orderExistStatus, CfBillingConstants.BillingFailReason billingFailReason, long batchId, Date createTime, Date updateTime) {
        this.id = id;
        this.orderId = orderId;
        this.orderTime = orderTime;
        this.orderAmount = orderAmount;
        this.thirdOrderId = thirdOrderId;
        this.thirdOrderTime = thirdOrderTime;
        this.thirdOrderAmount = thirdOrderAmount;
        this.thirdOrderExistStatus = thirdOrderExistStatus;
        OrderExistStatus = orderExistStatus;
        this.billingFailReason = billingFailReason.getCode();
        this.batchId = batchId;
        this.createTime = createTime;
        this.updateTime = updateTime;
    }

    public CfDuiZhangBillingMail() {
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public Date getOrderTime() {
        return orderTime;
    }

    public void setOrderTime(Date orderTime) {
        this.orderTime = orderTime;
    }

    public int getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(int orderAmount) {
        this.orderAmount = orderAmount;
    }

    public String getThirdOrderId() {
        return thirdOrderId;
    }

    public void setThirdOrderId(String thirdOrderId) {
        this.thirdOrderId = thirdOrderId;
    }

    public Date getThirdOrderTime() {
        return thirdOrderTime;
    }

    public void setThirdOrderTime(Date thirdOrderTime) {
        this.thirdOrderTime = thirdOrderTime;
    }

    public int getThirdOrderAmount() {
        return thirdOrderAmount;
    }

    public void setThirdOrderAmount(int thirdOrderAmount) {
        this.thirdOrderAmount = thirdOrderAmount;
    }

    public int getThirdOrderExistStatus() {
        return thirdOrderExistStatus;
    }

    public void setThirdOrderExistStatus(int thirdOrderExistStatus) {
        this.thirdOrderExistStatus = thirdOrderExistStatus;
    }

    public int getOrderExistStatus() {
        return OrderExistStatus;
    }

    public void setOrderExistStatus(int orderExistStatus) {
        OrderExistStatus = orderExistStatus;
    }

    public int getBillingFailReason() {
        return billingFailReason;
    }

    public void setBillingFailReason(int billingFailReason) {
        this.billingFailReason = billingFailReason;
    }

    public long getBatchId() {
        return batchId;
    }

    public void setBatchId(long batchId) {
        this.batchId = batchId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
