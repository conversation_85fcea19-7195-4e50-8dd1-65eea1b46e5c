package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @package: com.shuidihuzhu.baseservice.userpoints.model
 * @Author: l<PERSON><PERSON>aw<PERSON>
 * @Date: 2018/8/9  15:25
 */
@NoArgsConstructor
@Data
@AllArgsConstructor
public class UserAccountsModel {
    private long userId;
    private Integer allPoints = 0;//现有积分
    private Integer bonusPoints = 0;//收入
    private Integer consumePoints = 0;//支出
    private boolean isInit = false;//是否被构造

    public UserAccountsModel(long userId, Integer allPoints, Integer bonusPoints, Integer consumePoint){
        this.userId = userId;
        this.allPoints = allPoints;
        this.bonusPoints = bonusPoints;
        this.consumePoints = consumePoint;
        this.isInit = true;
    }

    public boolean isBlance() {
        if (this.isInit && (this.allPoints == this.bonusPoints - this.consumePoints)) {
            return true;
        }
        return false;
    }

    @Override
    public String toString() {
        return "用户详情：{" +
                "userId=" + userId +
                ", 当前积分：" + allPoints +
                ", 收入积分：" + bonusPoints +
                ", 消费积分：" + consumePoints +
                ", 差额(a-b+c)：" + (allPoints -(bonusPoints - consumePoints)) +
                '}';
    }
}
