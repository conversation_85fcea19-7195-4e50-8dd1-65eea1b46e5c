package com.shuidihuzhu.cf.model.miniprogram;

public class CfTopicShareCommentCount {
    private int id;
    private int topicId;
    private int commentCount;
    private int shareCount;
    private int praiseCount;

    public CfTopicShareCommentCount() {
    }

    public CfTopicShareCommentCount(int topicId, int commentCount, int shareCount, int praiseCount) {
        this.topicId = topicId;
        this.commentCount = commentCount;
        this.shareCount = shareCount;
        this.praiseCount = praiseCount;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getTopicId() {
        return topicId;
    }

    public void setTopicId(int topicId) {
        this.topicId = topicId;
    }

    public int getCommentCount() {
        return commentCount;
    }

    public void setCommentCount(int commentCount) {
        this.commentCount = commentCount;
    }

    public int getShareCount() {
        return shareCount;
    }

    public void setShareCount(int shareCount) {
        this.shareCount = shareCount;
    }

    public int getPraiseCount() {
        return praiseCount;
    }

    public void setPraiseCount(int praiseCount) {
        this.praiseCount = praiseCount;
    }

    @Override
    public String toString() {
        return "CfTopicShareCommentCount{" +
                "id=" + id +
                ", topicId=" + topicId +
                ", commentCount=" + commentCount +
                ", shareCount=" + shareCount +
                ", praiseCount=" + praiseCount +
                '}';
    }
}
