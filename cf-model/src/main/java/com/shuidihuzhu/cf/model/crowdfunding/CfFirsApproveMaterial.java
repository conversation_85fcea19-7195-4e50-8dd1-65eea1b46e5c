package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.cf.client.material.model.preSubmit.CfMaterialPreModifySign;
import com.shuidihuzhu.cf.enums.raise.MedicalImageTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoBaseVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Author: duchao
 * @Date: 2018/8/4 下午3:34
 * <p>
 * 初次审核的资料
 */
@Data
public class CfFirsApproveMaterial {
    private int id;
    private long userId;
    private int date;
    private long ip;
    private int infoId;
    private String infoUuid;
    private String selfRealName;
    private String selfCryptoIdcard;
    private String patientRealName;
    private String patientCryptoIdcard;
    private boolean patientHasIdCard;
    private int userRelationType;
    private String imageUrl;
    /**
     * {@link MedicalImageTypeEnum}
     */
    private int imageUrlType;
    private int status;
    private String targetAmountDesc;
    @Deprecated
    private int rejectReasonType;
    @Deprecated
    private String rejectMessage;
    private Date createTime;
    private Date updateTime;
    // 0 默认  1  是  2 否
    private Integer poverty;

    @ApiModelProperty("建档立卡脱贫户  补充材料 eg: url1,url2,url3")
    private String povertyImageUrl;

    private String patientBornCard;

    private int patientIdType;
    private int userRelationTypeForC;  // 3100版本 C端用的关系字段

    @ApiModelProperty("代修改情况")
    private CfMaterialPreModifySign modifySign;

    public CfFirsApproveMaterial() {
        this.infoUuid = "";
        this.selfRealName = "";
        this.selfCryptoIdcard = "";
        this.patientCryptoIdcard = "";
        this.patientRealName = "";
        this.imageUrl = "";
        this.status = 0;
        this.patientBornCard = "";
        this.patientIdType = 0;
    }

}
