package com.shuidihuzhu.cf.model.crowdfunding.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@ApiModel
public class TotalCasesVo {

    private String totalAmount;

    private String donationCount;

    private String donateOrderCount;

    private String onceMaxCrowfundingCount;

    private String caseCount;

    private String onceMaxCrowfundingCountFormat;

    private String caseCountShort;

    private String investedAmount;

}
