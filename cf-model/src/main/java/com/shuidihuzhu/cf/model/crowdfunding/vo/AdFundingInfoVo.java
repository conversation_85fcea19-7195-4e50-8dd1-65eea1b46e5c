package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfCreditSupplementVo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingBaseInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingPayeeBaseInfoVo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingTreatmentInfo;
import com.shuidihuzhu.cf.param.UserAccessParam;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@ApiModel
public class AdFundingInfoVo {

    private CrowdfundingBaseInfo baseInfo;

    @JsonProperty("hasFinished")
    private Boolean hasFinished;

    private Integer applyRefundStatus;

    @JsonProperty("baseInfoRejected")
    private Boolean baseInfoRejected;

    private UserAccessParam userAccessParam;

    @JsonProperty("isHelp")
    private Boolean help;

    private Integer dataStatus;

    private Integer displayStatus;

    private Integer applyDrawStatus;

    private List<CfCreditSupplementVo> creditSupplements = new ArrayList<>();

    private Integer shareCount;

    private UserInfoModel crowdfundingUserInfo;

    @JsonProperty("extInfoRejected")
    private Boolean extInfoRejected;

    private CrowdfundingPayeeBaseInfoVo payeeInfo;

    @JsonProperty("hasVerified")
    private Boolean hasVerified;

    private Map<Integer, Integer> infoDataStatus = new HashMap<>();

    private Integer rejectType;

    private CrowdfundingTreatmentInfo treatmentInfo;

    private Integer thirdType;

    @JsonProperty("isSelf")
    private Boolean self;
    private String purpose;
    private Integer originatorType;


}
