package com.shuidihuzhu.cf.model;

import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;

import java.util.Date;
import java.util.List;

/**
 * @author: fengxuan
 * @create 2020-01-09 15:49
 **/
@ApiModel("sea后台下发动作,一次下发对应一条记录")
@Data
public class CfInfoSupplyAction {

    @ApiModelProperty("下发记录id")
    private long id;

    @ApiModelProperty("案例id")
    private int caseId;

    /**
     * @see ActionType
     */
    @ApiModelProperty("下发操作类型")
    private int actionType;

    @ApiModelProperty("下发操作的处理状态")
    private int handleStatus;

    /**
     * @see SupplyHandleStatus
     */
    @ApiModelProperty("下发人")
    private int supplyUserId;

    @ApiModelProperty("下发组织")
    private int supplyOrgId;

    @ApiModelProperty
    private String supplyOrgName;

    /**
     * 具体使用参考
     */
    @ApiModelProperty("下发原因,对应的勾选项")
    private String supplyReason;

    @ApiModelProperty("对固定下发原因的补充")
    private String comment;

    private Date createTime;

    @ApiModelProperty("更新时间")
    private Date updateTime;

    private boolean delete;


    @Getter
    public enum SupplyHandleStatus {
        init(0, "已下发"),
        wait_audit(1, "待审核"),
        pass(2, "审核通过"),
        reject(3, "审核驳回"),
        cancel(4, "已撤销"),

        ;
        private int code;
        private String desc;

        SupplyHandleStatus(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }


    @Getter
    public enum ActionType {
        progress(1, "下发动态审核"),
        ;
        private int code;
        private String desc;

        ActionType(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    public static SupplyHandleStatus findHandleStatusByCode(int code) {
        for (SupplyHandleStatus value : SupplyHandleStatus.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }


    private static final List<SupplyHandleStatus> user_can_submit_status = Lists.newArrayList(
            SupplyHandleStatus.init,
            SupplyHandleStatus.reject
    );

    /**
     * 用户是否能提交 = 链接是否失效
     * true:能提交
     */
    public static boolean canSubmit(SupplyHandleStatus handleResultEnum) {
        return user_can_submit_status.contains(handleResultEnum);
    }


    public enum RejectItemEnum {

        reject_1(1, "部分审核后通过修改材料"),
        reject_2(2, "患者去世"),
        reject_3(3, "文珍不符"),
        reject_4(4, "无法提供诊断证明"),
        reject_5(5, "二次票据不足或未上传二次票据"),
        reject_6(6, "诊断证明无公章(有相同住院号或其他辅助材料)"),
        reject_7(7, "为多人筹款(无法确定为谁筹款)"),
        reject_8(8, "无法提供关系证明且无法对公"),
        reject_9(9, "增信内容不符"),
        reject_10(10, "患者姓名与诊断不符(错别字)"),
        ;

        private int code;
        private String desc;

        RejectItemEnum(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }
    }

    public static List<RejectItemEnum> getPushReason(String supplyReason) {

        List<RejectItemEnum> list = Lists.newArrayList();

        Lists.newArrayList(supplyReason.split(",")).forEach(item -> {
            for (RejectItemEnum value : RejectItemEnum.values()) {
                try {
                    if (value.getCode() == Integer.parseInt(item)) {
                        list.add(value);
                    }
                } catch (Exception e) {
                    return;
                }
            }
        });
        return list;
    }


}
