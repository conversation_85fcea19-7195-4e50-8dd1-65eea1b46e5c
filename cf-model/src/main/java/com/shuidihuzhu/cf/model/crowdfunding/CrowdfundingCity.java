package com.shuidihuzhu.cf.model.crowdfunding;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.shuidihuzhu.common.web.model.AbstractModel;
import com.shuidihuzhu.eb.grafana.configuration.plugin.response.check.annotation.IgnoreCheckSensitive;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class CrowdfundingCity extends AbstractModel {

    private static final long serialVersionUID = -8630532560700429102L;

    @IgnoreCheckSensitive
    private int id;
    private String code;
    private String name;

    @IgnoreCheckSensitive
    private int parentId;
    private String firstLetter;
    private int level;
    private int valid;

    @IgnoreCheckSensitive
    private int realParentId;
    private int realLevel;

    /**
     * 新城市名称和code json结构，当城市信息改变后需要增加一个新版本信息，名称与code必须对应
     * json结构举例:
     * 1，2，3主要用于区分版本，越大代表城市信息最新
     * {
     *    "1": {
     *       "newName": "北京1",
     *       "newCode": "1111"
     *    },
     *    "2": {
     *       "newName": "北京2",
     *       "newCode": "2222"
     *    }
     * }
     */
    private String newCityJsonInfoStr;

    @ApiModelProperty("排序后的新城市信息集合 按照城市最新到最老版本排序")
    private List<NewCityVersionInfo> newCityVersionInfoList;

    @ApiModelProperty("最新版本的城市信息")
    private NewCityVersionInfo latestCityInfo;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getParentId() {
        return parentId;
    }

    public void setParentId(int parentId) {
        this.parentId = parentId;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public String getFirstLetter() {
        return firstLetter;
    }

    public void setFirstLetter(String firstLetter) {
        this.firstLetter = firstLetter;
    }

    public int getValid() {
        return valid;
    }

    public void setValid(int valid) {
        this.valid = valid;
    }

    public void setRealParentId(int realParentId) {
        this.realParentId = realParentId;
    }

    public int getRealParentId(){
        return realParentId;
    }

    public void setRealLevel(int realLevel){
        this.realLevel = realLevel;
    }

    public int getRealLevel(){
        return realLevel;
    }

    public void setNewCityJsonInfoStr(String newCityJsonInfoStr){
        this.newCityJsonInfoStr = newCityJsonInfoStr;
    }

    public String getNewCityJsonInfoStr() {
        return newCityJsonInfoStr;
    }

    public void setNewCityVersionInfoList(List<NewCityVersionInfo> newCityVersionInfoList) {
        this.newCityVersionInfoList = newCityVersionInfoList;
    }

    public List<NewCityVersionInfo> getNewCityVersionInfoList() {
        Map<Integer, NewCityVersionInfo> cityVersionInfoMap = convertNewCityJsonInfoStrToMap();
        if (cityVersionInfoMap == null) {
            return Lists.newArrayList();
        }
        return cityVersionInfoMap.entrySet()
                .stream().sorted(Map.Entry.comparingByKey(Comparator.reverseOrder()))
                .map(Map.Entry::getValue)
                .collect(Collectors.toList());
    }

    public void setLatestCityInfo(NewCityVersionInfo latestCityInfo) {
        this.latestCityInfo = latestCityInfo;
    }

    public NewCityVersionInfo getLatestCityInfo() {
        Map<Integer, NewCityVersionInfo> cityVersionInfoMap = convertNewCityJsonInfoStrToMap();
        if (cityVersionInfoMap == null) {
           return new NewCityVersionInfo();
        }
        Integer maxKey = cityVersionInfoMap.keySet()
                .stream()
                .max(Integer::compareTo)
                .orElse(0);
        return cityVersionInfoMap.getOrDefault(maxKey, new NewCityVersionInfo());
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class NewCityVersionInfo {

        @ApiModelProperty("新城市名称")
        private String newName;

        @ApiModelProperty("新城市行政区划代码")
        private String newCode;
    }

    /**
     * 新城市信息json数据转换
     * @return Map<Integer, NewCityVersionInfo>
     */
    public Map<Integer, NewCityVersionInfo> convertNewCityJsonInfoStrToMap() {
        Map<Integer, NewCityVersionInfo> cityVersionInfoMap = null;
        if (StringUtils.isBlank(newCityJsonInfoStr)) {
            return null;
        }
        try {
            TypeReference<Map<Integer, CrowdfundingCity.NewCityVersionInfo>> typeReference = new TypeReference<>(){};
            cityVersionInfoMap = JSONObject.parseObject(newCityJsonInfoStr, typeReference);
        } catch (Exception e) {
            log.error("convertNewCityJsonInfoStrToMap transform newCityJsonInfoStr to map error: {}", newCityJsonInfoStr, e);
        }
        return cityVersionInfoMap;
    }

    /**
     * 根据城市名称校验是否存在新城市名称中
     * @param cityName
     * @return
     */
    public boolean containNewCityName(String cityName) {
        return getNewCityVersionInfoList().stream().anyMatch(newCityVersionInfo -> newCityVersionInfo.getNewName().equals(cityName));
    }

    /**
     * 根据城市code校验是否存在新城市code中
     * @param cityCode
     * @return
     */
    public boolean containNewCityCode(String cityCode) {
        return getNewCityVersionInfoList().stream().anyMatch(newCityVersionInfo -> newCityVersionInfo.getNewCode().equals(cityCode));
    }

    /**
     * 根据城市名称校验是否存在新城市名称中
     * @param cityNames
     * @return
     */
    public boolean containNewCityNames(List<String> cityNames) {
        if (CollectionUtils.isEmpty(cityNames)) {
            return false;
        }
        return getNewCityVersionInfoList().stream().anyMatch(newCityVersionInfo -> cityNames.contains(newCityVersionInfo.getNewName()));
    }

    /**
     * 根据城市code校验是否存在新城市code中
     * @param cityCodes
     * @return
     */
    public boolean containNewCityCodes(List<String> cityCodes) {
        if (CollectionUtils.isEmpty(cityCodes)) {
            return false;
        }
        return getNewCityVersionInfoList().stream().anyMatch(newCityVersionInfo -> cityCodes.contains(newCityVersionInfo.getNewCode()));
    }

}
