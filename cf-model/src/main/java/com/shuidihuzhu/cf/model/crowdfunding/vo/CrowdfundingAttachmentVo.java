package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * Created by wux<PERSON><PERSON> on 6/21/16.
 * Modified by <PERSON> on 2016-09-12 for V3
 */
public class CrowdfundingAttachmentVo implements Comparable {
	private int type;

	private String url;

	private Integer sequence;

	private int id;

	// 与其它案例有重复的
	private List<CaseIdAndInfoId> repeatCases;
	// 图片是ps
	private int ps;
	// 图片有水印
	private int watermark;
	/**
	 * 创建时间
	 */
	private long createtime;

	public long getCreatetime() {
		return createtime;
	}

	public void setCreatetime(long createtime) {
		this.createtime = createtime;
	}

	@Data
	@AllArgsConstructor
	public static class CaseIdAndInfoId {
		private int caseId;
		private String infoUuid;
	}

	public CrowdfundingAttachmentVo() {
	}

	public CrowdfundingAttachmentVo(CrowdfundingAttachmentVo crowdfundingAttachmentVo) {
		this.type = crowdfundingAttachmentVo.getType();
		this.url = crowdfundingAttachmentVo.getUrl();
		this.ps = crowdfundingAttachmentVo.getPs();
		this.createtime = crowdfundingAttachmentVo.getCreatetime();
		this.watermark = crowdfundingAttachmentVo.getWatermark();
		this.repeatCases = crowdfundingAttachmentVo.getRepeatCases();
		this.sequence = crowdfundingAttachmentVo.getSequence();
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public void setSequence(int sequence) {
		this.sequence = sequence;
	}

	public void setType(int type) {
		this.type = type;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public CrowdfundingAttachmentVo(CrowdfundingAttachment crowdfundingAttachment) {
		AttachmentTypeEnum typeEnum = crowdfundingAttachment.getType();
		if (typeEnum != null) {
			type = typeEnum.ordinal();
		}
		url = crowdfundingAttachment.getUrl();
		sequence = crowdfundingAttachment.getSequence();
		id=crowdfundingAttachment.getId();
		if(crowdfundingAttachment.getCreateTime()!=null) {
			createtime = crowdfundingAttachment.getCreateTime().getTime();
		}
	}

	public int getType() {
		return type;
	}

	public String getUrl() {
		return url;
	}

	public Integer getSequence() {
		return sequence;
	}

	public void setSequence(Integer sequence) {
		this.sequence = sequence;
	}

	public List<CaseIdAndInfoId> getRepeatCases() {
		return repeatCases;
	}

	public void setRepeatCases(List<CaseIdAndInfoId> repeatCases) {
		this.repeatCases = repeatCases;
	}

	public int getPs() {
		return ps;
	}

	public void setPs(int ps) {
		this.ps = ps;
	}

	public int getWatermark() {
		return watermark;
	}

	public void setWatermark(int watermark) {
		this.watermark = watermark;
	}

	@Override
	public int compareTo(Object o) {
		CrowdfundingAttachmentVo crowdfundingAttachment = (CrowdfundingAttachmentVo) o;
		if(this.id < crowdfundingAttachment.getId()){
			return -1;
		}else {
			return 1;
		}
	}
}
