package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.cf.model.gdmap.GdMapPoiReduceModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserGainGpsInfoRecordExecl{
    /**
     * 主键Id
     */
    private long id;
    /**
     * 用户Id
     */
    private long userId;
    /**
     * 收集节点
     */
    private int gainNode;
    /**
     * 收集经度
     */
    private String gpsLongitude;
    /**
     * 收集经度
     */
    private String gpsLatitude;
    /**
     * 收集时间
     */
    private Date gpsTime;
    /**
     * 周围数量
     */
    private int size;
    /**
     * 周围信息
     */
    private String gdMapPoiReduceModels;
}
