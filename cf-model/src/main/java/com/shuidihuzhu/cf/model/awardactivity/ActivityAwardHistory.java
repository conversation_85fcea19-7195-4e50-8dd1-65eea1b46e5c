package com.shuidihuzhu.cf.model.awardactivity;

import com.shuidihuzhu.common.web.model.AbstractModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * Created by chao on 16/9/12.
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@ToString
public class ActivityAwardHistory extends AbstractModel {
	private Integer id;

	private long userId;

	private Integer awardId;

	private Integer activityId;

	private Date addTime;
	private String content;
	private Integer status;
	private String tradeNo;
	private Integer payType;
}
