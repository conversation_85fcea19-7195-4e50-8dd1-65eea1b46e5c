package com.shuidihuzhu.cf.model.crowdfunding.material;

import com.google.common.collect.Sets;
import lombok.Data;
import org.apache.commons.collections4.MapUtils;

import java.util.Map;
import java.util.Set;

@Data
public class AuditSuggestModifyDetail {

    private static final String SUGGEST_FORMAT = "建议用户修改: %s";

    private int materialId;

    private Map<Integer, Set<Integer>> rejectTModify;

    private String suggestUserModify;

    private Set<String> rejectMsgs;

    public static String getFormatSuggestView(String reject) {
        return String.format(SUGGEST_FORMAT, reject);
    }


    public Set<Integer> getAllModifyIds() {

        Set<Integer> modifyIds = Sets.newHashSet();
        if (MapUtils.isNotEmpty(rejectTModify)) {
            for (Map.Entry<Integer, Set<Integer>> entry : rejectTModify.entrySet()) {
                modifyIds.addAll(entry.getValue());
            }
        }

        return modifyIds;
    }

}
