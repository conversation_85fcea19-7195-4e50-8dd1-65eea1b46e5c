package com.shuidihuzhu.cf.model.crowdfunding;

/**
 * Created by wangsf on 17/3/17.
 */
public class CfCaseFromInference {

	private int method;
	private String key1;
	private String key2;
	private String from;
	private String detail;

	public int getMethod() {
		return method;
	}

	public void setMethod(int method) {
		this.method = method;
	}

	public String getKey1() {
		return key1;
	}

	public void setKey1(String key1) {
		this.key1 = key1;
	}

	public String getKey2() {
		return key2;
	}

	public void setKey2(String key2) {
		this.key2 = key2;
	}

	public String getFrom() {
		return from;
	}

	public void setFrom(String from) {
		this.from = from;
	}

	public String getDetail() {
		return detail;
	}

	public void setDetail(String detail) {
		this.detail = detail;
	}

	@Override
	public String toString() {
		return "CfCaseFromInference{" +
				"method=" + method +
				", key1='" + key1 + '\'' +
				", key2='" + key2 + '\'' +
				", from='" + from + '\'' +
				", detail='" + detail + '\'' +
				'}';
	}
}
