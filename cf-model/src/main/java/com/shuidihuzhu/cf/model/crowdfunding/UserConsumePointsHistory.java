package com.shuidihuzhu.cf.model.crowdfunding;

import java.util.Date;

public class UserConsumePointsHistory {
    private Long id;

    private long userId;

    private Integer bizType;

    private Integer userThirdType;

    private Integer points;

    private Integer status;

    private Integer action;

    private String outTradeNo;

    private Date createTime;

    private Date updateTime;

    private Boolean isDelete;

    private String channel;

    private String remark;
    private String tradeNo;

    public UserConsumePointsHistory(Long id, long userId, Integer bizType, Integer userThirdType, Integer points, Integer status,
                                    Integer action, String outTradeNo, Date createTime, Date updateTime, Boolean isDelete, String channel,
                                    String remark, String tradeNo) {
        this.id = id;
        this.userId = userId;
        this.bizType = bizType;
        this.userThirdType = userThirdType;
        this.points = points;
        this.status = status;
        this.action = action;
        this.outTradeNo = outTradeNo;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.isDelete = isDelete;
        this.channel = channel;
        this.remark = remark;
        this.tradeNo = tradeNo;
    }

    public UserConsumePointsHistory() {
        super();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public Integer getBizType() {
        return bizType;
    }

    public void setBizType(Integer bizType) {
        this.bizType = bizType;
    }

    public Integer getUserThirdType() {
        return userThirdType;
    }

    public void setUserThirdType(Integer userThirdType) {
        this.userThirdType = userThirdType;
    }

    public Integer getPoints() {
        return points;
    }

    public void setPoints(Integer points) {
        this.points = points;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getAction() {
        return action;
    }

    public void setAction(Integer action) {
        this.action = action;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo == null ? null : outTradeNo.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Boolean getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Boolean isDelete) {
        this.isDelete = isDelete;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel == null ? null : channel.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }
}