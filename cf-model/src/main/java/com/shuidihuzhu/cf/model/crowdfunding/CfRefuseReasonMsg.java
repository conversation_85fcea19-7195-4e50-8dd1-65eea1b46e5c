package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.sql.Timestamp;

/**
 * Created by Ahrievil on 2017/7/31
 */
@Data
public class CfRefuseReasonMsg {
    private long id;
    private String infoUuid;
    private int type;
    private String reasonIds;
    private String itemIds;
    private String itemReason;
    private String suggestModify;
    private int refuseCount;
    private int disable;
    private int isDelete;
    private Timestamp createTime;
    private Timestamp updateTime;

    public CfRefuseReasonMsg() {
    }

    public CfRefuseReasonMsg(String infoUuid, int type, String reasonIds, String itemIds, String itemReason, String suggestModify) {
        this.infoUuid = infoUuid;
        this.type = type;
        this.reasonIds = reasonIds;
        this.itemIds = itemIds;
        this.itemReason = itemReason;
        this.suggestModify = StringUtils.trimToEmpty(suggestModify);
    }

    public CfRefuseReasonMsg(String infoUuid, int type, String reasonIds, String itemIds, String itemReason) {
        this.infoUuid = infoUuid;
        this.type = type;
        this.reasonIds = reasonIds;
        this.itemIds = itemIds;
        this.itemReason = itemReason;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
