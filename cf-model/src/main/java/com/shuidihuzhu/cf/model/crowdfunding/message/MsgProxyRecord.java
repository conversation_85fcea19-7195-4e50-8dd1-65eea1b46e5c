package com.shuidihuzhu.cf.model.crowdfunding.message;

import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @time 2019/3/28 上午11:09
 * @desc
 */
@Data
public class MsgProxyRecord {
    /**
     * 以下属性含义和com.shuidihuzhu.msg.vo.rpc.MsgRecord保持一至
     */
    private Map<Integer, String> params;
    private Map<String, String> urlParams;
    private Map<String, String> tagParams;
    private long userId;
    private String cryptoMobile;
    private int userThirdType;

    /**
     * infoUuid:案例的uuid 如果发送的消息和案例有关则必传
     * userSourceId:转发该案例的userId 如果该消息和转发人有关则必传
     */
    private String infoUuid;
    private long userSourceId;
}
