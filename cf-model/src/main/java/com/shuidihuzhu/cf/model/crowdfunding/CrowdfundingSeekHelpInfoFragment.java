package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingSeekHelpInfoFragmentVo;

import java.util.Date;

/**
 * Created by chao on 2017/8/6.
 */
public class CrowdfundingSeekHelpInfoFragment {
	private int id;
	private long userId;
	private String infoUuid;

	private String patientIntro;
	private String disease;
	private String diagnoseIntro;
	private String wantToSay;

	private Date createTime;
	private Date updateTime;

	public CrowdfundingSeekHelpInfoFragment(CrowdfundingSeekHelpInfoFragmentVo vo) {
		this.patientIntro = vo.getPatientIntro();
		this.disease = vo.getDiseaseName();
		this.diagnoseIntro = vo.getMedicalRecords();
		this.wantToSay = vo.getDesc();
	}

	public CrowdfundingSeekHelpInfoFragment() {
		this.patientIntro = "";
		this.diagnoseIntro = "";
		this.wantToSay = "";
		this.disease = "";
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}

	public String getInfoUuid() {
		return infoUuid;
	}

	public void setInfoUuid(String infoUuid) {
		this.infoUuid = infoUuid;
	}

	public String getPatientIntro() {
		return patientIntro;
	}

	public void setPatientIntro(String patientIntro) {
		this.patientIntro = patientIntro;
	}

	public String getDisease() {
		return disease;
	}

	public void setDisease(String disease) {
		this.disease = disease;
	}

	public String getDiagnoseIntro() {
		return diagnoseIntro;
	}

	public void setDiagnoseIntro(String diagnoseIntro) {
		this.diagnoseIntro = diagnoseIntro;
	}

	public String getWantToSay() {
		return wantToSay;
	}

	public void setWantToSay(String wantToSay) {
		this.wantToSay = wantToSay;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
}
