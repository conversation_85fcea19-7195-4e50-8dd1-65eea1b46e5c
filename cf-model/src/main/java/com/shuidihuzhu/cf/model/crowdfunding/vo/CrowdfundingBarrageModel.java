package com.shuidihuzhu.cf.model.crowdfunding.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class CrowdfundingBarrageModel implements Serializable {

    @ApiModelProperty("发起人省份")
    private String province;

    @ApiModelProperty("发起人头像")
    private String headImgUri;

    @ApiModelProperty("筹款金额")
    private int amount;

    @ApiModelProperty("患者姓名")
    private String patientName;
}
