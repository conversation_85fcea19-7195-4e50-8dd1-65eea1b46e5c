package com.shuidihuzhu.cf.model.topList;

import lombok.Data;

import java.util.Date;

@Data
public class CfTopListLifeCycleRecord {

    private long id;

    private int caseId;
    private int joinPrize;
    private String prizeBeginTime;
    private String prizeEndTime;

    // YYYY-MM-DD
    private String todayTime;


    // 1:转发-全部榜、2:转发-好友榜、
    //3:捐款-全部榜、4:捐款-好友榜
    private int topListTypeId;

    private String rankChangeTime;
    private String topListChangeTime;


    private long userId;
    private int rank;


    // 捐款人转发带来的访问
    private long shareBringVisit;
    // 捐款人总的贡献额（包括转发带来的捐款）
    private int totalContributeDonate;
    // 捐款人自己的捐款
    private int ownDonate;
    // 带来的捐款
    private int shareBringDonate;



    private int ifTodayLastTopList;

    private int ifThanks;
    private String thanksTime;
    private int ifInvite;
    private String inviteTime;

    private int ifPrize;
    private int ifHasPrize;
    private String userPrizeTime;

    private String prizeChannel = "";

}
