package com.shuidihuzhu.cf.model.crowdfunding;

import java.util.Date;

public class UserBonusPointsHistory {
    private Long id;

    private long userId;

    private Date dateKey;

    private Integer bizType;

    private Integer userThirdType;

    private Integer points;

    private Integer action;

    private String outTradeNo;

    private long createTime;

    private Date updateTime;

    private Boolean isDelete;

    private String channel;

    private String remark;

    public UserBonusPointsHistory(Long id, Long userId, Date dateKey, Integer bizType, int userThirdType, Integer points, Integer action, String outTradeNo, long createTime, Date updateTime, Boolean isDelete, String channel, String remark) {
        this.id = id;
        this.userId = userId;
        this.dateKey = dateKey;
        this.bizType = bizType;
        this.userThirdType = userThirdType;
        this.points = points;
        this.action = action;
        this.outTradeNo = outTradeNo;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.isDelete = isDelete;
        this.channel = channel;
        this.remark = remark;
    }

    public UserBonusPointsHistory() {
        super();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public Date getDateKey() {
        return dateKey;
    }

    public void setDateKey(Date dateKey) {
        this.dateKey = dateKey;
    }

    public Integer getBizType() {
        return bizType;
    }

    public void setBizType(Integer bizType) {
        this.bizType = bizType;
    }

    public Integer getUserThirdType() {
        return userThirdType;
    }

    public void setUserThirdType(Integer userThirdType) {
        this.userThirdType = userThirdType;
    }

    public Integer getPoints() {
        return points;
    }

    public void setPoints(Integer points) {
        this.points = points;
    }

    public Integer getAction() {
        return action;
    }

    public void setAction(Integer action) {
        this.action = action;
    }

    public String getOutTradeNo() {
        return outTradeNo;
    }

    public void setOutTradeNo(String outTradeNo) {
        this.outTradeNo = outTradeNo == null ? null : outTradeNo.trim();
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Boolean getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Boolean isDelete) {
        this.isDelete = isDelete;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel == null ? null : channel.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }
}