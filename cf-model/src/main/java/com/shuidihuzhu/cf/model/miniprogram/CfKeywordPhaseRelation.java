package com.shuidihuzhu.cf.model.miniprogram;

public class CfKeywordPhaseRelation {
    private int id;
    private int keywordId;
    private int phaseId;
    private int expectNumber;
    private int notExpectNumber;
    private int isDelete;

    public CfKeywordPhaseRelation() {
    }

    public CfKeywordPhaseRelation(int keywordId, int phaseId, int expectNumber, int notExpectNumber) {
        this.keywordId = keywordId;
        this.phaseId = phaseId;
        this.expectNumber = expectNumber;
        this.notExpectNumber = notExpectNumber;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getKeywordId() {
        return keywordId;
    }

    public void setKeywordId(int keywordId) {
        this.keywordId = keywordId;
    }

    public int getPhaseId() {
        return phaseId;
    }

    public void setPhaseId(int phaseId) {
        this.phaseId = phaseId;
    }

    public int getExpectNumber() {
        return expectNumber;
    }

    public void setExpectNumber(int expectNumber) {
        this.expectNumber = expectNumber;
    }

    public int getNotExpectNumber() {
        return notExpectNumber;
    }

    public void setNotExpectNumber(int notExpectNumber) {
        this.notExpectNumber = notExpectNumber;
    }

    public int getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(int isDelete) {
        this.isDelete = isDelete;
    }

    @Override
    public String toString() {
        return "CfKeywordPhaseRelation{" +
                "id=" + id +
                ", keywordId=" + keywordId +
                ", phaseId=" + phaseId +
                ", expectNumber=" + expectNumber +
                ", notExpectNumber=" + notExpectNumber +
                ", isDelete=" + isDelete +
                '}';
    }
}
