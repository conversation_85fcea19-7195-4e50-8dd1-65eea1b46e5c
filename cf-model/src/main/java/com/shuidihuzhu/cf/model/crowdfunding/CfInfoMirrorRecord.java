package com.shuidihuzhu.cf.model.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.shuidihuzhu.cf.enums.crowdfunding.UserIdentityType;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingAttachmentVo;
import com.shuidihuzhu.common.web.model.AbstractModel;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Timestamp;
import java.util.List;
import java.util.stream.Collectors;

public class CfInfoMirrorRecord extends AbstractModel {

	private static final Logger LOGGER = LoggerFactory.getLogger(CfInfoMirrorRecord.class);

	private static final long serialVersionUID = 2660731850156790946L;

	private long id;
	private long operationRecordId;
	private int type;
	private String infoUuid;
	private int infoType;
	private int infoAuditStatus;
	private Timestamp infoBeginTime;
	private Timestamp infoEndTime;
	private String baseInfo;
	private String payeeInfo;
	private String patientInfo;
	private String treatmentInfo;
	private String otherInfo;//此处有存的增信数据
	private String hospitalPayeeInfo;
	private String dataTypeInfo;
	private BaseInfo baseInfoObject;
	private PayeeInfo payeeInfoObject;
	private PatientInfo patientInfoObject;
	private TreatmentInfo treatmentInfoObject;
	private List<DataTypeInfo> dataTypeInfoObject;
	private OtherInfo otherInfoObject;
	private HospitalPayeeInfo HospitalPayeeInfoObject;
	private Timestamp dateCreated;
	private Timestamp lastModified;
	private int drawcashstatus;
	private int refundstatus;

	public int getDrawcashstatus() {
		return drawcashstatus;
	}

	public void setDrawcashstatus(int drawcashstatus) {
		this.drawcashstatus = drawcashstatus;
	}

	public int getRefundstatus() {
		return refundstatus;
	}

	public void setRefundstatus(int refundstatus) {
		this.refundstatus = refundstatus;
	}

	public CfInfoMirrorRecord() {
		this.baseInfo = "";
		this.payeeInfo = "";
		this.patientInfo = "";
		this.treatmentInfo = "";
		this.otherInfo = "";
		this.hospitalPayeeInfo = "";
		this.dataTypeInfo = "";
	}

	public void buildBaseInfo(CrowdfundingInfo crowdfundingInfo, List<CrowdfundingAttachmentVo> attachments) {
		this.baseInfoObject = new BaseInfo();
		if (crowdfundingInfo != null) {
			this.baseInfoObject.setContent(crowdfundingInfo.getContent());
			this.baseInfoObject.setTargetAmount(crowdfundingInfo.getTargetAmount());
			this.baseInfoObject.setTitle(crowdfundingInfo.getTitle());
		}
		if (CollectionUtils.isNotEmpty(attachments)) {
			this.baseInfoObject.setAttachments(
					attachments.stream().map(CrowdfundingAttachmentVo::getUrl).collect(Collectors.toList()));
		}
		this.baseInfo = JSON.toJSONString(baseInfoObject);
	}

	public void buildPayeeInfo(CrowdfundingInfo crowdfundingInfo, List<CrowdfundingAttachmentVo> idCardAttachments,
			List<CrowdfundingAttachmentVo> relationAttachments) {
		this.payeeInfoObject = new PayeeInfo();
		if (crowdfundingInfo != null) {
			this.payeeInfoObject.setBankBranchName(crowdfundingInfo.getPayeeBankBranchName());
			this.payeeInfoObject.setBankCard(crowdfundingInfo.getPayeeBankCard());
			this.payeeInfoObject.setBankName(crowdfundingInfo.getPayeeBankName());
			this.payeeInfoObject.setIdCard(crowdfundingInfo.getPayeeIdCard());
			this.payeeInfoObject.setMobile(crowdfundingInfo.getPayeeMobile());
			this.payeeInfoObject.setName(crowdfundingInfo.getPayeeName());
			this.payeeInfoObject.setRelationType(crowdfundingInfo.getRelationType().getDescription());
		}
		if (CollectionUtils.isNotEmpty(idCardAttachments)) {
			this.payeeInfoObject.setIdCardAttachment(
					idCardAttachments.stream().map(CrowdfundingAttachmentVo::getUrl).collect(Collectors.toList()));
		}
		if (CollectionUtils.isNotEmpty(relationAttachments)) {
			this.payeeInfoObject.setRelationAttachments(
					relationAttachments.stream().map(CrowdfundingAttachmentVo::getUrl).collect(Collectors.toList()));
		}
		this.payeeInfo = JSON.toJSONString(payeeInfoObject);
	}

	public void buildPatientInfo(CrowdfundingAuthor crowdfundingAuthor, List<CrowdfundingAttachmentVo> attachments) {
		this.patientInfoObject = new PatientInfo();
		if (crowdfundingAuthor != null) {
			this.patientInfoObject.setIdCard(StringUtils.trimToEmpty(crowdfundingAuthor.getCryptoIdCard()));
			this.patientInfoObject.setIdCardType(UserIdentityType.getCode(crowdfundingAuthor.getIdType()));
			this.patientInfoObject.setName(crowdfundingAuthor.getName());
		}
		if (CollectionUtils.isNotEmpty(attachments)) {
			this.patientInfoObject.setAttachments(
					attachments.stream().map(CrowdfundingAttachmentVo::getUrl).collect(Collectors.toList()));
		}
		this.patientInfo = JSON.toJSONString(patientInfoObject);
	}

	public void buildTreatmentInfo(CrowdfundingTreatment crowdfundingTreatment,
			List<CrowdfundingAttachmentVo> attachments) {
		this.treatmentInfoObject = new TreatmentInfo();
		if (crowdfundingTreatment != null) {
			this.treatmentInfoObject.setDiagnoseHospitalName(crowdfundingTreatment.getDiagnoseHospitalName());
			this.treatmentInfoObject.setDiseaseName(crowdfundingTreatment.getDiseaseName());
			this.treatmentInfoObject.setHospitalName(crowdfundingTreatment.getHospitalName());
		}
		if (CollectionUtils.isNotEmpty(attachments)) {
			this.treatmentInfoObject.setAttachments(
					attachments.stream().map(CrowdfundingAttachmentVo::getUrl).collect(Collectors.toList()));
		}
		this.treatmentInfo = JSON.toJSONString(treatmentInfoObject);
	}

	public void buildOtherInfo(CrowdfundingAuthor crowdfundingAuthor, List<CfCreditSupplement> cfCreditSupplements) {
		this.otherInfoObject = new OtherInfo();
		if (crowdfundingAuthor != null) {
			this.otherInfoObject.setCommercialInsurance(crowdfundingAuthor.getCommercialInsurance());
			this.otherInfoObject.setHealthInsurance(crowdfundingAuthor.getHealthInsurance());
		}
		List<CreditSupplement> creditSupplements = Lists.newArrayList();
		if (CollectionUtils.isNotEmpty(cfCreditSupplements)) {
			for (CfCreditSupplement cfCreditSupplement : cfCreditSupplements) {
				creditSupplements.add(new CreditSupplement(cfCreditSupplement));
			}
			this.otherInfoObject.setCreditSupplements(creditSupplements);
		}
		this.otherInfo = JSON.toJSONString(otherInfoObject);
	}


	public void buildHospitalPayeeInfo(CrowdfundingInfoHospitalPayee crowdfundingInfoHospitalPayee){
		this.HospitalPayeeInfoObject = new HospitalPayeeInfo();
		if (crowdfundingInfoHospitalPayee != null){
			this.HospitalPayeeInfoObject.setRelationType(crowdfundingInfoHospitalPayee.getRelationType());
			this.HospitalPayeeInfoObject.setDepartment(crowdfundingInfoHospitalPayee.getDepartment());
			this.HospitalPayeeInfoObject.setBedNum(crowdfundingInfoHospitalPayee.getBedNum());
			this.HospitalPayeeInfoObject.setHospitalizationNum(crowdfundingInfoHospitalPayee.getHospitalizationNum());
			this.HospitalPayeeInfoObject.setHospitalAccountName(crowdfundingInfoHospitalPayee.getHospitalAccountName());
			this.HospitalPayeeInfoObject.setHospitalBankCard(crowdfundingInfoHospitalPayee.getHospitalBankCard());
			this.HospitalPayeeInfoObject.setHospitalBankBranchName(crowdfundingInfoHospitalPayee.getHospitalBankBranchName());
		}
		this.hospitalPayeeInfo = JSON.toJSONString(crowdfundingInfoHospitalPayee);
	}

	public void buildDataTypeInfo(List<CrowdfundingInfoStatus> crowdfundingInfoStatuses) {
	    if (CollectionUtils.isEmpty(crowdfundingInfoStatuses)) {
            return;
        }
        this.dataTypeInfoObject = crowdfundingInfoStatuses.stream().map(DataTypeInfo::new).collect(Collectors.toList());
        this.dataTypeInfo = JSON.toJSONString(dataTypeInfoObject);
    }


	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getOperationRecordId() {
		return operationRecordId;
	}

	public void setOperationRecordId(long operationRecordId) {
		this.operationRecordId = operationRecordId;
	}

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

	public String getInfoUuid() {
		return infoUuid;
	}

	public void setInfoUuid(String infoUuid) {
		this.infoUuid = infoUuid;
	}

	public int getInfoType() {
		return infoType;
	}

	public void setInfoType(int infoType) {
		this.infoType = infoType;
	}

	public int getInfoAuditStatus() {
		return infoAuditStatus;
	}

	public void setInfoAuditStatus(int infoAuditStatus) {
		this.infoAuditStatus = infoAuditStatus;
	}

	public Timestamp getInfoBeginTime() {
		return infoBeginTime;
	}

	public void setInfoBeginTime(Timestamp infoBeginTime) {
		this.infoBeginTime = infoBeginTime;
	}

	public Timestamp getInfoEndTime() {
		return infoEndTime;
	}

	public void setInfoEndTime(Timestamp infoEndTime) {
		this.infoEndTime = infoEndTime;
	}

	public String getBaseInfo() {
		return baseInfo;
	}

	public void setBaseInfo(String baseInfo) {
		this.baseInfo = baseInfo;
		if (StringUtils.isNotBlank(baseInfo)) {
			try {
				this.baseInfoObject = new Gson().fromJson(baseInfo, BaseInfo.class);
			} catch (Exception e) {
				LOGGER.info("", e);
			}
		}
	}

	public String getPayeeInfo() {
		return payeeInfo;
	}

	public void setPayeeInfo(String payeeInfo) {
		this.payeeInfo = payeeInfo;
		if (StringUtils.isNotBlank(payeeInfo)) {
			try {
				this.payeeInfoObject = new Gson().fromJson(payeeInfo, PayeeInfo.class);
			} catch (Exception e) {
				LOGGER.info("", e);
			}
		}
	}

	public String getPatientInfo() {
		return patientInfo;
	}

	public void setPatientInfo(String patientInfo) {
		this.patientInfo = patientInfo;
		if (StringUtils.isNotBlank(patientInfo)) {
			try {
				this.patientInfoObject = new Gson().fromJson(patientInfo, PatientInfo.class);
			} catch (Exception e) {
				LOGGER.info("", e);
			}
		}
	}

	public String getTreatmentInfo() {
		return treatmentInfo;
	}

	public void setTreatmentInfo(String treatmentInfo) {
		this.treatmentInfo = treatmentInfo;
		if (StringUtils.isNotBlank(treatmentInfo)) {
			try {
				this.treatmentInfoObject = new Gson().fromJson(treatmentInfo, TreatmentInfo.class);
			} catch (Exception e) {
				LOGGER.info("", e);
			}
		}
	}

	public String getOtherInfo() {
		return otherInfo;
	}

	public void setOtherInfo(String otherInfo) {
		this.otherInfo = otherInfo;
		if(StringUtils.isNotBlank(otherInfo)){
			try {
				this.otherInfoObject = new Gson().fromJson(otherInfo, OtherInfo.class);
			} catch (Exception e) {
				LOGGER.info("", e);
			}
		}
	}

	public String getHospitalPayeeInfo() {
		return hospitalPayeeInfo;
	}

	public void setHospitalPayeeInfo(String hospitalPayeeInfo) {
		this.hospitalPayeeInfo = hospitalPayeeInfo;
		if (StringUtils.isNoneBlank(hospitalPayeeInfo)){
			try {
				this.HospitalPayeeInfoObject = new Gson().fromJson(hospitalPayeeInfo, HospitalPayeeInfo.class);
			}catch (Exception e){
				LOGGER.info("", e);
			}
		}
	}


    public String getDataTypeInfo() {
        return dataTypeInfo;
    }

    public void setDataTypeInfo(String dataTypeInfo) {
        this.dataTypeInfo = dataTypeInfo;
        if(StringUtils.isNotBlank(dataTypeInfo)){
            try {
                this.dataTypeInfoObject = new Gson().fromJson(dataTypeInfo, List.class);
            } catch (Exception e) {
                LOGGER.info("", e);
            }
        }
    }

    public BaseInfo getBaseInfoObject() {
		return baseInfoObject;
	}

	public PayeeInfo getPayeeInfoObject() {
		return payeeInfoObject;
	}

	public PatientInfo getPatientInfoObject() {
		return patientInfoObject;
	}

	public TreatmentInfo getTreatmentInfoObject() {
		return treatmentInfoObject;
	}

	public OtherInfo getOtherInfoObject() {
		return otherInfoObject;
	}

	public HospitalPayeeInfo getHospitalPayeeInfoObject() {
		return HospitalPayeeInfoObject;
	}

    public List<DataTypeInfo> getDataTypeInfoObject() {
        return dataTypeInfoObject;
    }

    public void setDataTypeInfoObject(List<DataTypeInfo> dataTypeInfoObject) {
        this.dataTypeInfoObject = dataTypeInfoObject;
    }

    public Timestamp getDateCreated() {
		return dateCreated;
	}

	public void setDateCreated(Timestamp dateCreated) {
		this.dateCreated = dateCreated;
	}

	public Timestamp getLastModified() {
		return lastModified;
	}

	public void setLastModified(Timestamp lastModified) {
		this.lastModified = lastModified;
	}

	public class BaseInfo {
		private String title;
		private String content;
		private int targetAmount;
		private List<String> attachments;

		public String getTitle() {
			return title;
		}

		public void setTitle(String title) {
			this.title = title;
		}

		public String getContent() {
			return content;
		}

		public void setContent(String content) {
			this.content = content;
		}

		public int getTargetAmount() {
			return targetAmount;
		}

		public void setTargetAmount(int targetAmount) {
			this.targetAmount = targetAmount;
		}

		public List<String> getAttachments() {
			return attachments;
		}

		public void setAttachments(List<String> attachments) {
			this.attachments = attachments;
		}

	}

	public class PayeeInfo {
		private String relationType;
		private String name;
		private String idCard;
		private String mobile;
		private String bankName;
		private String bankBranchName;
		private String bankCard;
		private List<String> idCardAttachment;
		private List<String> relationAttachments;

		public String getRelationType() {
			return relationType;
		}

		public void setRelationType(String relationType) {
			this.relationType = relationType;
		}

		public String getName() {
			return name;
		}

		public void setName(String name) {
			this.name = name;
		}

		public String getIdCard() {
			return idCard;
		}

		public void setIdCard(String idCard) {
			this.idCard = idCard;
		}

		public String getMobile() {
			return mobile;
		}

		public void setMobile(String mobile) {
			this.mobile = mobile;
		}

		public String getBankName() {
			return bankName;
		}

		public void setBankName(String bankName) {
			this.bankName = bankName;
		}

		public String getBankBranchName() {
			return bankBranchName;
		}

		public void setBankBranchName(String bankBranchName) {
			this.bankBranchName = bankBranchName;
		}

		public String getBankCard() {
			return bankCard;
		}

		public void setBankCard(String bankCard) {
			this.bankCard = bankCard;
		}

		public List<String> getIdCardAttachment() {
			return idCardAttachment;
		}

		public void setIdCardAttachment(List<String> idCardAttachment) {
			this.idCardAttachment = idCardAttachment;
		}

		public List<String> getRelationAttachments() {
			return relationAttachments;
		}

		public void setRelationAttachments(List<String> relationAttachments) {
			this.relationAttachments = relationAttachments;
		}

	}

	public class PatientInfo {
		private String name;
		private String idCard;
		private int idCardType;
		private List<String> attachments;

		public String getName() {
			return name;
		}

		public void setName(String name) {
			this.name = name;
		}

		public String getIdCard() {
			return idCard;
		}

		public void setIdCard(String idCard) {
			this.idCard = idCard;
		}

		public int getIdCardType() {
			return idCardType;
		}

		public void setIdCardType(int idCardType) {
			this.idCardType = idCardType;
		}

		public List<String> getAttachments() {
			return attachments;
		}

		public void setAttachments(List<String> attachments) {
			this.attachments = attachments;
		}

	}

	public class TreatmentInfo {
		private String diseaseName;
		private String hospitalName;
		private String diagnoseHospitalName;
		private List<String> attachments;

		public String getDiseaseName() {
			return diseaseName;
		}

		public void setDiseaseName(String diseaseName) {
			this.diseaseName = diseaseName;
		}

		public String getHospitalName() {
			return hospitalName;
		}

		public void setHospitalName(String hospitalName) {
			this.hospitalName = hospitalName;
		}

		public String getDiagnoseHospitalName() {
			return diagnoseHospitalName;
		}

		public void setDiagnoseHospitalName(String diagnoseHospitalName) {
			this.diagnoseHospitalName = diagnoseHospitalName;
		}

		public List<String> getAttachments() {
			return attachments;
		}

		public void setAttachments(List<String> attachments) {
			this.attachments = attachments;
		}

	}

	public class OtherInfo {
		private int healthInsurance;
		private int commercialInsurance;
		private List<CreditSupplement> creditSupplements;

		public int getHealthInsurance() {
			return healthInsurance;
		}

		public void setHealthInsurance(int healthInsurance) {
			this.healthInsurance = healthInsurance;
		}

		public int getCommercialInsurance() {
			return commercialInsurance;
		}

		public void setCommercialInsurance(int commercialInsurance) {
			this.commercialInsurance = commercialInsurance;
		}

		public List<CreditSupplement> getCreditSupplements() {
			return creditSupplements;
		}

		public void setCreditSupplements(List<CreditSupplement> creditSupplements) {
			this.creditSupplements = creditSupplements;
		}

	}

	public class CreditSupplement {
		private int count;
		private int propertyType;
		private int totalValue;
		private int status;
		private int sellCount;

		public CreditSupplement(CfCreditSupplement cfCreditSupplement) {
			this.count = cfCreditSupplement.getCount();
			this.propertyType = cfCreditSupplement.getPropertyType();
			this.totalValue = cfCreditSupplement.getTotalValue();
			this.status = cfCreditSupplement.getStatus();
			this.sellCount = cfCreditSupplement.getSellCount();
		}

		public int getCount() {
			return count;
		}

		public void setCount(int count) {
			this.count = count;
		}

		public int getPropertyType() {
			return propertyType;
		}

		public void setPropertyType(int propertyType) {
			this.propertyType = propertyType;
		}

		public int getTotalValue() {
			return totalValue;
		}

		public void setTotalValue(int totalValue) {
			this.totalValue = totalValue;
		}

		public int getStatus() {
			return status;
		}

		public void setStatus(int status) {
			this.status = status;
		}

		public int getSellCount() {
			return sellCount;
		}

		public void setSellCount(int sellCount) {
			this.sellCount = sellCount;
		}

	}

	public class HospitalPayeeInfo{
		private int relationType;
		private String department;
		private String bedNum;
		private String hospitalizationNum;
		private String hospitalAccountName;
		private String hospitalBankCard;
		private String hospitalBankBranchName;

		public int getRelationType() {
			return relationType;
		}

		public void setRelationType(int relationType) {
			this.relationType = relationType;
		}

		public String getDepartment() {
			return department;
		}

		public void setDepartment(String department) {
			this.department = department;
		}

		public String getBedNum() {
			return bedNum;
		}

		public void setBedNum(String bedNum) {
			this.bedNum = bedNum;
		}

		public String getHospitalizationNum() {
			return hospitalizationNum;
		}

		public void setHospitalizationNum(String hospitalizationNum) {
			this.hospitalizationNum = hospitalizationNum;
		}

		public String getHospitalAccountName() {
			return hospitalAccountName;
		}

		public void setHospitalAccountName(String hospitalAccountName) {
			this.hospitalAccountName = hospitalAccountName;
		}

		public String getHospitalBankCard() {
			return hospitalBankCard;
		}

		public void setHospitalBankCard(String hospitalBankCard) {
			this.hospitalBankCard = hospitalBankCard;
		}

		public String getHospitalBankBranchName() {
			return hospitalBankBranchName;
		}

		public void setHospitalBankBranchName(String hospitalBankBranchName) {
			this.hospitalBankBranchName = hospitalBankBranchName;
		}
	}

	public class DataTypeInfo {
	    private int type;
        private int status;

		public DataTypeInfo(int type, int status) {
			this.type = type;
			this.status = status;
		}

		public DataTypeInfo(CrowdfundingInfoStatus crowdfundingInfoStatus) {
            type = crowdfundingInfoStatus.getType();
            status = crowdfundingInfoStatus.getStatus();
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }

		@Override
		public String toString() {
			return "DataTypeInfo{" +
					"type=" + type +
					", status=" + status +
					'}';
		}
	}
}
