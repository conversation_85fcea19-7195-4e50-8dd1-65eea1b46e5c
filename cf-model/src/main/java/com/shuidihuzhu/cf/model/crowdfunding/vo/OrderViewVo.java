package com.shuidihuzhu.cf.model.crowdfunding.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created by dongcf on 2021/6/3
 */
@ApiModel("下单页展示")
@Data
public class OrderViewVo {

    @ApiModelProperty("是否爆款案例")
    private boolean isHot;

    @ApiModelProperty("爆款实验鹰眼值")
    private String eyeValue;

    @ApiModelProperty("是否首捐")
    private boolean isFirstDonate;

    @ApiModelProperty("是否资助")
    private boolean isSubsidize;

    @ApiModelProperty("资助组别")
    private int groupValue;

    @ApiModelProperty("案例是否收费 0:不收费 1:收费")
    private int caseCharge;
}
