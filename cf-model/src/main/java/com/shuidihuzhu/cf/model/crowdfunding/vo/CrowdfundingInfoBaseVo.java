package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.cf.client.material.model.CfBasicLivingGuardModel;
import com.shuidihuzhu.cf.client.material.model.MaterialPlanVersion;
import com.shuidihuzhu.cf.client.material.model.medication.UserMedicineView;
import com.shuidihuzhu.cf.client.material.model.preSubmit.CfMaterialPreModifySign;
import com.shuidihuzhu.cf.enums.crowdfunding.CfContentTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingRelationType;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveIdcardVerifyStatusEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTemplateRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseStatus;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoStatus;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.InitialAuditItem;
import com.shuidihuzhu.cf.param.raise.RaiseBasicInfoParam;
import com.shuidihuzhu.cf.vo.initialaudit.CfPropertyInsuranceVO;
import com.shuidihuzhu.cf.vo.questionnaire.TemplateTitleAndContentVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveIdcardVerifyStatusEnum.MATCH;

@Data
public class CrowdfundingInfoBaseVo extends CrowdfundingInfoParentVo {
	private Integer targetAmount;
	private String title;
	private String content;
	private List<String> attachments;
	private String channelTypeName;
	private String channel;
	private long userId;
	private Integer platform;
	private String selfTag;
	private String payeeName;
	// 初审的状态
	private InitialAuditItem.MaterialStatus materialStatus;
	private Map<Integer, List<String>> rejectDetails;
	private CrowdfundingSeekHelpInfoFragmentVo contentV2;
	private CfContentTypeEnum contentType;
	private Integer userThirdType;
	// 去掉后置手机号验证功能
//	private Integer needMobile;
//	private String registerMobile;
	private String volunteerUniqueCode;
    /**
     * 用户自动生成文案选择记录
     */
	private CfBaseInfoTemplateRecord cfBaseInfoTemplateRecord;
	private CrowdfundingRelationType relationType;

	private String selfRealName;  // 发起者的姓名
	private String selfIdCard;    // 发起者的身份证号
	private String patientRealName; // 患者的姓名
	private String patientIdCard;  // 患者的身份证号
	private boolean patientHasIdCard; //患者是否有身份证号
	private UserRelTypeEnum relType; //本人与患者的关系
	private int userRelationTypeForC;  // 3100版本-C端用的关系字段
	private String preAuditImageUrl;  //初次审核的照片
    private int imageUrlType;
	private FirstApproveIdcardVerifyStatusEnum firstApproveIdcardVerifyStatusEnum; // 身份校验状态
	private String targetAmountDesc; //目标金额说明
	private Integer poverty; //建档立卡脱贫户  0 默认  1  是  2 否

	@ApiModelProperty("建档立卡脱贫户  补充材料 eg: url1,url2,url3")
	private String povertyImageUrl;

	//客户端版本号
	private String appVersion;
	//发起草稿箱id  不存在为0
	private Long preId;

	private String patientBornCard;

	private int patientIdType;

	private String hawkEyeVersion;

	private String deviceId;
	private String uniqueId;
	private int checkUserScan;

	@ApiModelProperty("指定案例版本 {3000: 增信版本, 2000: 公约版} 不传走默认逻辑")
	private Integer cfVersion;

	// 初审增加的增信相关信息
	private CfPropertyInsuranceVO propertyInsuranceParam;

	private CfBasicLivingGuardModel livingGuardParam;

	// 1 bd提交的  2、用户提交的
	private int initialModifyType;

	private RaiseBasicInfoParam raiseBasicInfoParam;

	@ApiModelProperty("记录是否是智能发起 0不是 1是 ")
	private Integer useTemplateRaise;
	@ApiModelProperty("用户的智能模版的参数")
	private TemplateTitleAndContentVo templateParam;

	private CfMaterialPreModifySign modifySign;

	@ApiModelProperty("是否被运营修改过目标金额")
	private boolean hasModifiedTargetAmount;

	@ApiModelProperty("运营上一次修改的目标金额，单位为分")
	private long latestTartAmount;

	@ApiModelProperty("用户填写的用药情况")
	private UserMedicineView medicineView;

	@ApiModelProperty("修改图文渠道 0:非用户自主 1:用户自主")
	private int twModifyChannel;

	@ApiModelProperty("案例标题授权开关 0 关 1 开")
	private int caseTitleAuthorizationSwitch;

	@ApiModelProperty("代录入id标识")
	private String reportLinkInfo;

	@ApiModelProperty("案例版本")
	private Integer planId;

	public CrowdfundingInfoBaseVo() {
		this.firstApproveIdcardVerifyStatusEnum = MATCH;
	}

	public CrowdfundingInfoBaseVo(CrowdfundingInfo crowdfundingInfo, List<String> attachments,
	                              CfContentTypeEnum contentType, CrowdfundingSeekHelpInfoFragmentVo fragment,
	                              CrowdfundingInfoStatus infoStatus, CfRefuseStatus cfRefuseStatus) {
		this.buildParentParam(crowdfundingInfo);
		this.setTitle(crowdfundingInfo.getTitle());
		this.setContent(crowdfundingInfo.getContent());
		this.setTargetAmount(crowdfundingInfo.getTargetAmount() / 100);
		this.setAttachments(attachments);
		this.setContentType(contentType);
		this.setContentV2(fragment);
		if (cfRefuseStatus != null) {
			this.setRejectDetails(cfRefuseStatus.getType3());
		}
		this.setStatus(infoStatus == null ? 0 : infoStatus.getStatus());
	}

	/**
	 * 是否命中家庭经济结构版本
	 */
	public Boolean familyEconomicHit() {
		if (Objects.isNull(this.planId)) {
			return false;
		}
		return this.planId == MaterialPlanVersion.PLAN_160.getCode();
	}
}
