package com.shuidihuzhu.cf.model.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Created by niejiangnan on 2017/11/16.
 */
public class CfHospitalAuditMirror {

    private static final Logger LOGGER = LoggerFactory.getLogger(CfHospitalAuditMirror.class);

    private long id;
    private String infoUuid;
    private long operatingRecordId;
    private int type;
    private int auditStatus;
    private int operatingType;
    private String hospitalAuditInfo;
    private HospitalAuditInfo hospitalAuditInfoObject;


    public void buildAddTrustInfo(CfHospitalAuditInfo cfHospitalAuditInfo){
        this.hospitalAuditInfoObject = new HospitalAuditInfo();
        if (cfHospitalAuditInfo!=null){
            hospitalAuditInfoObject.setPatientName(cfHospitalAuditInfo.getPatientName());
            hospitalAuditInfoObject.setHospitalName(cfHospitalAuditInfo.getHospitalName());
            hospitalAuditInfoObject.setDepartment(cfHospitalAuditInfo.getDepartment());
            hospitalAuditInfoObject.setFloorNumber(cfHospitalAuditInfo.getFloorNumber());
            hospitalAuditInfoObject.setBedNumber(cfHospitalAuditInfo.getBedNumber());
            hospitalAuditInfoObject.setHospitalizationNumber(
                    cfHospitalAuditInfo.getHospitalizationNumber());
            hospitalAuditInfoObject.setDoctorName(cfHospitalAuditInfo.getDoctorName());
            hospitalAuditInfoObject.setDepartmentTelNumber(cfHospitalAuditInfo.getDepartmentTelNumber());
            hospitalAuditInfoObject.setOperatorContent(cfHospitalAuditInfo.getOperatorContent());
        }
        this.hospitalAuditInfo = JSON.toJSONString(hospitalAuditInfoObject);
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getInfoUuid() {
        return infoUuid;
    }

    public void setInfoUuid(String infoUuid) {
        this.infoUuid = infoUuid;
    }

    public long getOperatingRecordId() {
        return operatingRecordId;
    }

    public void setOperatingRecordId(long operatingRecordId) {
        this.operatingRecordId = operatingRecordId;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(int auditStatus) {
        this.auditStatus = auditStatus;
    }

    public int getOperatingType() {
        return operatingType;
    }

    public void setOperatingType(int operatingType) {
        this.operatingType = operatingType;
    }

    public String getHospitalAuditInfo() {
        return hospitalAuditInfo;
    }

    public void setHospitalAuditInfo(String hospitalAuditInfo) {
        this.hospitalAuditInfo = hospitalAuditInfo;
        if (StringUtils.isNotBlank(hospitalAuditInfo)){
            try {
                this.hospitalAuditInfoObject = new Gson().
                        fromJson(hospitalAuditInfo, HospitalAuditInfo.class);
            } catch (Exception e) {
                LOGGER.error("error :", e);
            }
        }
    }

    public HospitalAuditInfo getHospitalAuditInfoObject() {
        return hospitalAuditInfoObject;
    }




    public class  HospitalAuditInfo{
        private String patientName;
        private String hospitalName;
        private String department;
        private String floorNumber;
        private String bedNumber;
        private String hospitalizationNumber;
        private String doctorName;
        private String departmentTelNumber;
        private String operatorContent;

        public String getPatientName() {
            return patientName;
        }

        public void setPatientName(String patientName) {
            this.patientName = patientName;
        }

        public String getHospitalName() {
            return hospitalName;
        }

        public void setHospitalName(String hospitalName) {
            this.hospitalName = hospitalName;
        }

        public String getDepartment() {
            return department;
        }

        public void setDepartment(String department) {
            this.department = department;
        }

        public String getFloorNumber() {
            return floorNumber;
        }

        public void setFloorNumber(String floorNumber) {
            this.floorNumber = floorNumber;
        }

        public String getBedNumber() {
            return bedNumber;
        }

        public void setBedNumber(String bedNumber) {
            this.bedNumber = bedNumber;
        }

        public String getHospitalizationNumber() {
            return hospitalizationNumber;
        }

        public void setHospitalizationNumber(String hospitalizationNumber) {
            this.hospitalizationNumber = hospitalizationNumber;
        }

        public String getDoctorName() {
            return doctorName;
        }

        public void setDoctorName(String doctorName) {
            this.doctorName = doctorName;
        }

        public String getDepartmentTelNumber() {
            return departmentTelNumber;
        }

        public void setDepartmentTelNumber(String departmentTelNumber) {
            this.departmentTelNumber = departmentTelNumber;
        }

        public String getOperatorContent() {
            return operatorContent;
        }

        public void setOperatorContent(String operatorContent) {
            this.operatorContent = operatorContent;
        }
    }

}
