package com.shuidihuzhu.cf.model.crowdfunding.message;

import com.shuidihuzhu.cf.enums.crowdfunding.CfBaseStatus;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @DATE 2018/7/16
 */
@Data
@ApiModel
public class CfStatusChangeRecord implements Serializable {

    private static final long serialVersionUID = 9069138921525527523L;

    @ApiModelProperty("案例id")
    public long caseId;

    @ApiModelProperty("案例uuid")
    public String caseUuId;

    @ApiModelProperty("案例筹款人id")
    public long userId;

    @ApiModelProperty("案例状态 CfBaseStatus")
    public int status;

    @ApiModelProperty("案例渠道")
    public String channel;

    @ApiModelProperty("案例开始时间")
    public Date beginTime;

    @ApiModelProperty("案例结束时间")
    public Date endTime;

    @ApiModelProperty("案例目标筹款金额")
    public int targetAmount;

    @ApiModelProperty("案例已筹款金额")
    public int amount;

    @ApiModelProperty("消息发送时间")
    public Date sendTime;


    public CfStatusChangeRecord() {
    }



    public CfStatusChangeRecord(CrowdfundingInfo crowdfundingInfo, CfBaseStatus status) {
        this.caseId = crowdfundingInfo.getId();
        this.caseUuId = crowdfundingInfo.getInfoId();
        this.userId = crowdfundingInfo.getUserId();
        this.status = status.getCode();
        this.channel = crowdfundingInfo.getChannel();
        this.beginTime = crowdfundingInfo.getBeginTime();
        this.endTime = crowdfundingInfo.getEndTime();
        this.targetAmount = crowdfundingInfo.getTargetAmount();
        this.amount = crowdfundingInfo.getAmount();
        this.sendTime = new Date();
    }
}
