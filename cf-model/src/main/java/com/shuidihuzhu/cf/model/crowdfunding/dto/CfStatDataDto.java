package com.shuidihuzhu.cf.model.crowdfunding.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/11/25  20:58
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CfStatDataDto {
    private long visitCount;

    //案例转发次数（不包含筹款人自己转发、访问）
    private long shareCount;

    //捐单数
    private long donationCount;

    //获捐金额
    private long donationAmount;
}
