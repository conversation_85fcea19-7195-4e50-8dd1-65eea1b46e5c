package com.shuidihuzhu.cf.model.crowdfunding;

import com.google.common.collect.Maps;

import java.util.Map;

/**
 * Created by duchao
 */
public enum CrowdFundingVerificationType {
    FRIENDS(3),
    DOCTORS(2),
    SICK_FRIENDS(1),
    OTHER(0),
    ;

    private int code;

    CrowdFundingVerificationType(int code) {
        this.code = code;
    }

    private static final Map<Integer, CrowdFundingVerificationType> map = Maps.newHashMap();

    static {
        for (CrowdFundingVerificationType type : CrowdFundingVerificationType.values()){
            map.put(type.getCode(), type);
        }
    }

    public static CrowdFundingVerificationType getByCode(int code){
        CrowdFundingVerificationType type = map.get(code);
        if (type == null){
            throw new IllegalArgumentException("证实类型不正确");
        }
        return type;
    }

    public int getCode() {
        return code;
    }
}
