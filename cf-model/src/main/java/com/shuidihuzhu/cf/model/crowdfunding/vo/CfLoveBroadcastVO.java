package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class CfLoveBroadcastVO {
	private String infoUuid;
	private String nickname;
	private String headImgUrl;
	@Deprecated
	private int amount;
	private int amountInFen;
	private double amountInYuan;
	private boolean hasShare;
	@JsonIgnore
	private long userId;
	@JsonIgnore
	private long orderId;
	private Date payTime;

	@ApiModelProperty("播报类型 1:帮助；2:转发")
	private int type;

	@ApiModelProperty("关系类型")
	private String relationDesc;

	@ApiModelProperty("播报类型 11：一度好友 12：证实 13：正常")
	private int broadCastType;

	@ApiModelProperty("爱心值")
	private int loveScore;

	@ApiModelProperty("卡片等级")
	private Integer rightsRank;

	@ApiModelProperty("卡片名称")
	private String rightsRankName;
	@ApiModelProperty("证实姓名")
	private String verificationName;


	public Date getPayTime() {
		return payTime;
	}

	public void setPayTime(Date payTime) {
		this.payTime = payTime;
	}

	public long getOrderId() {
		return orderId;
	}

	public void setOrderId(long orderId) {
		this.orderId = orderId;
	}

	public String getInfoUuid() {
		return infoUuid;
	}

	public void setInfoUuid(String infoUuid) {
		this.infoUuid = infoUuid;
	}

	public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}

	public String getNickname() {
		return nickname;
	}

	public void setNickname(String nickname) {
		this.nickname = nickname;
	}

	public String getHeadImgUrl() {
		return headImgUrl;
	}

	public void setHeadImgUrl(String headImgUrl) {
		this.headImgUrl = headImgUrl;
	}

	public int getAmount() {
		return amount;
	}

	public void setAmount(int amount) {
		this.amount = amount;
	}

	public boolean isHasShare() {
		return hasShare;
	}

	public void setHasShare(boolean hasShare) {
		this.hasShare = hasShare;
	}

	public double getAmountInYuan() {
		return amountInYuan;
	}

	public void setAmountInYuan(double amountInYuan) {
		this.amountInYuan = amountInYuan;
	}

	public int getAmountInFen() {
		return amountInFen;
	}

	public void setAmountInFen(int amountInFen) {
		this.amountInFen = amountInFen;
	}
}
