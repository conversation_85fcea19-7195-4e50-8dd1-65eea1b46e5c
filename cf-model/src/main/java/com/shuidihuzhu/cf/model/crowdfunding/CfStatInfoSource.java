package com.shuidihuzhu.cf.model.crowdfunding;


import com.shuidihuzhu.msg.util.DateUtil;

import java.sql.Date;
import java.sql.Timestamp;

/**
 * Created by wangsf on 17/4/28.
 */
public class CfStatInfoSource implements Cloneable {

	private int id;
	private String caseId = "";
	private long userId = 0;
	private String openId = "";
	private String mobile = "";
	private String uniqueId = "";
	private Date createDate;
	private Timestamp createTime;
	private boolean isAutoTagged = false;
	private String meetPointType = "";
	private String autoTagCategory = "";
	private int autoTagSceneId = 0;
	private Timestamp firstMeetTime;
	private boolean isManualTagged = false;
	private String manualTagCategory = "";
	private int manualTagSceneId = 0;
	private Timestamp mannualTagTime;
	private String channel = "";
	private boolean isToufangSign = false;
	private boolean isApp = false;

	@Override
	public CfStatInfoSource clone() throws CloneNotSupportedException {
		return (CfStatInfoSource) super.clone();
	}

	public CfStatInfoSource() {
		this.createDate = new Date(DateUtil.getCurTimeMillis());
		this.firstMeetTime = new Timestamp(DateUtil.getCurTimeMillis());
		this.mannualTagTime = new Timestamp(DateUtil.getCurTimeMillis());
	}

	public int getAutoTagSceneId() {
		return autoTagSceneId;
	}

	public void setAutoTagSceneId(int autoTagSceneId) {
		this.autoTagSceneId = autoTagSceneId;
	}

	public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getCaseId() {
		return caseId;
	}

	public void setCaseId(String caseId) {
		this.caseId = caseId;
	}

	public String getOpenId() {
		return openId;
	}

	public void setOpenId(String openId) {
		this.openId = openId;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getUniqueId() {
		return uniqueId;
	}

	public void setUniqueId(String uniqueId) {
		this.uniqueId = uniqueId;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Timestamp getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Timestamp createTime) {
		this.createTime = createTime;
	}

	public boolean isAutoTagged() {
		return isAutoTagged;
	}

	public void setAutoTagged(boolean autoTagged) {
		isAutoTagged = autoTagged;
	}

	public String getMeetPointType() {
		return meetPointType;
	}

	public void setMeetPointType(String meetPointType) {
		this.meetPointType = meetPointType;
	}

	public String getAutoTagCategory() {
		return autoTagCategory;
	}

	public void setAutoTagCategory(String autoTagCategory) {
		this.autoTagCategory = autoTagCategory;
	}

	public Timestamp getFirstMeetTime() {
		return firstMeetTime;
	}

	public void setFirstMeetTime(Timestamp firstMeetTime) {
		this.firstMeetTime = firstMeetTime;
	}

	public boolean isManualTagged() {
		return isManualTagged;
	}

	public void setManualTagged(boolean manualTagged) {
		isManualTagged = manualTagged;
	}

	public String getManualTagCategory() {
		return manualTagCategory;
	}

	public void setManualTagCategory(String manualTagCategory) {
		this.manualTagCategory = manualTagCategory;
	}

	public int getManualTagSceneId() {
		return manualTagSceneId;
	}

	public void setManualTagSceneId(int manualTagSceneId) {
		this.manualTagSceneId = manualTagSceneId;
	}

	public Timestamp getMannualTagTime() {
		return mannualTagTime;
	}

	public void setMannualTagTime(Timestamp mannualTagTime) {
		this.mannualTagTime = mannualTagTime;
	}

	public String getChannel() {
		return channel;
	}

	public void setChannel(String channel) {
		this.channel = channel;
	}

	public boolean isToufangSign() {
		return isToufangSign;
	}

	public void setToufangSign(boolean toufangSign) {
		isToufangSign = toufangSign;
	}

	public boolean isApp() {
		return isApp;
	}

	public void setApp(boolean app) {
		isApp = app;
	}

	@Override
	public String toString() {
		return "CfStatInfoSource{" +
				"id=" + id +
				", caseId='" + caseId + '\'' +
				", userId=" + userId +
				", openId='" + openId + '\'' +
				", mobile='" + mobile + '\'' +
				", uniqueId='" + uniqueId + '\'' +
				", createDate=" + createDate +
				", createTime=" + createTime +
				", isAutoTagged=" + isAutoTagged +
				", meetPointType='" + meetPointType + '\'' +
				", autoTagCategory='" + autoTagCategory + '\'' +
				", autoTagSceneId=" + autoTagSceneId +
				", firstMeetTime=" + firstMeetTime +
				", isManualTagged=" + isManualTagged +
				", manualTagCategory='" + manualTagCategory + '\'' +
				", manualTagSceneId=" + manualTagSceneId +
				", mannualTagTime=" + mannualTagTime +
				", channel='" + channel + '\'' +
				", isToufangSign=" + isToufangSign +
				", isApp=" + isApp +
				'}';
	}
}
