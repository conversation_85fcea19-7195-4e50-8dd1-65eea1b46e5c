package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingRelationType;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;

/**
 *  筹款案例-收款人基础信息，不含身份证、手机号、银行卡等敏感数据
 *
 *  Created by wangsf on 17/2/14.
 */
@ApiModel("收款人信息")
public class CrowdfundingPayeeBaseInfoVo {

    @ApiModelProperty("收款人和患者关系")
	private CrowdfundingRelationType relationType;

    @ApiModelProperty("收款人姓名")
	private String payeeName;

    @ApiModelProperty("银行支行名称")
	private String payeeBankName;

	@ApiModelProperty("银行卡号")
	private String payeeBankCard;

	@ApiModelProperty("关系")
	private String relation;



	public CrowdfundingPayeeBaseInfoVo() {

	}

	public CrowdfundingPayeeBaseInfoVo(CrowdfundingInfo crowdfundingInfo) {
		if(crowdfundingInfo != null) {
			this.payeeName = crowdfundingInfo.getPayeeName();
			this.relationType = crowdfundingInfo.getRelationType();
			this.payeeBankName = crowdfundingInfo.getPayeeBankName();
			this.payeeBankCard = crowdfundingInfo.getPayeeBankCard();
			this.relation = crowdfundingInfo.getRelation();
		}
	}

	public CrowdfundingPayeeBaseInfoVo(String payeeName,CrowdfundingRelationType crowdfundingRelationType,String orgBankName,
									   String orgBankCard) {
		if(StringUtils.isNoneBlank(payeeName) && crowdfundingRelationType != null) {
			this.payeeName = payeeName;
			this.relationType = crowdfundingRelationType;
			this.payeeBankCard = orgBankCard;
			this.payeeBankName = orgBankName;

		}
	}

	public CrowdfundingRelationType getRelationType() {
		return relationType;
	}

	public void setRelationType(CrowdfundingRelationType relationType) {
		this.relationType = relationType;
	}

	public String getPayeeName() {
		return payeeName;
	}

	public void setPayeeName(String payeeName) {
		this.payeeName = payeeName;
	}

	public String getPayeeBankName() {
		return payeeBankName;
	}

	public void setPayeeBankName(String payeeBankName) {
		this.payeeBankName = payeeBankName;
	}

	public String getPayeeBankCard() {
		return payeeBankCard;
	}

	public void setPayeeBankCard(String payeeBankCard) {
		this.payeeBankCard = payeeBankCard;
	}

	public String getRelation() {
		return relation;
	}

	public void setRelation(String relation) {
		this.relation = relation;
	}
}
