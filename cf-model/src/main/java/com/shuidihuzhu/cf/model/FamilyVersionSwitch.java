package com.shuidihuzhu.cf.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

/**
 * @Description: 家庭经济结构调整开关。后续apollo删了，这个类可删
 * @Author: pan<PERSON><PERSON><PERSON>
 * @Date: 2024/9/5 4:11 PM
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FamilyVersionSwitch {

    /**
     * 自发起开关 true 走家庭经济结构版本
     */
    private boolean raiseSwitch;

    /**
     * 扫码自发起开关 true 走家庭经济结构版本
     */
    private boolean qRRaiseSwitch;

    /**
     * 回测使用的白名单用户
     */
    private Set<Long> whiteListUsers;

}
