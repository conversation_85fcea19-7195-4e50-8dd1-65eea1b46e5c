package com.shuidihuzhu.cf.model.crowdfunding;

import java.sql.Timestamp;

/**
 * Author   : roy
 * Date     : 2018-05-07  16:17
 * Describe :
 *
 * backup @JSONField(serialize = false)
 * <AUTHOR>
 */
public class CfTransformArticleDo {
    private long id;
    private String url;
    private String title;
    private String imgUrl;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    @Override
    public String toString() {
        return "CfTransformArticleDo{" +
                "id=" + id +
                ", url='" + url + '\'' +
                ", title='" + title + '\'' +
                ", imgUrl='" + imgUrl + '\'' +
                '}';
    }
}
