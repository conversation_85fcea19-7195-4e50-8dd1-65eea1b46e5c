package com.shuidihuzhu.cf.model.task;

import lombok.Data;

import java.sql.Timestamp;
import java.util.Date;

/**
 * @author: lixuan
 * @date: 2018/3/15 17:27
 */
@Data
public class CfInfoTaskRule {

    private long id;
    private String title;
    private String describe;
    private int startDay;
    private int endDay;
    private int sendInIntervalDay;
    private int noSendInIntervalDay;
    private int minVerifyNum;
    private int maxVerifyNum;
    private int completeMaterial;
    private String auditStatus;
    private String auditStatusNotIn;
    private int minDonationNum;
    private int maxDonationNum;
    private int minAttachNum;
    private int maxAttachNum;
    private String matchRules;
    private int timesInOneDay;
    private int sort;
    private int isDelete;
    private Timestamp createTime;
    private Timestamp updateTime;
    private int idcaseStatus;

}
