package com.shuidihuzhu.cf.model.crowdfunding;

import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2019/3/21 下午4:58
 * @desc
 */
@Data
public class CfUserLabelInfo {
    private long userId;
    //筹款用户拥有的案例数量
    private int caseNum = -1;
    //筹款用户当前是否有草稿
    private boolean draft;
    //30天内访问发起页次数
    private int accessRaisePageNum = -1;
    //筹款用户登记渠道
    private int toufangChannel = -1;
    //筹款用户拥有的案例标签详情
    private List<CfUserCaseLabelDO> caseDetailList = Lists.newArrayList();
}
