package com.shuidihuzhu.cf.model.ban;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/26  11:58 AM
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BanCaseInfo {
    private boolean flag;

    private List<CaseInfo> caseInfo;

    @Data
    public static class CaseInfo {
        private int caseId;
        private int count;
    }
}
