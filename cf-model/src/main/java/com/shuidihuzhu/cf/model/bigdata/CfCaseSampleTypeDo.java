package com.shuidihuzhu.cf.model.bigdata;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/12/26  22:00
 */
@Data
public class CfCaseSampleTypeDo {

    @ApiModelProperty("案例id")
    private Long infoId;
    @ApiModelProperty("相似案例id")
    private Long sampleTypeInfoId;
    @ApiModelProperty("类型,满足兜底则为2，不是兜底的为1")
    private Integer typeFlag;
    @ApiModelProperty("总筹款金额,单位：元")
    private BigDecimal donateAmt;
    @ApiModelProperty("总捐款次数")
    private Long donateAnt;
    @ApiModelProperty("总转发次数")
    private Long shareCnt;
    @ApiModelProperty("筹款人总转发次数")
    private Long ckrShareCnt;
    @ApiModelProperty("日期")
    private String dt;

}
