package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.common.web.model.AbstractModel;

import java.util.Date;

/**
 * Created by ch<PERSON> on 16/7/15.
 */
public class CrowdFundingPushFlag extends AbstractModel {

    private long userId;

    private Integer crowdFundingId;

    private Boolean pushFlag;

    private Date createTime;

    private Date updateTime;

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public Integer getCrowdFundingId() {
        return crowdFundingId;
    }

    public void setCrowdFundingId(Integer crowdFundingId) {
        this.crowdFundingId = crowdFundingId;
    }

    public Boolean isPushFlag() {
        return pushFlag;
    }

    public void setPushFlag(Boolean pushFlag) {
        this.pushFlag = pushFlag;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
