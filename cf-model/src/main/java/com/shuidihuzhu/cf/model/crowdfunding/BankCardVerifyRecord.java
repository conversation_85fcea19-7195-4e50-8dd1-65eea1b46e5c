package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.BankCardVerifyStatus;
import com.shuidihuzhu.cf.enums.crowdfunding.UserIdentityType;

import java.sql.Timestamp;

/**
 * Author: <PERSON> Wu
 * Date: 16/9/9 19:41
 */
public class BankCardVerifyRecord {
    /**
     * 流水号
     */
    private int id;
    /**
     * 所属的 CrowdfundingInfo 的 id, 参照
     *
     * @see CrowdfundingInfo#id
     */
    private int crowdfundingId;
    /**
     * 持卡人姓名
     */
    private String cryptoHolderName;
    /**
     * 证件类型,缺省为身份证号
     */
    private UserIdentityType identityType = UserIdentityType.identity;
    /**
     * 加密存储的身份证号码
     */
    private String cryptoIdCard;
    /**
     * 加密存储的银行卡号
     */
    private String cryptoBankCard;
    /**
     * 加密存储的手机号码
     * 不参与验证
     */
    private String cryptoMobile;
    /**
     * 验证状态,缺省为等待验证
     */
    private BankCardVerifyStatus verifyStatus = BankCardVerifyStatus.pending;
    /**
     * 验证的返回信息,缺省为 null
     */
    private String verifyMessage;
    /**
     * 验证的返回信息2,缺省为 null
     */
    private String verifyMessage2;
    private String tradeNo;
    private String thirdTradeNo;
    /**
     * 创建时间
     */
    private Timestamp dateCreated;
    /**
     * 最后更新时间
     */
    private Timestamp lastModified;

    public BankCardVerifyRecord() {
    }

    public BankCardVerifyRecord(int crowdfundingId, String cryptoHolderName,
                                UserIdentityType identityType, String cryptoIdCard,
                                String cryptoBankCard) {
        this.crowdfundingId = crowdfundingId;
        this.cryptoHolderName = cryptoHolderName;
        this.identityType = identityType;
        this.cryptoIdCard = cryptoIdCard;
        this.cryptoBankCard = cryptoBankCard;
    }

    public BankCardVerifyRecord(int crowdfundingId, String cryptoHolderName,
                                UserIdentityType identityType, String cryptoIdCard, String cryptoBankCard,
                                BankCardVerifyStatus verifyStatus, String verifyMessage, String verifyMessage2,
                                String tradeNo, String thirdTradeNo) {
        this.crowdfundingId = crowdfundingId;
        this.cryptoHolderName = cryptoHolderName;
        this.identityType = identityType;
        this.cryptoIdCard = cryptoIdCard;
        this.cryptoBankCard = cryptoBankCard;
        this.verifyStatus = verifyStatus;
        this.verifyMessage = verifyMessage;
        this.verifyMessage2 = verifyMessage2;
        this.tradeNo = tradeNo;
        this.thirdTradeNo = thirdTradeNo;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getCrowdfundingId() {
        return crowdfundingId;
    }

    public void setCrowdfundingId(int crowdfundingId) {
        this.crowdfundingId = crowdfundingId;
    }

    public String getCryptoHolderName() {
        return cryptoHolderName;
    }

    public void setCryptoHolderName(String cryptoHolderName) {
        this.cryptoHolderName = cryptoHolderName;
    }

    public UserIdentityType getIdentityType() {
        return identityType;
    }

    public void setIdentityType(UserIdentityType identityType) {
        this.identityType = identityType;
    }

    public String getCryptoIdCard() {
        return cryptoIdCard;
    }

    public void setCryptoIdCard(String cryptoIdCard) {
        this.cryptoIdCard = cryptoIdCard;
    }

    public String getCryptoBankCard() {
        return cryptoBankCard;
    }

    public void setCryptoBankCard(String cryptoBankCard) {
        this.cryptoBankCard = cryptoBankCard;
    }

    public String getCryptoMobile() {
        return cryptoMobile;
    }

    public void setCryptoMobile(String cryptoMobile) {
        this.cryptoMobile = cryptoMobile;
    }

    public BankCardVerifyStatus getVerifyStatus() {
        return verifyStatus;
    }

    public void setVerifyStatus(BankCardVerifyStatus verifyStatus) {
        this.verifyStatus = verifyStatus;
    }

    public String getVerifyMessage() {
        return verifyMessage;
    }

    public void setVerifyMessage(String verifyMessage) {
        this.verifyMessage = verifyMessage;
    }

    public String getVerifyMessage2() {
        return verifyMessage2;
    }

    public void setVerifyMessage2(String verifyMessage2) {
        this.verifyMessage2 = verifyMessage2;
    }

    public Timestamp getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(Timestamp dateCreated) {
        this.dateCreated = dateCreated;
    }

    public Timestamp getLastModified() {
        return lastModified;
    }

    public void setLastModified(Timestamp lastModified) {
        this.lastModified = lastModified;
    }

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public String getThirdTradeNo() {
        return thirdTradeNo;
    }

    public void setThirdTradeNo(String thirdTradeNo) {
        this.thirdTradeNo = thirdTradeNo;
    }
}
