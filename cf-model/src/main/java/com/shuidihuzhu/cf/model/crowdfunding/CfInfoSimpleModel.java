package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * @package: com.shuidihuzhu.cf.model.crowdfunding
 * @Author: l<PERSON>jiaw<PERSON>
 * @Date: 2018-11-28  20:48
 */
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Data
public class CfInfoSimpleModel {
    /**
     * 筹款ID,自增长
     */
    private int id;
    /**
     * UUID,前端引用时使用(避免暴露顺序ID,防止被猜中)
     */
    private String infoId;
    /**
     * 发起筹款人用户ID,发起筹款人必须通过水滴账号验证
     */
    private long userId = -1;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 终端渠道
     */
    private String channel;

    /**
     * 案例的材料提交的各种方案
     */
    private int materialPlanId;

    public boolean equalsCfInfo(CrowdfundingInfo info) {
        if (info == null) {
            return false;
        }
        if (this.channel.equals(info.getChannel()) &&
                this.userId == info.getUserId() &&
                this.createTime.equals(info.getCreateTime()) &&
                this.id == info.getId() &&
                this.infoId.equals(info.getInfoId()) &&
                this.materialPlanId == info.getMaterialPlanId()
        ) {
            return true;
        }
        return false;
    }

}
