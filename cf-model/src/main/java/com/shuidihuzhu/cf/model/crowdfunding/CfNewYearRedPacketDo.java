package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @time 2019/1/21 下午7:12
 * @desc
 */
@Data
@ToString
public class CfNewYearRedPacketDo implements Serializable {
    private long id;
    private long userId;
    private long caseId;
    private int redPacketType;
    private String redPacketDesc;
    private int blessingType;
    private String blessingDesc;
    private String name;
    private String phone;
    private String identify;
    private Date createTime;
    private Date updateTime;
}
