package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingRelationType;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.cf.enums.crowdfunding.UserIdentityType;
import com.shuidihuzhu.cf.finance.model.vo.DrawApplyCheckResult;
import com.shuidihuzhu.cf.vo.v5.CfMaterialAuditListView;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class CrowdfundingInfoPayeeVo extends CrowdfundingInfoParentVo {

	private CrowdfundingRelationType relationType;
	private String payeeName;
	private String idCard;
	private UserIdentityType idType;
	private String mobile;
	private String bankName;
	private String bankBranchName;
	private String bankCard;
	private String idCardPhoto;
	private List<String> attachments;
	private Map<Integer, List<String>> rejectDetails;
	private int status;
	//近亲属关系  @sea RelativesTypeEnum
	private int relativesType;
	//紧急联系人
	private String emergency;
	private String emergencyPhone;
	//关系证明
	private List<ProveVo> proves;
	//身份验证
	private List<ProveVo> authentication;
	//患者和授权委托书合照
	private String weituoshuHezhao;
	//
	private Map<Integer, Set<String>> rejects;
	//
	private List<CfMaterialAuditListView.ModifySuggest> suggests;

	/**
	 * 人脸识别结果  0 默认  10 失败   20 成功
	 */
	private int faceIdResult;

	/**
	 * 人脸识别图片
	 */
	private String faceVideo;

	//医院对公打款的相关信息
	private String department;
	private String bedNum;
	private String hospitalizationNum;
	private String hospitalAccountName;
	private String hospitalBankCard;
	private String hospitalBankBranchName;
	/**
	 * 对公紧急联系人姓名
	 */
	private String hospitalEmergencyName = "";
	/**
	 * 对公紧急联系人电话号
	 */
	private String hospitalEmergencyPhone = "";

	//慈善组织
	private String orgName;

	private String orgMobile;

	private String orgBankName;

	private String orgBankBranchName;

	private String orgBankCard;

	//图片地址 多个逗号分隔
	private String orgPic;
	// 收款人与患者关系视频
	private List<String> relationVideos;

	private boolean canSubmitRelationVideo;
	/**
	 * 上传其他身份证件 0：否; 1：是
	 */
	private int otherIdPhoto;

	/**
	 * 户口勾选状况
	 */
	private HuKouOption hukouOption;

	public CrowdfundingRelationType getRelationType() {
		return relationType;
	}

	public void setRelationType(CrowdfundingRelationType relationType) {
		this.relationType = relationType;
	}

	public String getPayeeName() {
		return payeeName;
	}

	public void setPayeeName(String payeeName) {
		this.payeeName = payeeName;
	}

	public String getIdCard() {
		return idCard;
	}

	public void setIdCard(String idCard) {
		this.idCard = idCard;
	}

	public UserIdentityType getIdType() {
		return idType;
	}

	public void setIdType(UserIdentityType idType) {
		this.idType = idType;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getBankName() {
		return bankName;
	}

	public void setBankName(String bankName) {
		this.bankName = bankName;
	}

	public String getBankBranchName() {
		return bankBranchName;
	}

	public void setBankBranchName(String bankBranchName) {
		this.bankBranchName = bankBranchName;
	}

	public String getBankCard() {
		return bankCard;
	}

	public void setBankCard(String bankCard) {
		this.bankCard = bankCard;
	}

	public String getIdCardPhoto() {
		return idCardPhoto;
	}

	public void setIdCardPhoto(String idCardPhoto) {
		this.idCardPhoto = idCardPhoto;
	}

	public List<String> getAttachments() {
		return attachments;
	}

	public void setAttachments(List<String> attachments) {
		this.attachments = attachments;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}

	public void setRejectDetails(Map<Integer, List<String>> rejectDetails) {
		this.rejectDetails = rejectDetails;
	}

	public Map<Integer, List<String>> getRejectDetails() {
		return rejectDetails;
	}

	@Override
	public int getStatus() {
		return status;
	}

	@Override
	public void setStatus(int status) {
		this.status = status;
	}

	public String getDepartment() {
		return department;
	}

	public void setDepartment(String department) {
		this.department = department;
	}

	public String getBedNum() {
		return bedNum;
	}

	public void setBedNum(String bedNum) {
		this.bedNum = bedNum;
	}

	public String getHospitalizationNum() {
		return hospitalizationNum;
	}

	public void setHospitalizationNum(String hospitalizationNum) {
		this.hospitalizationNum = hospitalizationNum;
	}

	public String getHospitalAccountName() {
		return hospitalAccountName;
	}

	public void setHospitalAccountName(String hospitalAccountName) {
		this.hospitalAccountName = hospitalAccountName;
	}

	public String getHospitalBankCard() {
		return hospitalBankCard;
	}

	public void setHospitalBankCard(String hospitalBankCard) {
		this.hospitalBankCard = hospitalBankCard;
	}

	public String getHospitalBankBranchName() {
		return hospitalBankBranchName;
	}

	public void setHospitalBankBranchName(String hospitalBankBranchName) {
		this.hospitalBankBranchName = hospitalBankBranchName;
	}

	public String getHospitalEmergencyName() {
		return hospitalEmergencyName;
	}

	public void setHospitalEmergencyName(String hospitalEmergencyName) {
		this.hospitalEmergencyName = hospitalEmergencyName;
	}

	public String getHospitalEmergencyPhone() {
		return hospitalEmergencyPhone;
	}

	public void setHospitalEmergencyPhone(String hospitalEmergencyPhone) {
		this.hospitalEmergencyPhone = hospitalEmergencyPhone;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getOrgMobile() {
		return orgMobile;
	}

	public void setOrgMobile(String orgMobile) {
		this.orgMobile = orgMobile;
	}

	public String getOrgBankName() {
		return orgBankName;
	}

	public void setOrgBankName(String orgBankName) {
		this.orgBankName = orgBankName;
	}

	public String getOrgBankBranchName() {
		return orgBankBranchName;
	}

	public void setOrgBankBranchName(String orgBankBranchName) {
		this.orgBankBranchName = orgBankBranchName;
	}

	public String getOrgBankCard() {
		return orgBankCard;
	}

	public void setOrgBankCard(String orgBankCard) {
		this.orgBankCard = orgBankCard;
	}

	public String getOrgPic() {
		return orgPic;
	}

	public void setOrgPic(String orgPic) {
		this.orgPic = orgPic;
	}

	public int getRelativesType() {
		return relativesType;
	}

	public void setRelativesType(int relativesType) {
		this.relativesType = relativesType;
	}

	public String getEmergency() {
		return emergency;
	}

	public void setEmergency(String emergency) {
		this.emergency = emergency;
	}

	public String getEmergencyPhone() {
		return emergencyPhone;
	}

	public void setEmergencyPhone(String emergencyPhone) {
		this.emergencyPhone = emergencyPhone;
	}

	public List<ProveVo> getProves() {
		return proves;
	}

	public void setProves(List<ProveVo> proves) {
		this.proves = proves;
	}

	public List<ProveVo> getAuthentication() {
		return authentication;
	}

	public void setAuthentication(List<ProveVo> authentication) {
		this.authentication = authentication;
	}

	public Map<Integer, Set<String>> getRejects() {
		return rejects;
	}

	public void setRejects(Map<Integer, Set<String>> rejects) {
		this.rejects = rejects;
	}

	public List<CfMaterialAuditListView.ModifySuggest> getSuggests() {
		return suggests;
	}

	public void setSuggests(List<CfMaterialAuditListView.ModifySuggest> suggests) {
		this.suggests = suggests;
	}


	public String getWeituoshuHezhao() {
		return weituoshuHezhao;
	}

	public void setWeituoshuHezhao(String weituoshuHezhao) {
		this.weituoshuHezhao = weituoshuHezhao;
	}

	public HuKouOption getHukouOption() {return hukouOption;}

	public void setHukouOption(HuKouOption hukouOption) {this.hukouOption = hukouOption;}

	@Data
	@ApiModel(value = "举报信息")
	public static class ReportInfo {

		@ApiModelProperty(value = "是否被举报")
		@JsonProperty("isReport")
		private boolean isReport;
		@JsonProperty("isReportComplete")
		@ApiModelProperty(value = "举报是否处理完成")
		private boolean isReportComplete;
		@JsonProperty("isSendProxyOrder")
		@ApiModelProperty(value = "是否下发待录入工单")
		private boolean isSendProxyOrder;
		@JsonProperty("isConfirm")
		@ApiModelProperty(value = "下发待录入用户是否确认")
		private boolean isConfirm;
		@JsonProperty("isSplitDraw")
		@ApiModelProperty(value = "举报是否被标记为分批打款")
		private boolean isSplitDraw;
		@ApiModelProperty(value = "最后提现成功金额，元")
		private String latestDrawAmountStr;
		@ApiModelProperty(value = "最后提现成功时间")
		private Date latestDrawSuccessTime;
	}

	@Data
	@ApiModel(value = "能否申请提现检查结果")
	public static class CanCashResult {
		@ApiModelProperty(value = "能否提现检查结果")
		private boolean canCash;

		@ApiModelProperty(value = "备注信息")
		private String remark;

		@ApiModelProperty(value = "案例infoUuid")
		private String infoUuid;

		@ApiModelProperty(value = "是否支持 边筹边取")
		private boolean supportDrawSplit;

		@ApiModelProperty(value = "提现错误类型")
		private DrawApplyCheckResult.ResultVo resultErrorType;

		@ApiModelProperty("是否为提现流程异常")
		private boolean isDrawProcessError;

		@ApiModelProperty(value = "案例审核状态")
		private CrowdfundingStatus crowdfundingStatus;

		@ApiModelProperty(value = "举报信息")
		private ReportInfo reportInfo;

		public CanCashResult() {
		}

		public CanCashResult(boolean canCash, String remark, CrowdfundingStatus crowdfundingStatus) {
			this.canCash = canCash;
			this.remark = remark;
			this.crowdfundingStatus = crowdfundingStatus;
		}

		public CanCashResult(boolean canCash, String remark, String infoUuid, boolean supportDrawSplit,
							 DrawApplyCheckResult.ResultVo resultVo, CrowdfundingStatus crowdfundingStatus) {
			this.canCash = canCash;
			this.remark = remark;
			this.infoUuid = infoUuid;
			this.supportDrawSplit = supportDrawSplit;
			this.resultErrorType = resultVo;
			this.crowdfundingStatus = crowdfundingStatus;
		}
	}

	public List<String> getRelationVideos() {
		return relationVideos;
	}

	public void setRelationVideos(List<String> relationVideos) {
		this.relationVideos = relationVideos;
	}

	public boolean isCanSubmitRelationVideo() {
		return canSubmitRelationVideo;
	}

	public void setCanSubmitRelationVideo(boolean canSubmitRelationVideo) {
		this.canSubmitRelationVideo = canSubmitRelationVideo;
	}

	public int getFaceIdResult() {
		return faceIdResult;
	}

	public void setFaceIdResult(int faceIdResult) {
		this.faceIdResult = faceIdResult;
	}

	public String getFaceVideo() {
		return faceVideo;
	}

	public void setFaceVideo(String faceVideo) {
		this.faceVideo = faceVideo;
	}

	public boolean faceIdResultPass(){
		return getFaceIdResult() == 20;
	}

	public int getOtherIdPhoto() {
		return otherIdPhoto;
	}

	public void setOtherIdPhoto(int otherIdPhoto) {
		this.otherIdPhoto = otherIdPhoto;
	}
}
