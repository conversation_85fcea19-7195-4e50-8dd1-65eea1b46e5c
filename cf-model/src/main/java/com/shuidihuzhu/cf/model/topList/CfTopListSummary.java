package com.shuidihuzhu.cf.model.topList;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.model.crowdfunding.CfUserCaseInfoDO;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;

@Data
public class CfTopListSummary {

    private long id;
    private int caseId;
    private String infoUuid;

    private String allShareTopInfo;
    private String friendShareTopInfo;

    private String allDonateTopInfo;
    private String friendDonateTopInfo;

    private String activityBeginTime;
    private String activityEndTime;
    private String canPrizeUsers;
    // 更新榜单的时间
    private long lastUpdateRankTime;

    private Date createTime;


    public long getAllShareTopInfoMin() {

        List<CfTopListSummary.CfDonorData> currentDonors =
                parseArray(allShareTopInfo);

        return CollectionUtils.isEmpty(currentDonors) ? 0 :
                currentDonors.get(currentDonors.size() - 1).getShareBringVisit();
    }

    public long getFriendShareTopInfoMin() {
        List<CfTopListSummary.CfDonorData> currentDonors =
                parseArray(friendShareTopInfo);

        return CollectionUtils.isEmpty(currentDonors) ? 0 :
                currentDonors.get(currentDonors.size() - 1).getShareBringVisit();
    }

    public int getAllDonateTopInfoMin() {
        List<CfTopListSummary.CfDonorData> currentDonors =
                parseArray(allDonateTopInfo);

        return CollectionUtils.isEmpty(currentDonors) ? 0 :
                currentDonors.get(currentDonors.size() - 1).getTotalContributeDonate();
    }

    public int getFriendDonateTopInfoMin() {
        List<CfTopListSummary.CfDonorData> currentDonors =
                parseArray(friendDonateTopInfo);

        return CollectionUtils.isEmpty(currentDonors) ? 0 :
                currentDonors.get(currentDonors.size() - 1).getTotalContributeDonate();
    }


    public static String jsonArrayToString(List<CfTopListSummary.CfDonorData> dataList) {

        if (CollectionUtils.isEmpty(dataList)) {
            return "";
        }

        return JSON.toJSONString(dataList);
    }

    public static List<CfTopListSummary.CfDonorData> parseArray(String jsonString) {

        if (StringUtils.isBlank(jsonString)) {
            return Lists.newArrayList();
        }

        try {
            return JSON.parseArray(jsonString, CfTopListSummary.CfDonorData.class);
        } catch (Exception e) {

        }
        return Lists.newArrayList();
    }

    public CfDonorPrizeData parseCanPrizeUsers() {

        if (StringUtils.isBlank(canPrizeUsers)) {
            return null;
        }

        return JSON.parseObject(canPrizeUsers, CfDonorPrizeData.class);
    }

    @Data
    public static class CfDonorPrizeData {

        private int topListType;
        List<CfDonorData> cfDonorDataList;

        private int verificateTopListType;
        List<CfDonorData> verificateCfDonorDataList;
    }

    @Data
    public static class CfDonorData {

        private long userId;
        // 捐款人转发带来的访问
        private long shareBringVisit;
        // 捐款人总的贡献额（包括转发带来的捐款）
        private int totalContributeDonate;
        // 捐款人自己的捐款
        private int ownDonate;

        private int rank;

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            CfDonorData donorData = (CfDonorData) o;
            return userId == donorData.userId &&
                    shareBringVisit == donorData.shareBringVisit &&
                    totalContributeDonate == donorData.totalContributeDonate &&
                    ownDonate == donorData.ownDonate &&
                    rank == donorData.rank;
        }

        @Override
        public int hashCode() {
            return Objects.hash(userId, shareBringVisit, totalContributeDonate, ownDonate, rank);
        }
    }

    @Data
    public static class CfDonorDataLifeCycleExt extends CfDonorData {

        private int shareBringDonate;
        private String rankChangeTime;
        private String topListChangeTime;
    }

    public static class ShareUserCaseInfoComparator implements Comparator<CfUserCaseInfoDO> {

        @Override
        public int compare(CfUserCaseInfoDO o1, CfUserCaseInfoDO o2) {

            return o2.getShareContributeView() - o1.getShareContributeView();
        }
    }

    public static class DonateUserCaseComparator implements Comparator<CfUserCaseInfoDO> {

        @Override
        public int compare(CfUserCaseInfoDO o1, CfUserCaseInfoDO o2) {

            int donateTotal1 = o1.getAmount() + o1.getShareContributeAmount();
            int donateTotal2 = o2.getAmount() + o2.getShareContributeAmount();

            return donateTotal2 - donateTotal1;
        }
    }

    public static class ShareDonorComparator implements Comparator<CfTopListSummary.CfDonorData> {

        @Override
        public int compare(CfTopListSummary.CfDonorData o1, CfTopListSummary.CfDonorData o2) {
            return (int)(o2.getShareBringVisit() - o1.getShareBringVisit());
        }
    }

    public static class DonateDonorComparator implements Comparator<CfTopListSummary.CfDonorData> {

        @Override
        public int compare(CfDonorData o1, CfDonorData o2) {
            return o2.getTotalContributeDonate() - o1.getTotalContributeDonate();
        }
    }


    @Data
    public static class CallAllRankHandleParam {
        private boolean callFriends;
        private Set<Long> friendUserIds;

        private boolean filterVerification;
        private Set<Long> verificationUserIds;

        private int topListType;
        private long caseRaiseUserId;

        public CallAllRankHandleParam() {

        }




        public CallAllRankHandleParam(boolean callFriends,
                                      Set<Long> friendUserIds,
                                      boolean filterVerification,
                                      Set<Long> verificationUserIds,
                                      int topListType,
                                      long caseRaiseUserId) {
            this.callFriends = callFriends;
            this.friendUserIds = friendUserIds;
            this.filterVerification = filterVerification;
            this.verificationUserIds = verificationUserIds;
            this.topListType = topListType;
            this.caseRaiseUserId = caseRaiseUserId;
        }
    }

    @Data
    public static class DonorDataList {
        private List<CfDonorData> allTopInfo;
        private List<CfDonorData>  friendTopInfo;
    }

    public static void main(String[] args) {
    }
}
