package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @time 2019/2/20 下午7:25
 * @desc
 */
@Data
public class CfUserBaseStatDO {
    private Integer id;

    private Long userId;

    private Integer donateCaseCount;

    private Integer donateCount;

    private Long donateAmount;

    private Integer shareCaseCount;

    private Integer shareCount;

    private Date createTime;

    private Date updateTime;

    private int isDelete;

    public CfUserBaseStatDO() {
        donateCaseCount = 0;
        donateCount = 0;
        donateAmount = 0L;
        shareCaseCount = 0;
        shareCount = 0;
    }
}
