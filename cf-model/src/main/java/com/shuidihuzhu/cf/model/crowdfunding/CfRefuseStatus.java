package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.CFRefuseType1StatusEnum;

import java.util.*;

/**
 * 驳回状态Model,用于各个界面显示被驳回信息
 * <p>
 * Author: <PERSON>
 * Date: 2017/2/9 16:06
 */
public class CfRefuseStatus {

	public static final CfRefuseStatus EMPTY = new CfRefuseStatus();

	private int infoId;

	public int getInfoId() {
		return infoId;
	}

	public void setInfoId(int infoId) {
		this.infoId = infoId;
	}

	/**
	 * 筹款列表页显示驳回状态的信息
	 * {@link CFRefuseType1StatusEnum}
	 */
	private int type1 = 0;
	/**
	 * 资料认证页,各项显示的驳回状态
	 */
	Map<Integer, Integer> type2 = new HashMap<>();
	/**
	 * 详情页每一项的驳回状态
	 */
	Map<Integer, List<String>> type3 = new HashMap<>();

	public int getType1() {
		return type1;
	}

	public Map<Integer, Integer> getType2() {
		return type2;
	}

	private Set<Integer> type1Set = new HashSet<>();

	public Set<Integer> getType1Set() {
		return type1Set;
	}

	public Map<Integer, List<String>> getType3() {
		return type3;
	}

	public void setType1(int type1) {
		this.type1 = type1;
	}

	public void setType2(Map<Integer, Integer> type2) {
		this.type2 = type2;
	}

	public void setType3(Map<Integer, List<String>> type3) {
		this.type3 = type3;
	}
}
