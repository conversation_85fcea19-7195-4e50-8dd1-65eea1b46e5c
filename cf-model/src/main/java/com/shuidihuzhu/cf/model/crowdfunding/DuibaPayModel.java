package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * @package: com.shuidihuzhu.cf.model.crowdfunding
 * @Author: liujiawei
 * @Date: 2018/9/3  16:39
 */
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Data
public class DuibaPayModel {
    String sUid;
    Long uid;
    String orderNum;
    String tradeNo;
    String cash;
    int amount;
    int payStatus;
    int thirdType;
    Date payTime;
    int refundStatus;
    Date refundTime;
    Date refundStartTime;
    int otherConfirm;
    Date createTime;
    String refundFailedReason;
    String refundReason;
}
