package com.shuidihuzhu.cf.model.contribute;

import lombok.Data;
import lombok.Getter;

import java.util.Date;
import java.util.List;

@Data
public class ContributeOrderPageVo {
    private long anchorId;
    private Boolean hasNext = false;
//    private Integer size;

    List<ContributeOrderView> orderViewList;

    @Data
    public static class ContributeOrderView {

        private String titleImg;

        private String payUid;

        private String infoUuid;
        private String title;

        private Date donateTime;
        private long donateAmount;

        private boolean hasEnd;

        private int refundStatus;
        private String refundFailedMsg;
        private String channel;
    }

    @Getter
    public enum DisplayRefundStatus {

        DEFAULT(0, ""),
        CAN_REFUND(1, "申请退款"),
        REFUNDING(2, "退款中"),
        REFUND_FAILED(3, "退款失败"),
        REFUND_SUCCESS(4, "退款成功"),
        ;

        private int code;
        private String msg;

        DisplayRefundStatus(int code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        public static int convertDisplayStatus(int orderStatus) {

            CfContributeOrder.OrderStatus statusEnum =
                    CfContributeOrder.OrderStatus.valueOfCode(orderStatus);
            switch (statusEnum) {
                case NO_PAY:
                    return DisplayRefundStatus.DEFAULT.getCode();
                case PAY_SUCCESS:
                    return DisplayRefundStatus.CAN_REFUND.getCode();
                case REFUNDING:
                    return DisplayRefundStatus.REFUNDING.getCode();
                case REFUND_FAILED:
                    return DisplayRefundStatus.REFUND_FAILED.getCode();
                case REFUND_SUCCESS:
                    return DisplayRefundStatus.REFUND_SUCCESS.getCode();
            }

            return DisplayRefundStatus.DEFAULT.getCode();
        }
    }

}
