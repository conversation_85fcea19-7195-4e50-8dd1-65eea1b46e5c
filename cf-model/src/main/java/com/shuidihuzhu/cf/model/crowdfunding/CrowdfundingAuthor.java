package com.shuidihuzhu.cf.model.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfSimpleTrueOrFalseEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.UserIdentityType;
import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * Created by wuxin<PERSON> on 6/21/16.
 * Modified by <PERSON> on 2016-09-12 for V3
 * 受助人信息
 * 理论上与 {@link CrowdfundingInfo} 是 1:1 关系
 * 所以,只会使用到 crowdfundingId 来做查询(应该将其当做主键来看待)
 */
@Data
public class  CrowdfundingAuthor implements Serializable {
	/**
	 * 自增长ID
	 */
	private int id;
	/**
	 * 筹款ID
	 * 参照 {@link CrowdfundingInfo#id}
	 */
	private int crowdfundingId;
	/**
	 * 受助人真实姓名
	 */
	private String name;
	/**
	 * 受助人证件号码,不存在于数据库
	 */
	private String idCard;
	private NumberMaskVo idCardMask;
	/**
	 * 受助人联系电话,不存在于数据库
	 * V3版本不再需要
	 */
	private String phone;
	private NumberMaskVo phoneMask;
	/**
	 * 受助人证件号码(加密)
	 */
	private String cryptoIdCard;
	/**
	 * 受助人联系电话(加密)
	 * V3版本不再需要
	 */
	private String cryptoPhone;
	/**
	 * 创建时间
	 */
	private Timestamp createTime;
	/**
	 * 修改时间
	 */
	private Timestamp lastModified;
	/**
	 * 本字段不再使用
	 */
	@Deprecated
	private int status;
	/**
	 * 受助人证件类型,目前老版本数据全是0,即身份证
	 * 参考 {@link UserIdentityType}
	 */
	private UserIdentityType idType;
	/**
	 * 受助人邮件
	 * V3版本不再需要
	 */
	private String mail;
	/**
	 * 受助人QQ
	 * V3版本不再需要
	 */
	private String qq;
	
	private Integer healthInsurance = CfSimpleTrueOrFalseEnum.FALSE.value();

	private Integer commercialInsurance = CfSimpleTrueOrFalseEnum.FALSE.value();

	private int birthYear;

	private int birthMonth;

	private int birthDay;

	private String provinceCode;

	private String cityCode;

	private String districtCode;

	private int gender;

	/**
	 * 发起人和患者的关系
	 */
	private UserRelTypeEnum relation;

	/**
	 * 人脸识别结果  0 默认  10 失败   20 成功
	 */
	private int faceIdResult;

	/**
	 * 没有身份证合照原因
	 */
	private int noIdCardPhotoReason;

	/**
	 * 上传其他身份证件 0：否; 1：是
	 */
	private int otherIdPhoto;

	public CrowdfundingAuthor() {
	}

	public CrowdfundingAuthor(int crowdfundingId, UserIdentityType idType, String name, String cryptoIdCard) {
		this.crowdfundingId = crowdfundingId;
		this.name = name;
		this.cryptoIdCard = cryptoIdCard;
		this.idType = idType;
	}

	@Override
	public String toString() {
		return JSON.toJSONString(this);
	}

}
