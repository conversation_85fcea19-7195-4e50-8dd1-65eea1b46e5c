package com.shuidihuzhu.cf.model.topList;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

public class DailyActivityTopList {

    public static final Map<ListType, Comparator> USER_INFO_COMPARATOR = Maps.newHashMap();
    public static final Map<ListType, Comparator> USER_VIEW_COMPARATOR = Maps.newHashMap();

    public static final String DAILY_REFRESH_TOP_LIST_LOCK = "dailyRefreshTopListLock";

    public static int MAX_SIZE = 20;

    static {

        USER_INFO_COMPARATOR.put(ListType.DONATE_COUNT, new DonateCountUserSummaryComparator());
        USER_INFO_COMPARATOR.put(ListType.BRING_VISIT, new BringVisitUserSummaryComparator());

        USER_VIEW_COMPARATOR.put(ListType.DONATE_COUNT, new DonateCountUserViewComparator());
        USER_VIEW_COMPARATOR.put(ListType.BRING_VISIT, new BringVisitUserViewComparator());
    }

    @Data
    public static class TopListSummary {

        private int activityPeriod;
        private String donateCountTopInfo;
        private String shareBringVisitTopInfo;
        private long lastUpdateRankTime;


        public List<UserInfoView> getInfoViewByListType(ListType listType) {

            if (listType == ListType.DONATE_COUNT) {
                return parseArray(donateCountTopInfo);
            } else if (listType == ListType.BRING_VISIT) {
                return parseArray(shareBringVisitTopInfo);
            }

            return Lists.newArrayList();
        }

        private List<UserInfoView> parseArray(String jsonString) {

            if (StringUtils.isBlank(jsonString)) {
                return Lists.newArrayList();
            }

            return JSON.parseArray(jsonString, UserInfoView.class);
        }


    }

    @Data
    public static class PrizeUserIds {
        private List<Long> donateCountUserIds =  Lists.newArrayList();
        private List<Long> shareBringVisitUserIds = Lists.newArrayList();
    }

    @Data
    public static class TopListResult {

        @ApiModelProperty("捐助榜单")
        private List<UserInfoView> donateRankList;
        @ApiModelProperty("转发助力榜单")
        private List<UserInfoView> bringVisitRankList;

        @ApiModelProperty("用户本人的数据")
        private UserContributeInfo userContributeInfo;
    }

    @Data
    public static class UserInfoView {
        @JsonIgnore
        private long userId;
        @ApiModelProperty("昵称")
        private String nickName;
        @ApiModelProperty("图像url")
        private String headImgUrl;

        @ApiModelProperty("捐助次数")
        private long donateCount;
        @ApiModelProperty("转发带来了多少访问")
        private long shareBringVisit;

        private long lastDonateTime;
        private long lastShareBringVisitTime;
    }


    @Data
    @ApiModel
    public static class UserContributeInfo {

        @ApiModelProperty("昵称")
        private String nickName;
        @ApiModelProperty("图像url")
        private String headImgUrl;

        @ApiModelProperty("在捐助榜单中的排名 null表示没有上榜")
        private Integer donateCountRank;
        @ApiModelProperty("在转发助力榜单中排名")
        private Integer shareBringVisitRank;

        @ApiModelProperty("捐助次数")
        private long donateCount;
        @ApiModelProperty("转发带来了多少访问")
        private long shareBringVisit;
    }

    @Data
    public static class BringVisitRecord {
        @ApiModelProperty("")
        private long userId;
        @ApiModelProperty("")
        private long visitUserId;
        @ApiModelProperty("")
        private int activityPeriod;
    }

    @Data
    public static class UserInfoSummary {
        @JsonIgnore
        private long id;
        private long userId;
        private int donateCount;
        private long lastDonateTime;
        private long shareBringVisit;
        private long lastShareBringVisitTime;

        /**
         * 访问主会场的类型  VisitVenueType
         */
        private int visitVenueType;
        private String sourceBringVisit;

        private int activityPeriod;
    }

    public enum ActivityPeriod {

        PERIOD_20200311(1, "20200311"),
        ;

        ActivityPeriod(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        private int code;
        private String desc;
    }

    public enum ListType {

        DONATE_COUNT(1, "捐助次数"),
        BRING_VISIT(2, "转发带来的访问"),
        ;

        ListType(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        private int code;
        private String desc;
    }

    @Getter
    public enum VisitVenueType {
        SELF_INITIATIVE(1, "主动访问"),
        SHARE_BRING_VISIT(2, "通过别人的分享链接进入访问"),
        ;

        VisitVenueType(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        private int code;
        private String desc;
    }


    public static class DonateCountUserSummaryComparator implements Comparator<UserInfoSummary> {

        @Override
        public int compare(UserInfoSummary o1, UserInfoSummary o2) {
            if (o1.getDonateCount() != o2.getDonateCount()) {
                return o2.getDonateCount() - o1.getDonateCount() > 0 ? 1 : -1;
            }

            if (o1.getLastDonateTime() == o2.getLastDonateTime()) {
                return 0;
            }

            return o2.getLastDonateTime() - o1.getLastDonateTime() > 0 ? 1 : -1;
        }
    }

    public static class BringVisitUserSummaryComparator implements Comparator<UserInfoSummary> {

        @Override
        public int compare(UserInfoSummary o1, UserInfoSummary o2) {
            if (o1.getShareBringVisit() != o2.getShareBringVisit()) {
                return o2.getShareBringVisit() - o1.getShareBringVisit() > 0 ? 1 : -1;
            }

            if (o1.getLastShareBringVisitTime() == o2.getLastShareBringVisitTime()) {
                return 0;
            }

            return o2.getLastShareBringVisitTime() - o1.getLastShareBringVisitTime() > 0 ? 1 : -1;
        }
    }

    public static class DonateCountUserViewComparator implements Comparator<DailyActivityTopList.UserInfoView> {

        @Override
        public int compare(DailyActivityTopList.UserInfoView o1, DailyActivityTopList.UserInfoView o2) {
            if (o1.getDonateCount() != o2.getDonateCount()) {
                return o2.getDonateCount() - o1.getDonateCount() > 0 ? 1 : -1;
            }

            if (o1.getLastDonateTime() == o2.getLastDonateTime()) {
                return 0;
            }

            return o2.getLastDonateTime() - o1.getLastDonateTime() > 0 ? 1 : -1;
        }
    }


    public static class BringVisitUserViewComparator implements Comparator<DailyActivityTopList.UserInfoView> {

        @Override
        public int compare(DailyActivityTopList.UserInfoView o1, DailyActivityTopList.UserInfoView o2) {
            if (o1.getShareBringVisit() != o2.getShareBringVisit()) {
                return o2.getShareBringVisit() - o1.getShareBringVisit() > 0 ? 1 : -1;
            }

            if (o1.getLastShareBringVisitTime() == o2.getLastShareBringVisitTime()) {
                return 0;
            }

            return o2.getLastShareBringVisitTime() - o1.getLastShareBringVisitTime() > 0 ? 1 : -1;
        }
    }


    public static void main(String[] args) {


//        for (int i = 0; i < 100; ++i) {
//            System.out.println(String.format(dropRecordTableSql, i));
//        }

//        for (int i = 0; i < 100; ++i) {
//            System.out.println(String.format(createRecordTableSql, i));
//        }
    }


}
