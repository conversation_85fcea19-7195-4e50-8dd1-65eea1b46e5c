package com.shuidihuzhu.cf.model.miniprogram;

public class CfForbidComment {
    private int id;
    private int topicId;
    private long userId;
    private String content;
    private int idDelete;

    public CfForbidComment() {
    }

    public CfForbidComment(int topicId,long userId, String content) {
        this.topicId = topicId;
        this.userId = userId;
        this.content = content;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getTopicId() {
        return topicId;
    }

    public void setTopicId(int topicId) {
        this.topicId = topicId;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getIdDelete() {
        return idDelete;
    }

    public void setIdDelete(int idDelete) {
        this.idDelete = idDelete;
    }

    @Override
    public String toString() {
        return "CfForbidComment{" +
                "id=" + id +
                ", topicId=" + topicId +
                ", userId=" + userId +
                ", content='" + content + '\'' +
                ", idDelete=" + idDelete +
                '}';
    }
}
