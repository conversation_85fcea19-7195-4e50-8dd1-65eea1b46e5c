package com.shuidihuzhu.cf.model.crowdfunding;

public class CfInfoFromFaceId {
    private long id;
    private String uuid;
    private String address;
    private String birthday;
    private int gender;
    private String idCardNumber;
    private String cardName;
    private String race;
    private String issuedBy;
    private String validDate;
    private String idFrontUrl;
    private String idBackUrl;
    private String livingUrl;
    private int medicalInsurance;
    private int commerciaInsurance;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public int getGender() {
        return gender;
    }

    public void setGender(int gender) {
        this.gender = gender;
    }

    public String getIdCardNumber() {
        return idCardNumber;
    }

    public void setIdCardNumber(String idCardNumber) {
        this.idCardNumber = idCardNumber;
    }

    public String getCardName() {
        return cardName;
    }

    public void setCardName(String cardName) {
        this.cardName = cardName;
    }

    public String getRace() {
        return race;
    }

    public void setRace(String race) {
        this.race = race;
    }

    public String getIssuedBy() {
        return issuedBy;
    }

    public void setIssuedBy(String issuedBy) {
        this.issuedBy = issuedBy;
    }

    public String getValidDate() {
        return validDate;
    }

    public void setValidDate(String validDate) {
        this.validDate = validDate;
    }

    public String getIdFrontUrl() {
        return idFrontUrl;
    }

    public void setIdFrontUrl(String idFrontUrl) {
        this.idFrontUrl = idFrontUrl;
    }

    public String getIdBackUrl() {
        return idBackUrl;
    }

    public void setIdBackUrl(String idBackUrl) {
        this.idBackUrl = idBackUrl;
    }

    public String getLivingUrl() {
        return livingUrl;
    }

    public void setLivingUrl(String livingUrl) {
        this.livingUrl = livingUrl;
    }

    public int getMedicalInsurance() {
        return medicalInsurance;
    }

    public void setMedicalInsurance(int medicalInsurance) {
        this.medicalInsurance = medicalInsurance;
    }

    public int getCommerciaInsurance() {
        return commerciaInsurance;
    }

    public void setCommerciaInsurance(int commerciaInsurance) {
        this.commerciaInsurance = commerciaInsurance;
    }
}
