package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.CfShareBizMapEnum;

import java.util.Date;

/**
 * Created by chao on 2017/10/9.
 */
public class CfShareBizMap {
	private long id;
	private int crowdfundingId;
	private int shareId;
	private long userId;

	private long bizId;

	private int type;
	private Date createTime;

	public CfShareBizMap(int crowdfundingId, int shareId,long userId, Long bizId, CfShareBizMapEnum type) {
		this.crowdfundingId = crowdfundingId;
		this.shareId = shareId;
		this.userId = userId;
		this.bizId = bizId;
		this.type = type.getCode();
	}

	public CfShareBizMap() {

	}

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public int getCrowdfundingId() {
		return crowdfundingId;
	}

	public void setCrowdfundingId(int crowdfundingId) {
		this.crowdfundingId = crowdfundingId;
	}

	public int getShareId() {
		return shareId;
	}

	public void setShareId(int shareId) {
		this.shareId = shareId;
	}

	public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}

	public long getBizId() {
		return bizId;
	}

	public void setBizId(long bizId) {
		this.bizId = bizId;
	}




	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
}
