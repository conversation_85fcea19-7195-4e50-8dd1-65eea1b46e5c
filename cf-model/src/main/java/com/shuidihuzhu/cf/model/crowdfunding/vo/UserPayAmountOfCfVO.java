package com.shuidihuzhu.cf.model.crowdfunding.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@ApiModel(value = "用户捐款信息model")
public class UserPayAmountOfCfVO {
    @ApiModelProperty(value = "昵称")
    private String nickname;
    @ApiModelProperty(value = "金额---分")
    private String amount;
    @ApiModelProperty(value = "头像地址")
    private String headImgUrl;
}
