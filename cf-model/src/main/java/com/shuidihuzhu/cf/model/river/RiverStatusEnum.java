package com.shuidihuzhu.cf.model.river;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;

/**
 * <AUTHOR>
 */
public enum RiverStatusEnum {

    /**
     * 默认值
     */
    DEFAULT(0, "默认"),

    @ApiModelProperty("已提交")
    SUBMITTED(1, "已提交"),

    @ApiModelProperty("已驳回")
    REJECT(2, "已驳回"),

    @ApiModelProperty("已通过")
    PASS(3, "已通过"),

    @ApiModelProperty("未提交")
    WAIT_SUBMIT(4, "未提交"),
    ;

    @Getter
    private final int value;
    @Getter
    private final String msg;

    RiverStatusEnum(int v, String msg) {
        this.value = v;
        this.msg = msg;
    }

    public static RiverStatusEnum parse(int v){
        for (RiverStatusEnum e : RiverStatusEnum.values()) {
            if (e.getValue() == v) {
                return e;
            }
        }
        throw new IllegalArgumentException("can not find RiverStatusEnum code: " + v);
    }
}
