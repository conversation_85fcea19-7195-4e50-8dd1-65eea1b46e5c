package com.shuidihuzhu.cf.model.crowdfunding;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @author: fengxuan
 * @create 2019-10-12 19:10
 **/
@Data
@NoArgsConstructor
public class AreaDistanceInfoDo {

    private long id;

    @ApiModelProperty("当前位置")
    private String localArea;

    @ApiModelProperty("目标位置")
    private String destArea;

    @ApiModelProperty("当前位置到目标位置的排名")
    private int distanceLevel;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新时间")
    private Date updateTime;
}
