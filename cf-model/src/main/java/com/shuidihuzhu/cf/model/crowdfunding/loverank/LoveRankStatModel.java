package com.shuidihuzhu.cf.model.crowdfunding.loverank;

import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.model.loverank.UserLoveRankLikeRecord;
import com.shuidihuzhu.cf.model.loverank.UserLoveRankLikeStat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class LoveRankStatModel {

//    @ApiModelProperty("userId+匿名捐款")
//    Map<String, UserDonateInfo> userDonateInfoMap;

    @ApiModelProperty("userId分享次数")
    Map<Long, Long> userShareCountMap;

//    @ApiModelProperty("userId账号信息")
//    Map<Long, UserInfoModel> userModelmap;

//    @ApiModelProperty("userId点赞信息")
//    Map<Long, List<UserLoveRankLikeRecord>> likeRecordMap;

//    @ApiModelProperty("userId点赞统计")
//    Map<Long, UserLoveRankLikeStat> likeStatMap;

    @ApiModelProperty("好友筹款金额，单位为分 ")
    private int friendsAmount;

    @ApiModelProperty("捐款的好友人数")
    private int friendsCount;

    @ApiModelProperty("捐款的好友集合")
    private List<UserDonateInfoVo> userDonateInfoList;

    @ApiModelProperty("登录用户捐 false未捐转，true至少有其中之一")
    private boolean donateShare = false;

    @ApiModelProperty("登录用户捐款金额，单位为分")
    private int donateAmount;

    @ApiModelProperty("登录用户分享次数")
    private int shareCount;


}
