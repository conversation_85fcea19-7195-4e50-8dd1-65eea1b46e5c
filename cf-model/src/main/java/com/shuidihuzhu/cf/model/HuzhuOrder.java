package com.shuidihuzhu.cf.model;

import com.shuidihuzhu.common.web.model.AbstractModel;
import com.shuidihuzhu.common.web.model.base.HasCode;

import java.util.Date;

public class HuzhuOrder extends AbstractModel implements HasCode {

    private static final long serialVersionUID = -1415118642059529326L;

    private long id;
    private String code;
    private long userId;
    private int insuredUserId;
    private int insuranceId;
    private int ruleId;
    private int amount;
    private int status;
    private Date addTime;
    private Date payTime;
    private int beneficiaryId;
    private Date activeTime;
    private Date lastModified;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}

	public int getInsuredUserId() {
		return insuredUserId;
	}

	public void setInsuredUserId(int insuredUserId) {
		this.insuredUserId = insuredUserId;
	}

	public int getBeneficiaryId() {
		return beneficiaryId;
	}

	public void setBeneficiaryId(int beneficiaryId) {
		this.beneficiaryId = beneficiaryId;
	}

	public int getInsuranceId() {
		return insuranceId;
	}

	public void setInsuranceId(int insuranceId) {
		this.insuranceId = insuranceId;
	}

	public int getRuleId() {
		return ruleId;
	}

	public void setRuleId(int ruleId) {
		this.ruleId = ruleId;
	}

	public int getAmount() {
		return amount;
	}

	public void setAmount(int amount) {
		this.amount = amount;
	}

	public int getStatus() {
		return status;
	}

	public void setStatus(int status) {
		this.status = status;
	}

	public Date getAddTime() {
		return addTime;
	}

	public void setAddTime(Date addTime) {
		this.addTime = addTime;
	}

	public Date getPayTime() {
		return payTime;
	}

	public void setPayTime(Date payTime) {
		this.payTime = payTime;
	}

	public Date getActiveTime() {
		return activeTime;
	}

	public void setActiveTime(Date activeTime) {
		this.activeTime = activeTime;
	}

	public Date getLastModified() {
		return lastModified;
	}

	public void setLastModified(Date lastModified) {
		this.lastModified = lastModified;
	}

}
