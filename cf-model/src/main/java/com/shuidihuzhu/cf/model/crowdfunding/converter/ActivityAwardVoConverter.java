package com.shuidihuzhu.cf.model.crowdfunding.converter;

import com.shuidihuzhu.cf.model.awardactivity.ActivityAward;
import com.shuidihuzhu.cf.vo.ActivityAwardVO;

public class ActivityAwardVoConverter {
	public ActivityAwardVoConverter() { }

	public ActivityAwardVO getActivityAwardVO(ActivityAward activityAward) {
		String thumbUrl = activityAward.getThumbUrl();
		if (activityAward.getType() == 4) {
			thumbUrl = "http://cf.alioss.shuidihuzhu.com/img/ck/20181227/8e6a3f4b-79da-4e05-aeea-12d2f36df0f2";
		} else if (activityAward.getType() == 3) {
			thumbUrl = "http://cf.alioss.shuidihuzhu.com/img/ck/20181227/a4d3aac2-1bc9-41b2-8dca-61d8f4a124bc";
		}
		return new ActivityAwardVO(activityAward.getId(),
		                           activityAward.getDescription().replace("爱心值", "积分"), activityAward.getType(), activityAward.getExtraId(),
		                           activityAward.getAwardType(), thumbUrl, activityAward.getName().replace("爱心值", "积分"),
		                           activityAward.getSort());
	}
}