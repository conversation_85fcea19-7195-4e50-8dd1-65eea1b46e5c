package com.shuidihuzhu.cf.model.crowdfunding;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * <AUTHOR>
 * @time 2019/5/22 下午8:54
 * @desc
 */
@ApiModel("图片从属信息")
@Data
public class OssPictureAttrDO {
    private Integer id;
    private Integer attaId;
    private Integer caseId;
    private int type;
    private String format;
    private String maker;
    private String model;
    private String shootTime;
    private String modifyTime;
    private Integer width;
    private Integer height;
    private Integer xDimension;
    private Integer yDimension;
    private String lat;
    private String latRef;
    private String lng;
    private String lngRef;
    private Boolean adobe;

    @ApiModelProperty("0=未知，1=有水印，2=没有水印")
    private int waterMark;

    @ApiModelProperty("ai识别概率")
    private String aiPsProb;
    @ApiModelProperty("ai识别p图工具")
    private String aiPsSoftware;
    @ApiModelProperty("新ai模型识别结果")
    private String newAiPs;
    @ApiModelProperty("ai识别时间")
    private long aiTime;


    private Date createTime;
    private Date updateTime;


    public static  OssPictureAttrDO buildFromAttachImg(CrowdfundingAttachment attachment) {

        if (attachment == null) {
            return null;
        }

        OssPictureAttrDO attrDO = new OssPictureAttrDO();
        attrDO.setAttaId(attachment.getId());
        attrDO.setCaseId(attachment.getParentId());
        attrDO.setType(attachment != null ? attachment.getTypeValue() : 0);
        attrDO.setFormat(StringUtils.EMPTY);
        attrDO.setMaker(StringUtils.EMPTY);
        attrDO.setModel(StringUtils.EMPTY);
        attrDO.setShootTime(StringUtils.EMPTY);
        attrDO.setModifyTime(StringUtils.EMPTY);
        attrDO.setWidth(0);
        attrDO.setHeight(0);
        attrDO.setXDimension(0);
        attrDO.setYDimension(0);
        attrDO.setLat(StringUtils.EMPTY);
        attrDO.setLatRef(StringUtils.EMPTY);
        attrDO.setLng(StringUtils.EMPTY);
        attrDO.setLngRef(StringUtils.EMPTY);
        attrDO.setAdobe(false);
        attrDO.setWaterMark(0);

       return attrDO;
    }

}
