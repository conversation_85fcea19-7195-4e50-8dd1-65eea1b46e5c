package com.shuidihuzhu.cf.model.gdmap;

import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class GdMapPoiModel {
    /**
     * 唯一ID
     */
    private String id;
    /**
     * 父POI的ID
     */
    private String parent;
    /**
     * 名称
     */
    private String name;
    /**
     * 兴趣点类型
     */
    private String type;
    /**
     * 兴趣点类型编码
     */
    private String typecode;
    /**
     * 地址
     */
    private String address;
    /**
     * 经纬度 格式：X,Y
     */
    private String location;
    /**
     * 离中心点距离
     */
    private String distance;
    /**
     * poi 的电话
     */
    private String tel;
    /**
     * 邮编
     */
    private String postcode;
    /**
     * poi的网址
     */
    private String website;
    /**
     * poi的电子邮箱
     */
    private String email;
    /**
     * poi所在省份编码
     */
    private String pcode;
    /**
     * poi所在省份名称
     */
    private String pname;
    /**
     * 城市编码
     */
    private String citycode;
    /**
     * 城市名
     */
    private String cityname;
    /**
     * 区域编码
     */
    private String adcode;
    /**
     * 区域名称
     */
    private String adname;
    /**
     * poi 别名
     */
    private String alias;
    /**
     * 改poi的特色内容
     */
    private String tag;
    /**
     * 比较输入和输出的gd_name 的相似度
     */
    private float similarRate;

    private int originId;

    private String similarAlias="无";
}
