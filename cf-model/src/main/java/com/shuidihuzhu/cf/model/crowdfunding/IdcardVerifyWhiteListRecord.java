package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@NoArgsConstructor
@Data
public class IdcardVerifyWhiteListRecord {

    private long id;
    private int verifyId;
    private String operator; //操作人
    private String operationRemark; //操作备注
    /**
     * {@link com.shuidihuzhu.cf.enums.crowdfunding.WhiteListReasonEnum}
     */
    private int reason; //添加原因
    private String otherReason; //其他原因
    private Timestamp createTime;

    public IdcardVerifyWhiteListRecord(int verifyId, String operator, int reason, String otherReason, String operationRemark) {
        this.verifyId = verifyId;
        this.operator = operator;
        this.reason = reason;
        this.otherReason = otherReason;
        this.operationRemark = operationRemark;
    }
}
