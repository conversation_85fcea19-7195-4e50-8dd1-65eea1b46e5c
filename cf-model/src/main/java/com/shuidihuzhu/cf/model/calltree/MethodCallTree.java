package com.shuidihuzhu.cf.model.calltree;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/2/2
 */
@Slf4j
@Data
@Builder
@ToString(exclude = {"parent"})
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
@AllArgsConstructor
public class MethodCallTree {


    /**
     * methodSignature
     */
    private String methodSignature;

    /**
     * dao / Feign
     */
    private String type;

    private String tabelNames;
    private String sql;

    private String feignHystrixTimeoutInMilliseconds;

    private String daoOperateType;
    private String faultName;
    private String remark;

    private String dataSourceName;

    private String feignUri;
    /**
     * method call methods
     */
    private List<MethodCallTree> children;



    @JsonIgnore
    private MethodCallTree parent;



}
