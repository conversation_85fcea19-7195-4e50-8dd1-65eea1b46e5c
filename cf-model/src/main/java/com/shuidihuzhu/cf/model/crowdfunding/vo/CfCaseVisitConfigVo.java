package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * Created by wangsf on 18/4/16.
 */
@Data
@ApiModel("控制案例展示详情的model")
public class CfCaseVisitConfigVo {

	/** 是否显示订制banner */
	private boolean showBanner;

	/** 是否可分享 */
	private boolean sharable;

	/**
	 * 是否可以捐款
	 */
	private boolean canDonate;

	@ApiModelProperty("是否能展示，如果为false，需要弹蒙层遮蔽住详情页不让显示")
	private boolean canShow;

	/** 案例图片地址 */
	private String bannerImgUrl;

	/** 案例banner跳转的地址 */
	private String bannerUrl;

	@ApiModelProperty("案例banner文字，可以和banner图片共存，需求请见：https://wiki.shuiditech.com/pages/viewpage.action?pageId=19961351 ")
	private String bannerText;

	/** 是否展示弹窗 */
	private boolean showPopup;

	/** 弹窗文本 */
	private String popupText;

	/** 弹窗标题 */
	private String popupTitle;

	/** 左滑页面的url */
	private String backPageUrl;

	/** 左滑页对应的组 */
	private int backPageUrlGroup;

	/**
	 * 左滑页是否要展示gif
	 */
	private boolean backPageShowGif;


	/**
	 * 隐藏内容 提示文案
	 */
	private String hiddenTitle;

	/**
	 * 发起图文AI检测 是否隐藏内容
	 */
	private boolean hidden;

	/**
	 * 是否是强制停止的筹款
	 */
	private boolean isForceStop;

	/**
	 * https://wiki.shuiditech.com/pages/viewpage.action?pageId=33588039
	 * 异常案例屏蔽详情页
	 */
	private boolean abnormalHidden;

	private String abnormalHiddenSelfTitle;

	private String abnormalHiddenOtherTitle;

	@ApiModelProperty("用户是否能主动结束案例")
	private boolean raiserFinish = true;

	@ApiModelProperty("是否能参加配捐")
	private boolean canDonateCooperate = true;

	@ApiModelProperty("不只能在微信展示，false表示只能在微信展示")
	private boolean notOnlyWechatShow = true;

	private JSONObject ninetyDaysSwitch;
}
