package com.shuidihuzhu.cf.model.crowdfunding;

public class CfDonateTestInfo {
    private long id;
    private long userId;
    private String openId;
    private int msgStatus;
    private int isDelete;

    public CfDonateTestInfo() {
    }

    public CfDonateTestInfo(long userId, String openId, int msgStatus, int isDelete) {
        this.userId = userId;
        this.openId = openId;
        this.msgStatus = msgStatus;
        this.isDelete = isDelete;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public int getMsgStatus() {
        return msgStatus;
    }

    public void setMsgStatus(int msgStatus) {
        this.msgStatus = msgStatus;
    }

    public int getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(int isDelete) {
        this.isDelete = isDelete;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    @Override
    public String toString() {
        return "CfDonateTestInfo{" +
                "id=" + id +
                ", userId=" + userId +
                ", openId='" + openId + '\'' +
                ", msgStatus=" + msgStatus +
                ", isDelete=" + isDelete +
                '}';
    }
}

