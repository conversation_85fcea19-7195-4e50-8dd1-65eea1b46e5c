package com.shuidihuzhu.cf.model.crowdfunding;

import java.util.Date;

public class CfRecommendInfoTagRelation {
    private Integer id;

    private Integer infoId;

    private Integer tagId;

    private Boolean valid;

    private Date createTime;

    private Date lastModified;

    private Integer orderNum;

    private String tag;

    private Short type;

    public CfRecommendInfoTagRelation(Integer id, Integer infoId, Integer tagId, Boolean valid, Date createTime, Date lastModified, Integer orderNum, String tag, Short type) {
        this.id = id;
        this.infoId = infoId;
        this.tagId = tagId;
        this.valid = valid;
        this.createTime = createTime;
        this.lastModified = lastModified;
        this.orderNum = orderNum;
        this.tag = tag;
        this.type = type;
    }

    public CfRecommendInfoTagRelation() {
        super();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getInfoId() {
        return infoId;
    }

    public void setInfoId(Integer infoId) {
        this.infoId = infoId;
    }

    public Integer getTagId() {
        return tagId;
    }

    public void setTagId(Integer tagId) {
        this.tagId = tagId;
    }

    public Boolean getValid() {
        return valid;
    }

    public void setValid(Boolean valid) {
        this.valid = valid;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getLastModified() {
        return lastModified;
    }

    public void setLastModified(Date lastModified) {
        this.lastModified = lastModified;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag == null ? null : tag.trim();
    }

    public Short getType() {
        return type;
    }

    public void setType(Short type) {
        this.type = type;
    }
}