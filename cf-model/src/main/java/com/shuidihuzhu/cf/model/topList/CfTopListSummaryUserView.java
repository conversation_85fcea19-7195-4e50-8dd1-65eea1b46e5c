package com.shuidihuzhu.cf.model.topList;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

@Data
public class CfTopListSummaryUserView {

    @Data
    public static class CfRaiserView {

        private List<ContributeRecordView> totalRecords;
        private List<ContributeRecordView> friendRecords;

        private int totalCanEdit;
        private int friendCanEdit;
        private int topListType;

        private int allTotalNum;
        private int friendTotalNum;

        private CfActivityTime activityTime;

        public CfRaiserView() {
        }

        public CfRaiserView(List<CfTopListSummaryUserView.ContributeRecordView> allRecords,
                            List<CfTopListSummaryUserView.ContributeRecordView> friendRecords,
                            int topListType) {

            this.totalRecords = allRecords;
            this.friendRecords = friendRecords;
            this.topListType = topListType;

            this.totalCanEdit = countCanEdit(allRecords);
            this.friendCanEdit = countCanEdit(friendRecords);
        }
    }

    @Data
    public static class CfRaiserViewV1 {
        private List<ContributeRecordView> records;
        private CfActivityTime activityTime;

        private int topListType;

        private int totalNum;
        private int totalCanEdit;

        public void setTotalRecords(List<ContributeRecordView> recordViews) {

            if (recordViews == null) {
                this.records = Lists.newArrayList();
                return;
            }
            this.records = recordViews;
            this.setTotalCanEdit(countCanEdit(recordViews));
        }

    }

    @Data
    public static class CfDonorView {
        private List<ContributeRecordView> records;

        private String prizeUrl;

        private CfSelfView cfSelfView;

        private CfActivityTime activityTime;

        private int topListType;

        private int totalNum;
    }

    @Data
    public static class CfSelfView {
        private String nickName;
        private String headImg;

        private String cryptoUserId;

        private int totalContributeDonate;
        private int ownDonate;
        private int shareBringDonate;
        private long shareBringVisit;

        private int rank;
        private int position;

    }

    @Data
    public static class CfActivityTime {
        private long activityBeginTime = 0;
        private long activityEndTime = 0;
    }

    @Data
    public static class ContributeRecordView {

        private String cryptoUserId;
        private String nickName;
        private String headImg;

        private long shareBringVisit;

        private int totalContributeDonate;
        private int ownDonate;
        private int shareBringDonate;

        private int canOperate;
        private int rank;
    }

    @Data
    public static class PrizeResult {
        private String prizeUrl;
        private String forSaveUrl;
    }

    @Data
    public static class TopListEagleResult {
        private int topListType;
        private int inAllTopList;
        private int inFriendTopList;

        public TopListEagleResult() {
        }

        public TopListEagleResult(int topListType, int inAllTopList, int inFriendTopList) {
            this.topListType = topListType;
            this.inAllTopList = inAllTopList;
            this.inFriendTopList = inFriendTopList;
        }

        public static TopListEagleResult NO_TOP_LIST_EAGLE_RESULT = new TopListEagleResult(
                CfTopListConstant.TopListType.NO_TOP_LIST.getCode(),
                CfTopListConstant.NOT_IN_RANK, CfTopListConstant.NOT_IN_RANK);
    }

    @Data
    public static class TopListQueryParam {
        private String infoUuid;
        private int topListType;
        private int topListScope;
        private int pageSize;
        private int pageNum;

        private int filterType;

    }

    public static int countCanEdit(List<CfTopListSummaryUserView.ContributeRecordView> allRecords) {
        if (CollectionUtils.isEmpty(allRecords)) {
            return CfTopListConstant.CAN_OPERATE;
        }

        for (CfTopListSummaryUserView.ContributeRecordView record : allRecords) {
            if (record.getCanOperate() == CfTopListConstant.CAN_OPERATE) {
                return CfTopListConstant.CAN_OPERATE;
            }
        }

        return CfTopListConstant.NOT_OPERATE;
    }


    public static void main(String[] args) {

       String param =  "{\"infoUuid\":\"143677a7-d9c7-4815-8466-67ba15d4f398\",\"topListType\":2,\"pageSize\":4,\"pageNum\":1,\"topListScope\":2}";

       System.out.println( JSON.parseObject(param, CfTopListSummaryUserView.TopListQueryParam.class));
    }
}
