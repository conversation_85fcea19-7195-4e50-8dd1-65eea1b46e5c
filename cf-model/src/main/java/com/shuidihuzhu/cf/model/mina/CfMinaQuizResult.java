package com.shuidihuzhu.cf.model.mina;

/**
 * Created by dongcf on 2017/12/12
 */
public class CfMinaQuizResult {

    private Integer id;

    private Integer minaQuizId;

    private Integer beginScore;

    private Integer endScore;

    private String scorePrefix;

    private String scoreSuffix;

    private String imgUrl;

    private String result;

    private String shareTitle;

    private String shareImgUrl;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getMinaQuizId() {
        return minaQuizId;
    }

    public void setMinaQuizId(Integer minaQuizId) {
        this.minaQuizId = minaQuizId;
    }

    public Integer getBeginScore() {
        return beginScore;
    }

    public void setBeginScore(Integer beginScore) {
        this.beginScore = beginScore;
    }

    public Integer getEndScore() {
        return endScore;
    }

    public void setEndScore(Integer endScore) {
        this.endScore = endScore;
    }

    public String getScorePrefix() {
        return scorePrefix;
    }

    public void setScorePrefix(String scorePrefix) {
        this.scorePrefix = scorePrefix;
    }

    public String getScoreSuffix() {
        return scoreSuffix;
    }

    public void setScoreSuffix(String scoreSuffix) {
        this.scoreSuffix = scoreSuffix;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getShareTitle() {
        return shareTitle;
    }

    public void setShareTitle(String shareTitle) {
        this.shareTitle = shareTitle;
    }

    public String getShareImgUrl() {
        return shareImgUrl;
    }

    public void setShareImgUrl(String shareImgUrl) {
        this.shareImgUrl = shareImgUrl;
    }
}
