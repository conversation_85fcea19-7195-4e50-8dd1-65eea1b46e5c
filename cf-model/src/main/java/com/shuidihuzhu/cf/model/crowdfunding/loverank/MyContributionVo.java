package com.shuidihuzhu.cf.model.crowdfunding.loverank;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("我的贡献")
public class MyContributionVo implements Serializable {


    @ApiModelProperty("好友关注数")
    private int friendsFollow ;
    @ApiModelProperty("几个好友捐款")
    private int friendsDonateCount;
    @ApiModelProperty("几个好友分享")
    private int friendsShareCount;
    @ApiModelProperty("好友捐款金额")
    private int friendsDonate;

    @ApiModelProperty("案例头图")
    private String titleImg;

    @ApiModelProperty("求助标题")
    private String title;

    @ApiModelProperty("求助文章")
    private String content;

    @ApiModelProperty("排行榜列表")
    private List<UserDonateInfoForC> userDonateList;
}
