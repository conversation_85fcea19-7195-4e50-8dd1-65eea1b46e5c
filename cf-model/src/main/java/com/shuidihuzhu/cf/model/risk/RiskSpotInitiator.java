package com.shuidihuzhu.cf.model.risk;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;


/**
 * 异常身份风险识别策略-发起人身份信息异常
 * <AUTHOR>
 * @date 2020-09-17 16:24:13
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RiskSpotInitiator {

    @ApiModelProperty(value = "加密的发起人手机号", required = true)
    private String cipherInitiatorMobile;

    @ApiModelProperty(value = "调用时机，见BlacklistCallPhaseEnum", required = true)
    private Integer callPhase = 1;

    @ApiModelProperty(value = "案例发起时间", required = true)
    private Date launchTime;

    @ApiModelProperty(value = "风控二级策略，见RiskStrategySecondEnum", required = true)
    private Integer secondStrategy = 1;

    @ApiModelProperty(value = "加密的发起人身份证号", required = true)
    private String cipherInitiatorIdCard;

    @ApiModelProperty(value = "caseId", required = true)
    private Integer caseId;

    @ApiModelProperty(value = "风控策略，见RiskStrategyEnum", required = true)
    private Integer riskStrategy = 2;

    @ApiModelProperty(value = "发起人userId", required = true)
    private Long userId;

    @ApiModelProperty(value = "当前调用时间", required = true)
    private Date callTime;

    @ApiModelProperty(value = "命中后限制动作", required = true)
    private Long limitAction = 3L;

    @ApiModelProperty(value = "患者证件类型")
    private Integer patientIdCardType;

    @ApiModelProperty(value = "加密证件号码")
    private String cipherIdCardNumber;

}