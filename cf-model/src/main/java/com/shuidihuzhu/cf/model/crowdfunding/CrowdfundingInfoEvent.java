package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.sql.Timestamp;

@ToString
@NoArgsConstructor
@AllArgsConstructor
public class CrowdfundingInfoEvent {

	private long id;
	private long userId;
	private int userThirdId;
	private int userThirdType;
	private int crowfundingId;
	private int eventType;
	private Timestamp dateCreated;
	private Timestamp lastModified;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}

	public int getUserThirdId() {
		return userThirdId;
	}

	public void setUserThirdId(int userThirdId) {
		this.userThirdId = userThirdId;
	}

	public int getUserThirdType() {
		return userThirdType;
	}

	public void setUserThirdType(int userThirdType) {
		this.userThirdType = userThirdType;
	}

	public int getCrowfundingId() {
		return crowfundingId;
	}

	public void setCrowfundingId(int crowfundingId) {
		this.crowfundingId = crowfundingId;
	}

	public int getEventType() {
		return eventType;
	}

	public void setEventType(int eventType) {
		this.eventType = eventType;
	}

	public Timestamp getDateCreated() {
		return dateCreated;
	}

	public void setDateCreated(Timestamp dateCreated) {
		this.dateCreated = dateCreated;
	}

	public Timestamp getLastModified() {
		return lastModified;
	}

	public void setLastModified(Timestamp lastModified) {
		this.lastModified = lastModified;
	}

}
