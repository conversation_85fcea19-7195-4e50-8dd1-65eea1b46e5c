package com.shuidihuzhu.cf.model.crowdfunding.message;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

@Data
@ApiModel
public class CfRefundRecord implements Serializable {

    @ApiModelProperty("案例id")
    public long caseId;

    @ApiModelProperty("案例退款次数")
    public int refundCount;

    @ApiModelProperty("案例退款金額")
    public int refundAmount;

    @ApiModelProperty("案例结束时间")
    private Timestamp finishTime;

    @ApiModelProperty("案例开始时间")
    private Timestamp caseCreateTime;


}
