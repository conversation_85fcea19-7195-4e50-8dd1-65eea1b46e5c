package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.shuidihuzhu.cf.client.material.model.CfRaiseFundUseModel;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum;
import com.shuidihuzhu.cf.model.CaseDisplaySettingDo;
import com.shuidihuzhu.cf.model.ban.BanInfoVo;
import com.shuidihuzhu.cf.model.crowdfunding.FoundationLabelInfoVO;
import com.shuidihuzhu.cf.model.crowdfunding.material.credit.CreditSupplementModel;
import com.shuidihuzhu.cf.model.getfundinginfo.CrowdfundingUserInfo;
import com.shuidihuzhu.cf.param.UserAccessParam;
import com.shuidihuzhu.cf.vo.crowdfunding.CfEndReasonVO;
import com.shuidihuzhu.cf.vo.crowdfunding.CrowdfundingBilingualVo;
import com.shuidihuzhu.cf.vo.initialaudit.CfInsuranceLivingAuditVo;
import com.shuidihuzhu.cf.vo.initialaudit.CfPropertyInsuranceVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.*;

@Data
@NoArgsConstructor
@ApiModel("详情页数据")
@JsonIgnoreProperties({"self","help","haveAddTrust","highRiskVerifying","haveHospitalAudit"})
public class FundingInfoVo {

    @ApiModelProperty("案例基本信息")
    private CrowdfundingBaseInfo baseInfo;

    @ApiModelProperty("案例初次审核状态")
    private FirstApproveStatusEnum firstApproveStatus;

    @ApiModelProperty("是否结束")
    private Boolean hasFinished;

    @ApiModelProperty("当前用户访问参数")
    private UserAccessParam userAccessParam;

    @ApiModelProperty("增信相关信息")
    private CreditSupplementModel creditSupplementModel;

    @ApiModelProperty("初审增加的增信相关信息")
    private CfPropertyInsuranceVO insuranceModelVo;

    @ApiModelProperty("展示状态")
    private Integer displayStatus;

    @ApiModelProperty("老的增信信息，已废弃，用creditSupplementModel")
    private List<CfCreditSupplementVo> creditSupplements = new ArrayList<>();

    @ApiModelProperty("当前案例分享数量")
    private Integer shareCount;

    @ApiModelProperty("筹款人用户信息")
    private CrowdfundingUserInfo crowdfundingUserInfo;

    @ApiModelProperty("案例每项材料状态")
    private Map<Integer, Integer> infoDataStatus = new HashMap<>();

    @ApiModelProperty("此案例为重复发起案例，请跳转到这个案例")
    private String redirectUuid;

    @ApiModelProperty("是否展示贫困说明")
    private Integer poverty;

    @ApiModelProperty("结束展示语，如案例已结束，优先使用这个字段，其次才用displayStatus对应的默认文案")
    private String finishStr;

    @ApiModelProperty("结束时间")
    private Timestamp finishTime;

    @ApiModelProperty("申请退款状态")
    private Integer applyRefundStatus;

    @JsonProperty("isHelp")
    @JSONField(name = "isHelp")
    private Boolean help;

    @JsonProperty("isTodayHelp")
    @JSONField(name = "isTodayHelp")
    private Boolean todayHelp;

    @ApiModelProperty("用户当天对该案例转发次数")
    private Integer userShareCountToday;

    @JsonProperty("isHaveAddTrust")
    @JSONField(name = "isHaveAddTrust")
    private Boolean haveAddTrust;

    @ApiModelProperty("公约版、新版的区别，默认都是2000公约版")
    private Integer cfVersion;

    @ApiModelProperty("材料是否齐全，1是齐全")
    private Integer dataStatus;

    @JsonProperty("isHighRiskVerifying")
    @JSONField(name = "isHighRiskVerifying")
    private Boolean highRiskVerifying;

    @ApiModelProperty("申请提现状态")
    private Integer applyDrawStatus;

    @JsonProperty("isHaveHospitalAudit")
    @JSONField(name = "isHaveHospitalAudit")
    private Boolean haveHospitalAudit;

    @ApiModelProperty("收款人信息")
    private CrowdfundingPayeeBaseInfoVo payeeInfo;

    @ApiModelProperty("案例是否有人证实")
    private Boolean hasVerified;

    @ApiModelProperty("案例结束原因")
    private Integer finishReason;

    @ApiModelProperty("平台案例结束文案")
    private CfEndReasonVO platformFinishReason;

    @ApiModelProperty("拒绝原因，参见CFRefuseType1StatusEnum")
    private Integer rejectType;

    @ApiModelProperty("诊断信息")
    private CrowdfundingTreatmentInfo treatmentInfo;

    @JsonProperty("isSelf")
    @JSONField(name = "isSelf")
    private Boolean self;

    @ApiModelProperty("医院审核状态")
    private Integer cfHospitalAuditInfoStatus;

    @ApiModelProperty("控制案例展示详情的model")
    private CfCaseVisitConfigVo visitConfig;

    @ApiModelProperty("发起人的真实姓名")
    private String raiserName;

    @ApiModelProperty("患者的真实姓名")
    private String patientName;

    @ApiModelProperty("自定义关系展示")
    private String customRelationDesc;

    @ApiModelProperty("免手续费标记")
    private int noHandlingFee;

    @ApiModelProperty("风险案例标识 true:风险案例")
    private boolean caseEndFlag;

    @ApiModelProperty("动态列表改版，新老案例标识别 true:新案例；false:老案例")
    private boolean caseProgressVersion;

    /**
     * {@link PropertyInsuranceCaseType}
     */
    @ApiModelProperty("案例的增信是在那个阶段提交")
    private int caseRaiseMaterialType;

    @ApiModelProperty("提现用途审核")
    private List<FundUseDetail> fundUseDetails;

    @ApiModelProperty("案例是否展示疾病标签、科普等扩展信息")
    private Boolean showDiseaseExtraInfo;

    @ApiModelProperty("案例的双语展示设置")
    private CrowdfundingBilingualVo crowdfundingBilingual;

    /**
     * 资金用途
     */
    @Data
    @ApiModel
    public static class FundUseDetail {
        @ApiModelProperty("票据文案")
        private String content;
        @ApiModelProperty("票据图片地址、多张图片用逗号分割")
        private List<String> imageUrls;
        @ApiModelProperty("票据上传时间")
        private long createTime;


        public FundUseDetail() {
        }

        public FundUseDetail(String content, List<String> imageUrls, long createTime) {
            this.content = content;
            this.imageUrls = imageUrls;
            this.createTime = createTime;
        }
    }

    @ApiModelProperty("是否已经开始打款 0 未打款 1 开始打款")
    private Integer startPaying;

    @ApiModelProperty("开始打款时间 单位ms")
    private Long startPayingTime;

    @ApiModelProperty("提现公示起始时间 单位ms")
    private Long drawPublicStartTime;

    @ApiModelProperty("申请提现的原因")
    private ApplyDrawReason applyDrawReason;

    @Data
    public static class ApplyDrawReason {
        @ApiModelProperty(value = "提现申请终止原因")
        private String stopCfReason;
        @ApiModelProperty(value = "资金用途")
        private String useOfFunds;
        @ApiModelProperty(value = "患者病情现状")
        private String patientConditionNow;
        @ApiModelProperty(value = "提现说明的图片")
        private List<String> imageUrls;


        public ApplyDrawReason() {
        }

        public ApplyDrawReason(String stopCfReason, String useOfFunds, String patientConditionNow,
                               List<String> imageUrls) {
            this.stopCfReason = stopCfReason;
            this.useOfFunds = useOfFunds;
            this.patientConditionNow = patientConditionNow;
            this.imageUrls = imageUrls;
        }
    }

    @ApiModelProperty("案例的生命周期线")
    private CaseDetailStage detailStage;

    @ApiModelProperty("案例的增信审核状态")
    private int insuranceStatus;

    @ApiModelProperty("案例材料审核状态")
    private CrowdfundingStatus materialStatus;

    @ApiModelProperty("资金用途")
    private CfRaiseFundUseModel fundUseModel;

    @ApiModelProperty("低保情况")
    private CfInsuranceLivingAuditVo.CfBasicLivingGuardVo livingGuardVo;

    @ApiModelProperty("支付详情页里需要展示的信息")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private CfOrderPaySuccessPageVO paySuccessPageVO;

    @ApiModelProperty("pr详情页展示")
    private PrDetailsPageCollocateVo prDetailsPageCollocateVo;

    @ApiModelProperty("pr详情页展示新版或者老版")
    private boolean prNewOrOld;

    @ApiModelProperty("为谁筹款-发起人与患者关系")
    private int relationTypeForC;

    @ApiModelProperty("是否在分享内容测试黑名单中")
    private boolean inShareContentTestBlackList;

    @ApiModelProperty("新版案例详情页信息")
    private NewDetailVo newDetailVo;

    @ApiModelProperty("多域名and是否资产合并")
    private Map<String, Object> domainAddressAndAccountOpenMap;

    @ApiModelProperty("商业转发人管控")
    private BanInfoVo banInfoVo;

    /**
     * 案例详情页的阶段
     */
    public enum CaseDetailStage {
        BASE_AUDIT(1, "初审"),
        MATERIAL_AUDIT(2, "材料公示"),
        DRAW_PUBLIC(3, "提现公示"),
        // 李戎的 边筹边取，公示、付款合并成一个。
        @Deprecated
        BEGIN_DRAW(4, "开始打款"),
        FUND_USE_SUBMIT(5, "提现用途审核"),
        ;

        CaseDetailStage(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        private int code;
        private String desc;
    }

    @ApiModelProperty("判断贫困建档和低保户新旧文案标识别 true：新文案；false：旧文案")
    private boolean poorHouseholdsNewTextSwitch;

    @ApiModelProperty("缙云医保局“困难申报人员”已核验 true：展示；false：不展示")
    private boolean jinYunLabel;

    @ApiModelProperty("缙云医保局“困难申报人员”弹窗")
    private boolean jinYunDifficultyPersonPop;

    @ApiModelProperty("是否合作方核实")
    private boolean missionVerify;

    @ApiModelProperty("当天访问次数")
    private long todayVisitCount;

    @ApiModelProperty("医院核实信息")
    private HospitalAuditInfo hospitalAuditInfo;

    @ApiModelProperty("就诊医院和核实医院是否一致标识")
    private boolean treatMentHospitalIsSameAuditHospital;

    @ApiModelProperty("医院核实时间控制开关, 展示时机 -> 初审通过时间 > 配置时间")
    private boolean showHospitalAuditTimeFlag;

    /***
     * 提升案例美誉度需求, 新增-> 医院审核状态字段 ,没有复用cfHospitalAuditInfoStatus字段 ,历史逻辑未知
     */
    @ApiModelProperty("医院审核状态 ")
    private Integer auditStatus;

    @ApiModelProperty("是否展示月报入口 ")
    private boolean monthlyReportFlag;

    @ApiModelProperty("月报月份")
    private String monthlyReportTime;

    @ApiModelProperty("基金会标签展示信息")
    private FoundationLabelInfoVO foundationLabelInfo;

    @ApiModelProperty("案例页辟谣公告栏")
    private List<CfCasePageAnnouncementManageVo> cfCasePageAnnouncementManageVo;

    @ApiModelProperty("统计数据")
    private CfUserCaseBaseStatVo cfUserCaseBaseStatVo;

    @Data
    public static class HospitalAuditInfo{

        @ApiModelProperty(value = "医院名称")
        private String hospitalName;

        @ApiModelProperty(value = "科室名称")
        private String departmentName;

        @ApiModelProperty(value = "住院号")
        private String hospitalizationNumber;

        @ApiModelProperty(value = "主治医生")
        private String doctorName;

        @ApiModelProperty(value = "致电核实时间")
        private String auditTime;

    }

    @Data
    public static class CfCasePageAnnouncementManageVo {

        private String title;
        private String imgUrl;
        private String popImgUrl;
        private String shortcutUrl;
        private String shortcutUrlDesc;

    }

    @Data
    public static class CfUserCaseBaseStatVo {

        @ApiModelProperty(value = "用户转发当前案例带来的总捐款金额（单位分）")
        private Integer shareBroughtAmount;
        @ApiModelProperty(value = "用户转发当前案例带来的第一笔捐款金额（单位分）")
        private Integer shareFirstAmount;
        @ApiModelProperty(value = "用户对当前案例的捐款金额")
        private Integer donateAmount;
        @ApiModelProperty(value = "用户对当前案例的转发次数")
        private Integer shareCount;
        @ApiModelProperty(value = "用户转发当前案例带来访问次数")
        private Integer shareVisitCount;

    }

    public void dataInit() {
        this.todayHelp = false;
        this.help = false;
        this.hasVerified = false;
    }


}
