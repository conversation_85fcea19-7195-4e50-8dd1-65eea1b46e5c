package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.Data;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @time 2019/7/8 上午11:29
 * @desc
 */
@Data
public class CfTitleTemplate {
    private long id;
    private int titleType;
    private String template;
    private Timestamp createTime;
    private Timestamp updateTime;
    private int isDelete;

    public static CfTitleTemplate build(CfBaseInfoTitleTemplate template){
        CfTitleTemplate titleTemplate = new CfTitleTemplate();
        titleTemplate.setId(template.getId());
        titleTemplate.setTitleType(template.getTitleType());
        titleTemplate.setTemplate(template.getTemplate());
        titleTemplate.setCreateTime(template.getCreateTime());
        titleTemplate.setUpdateTime(template.getUpdateTime());
        titleTemplate.setIsDelete(template.getIsDelete());
        return titleTemplate;
    }
}
