package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.Data;

import java.io.Serializable;

@Data
public class CfCaseCountStat implements Serializable {
    /**
     * 案例已筹金额
     */
    private double amout;
    /**
     * 案例id
     */
    private int infoId;
    /**
     * 案例id，字符串标识
     */
    private String infoUuid;
    /**
     * 捐款次数
     */
    private int donationCount;
    /**
     * 证实次数
     */
    private int verifyCount;
    /**
     * 转发次数
     */
    private int shareCount;
    /**
     * 资金状态
     */
    private Integer cfStateStatus;
    /**
     * 案例状态
     */
    private Integer cfBaseStatus;
}
