package com.shuidihuzhu.cf.model.crowdfunding;

/**
 * 驳回项目
 *
 * 患者合照/收款人合照 等
 *
 * Created by wangsf on 17/1/20.
 */
public class CrowdfundingRefuseReasonItem {
    private int id;
    private String content;
    private int type;
    private int proType;

    private int groupRank;
    // 在新增C端驳回位置时、要和pm确定展示给用户的文案， 没有就用content。 https://wiki.shuiditech.com/pages/viewpage.action?pageId=277284602
    private String contentForUser;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getProType() {
        return proType;
    }

    public void setProType(int proType) {
        this.proType = proType;
    }

    public int getGroupRank() {
        return groupRank;
    }

    public void setGroupRank(int groupRank) {
        this.groupRank = groupRank;
    }

    public String getContentForUser() {
        return contentForUser;
    }

    public void setContentForUser(String contentForUser) {
        this.contentForUser = contentForUser;
    }
}
