package com.shuidihuzhu.cf.model.crowdfunding.initialAudit;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoDataStatusTypeEnum;
import com.google.common.collect.Sets;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.model.river.RiverStatusEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;

public class InitialAuditItem {
    // 图文的材料
    public static final int CASE_BASE_INFO_MATERIAL = 1;
    // 前置的材料
    public static final int FIRST_APPROVE_INFO = 100;

    // 新增信
    public static final int CREDIT_INFO = CrowdfundingInfoDataStatusTypeEnum.CREDIT_INFO_NEW.getCode();

    @AllArgsConstructor
    @Getter
    public enum EditMaterialType {

        NO_OP(-1, "没有驳回", " "),

        // 前置相关
        PATIENT_NAME_IDCARD(1, "患者姓名和身份证号", "患者信息"),
        IMAGE_URL(2, "医疗材料图片", "医疗材料"),
        TARGET_AMOUNT_DESC(3, " 目标金额原因", "所需金额说明"),
        SUGGEST_END_CASE(4, " 建议停止筹款", "停止筹款"),
        POVERTY(5, "贫困证明材料", "贫困证明"),
        ALL_FIRST_APPROVE(6, "患者姓名和身份证号、医疗材料、目标金额原因", " "),
        SELF_NAME_ID_CARD(9, "发起人姓名和身份证号", "发起人信息"),

        /**
         * 只入库使用，不返回给前端
         * 老前置数据状态解析
         */
        @Deprecated
        ALL_FIRST_APPROVE_OLD(99, "患者姓名和身份证号、医疗材料、目标金额原因", " "),

        // 图文相关
        BASE_INFO_TITLE(16, "筹款标题", "标题"),
        BASE_INFO_CONTENT(8, "求助说明", "求助说明"),
        BASE_INFO_IMAGE(7, "添加图片", "展示图片"),
        TARGET_AMOUNT(10, "筹款目标金额", "筹款目标金额"),


        // 驳回位置
        HOUSE(21, "房产信息", "房产信息"),
        CAR(22, "车产信息", "车产信息"),
        HOME_INCOME(23, "家庭年收入", "家庭年收入"),
        HOME_STOCK(24, "家庭金融资产", "家庭金融资产"),
        HOME_DEBT(25, "负债情况", "负债情况"),
        MEDICAL_INSURANCE(26, "医保情况", "医保情况"),
        LIFE_INSURANCE(27, "人身险", "人身险"),
        PROPERTY_INSURANCE(28, "财产险", "财产险"),
        GOV_RELIEF(29, "政府医疗救助情况", "政府医疗救助情况"),

        DI_BAO(31, "低保情况", "低保情况"),
        PIN_KUN(32, "建档立卡贫困户", "建档立卡贫困户"),
        HAS_OTHER_PLATFORM(33, "其他平台筹款", "其他平台筹款"),

        BUSINESS_INSURANCE(36, "商业保险", ""),

        SELF_BUILT_HOUSE(38, "自建房信息", "自建房信息"),
        OTHER_HOUSE(39, "其他房屋信息", "其他房屋信息"),
        PATIENT_FAMILY_HAS_HOUSE(40, "患者核心家庭经济情况-有无房产", "患者核心家庭经济情况-有无房产"),
        PATIENT_PARENT_FAMILY_MARRIED(41, "患者核心家庭经济情况-婚姻状态", "患者核心家庭经济情况-婚姻状态"),
        PATIENT_FAMILY_SELF_HOUSE(42, "患者核心家庭经济情况-自建房信息", "患者核心家庭经济情况-自建房信息"),
        PATIENT_FAMILY_COMMODITY_HOUSE(43, "患者核心家庭经济情况-商品房信息", "患者核心家庭经济情况-商品房信息"),
        PATIENT_FAMILY_OTHER_HOUSE(44, "患者核心家庭经济情况-其他房产信息", "患者核心家庭经济情况-其他房产信息"),
        PATIENT_FAMILY_EARN_CAR(45, "患者核心家庭经济情况-营生车产信息", "患者核心家庭经济情况-营生车产信息"),
        PATIENT_FAMILY_NO_EARN_CAR(46, "患者核心家庭经济情况-非营生车产信息", "患者核心家庭经济情况-非营生车产信息"),
        PATIENT_FAMILY_INCOME(47, "患者核心家庭经济情况-收入信息", "患者核心家庭经济情况-收入信息"),
        PATIENT_FAMILY_LIABILITIES(48, "患者核心家庭经济情况-负债信息", "患者核心家庭经济情况-负债信息"),
        PATIENT_FAMILY_FINANCIAL(49, "患者核心家庭经济情况-金融资产信息", "患者核心家庭经济情况-金融资产信息"),
        PATIENT_FAMILY_MEDICAL_INSURANCE(50, "患者核心家庭经济情况-医保信息", "患者核心家庭经济情况-医保信息"),
        PATIENT_FAMILY_LIFE_INSURANCE(51, "患者核心家庭经济情况-人身险信息", "患者核心家庭经济情况-人身险信息"),
        PATIENT_FAMILY_OTHER_SALVATION(52, "患者核心家庭经济情况-其他救助信息", "患者核心家庭经济情况-其他救助信息"),
        PATIENT_FAMILY_DI_BAO(53, "患者核心家庭经济情况-低保信息", "患者核心家庭经济情况-低保信息"),
        PATIENT_CHILD_FAMILY_HOUSE(54, "患者已婚子女家庭经济情况-高价值房产", "患者已婚子女家庭经济情况-高价值房产"),
        PATIENT_CHILD_FAMILY_FREE_HOUSE(55, "患者已婚子女家庭经济情况-闲置房产", "患者已婚子女家庭经济情况-闲置房产"),
        PATIENT_CHILD_FAMILY_CAR(56, "患者已婚子女家庭经济情况-高价值车产", "患者已婚子女家庭经济情况-高价值车产"),
        PATIENT_CHILD_FAMILY_MUCH_CAR(57, "患者已婚子女家庭经济情况-多辆车产", "患者已婚子女家庭经济情况-多辆车产"),
        PATIENT_PARENT_FAMILY_HOUSE(58, "患者父母家庭经济情况-房产信息", "患者父母家庭经济情况-房产信息"),
        PATIENT_PARENT_FAMILY_CAR(59, "患者父母家庭经济情况-车产信息", "患者父母家庭经济情况-车产信息"),
        PATIENT_FAMILY_CAR(60, "患者核心家庭经济情况-车产信息", "患者核心家庭经济情况-车产信息"),
        PATIENT_MATRIMONY_INFO(61, "患者婚姻信息", "患者婚姻信息"),
        PATIENT_CHILD_INFO(62, "患者已婚子女信息", "患者已婚子女信息"),
        PATIENT_PARENT_INFO(63, "患者父母信息", "患者父母信息"),
        PATIENT_CHILD_COUNT(64, "患者已婚子女数量", "患者已婚子女数量"),
        ;

        private int code;
        private String desc;
        // shuidi_crowdfunding.cf_refuse_reason_item.content 字段   dbContent = "  "这些驳回位置是给C端驳回多个用。
        private String dbContent;
        /**
         * 前置
         */
        public static Set<EditMaterialType> FIRST_APPROVE_BASE_SET = Sets.newHashSet(PATIENT_NAME_IDCARD,
                IMAGE_URL, TARGET_AMOUNT_DESC, POVERTY, ALL_FIRST_APPROVE, SUGGEST_END_CASE, SELF_NAME_ID_CARD);

        /**
         * 前置常用全部修改项
         */
        public static final ImmutableSet<EditMaterialType> FIRST_APPROVE_ALL_EDIT_SET = ImmutableSet.of(
                PATIENT_NAME_IDCARD, IMAGE_URL, TARGET_AMOUNT_DESC, POVERTY);
        /**
         * 图文
         */
        public static Set<EditMaterialType> CASE_BASE_INFO_SET = Sets.newHashSet(
                BASE_INFO_TITLE, BASE_INFO_CONTENT, BASE_INFO_IMAGE, TARGET_AMOUNT
        );

        private static Map<String, EditMaterialType> DB_EDIT_MAPPING = Maps.newHashMap();
        static {
            for (EditMaterialType materialType : EditMaterialType.values()) {
                if (StringUtils.isBlank(materialType.getDbContent())) {
                    continue;
                }
                if (DB_EDIT_MAPPING.containsKey(materialType.getDbContent())) {
                    throw new RuntimeException("重复的dbContent: " + materialType.getDbContent());
                }

                DB_EDIT_MAPPING.put(materialType.getDbContent(), materialType);
            }
        }

        public static EditMaterialType getEditTypeByDbContent(String dbContent) {
            if (StringUtils.isBlank(dbContent)) {
                return null;
            }
            return  DB_EDIT_MAPPING.get(StringUtils.trim(dbContent));
        }

        /**
         * 增信
         */
        public static Set<EditMaterialType> CREDIT_INFO_SET = Sets.newHashSet(
                HOUSE, CAR, HOME_INCOME, HOME_STOCK, HOME_DEBT,
                MEDICAL_INSURANCE, LIFE_INSURANCE, PROPERTY_INSURANCE, GOV_RELIEF, HAS_OTHER_PLATFORM,BUSINESS_INSURANCE,
                SELF_BUILT_HOUSE, OTHER_HOUSE,
                PATIENT_FAMILY_HAS_HOUSE, PATIENT_PARENT_FAMILY_MARRIED, PATIENT_FAMILY_SELF_HOUSE
                , PATIENT_FAMILY_COMMODITY_HOUSE, PATIENT_FAMILY_OTHER_HOUSE, PATIENT_FAMILY_EARN_CAR, PATIENT_FAMILY_NO_EARN_CAR
                , PATIENT_FAMILY_INCOME, PATIENT_FAMILY_LIABILITIES, PATIENT_FAMILY_FINANCIAL, PATIENT_FAMILY_MEDICAL_INSURANCE
                , PATIENT_FAMILY_LIFE_INSURANCE, PATIENT_FAMILY_OTHER_SALVATION, PATIENT_CHILD_FAMILY_HOUSE
                , PATIENT_CHILD_FAMILY_FREE_HOUSE, PATIENT_CHILD_FAMILY_CAR, PATIENT_CHILD_FAMILY_MUCH_CAR, PATIENT_PARENT_FAMILY_HOUSE
                , PATIENT_PARENT_FAMILY_CAR, PATIENT_FAMILY_CAR, PATIENT_MATRIMONY_INFO, PATIENT_CHILD_INFO, PATIENT_PARENT_INFO, PATIENT_CHILD_COUNT
        );

        /**
         * 低保/贫困户相关
         */
        public static Set<EditMaterialType> LIVING_GUARD_SET = Sets.newHashSet(
                DI_BAO, PIN_KUN, PATIENT_FAMILY_DI_BAO );

        private static Map<Integer, EditMaterialType> CODE_MATERIAL_MAP;
        static {
            CODE_MATERIAL_MAP = Maps.newHashMap();
            for (EditMaterialType type : EditMaterialType.values()) {
                CODE_MATERIAL_MAP.put(type.getCode(), type);
            }
        }

        public static EditMaterialType valueOfCode(int code) {

            return CODE_MATERIAL_MAP.get(code);
        }


        public static boolean isCaseBaseInfo(EditMaterialType materialType) {
            return CASE_BASE_INFO_SET.contains(materialType);
        }

        public static boolean isFirstApproveInfo(EditMaterialType materialType) {
            return FIRST_APPROVE_BASE_SET.contains(materialType);
        }

        public static boolean isCreditInfo(EditMaterialType materialType) {
            return CREDIT_INFO_SET.contains(materialType);
        }

        public static boolean isLivingGuard(EditMaterialType materialType) {
            return LIVING_GUARD_SET.contains(materialType);
        }

        public final static int FIFTY_W = 50_0000_00;
        /**
         * 获取驳回信息
         * @param rejectType
         * @param targetAmount
         * @return
         */
        public static String getMsg(int rejectType, int targetAmount) {
            boolean moreThan50w = targetAmount > FIFTY_W;
            return getMsg(rejectType, moreThan50w);
        }

        public static String getMsg(int rejectType, boolean moreThan50w) {
            EditMaterialType e = EditMaterialType.valueOfCode(rejectType);
            if (e == null) {
                return "医疗材料";
            }
            switch (e) {
                case ALL_FIRST_APPROVE:
                case ALL_FIRST_APPROVE_OLD:
                case PATIENT_NAME_IDCARD:
                    return moreThan50w ? "患者姓名和身份证号、医疗材料、筹款金额大于50万原因说明 " : "患者姓名和身份证号、医疗材料 ";
                case SELF_NAME_ID_CARD:
                    return SELF_NAME_ID_CARD.getDesc();
                case POVERTY:
                    return "贫困证明";
                case TARGET_AMOUNT_DESC:
                    return moreThan50w ? "筹款金额大于50万原因说明" : "所需金额说明";
                case BASE_INFO_TITLE:
                    return "筹款标题";
                case BASE_INFO_CONTENT:
                    return "求助说明";
                case BASE_INFO_IMAGE:
                    return "展示图片";
                case TARGET_AMOUNT:
                    return TARGET_AMOUNT.getDesc();
                case HOUSE:
                    return HOUSE.getDesc();
                case CAR:
                    return CAR.getDesc();
                case HOME_INCOME:
                    return HOME_INCOME.getDesc();
                case HOME_STOCK:
                    return HOME_STOCK.getDesc();
                case HOME_DEBT:
                    return HOME_DEBT.getDesc();
                case MEDICAL_INSURANCE:
                    return MEDICAL_INSURANCE.getDesc();
                case LIFE_INSURANCE:
                    return LIFE_INSURANCE.getDesc();
                case PROPERTY_INSURANCE:
                    return PROPERTY_INSURANCE.getDesc();
                case GOV_RELIEF:
                    return GOV_RELIEF.getDesc();
                case DI_BAO:
                    return DI_BAO.getDesc();
                case PIN_KUN:
                    return PIN_KUN.getDesc();
                case HAS_OTHER_PLATFORM:
                    return HAS_OTHER_PLATFORM.getDesc();
                case BUSINESS_INSURANCE:
                    return BUSINESS_INSURANCE.getDesc();
                case SELF_BUILT_HOUSE:
                    return SELF_BUILT_HOUSE.getDesc();
                case OTHER_HOUSE:
                    return OTHER_HOUSE.getDesc();
                case PATIENT_FAMILY_HAS_HOUSE:
                    return PATIENT_FAMILY_HAS_HOUSE.getDesc();
                case PATIENT_PARENT_FAMILY_MARRIED:
                    return PATIENT_PARENT_FAMILY_MARRIED.getDesc();
                case PATIENT_FAMILY_SELF_HOUSE:
                    return PATIENT_FAMILY_SELF_HOUSE.getDesc();
                case PATIENT_FAMILY_COMMODITY_HOUSE:
                    return PATIENT_FAMILY_COMMODITY_HOUSE.getDesc();
                case PATIENT_FAMILY_OTHER_HOUSE:
                    return PATIENT_FAMILY_OTHER_HOUSE.getDesc();
                case PATIENT_FAMILY_EARN_CAR:
                    return PATIENT_FAMILY_EARN_CAR.getDesc();
                case PATIENT_FAMILY_NO_EARN_CAR:
                    return PATIENT_FAMILY_NO_EARN_CAR.getDesc();
                case PATIENT_FAMILY_INCOME:
                    return PATIENT_FAMILY_INCOME.getDesc();
                case PATIENT_FAMILY_LIABILITIES:
                    return PATIENT_FAMILY_LIABILITIES.getDesc();
                case PATIENT_FAMILY_FINANCIAL:
                    return PATIENT_FAMILY_FINANCIAL.getDesc();
                case PATIENT_FAMILY_MEDICAL_INSURANCE:
                    return PATIENT_FAMILY_MEDICAL_INSURANCE.getDesc();
                case PATIENT_FAMILY_LIFE_INSURANCE:
                    return PATIENT_FAMILY_LIFE_INSURANCE.getDesc();
                case PATIENT_FAMILY_OTHER_SALVATION:
                    return PATIENT_FAMILY_OTHER_SALVATION.getDesc();
                case PATIENT_FAMILY_DI_BAO:
                    return PATIENT_FAMILY_DI_BAO.getDesc();
                case PATIENT_CHILD_FAMILY_HOUSE:
                    return PATIENT_CHILD_FAMILY_HOUSE.getDesc();
                case PATIENT_CHILD_FAMILY_FREE_HOUSE:
                    return PATIENT_CHILD_FAMILY_FREE_HOUSE.getDesc();
                case PATIENT_CHILD_FAMILY_CAR:
                    return PATIENT_CHILD_FAMILY_CAR.getDesc();
                case PATIENT_CHILD_FAMILY_MUCH_CAR:
                    return PATIENT_CHILD_FAMILY_MUCH_CAR.getDesc();
                case PATIENT_PARENT_FAMILY_HOUSE:
                    return PATIENT_PARENT_FAMILY_HOUSE.getDesc();
                case PATIENT_PARENT_FAMILY_CAR:
                    return PATIENT_PARENT_FAMILY_CAR.getDesc();
                case PATIENT_MATRIMONY_INFO:
                    return PATIENT_MATRIMONY_INFO.getDesc();
                case PATIENT_CHILD_INFO:
                    return PATIENT_CHILD_INFO.getDesc();
                case PATIENT_PARENT_INFO:
                    return PATIENT_PARENT_INFO.getDesc();
                case PATIENT_CHILD_COUNT:
                    return PATIENT_CHILD_COUNT.getDesc();
                case IMAGE_URL:
                default:
                    return "医疗材料";
            }
        }
    }

    @AllArgsConstructor
    @Getter
    public enum MaterialStatus {
        DEFAULT(0, "默认"),
        SUBMIT(1, "已提交"),
        REJECT(2, "已驳回"),
        PASS(3, "已通过"),
        ;

        private int code;
        private String desc;

        public static MaterialStatus parse(int v) {
            for (MaterialStatus e : MaterialStatus.values()) {
                if (e.getCode() == v) {
                    return e;
                }
            }
            return null;
//            throw new IllegalArgumentException("找不到对应的MaterialStatus with code: " + v);
        }


        public static MaterialStatus transFromRiverStatus(Integer code) {

            if (code == null) {
                return null;
            }

            RiverStatusEnum riverStatus = RiverStatusEnum.parse(code);
            switch(riverStatus) {
                case WAIT_SUBMIT:
                    return DEFAULT;
                case SUBMITTED:
                    return SUBMIT;
                case REJECT:
                    return REJECT;
                case PASS:
                    return PASS;

            }
            return null;
        }

    }


    /**
     * TAGS : {@link com.shuidihuzhu.cf.constants.MQTagCons#CF_INITIAL_AUDIT_OPERATION_MSG}
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class InitialAuditOperation {
        private int caseId;
        private long workOrderId;
        private List<Integer> passIds;
        private Map<Integer, List<String>> rejectItems;
    }

    /**
     * tags: {@link com.shuidihuzhu.cf.constants.MQTagCons#CF_INITIAL_AUDIT_OPERATION_RECALL_MSG}
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class InitialAuditRecall {
        private InitialAuditOperation operationDetail;
        // 1 第二天8点， 2 第三天8点
        private int recallType;
    }


    /**
     * key --> {@link EditMaterialType}
     */
    @ApiModel("驳回详情")
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class InitialAuditRejectOption {

        @ApiModelProperty("基本信息状态")
        private InitialAuditItem.MaterialStatus baseInfoStatus;

        @ApiModelProperty("前置信息状态")
        private InitialAuditItem.MaterialStatus firstApproveStatus;

        @ApiModelProperty("增信信息状态")
        private InitialAuditItem.MaterialStatus creditStatus;

        @ApiModelProperty("低保信息的状态")
        private InitialAuditItem.MaterialStatus livingGuardStatus;

        @ApiModelProperty("初审驳回详情")
        Map<Integer, List<String>> rejectDetail;

        @ApiModelProperty("初审材料提交页文案展示")
        Map<String, Set<ContentDisplay>> contentDisplays;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RejectReason {
        int rejectId;
        String rejectMsg;
    }

    @Data
    @AllArgsConstructor
    public static class RejectReasonSet {
        Map<Integer, List<Integer>> rejectIds;
        Map<Integer, List<String>> rejectMsgs;

        public RejectReasonSet() {
            rejectIds = Maps.newHashMap();
            rejectMsgs = Maps.newHashMap();
        }
    }

    @Data
    public static class ContentDisplay {
        public static int REJECT = 0;
        public static int SUBMIT = 1;
        public static int PASS = 2;
        public static int OPERATE_MODIFY = 3;
        public static int USER_CONFIRM = 4;

        private String content;
        // 0 驳回  1 已提交  2 已审核通过  3、材料被运营修改过 4、用户确认过运营的修改
        private int status;

        public ContentDisplay(String content, int status) {
            this.content = content;
            this.status = status;
        }

        public ContentDisplay() {
        }
    }

    @Getter
    public enum ContentDisplayType {
        BASE_INFO(1, "BASE_INFO", "图文信息"),
        FIRST_APPROVE(2, "FIRST_APPROVE", "前置信息"),
        CREDIT_INFO(3, "CREDIT_INFO",  "增信信息"),
        BASIC_LIVING_GUARD(4, "BASIC_LIVING_GUARD", "低保情况"),
        ;
        private int code;
        private String contentForUser;
        private String msg;

        ContentDisplayType(int code, String contentForUser, String msg) {
            this.code = code;
            this.contentForUser = contentForUser;
            this.msg = msg;
        }
    }


}
