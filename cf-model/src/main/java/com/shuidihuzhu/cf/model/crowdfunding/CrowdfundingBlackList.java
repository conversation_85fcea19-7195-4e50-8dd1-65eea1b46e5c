package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * Created by Ahrievil on 2017/11/1
 */
@NoArgsConstructor
@Data
public class CrowdfundingBlackList {
    private int id;
    private long userId;
    private String content;
    private int limitRange;
    private int limitType;
    private int limitPart;
    private int mode;
    private int contentValid;
    private int isDelete;
    private Timestamp createTime;
    private Timestamp updateTime;

    public CrowdfundingBlackList(long userId, String content, int limitRange, int limitType, int limitPart, int mode, int contentValid) {
        this.userId = userId;
        this.content = content;
        this.limitRange = limitRange;
        this.limitType = limitType;
        this.limitPart = limitPart;
        this.mode = mode;
        this.contentValid = contentValid;
    }

}
