package com.shuidihuzhu.cf.model.timeline;

import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.enums.timeline.TimeLineOperateType;
import lombok.Data;
import org.apache.commons.collections4.MapUtils;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018-08-28  17:43
 */
@Data
public class TimeLineModel {

    private long id;

    private long caseId;

    private TimeLineOperateType operateType = TimeLineOperateType.DEFAULT;

    private long userId;

    private String description;

    private String stackTrace;

    private  String ip;

    private String traceId;

    private Object content;

    private Date createTime;

    private Date updateTime;

    private TimeLineModel() {
    }


    public static Builder create(long caseId){
        Builder v = new Builder();
        v.m.setCaseId(caseId);
        return v;
    }

    public static class Builder {

        TimeLineModel m = new TimeLineModel();

        Map<String, Object> paramMap = Maps.newHashMap();

        Builder(){
        }

        public Builder type(TimeLineOperateType operateType){
            m.setOperateType(operateType);
            return this;
        }

        /**
         * @param content
         * @return
         */
        public Builder content(Object content){
            m.setContent(content);
            return this;
        }

        public Builder description(String desc){
            m.setDescription(desc);
            return this;
        }

        public Builder param(String key, Object value){
            paramMap.put(key, value);
            return this;
        }

        public TimeLineModel build(){
            if (MapUtils.isNotEmpty(paramMap)){
                m.setContent(paramMap);
            }
            return m;
        }

    }

}
