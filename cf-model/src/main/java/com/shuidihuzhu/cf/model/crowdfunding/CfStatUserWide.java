package com.shuidihuzhu.cf.model.crowdfunding;

import java.sql.Date;
import java.sql.Timestamp;

/**
 *
 * 用户款表里面的信息
 *
 * Created by wangsf on 17/4/28.
 */
public class CfStatUserWide {

	private int id;
	private Date createDate;
	private Timestamp createTime;
	private long userId;
	private String openId;
	private boolean isToufangSign;
	private String toufangSignChannel;
	private Timestamp toufangSignFirstTime;
	private boolean isFollow;
	private Timestamp followFirstTime;
	private int followType;
	private String followQrCode;
	private String followQrCodeChannel;
	private String followQrCodeChannelGroup;
	private boolean isApp;
	private Timestamp appFirstTime;
	private boolean isOtherSign;
	private String otherSignType;
	private String otherSignChannel;
	private Timestamp otherSignFirstTime;
	private int otherSignId;
	private String otherSignMobile;
	private Timestamp adviserSignTime;
	private String adviserSignChannel;
	private boolean isAdviserSign;
	private Timestamp adviserSignFirstTime;
	private boolean isFeedSign;
	private String feedSignChannel;
	private Timestamp feedSignFirstTime;


	//来源数据
	private String meetPointType;
	private String autoTagCategory;
	private Timestamp firstMeetTime;
	private int autoTagSceneId;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Timestamp getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Timestamp createTime) {
		this.createTime = createTime;
	}

	public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}

	public String getOpenId() {
		return openId;
	}

	public void setOpenId(String openId) {
		this.openId = openId;
	}

	public boolean isToufangSign() {
		return isToufangSign;
	}

	public void setToufangSign(boolean toufangSign) {
		isToufangSign = toufangSign;
	}

	public String getToufangSignChannel() {
		return toufangSignChannel;
	}

	public void setToufangSignChannel(String toufangSignChannel) {
		this.toufangSignChannel = toufangSignChannel;
	}

	public boolean isApp() {
		return isApp;
	}

	public void setApp(boolean app) {
		isApp = app;
	}

	public Timestamp getToufangSignFirstTime() {
		return toufangSignFirstTime;
	}

	public void setToufangSignFirstTime(Timestamp toufangSignFirstTime) {
		this.toufangSignFirstTime = toufangSignFirstTime;
	}

	public int getFollowType() {
		return followType;
	}

	public void setFollowType(int followType) {
		this.followType = followType;
	}

	public boolean isFollow() {
		return isFollow;
	}

	public void setFollow(boolean follow) {
		isFollow = follow;
	}

	public Timestamp getFollowFirstTime() {
		return followFirstTime;
	}

	public void setFollowFirstTime(Timestamp followFirstTime) {
		this.followFirstTime = followFirstTime;
	}

	public String getFollowQrCode() {
		return followQrCode;
	}

	public void setFollowQrCode(String followQrCode) {
		this.followQrCode = followQrCode;
	}

	public String getFollowQrCodeChannel() {
		return followQrCodeChannel;
	}

	public void setFollowQrCodeChannel(String followQrCodeChannel) {
		this.followQrCodeChannel = followQrCodeChannel;
	}

	public String getFollowQrCodeChannelGroup() {
		return followQrCodeChannelGroup;
	}

	public void setFollowQrCodeChannelGroup(String followQrCodeChannelGroup) {
		this.followQrCodeChannelGroup = followQrCodeChannelGroup;
	}

	public Timestamp getAppFirstTime() {
		return appFirstTime;
	}

	public void setAppFirstTime(Timestamp appFirstTime) {
		this.appFirstTime = appFirstTime;
	}

	public boolean isOtherSign() {
		return isOtherSign;
	}

	public void setOtherSign(boolean otherSign) {
		isOtherSign = otherSign;
	}

	public String getOtherSignType() {
		return otherSignType;
	}

	public void setOtherSignType(String otherSignType) {
		this.otherSignType = otherSignType;
	}

	public String getOtherSignChannel() {
		return otherSignChannel;
	}

	public void setOtherSignChannel(String otherSignChannel) {
		this.otherSignChannel = otherSignChannel;
	}

	public Timestamp getOtherSignFirstTime() {
		return otherSignFirstTime;
	}

	public void setOtherSignFirstTime(Timestamp otherSignFirstTime) {
		this.otherSignFirstTime = otherSignFirstTime;
	}

	public int getOtherSignId() {
		return otherSignId;
	}

	public void setOtherSignId(int otherSignId) {
		this.otherSignId = otherSignId;
	}

	public String getOtherSignMobile() {
		return otherSignMobile;
	}

	public void setOtherSignMobile(String otherSignMobile) {
		this.otherSignMobile = otherSignMobile;
	}


	public Timestamp getAdviserSignTime() {
		return adviserSignTime;
	}

	public void setAdviserSignTime(Timestamp adviserSignTime) {
		this.adviserSignTime = adviserSignTime;
	}

	public String getAdviserSignChannel() {
		return adviserSignChannel;
	}

	public void setAdviserSignChannel(String adviserSignChannel) {
		this.adviserSignChannel = adviserSignChannel;
	}

	public boolean isAdviserSign() {
		return isAdviserSign;
	}

	public void setAdviserSign(boolean adviserSign) {
		isAdviserSign = adviserSign;
	}

	public Timestamp getAdviserSignFirstTime() {
		return adviserSignFirstTime;
	}

	public void setAdviserSignFirstTime(Timestamp adviserSignFirstTime) {
		this.adviserSignFirstTime = adviserSignFirstTime;
	}

	public String getMeetPointType() {
		return meetPointType;
	}

	public void setMeetPointType(String meetPointType) {
		this.meetPointType = meetPointType;
	}

	public String getAutoTagCategory() {
		return autoTagCategory;
	}

	public void setAutoTagCategory(String autoTagCategory) {
		this.autoTagCategory = autoTagCategory;
	}

	public Timestamp getFirstMeetTime() {
		return firstMeetTime;
	}

	public void setFirstMeetTime(Timestamp firstMeetTime) {
		this.firstMeetTime = firstMeetTime;
	}

	public int getAutoTagSceneId() {
		return autoTagSceneId;
	}

	public void setAutoTagSceneId(int autoTagSceneId) {
		this.autoTagSceneId = autoTagSceneId;
	}

	public boolean isFeedSign() {
		return isFeedSign;
	}

	public void setFeedSign(boolean feedSign) {
		isFeedSign = feedSign;
	}

	public String getFeedSignChannel() {
		return feedSignChannel;
	}

	public void setFeedSignChannel(String feedSignChannel) {
		this.feedSignChannel = feedSignChannel;
	}

	public Timestamp getFeedSignFirstTime() {
		return feedSignFirstTime;
	}

	public void setFeedSignFirstTime(Timestamp feedSignFirstTime) {
		this.feedSignFirstTime = feedSignFirstTime;
	}

	@Override
	public String toString() {
		return "CfStatUserWide{" +
				"id=" + id +
				", createDate=" + createDate +
				", createTime=" + createTime +
				", userId=" + userId +
				", openId='" + openId + '\'' +
				", isToufangSign=" + isToufangSign +
				", toufangSignChannel='" + toufangSignChannel + '\'' +
				", toufangSignFirstTime=" + toufangSignFirstTime +
				", isFollow=" + isFollow +
				", followFirstTime=" + followFirstTime +
				", followType=" + followType +
				", followQrCode='" + followQrCode + '\'' +
				", followQrCodeChannel='" + followQrCodeChannel + '\'' +
				", followQrCodeChannelGroup='" + followQrCodeChannelGroup + '\'' +
				", isApp=" + isApp +
				", appFirstTime=" + appFirstTime +
				", isOtherSign=" + isOtherSign +
				", otherSignType='" + otherSignType + '\'' +
				", otherSignChannel='" + otherSignChannel + '\'' +
				", otherSignFirstTime=" + otherSignFirstTime +
				", otherSignId=" + otherSignId +
				", otherSignMobile='" + otherSignMobile + '\'' +
				'}';
	}
}
