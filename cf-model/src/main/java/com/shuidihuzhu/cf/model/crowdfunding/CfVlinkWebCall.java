package com.shuidihuzhu.cf.model.crowdfunding;

import java.sql.Timestamp;
import java.util.Date;

/**
 * vlink的呼叫记录
 *
 * Created by wangsf on 18/3/3.
 */
public class CfVlinkWebCall {

	private int id;

	private String uniqueId;
	private String requestUniqueId;
	private String customerNumber;
	private String customerProvince;
	private String customerCity;
	private String clid;
	private String codClid;
	private String calleeNumber;
	private String status;
	private String mark;
	private String code;
	private Timestamp startTime;
	private Timestamp endTime;
	private Timestamp answerTime;
	private Timestamp bridgeTime;
	private String bridgeDuration;
	private int bridgeDurSec ;
	private String billDuration;
	private int billDurSce;
	private String totalDuration;
	private int totalDurSec;
	private String recordFile;
	private String recordFileIn;
	private String recordFileOut;
	private String callType;
	private String ivrName;
	private String endReason;
	private String userField;
	private int sipCause;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getUniqueId() {
		return uniqueId;
	}

	public void setUniqueId(String uniqueId) {
		this.uniqueId = uniqueId;
	}

	public String getRequestUniqueId() {
		return requestUniqueId;
	}

	public void setRequestUniqueId(String requestUniqueId) {
		this.requestUniqueId = requestUniqueId;
	}

	public String getCustomerNumber() {
		return customerNumber;
	}

	public void setCustomerNumber(String customerNumber) {
		this.customerNumber = customerNumber;
	}

	public String getCustomerProvince() {
		return customerProvince;
	}

	public void setCustomerProvince(String customerProvince) {
		this.customerProvince = customerProvince;
	}

	public String getCustomerCity() {
		return customerCity;
	}

	public void setCustomerCity(String customerCity) {
		this.customerCity = customerCity;
	}

	public String getClid() {
		return clid;
	}

	public void setClid(String clid) {
		this.clid = clid;
	}

	public String getCodClid() {
		return codClid;
	}

	public void setCodClid(String codClid) {
		this.codClid = codClid;
	}

	public String getCalleeNumber() {
		return calleeNumber;
	}

	public void setCalleeNumber(String calleeNumber) {
		this.calleeNumber = calleeNumber;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getMark() {
		return mark;
	}

	public void setMark(String mark) {
		this.mark = mark;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public Timestamp getStartTime() {
		return startTime;
	}

	public void setStartTime(Timestamp startTime) {
		this.startTime = startTime;
	}

	public Timestamp getEndTime() {
		return endTime;
	}

	public void setEndTime(Timestamp endTime) {
		this.endTime = endTime;
	}

	public Timestamp getAnswerTime() {
		return answerTime;
	}

	public void setAnswerTime(Timestamp answerTime) {
		this.answerTime = answerTime;
	}

	public Timestamp getBridgeTime() {
		return bridgeTime;
	}

	public void setBridgeTime(Timestamp bridgeTime) {
		this.bridgeTime = bridgeTime;
	}

	public String getBridgeDuration() {
		return bridgeDuration;
	}

	public void setBridgeDuration(String bridgeDuration) {
		this.bridgeDuration = bridgeDuration;
	}

	public int getBridgeDurSec() {
		return bridgeDurSec;
	}

	public void setBridgeDurSec(int bridgeDurSec) {
		this.bridgeDurSec = bridgeDurSec;
	}

	public String getBillDuration() {
		return billDuration;
	}

	public void setBillDuration(String billDuration) {
		this.billDuration = billDuration;
	}

	public int getBillDurSce() {
		return billDurSce;
	}

	public void setBillDurSce(int billDurSce) {
		this.billDurSce = billDurSce;
	}

	public String getTotalDuration() {
		return totalDuration;
	}

	public void setTotalDuration(String totalDuration) {
		this.totalDuration = totalDuration;
	}

	public int getTotalDurSec() {
		return totalDurSec;
	}

	public void setTotalDurSec(int totalDurSec) {
		this.totalDurSec = totalDurSec;
	}

	public String getRecordFile() {
		return recordFile;
	}

	public void setRecordFile(String recordFile) {
		this.recordFile = recordFile;
	}

	public String getRecordFileIn() {
		return recordFileIn;
	}

	public void setRecordFileIn(String recordFileIn) {
		this.recordFileIn = recordFileIn;
	}

	public String getRecordFileOut() {
		return recordFileOut;
	}

	public void setRecordFileOut(String recordFileOut) {
		this.recordFileOut = recordFileOut;
	}

	public String getCallType() {
		return callType;
	}

	public void setCallType(String callType) {
		this.callType = callType;
	}

	public String getIvrName() {
		return ivrName;
	}

	public void setIvrName(String ivrName) {
		this.ivrName = ivrName;
	}

	public String getEndReason() {
		return endReason;
	}

	public void setEndReason(String endReason) {
		this.endReason = endReason;
	}

	public String getUserField() {
		return userField;
	}

	public void setUserField(String userField) {
		this.userField = userField;
	}

	public int getSipCause() {
		return sipCause;
	}

	public void setSipCause(int sipCause) {
		this.sipCause = sipCause;
	}
}
