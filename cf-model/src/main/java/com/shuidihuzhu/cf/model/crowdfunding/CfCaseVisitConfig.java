package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.Data;

import java.util.Date;

/**
 * Created by wangsf on 18/4/16.
 */

@Data
public class CfCaseVisitConfig {

	/** 案例id */
	private int caseId;

	/** 是否显示订制banner */
	private boolean showBanner;

	/** 是否可分享 */
	private boolean sharable;

	/**
	 * 是否可以捐款
	 */
	private boolean canDonate;

	/**
	 * 是否可以展示，如果不能，直接弹个蒙层给遮蔽住
	 */
	private boolean canShow;

	/** 案例图片地址 */
	private String bannerImgUrl;

	/** 案例banner跳转的地址 */
	private String bannerUrl;

	/** 案例banner文字，可以和banner图片共存，需求请见：https://wiki.shuiditech.com/pages/viewpage.action?pageId=19961351 */
	private String bannerText;

	/** bannerText的生效时间，闭区间 */
	private Date startTime;

	/** bannerText的失效时间，闭区间 */
	private Date endTime;

	/** 是否展示弹窗 */
	private boolean showPopup;

	/** 弹窗文本 */
	private String popupText;

	/** 弹窗title */
	private String popupTitle;

	/**
	 * 隐藏内容 提示文案
	 */
	private String hiddenTitle;

	/**
	 * 发起图文AI检测 是否隐藏内容
	 */
	private boolean hidden;

	/**
	 * 是否是强制停止的筹款
	 */
	private boolean forceStop;

	/**
	 * https://wiki.shuiditech.com/pages/viewpage.action?pageId=33588039
	 * 异常案例屏蔽详情页
	 */
	private boolean abnormalHidden;

	private String abnormalHiddenSelfTitle;

	private String abnormalHiddenOtherTitle;

	private boolean raiserFinish = true;

	private boolean canDonateCooperate = true;
}
