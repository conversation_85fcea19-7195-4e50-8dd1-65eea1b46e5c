package com.shuidihuzhu.cf.model.loverank;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * user_love_rank_like_record
 *
 * <AUTHOR>
 */
@Data
public class UserLoveRankLikeRecord implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 用户userId
     */
    private Long userId;

    /**
     * 案例id
     */
    private Integer caseId;

    private Long likeUserId;

    /**
     * 是否匿名，1匿名，0非匿名
     */
    private Integer anonymous;

    /**
     * 是否点赞，0没有，1点赞
     */
    private Integer likeFlag;


    private static final long serialVersionUID = 1L;
}