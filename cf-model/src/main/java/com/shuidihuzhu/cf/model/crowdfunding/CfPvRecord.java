package com.shuidihuzhu.cf.model.crowdfunding;

import java.sql.Date;

/**
 * Created by wangsf on 17/5/22.
 */
public class CfPvRecord {

	private String openId;
	private long userId;
	private String selfTag;
	private String channel;
	private String page;
	private Date createDate;
	private String path;
	private String grayMode;
	private int wxMpType;

	public String getGrayMode() {
		return grayMode;
	}

	public void setGrayMode(String grayMode) {
		this.grayMode = grayMode;
	}

	public String getOpenId() {
		return openId;
	}

	public void setOpenId(String openId) {
		this.openId = openId;
	}

	public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}

	public String getSelfTag() {
		return selfTag;
	}

	public void setSelfTag(String selfTag) {
		this.selfTag = selfTag;
	}

	public String getChannel() {
		return channel;
	}

	public void setChannel(String channel) {
		this.channel = channel;
	}

	public String getPage() {
		return page;
	}

	public void setPage(String page) {
		this.page = page;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public String getPath() {
		return path;
	}

	public void setPath(String path) {
		this.path = path;
	}

	public int getWxMpType() {
		return wxMpType;
	}

	public void setWxMpType(int wxMpType) {
		this.wxMpType = wxMpType;
	}
}
