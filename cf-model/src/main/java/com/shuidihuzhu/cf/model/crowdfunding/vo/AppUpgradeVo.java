package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.shuidihuzhu.cf.model.crowdfunding.app.AppUpgradeLog;
import lombok.Data;

import java.util.List;

/**
 *
 * App更新信息
 *
 * Created by wangsf on 17/2/27.
 */
@Data
public class AppUpgradeVo {

	/**
	 * 是否最新
	 */
	private boolean latest;

	/**
	 * 是否强制
	 */
	private boolean mandatory;

	/**
	 * 升级title
	 */
	private String title;

	/**
	 * changeLog
	 */
	private String changeLog;

	/**
	 * 升级的Android Url
	 */
	private String androidUrl;
	/**
	 * 升级的版本version
	 */
	private String  version;

	private Boolean updateSuggest;//是否建议升级



	@Override
	public String toString() {
		return "AppUpgradeVo{" +
				"latest=" + latest +
				", mandatory=" + mandatory +
				", title='" + title + '\'' +
				", changeLog='" + changeLog + '\'' +
				", androidUrl='" + androidUrl + '\'' +
				'}';
	}

}
