package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingSeekHelpInfoFragment;
import org.apache.commons.lang3.StringUtils;

/**
 * Created by <PERSON><PERSON> on 2017/8/6.
 */
public class CrowdfundingSeekHelpInfoFragmentVo {
	public static final int MAX_LENGTH = 4500;
	public static final int MAX_LENGTH_PATIENT_INTRO = 1000;

	private String infoUuid;
	private String patientIntro; // 患者介绍
	private String diseaseName;  // 所患疾病
	private String medicalRecords; // 确诊经过及治疗所需费用
	private String desc; // 想说的话

	public CrowdfundingSeekHelpInfoFragmentVo() {

	}

	public boolean verifyContentLength() {
		if (!StringUtils.isEmpty(patientIntro) && patientIntro.length() > MAX_LENGTH_PATIENT_INTRO) {
			return false;
		}
		if (!StringUtils.isEmpty(diseaseName) && diseaseName.length() > MAX_LENGTH_PATIENT_INTRO) {
			return false;
		}
		if (!StringUtils.isEmpty(medicalRecords) && medicalRecords.length() > MAX_LENGTH) {
			return false;
		}
		if (!StringUtils.isEmpty(desc) && desc.length() > MAX_LENGTH) {
			return false;
		}
		return true;
	}

	public String content() {
		return String.format("%s%s%s%s", patientIntro, diseaseName, medicalRecords, desc);
	}

	public CrowdfundingSeekHelpInfoFragmentVo(CrowdfundingSeekHelpInfoFragment infoFragment) {
		this.infoUuid = infoFragment.getInfoUuid();
		this.patientIntro = infoFragment.getPatientIntro();
		this.diseaseName = infoFragment.getDisease();
		this.desc = infoFragment.getWantToSay();
		this.medicalRecords = infoFragment.getDiagnoseIntro();
	}

	public String getInfoUuid() {
		return infoUuid;
	}

	public void setInfoUuid(String infoUuid) {
		this.infoUuid = infoUuid;
	}

	public String getPatientIntro() {
		return patientIntro;
	}

	public void setPatientIntro(String patientIntro) {
		this.patientIntro = patientIntro;
	}

	public String getDiseaseName() {
		return diseaseName;
	}

	public void setDiseaseName(String diseaseName) {
		this.diseaseName = diseaseName;
	}

	public String getDesc() {
		return desc;
	}

	public void setDesc(String desc) {
		this.desc = desc;
	}

	public String getMedicalRecords() {
		return medicalRecords;
	}

	public void setMedicalRecords(String medicalRecords) {
		this.medicalRecords = medicalRecords;
	}
}
