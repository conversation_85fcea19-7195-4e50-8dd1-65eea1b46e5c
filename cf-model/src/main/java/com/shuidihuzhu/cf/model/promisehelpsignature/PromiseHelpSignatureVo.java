package com.shuidihuzhu.cf.model.promisehelpsignature;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PromiseHelpSignatureVo {

    @ApiModelProperty("链接是否有效")
    private boolean valid;

    @ApiModelProperty("签署链接")
    private String url;

    @ApiModelProperty("倒计时完成")
    private boolean countdownComplete;

}
