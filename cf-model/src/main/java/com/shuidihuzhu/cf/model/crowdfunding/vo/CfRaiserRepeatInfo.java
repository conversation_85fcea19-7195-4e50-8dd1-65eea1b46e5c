package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSimpleModel;
import lombok.Data;

import java.util.Collection;

/**
 * <AUTHOR>
 * @time 2019/9/4 下午3:25
 * @desc
 */
@Data
public class CfRaiserRepeatInfo {

    @JSONField(ordinal = 1, name = "重复次数")
    private int repeatCount;

    @JSONField(ordinal = 2, name = "当前案例ID")
    private int caseId;

    @JSONField(ordinal = 3, name = "当前案例的发起人userId")
    private long userId;

    @JSONField(ordinal = 4, name = "当前案例发起人姓名")
    private String raiserName;

    @JSONField(ordinal = 5, name = "当前案例发起人身份证号")
    private String raiserIdCard;

    @JSONField(ordinal = 6, name = "和当前案例发起人重复的案例")
    private Collection<CfInfoSimpleModel> simpleModels;
}
