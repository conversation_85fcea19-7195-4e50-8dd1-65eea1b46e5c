package com.shuidihuzhu.cf.model.crowdfunding.message;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;

@Data
@ApiModel
public class CfPayRecord {

    @ApiModelProperty("案例id")
    private long caseId;

    @ApiModelProperty("案例支付次数")
    private int payCount;

    @ApiModelProperty("支付金额")
    private int payMoney;

    @ApiModelProperty("案例结束时间")
    private Timestamp finishTime;

    @ApiModelProperty("案例开始时间")
    private Timestamp caseCreateTime;





}
