package com.shuidihuzhu.cf.model.contribute;

import com.fasterxml.jackson.annotation.JsonIgnore;
import groovy.transform.builder.Builder;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.bind.annotation.RequestParam;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContributeRefundSubmitVo {

    private long amountInFen;
    private String refundReason;
    private  String refundName;
    private String mobile;
    private String infoUuid;
    private String payUid;

    @JsonIgnore
    private long userId;
}
