package com.shuidihuzhu.cf.model.goods;

import com.shuidihuzhu.common.web.model.AbstractModel;

import java.sql.Timestamp;

public class GoodsGear extends AbstractModel {

	private static final long serialVersionUID = 6480952914022493920L;

	private long id;
	private String infoUuid;
	private int amount;
	private int targetNum;
	private int num;
	private int valid;
	private String title;
	private String detail;
	private String email;
	private int needAddress;
	private int needEmail;
	/**
	 * 支持次数是否有上限，1表示没有，0表示有
	 */
	private int hasLimit;
	private Timestamp dateCreated;
	private Timestamp lastModified;

	public long getId() {
		return id;
	}

	public void setId(long id) {
		this.id = id;
	}

	public String getInfoUuid() {
		return infoUuid;
	}

	public void setInfoUuid(String infoUuid) {
		this.infoUuid = infoUuid;
	}

	public int getAmount() {
		return amount;
	}

	public void setAmount(int amount) {
		this.amount = amount;
	}

	public int getTargetNum() {
		return targetNum;
	}

	public void setTargetNum(int targetNum) {
		this.targetNum = targetNum;
	}

	public int getNum() {
		return num;
	}

	public void setNum(int num) {
		this.num = num;
	}

	public int getValid() {
		return valid;
	}

	public void setValid(int valid) {
		this.valid = valid;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getDetail() {
		return detail;
	}

	public void setDetail(String detail) {
		this.detail = detail;
	}

	public Timestamp getDateCreated() {
		return dateCreated;
	}

	public void setDateCreated(Timestamp dateCreated) {
		this.dateCreated = dateCreated;
	}

	public Timestamp getLastModified() {
		return lastModified;
	}

	public void setLastModified(Timestamp lastModified) {
		this.lastModified = lastModified;
	}

	public int getHasLimit() {
		return hasLimit;
	}

	public void setHasLimit(int hasLimit) {
		this.hasLimit = hasLimit;
	}

	public int getNeedAddress() {
		return needAddress;
	}

	public void setNeedAddress(int needAddress) {
		this.needAddress = needAddress;
	}

	public int getNeedEmail() {
		return needEmail;
	}

	public void setNeedEmail(int needEmail) {
		this.needEmail = needEmail;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

}
