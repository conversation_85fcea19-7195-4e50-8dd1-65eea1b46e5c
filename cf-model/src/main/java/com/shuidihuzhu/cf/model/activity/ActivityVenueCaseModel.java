package com.shuidihuzhu.cf.model.activity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * @author: fengxuan
 * @create 2020-02-20 17:11
 *
 * 获取主会场案例
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Valid
@Builder
public class ActivityVenueCaseModel {

    private long id;

    private int activityId;

    @Min(1)
    private int caseId;

    private String infoId;

    /**
     * {@link com.shuidihuzhu.cf.enums.activity.ActivityCaseType}
     */
    @Min(1)@Max(3)
    private int type;

    @NotNull
    private String desc;

    @NotNull
    private String title;

    @NotNull
    private String titleImg;

    @NotNull
    private String content;

    /**
     * 优先级,从0开始,越小优先级越高
     */
    @Min(0)
    private int order;

    /**
     * 案例池类型 1 主会场案例池 2 自救区随手捐池 3 随手捐推荐池
     * 上下线 切换poolType 为3 是上线 1为下线
     */
    @Min(1)@Max(3)
    private int poolType;

    /**
     * 总筹款金额 包括配捐转金额 分
     */
    private int totalDonate;

    /**
     * 主会场内金额 分
     */
    private int venueDonate;

    /**
     * 主会场外金额 分
     */
    private int venueDonateOut;

    /**
     * 配捐转金额 分
     */
    private int cooperateDonate;

    @ApiModelProperty("是否有效")
    private boolean valid;

    @ApiModelProperty("操作备注")
    private String comment;
}
