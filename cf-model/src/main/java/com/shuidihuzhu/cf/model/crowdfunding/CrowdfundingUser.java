package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.account.compatible.model.HasUserId;
import com.shuidihuzhu.account.compatible.model.HasUserInfo;
import com.shuidihuzhu.common.web.model.AbstractModel;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by lgj on 16/6/22.
 */
public class CrowdfundingUser extends AbstractModel implements HasUserId, HasUserInfo {

    private Long orderId;

    private long userId;

    private Integer userThirdId;

    private Integer userThirdType;

    private boolean anonymous;

    private String nickname;

    private String headImgUrl;

    private Double amt;

    private String comment;

    private Integer time;

    @Getter @Setter
    private String blessingCardText;

    @Getter @Setter
    private String blessingCardImage;

    public boolean isAnonymous() {
        return anonymous;
    }

    public void setAnonymous(boolean anonymous) {
        this.anonymous = anonymous;
    }

    public Integer getUserThirdType() {
        return userThirdType;
    }

    public void setUserThirdType(Integer userThirdType) {
        this.userThirdType = userThirdType;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }




    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public Integer getUserThirdId() {
        return userThirdId;
    }

    public void setUserThirdId(Integer userThirdId) {
        this.userThirdId = userThirdId;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getHeadImgUrl() {
        return headImgUrl;
    }

    public void setHeadImgUrl(String headImgUrl) {
        this.headImgUrl = headImgUrl;
    }

    public Double getAmt() {
        return amt;
    }

    public void setAmt(Double amt) {
        this.amt = amt;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public Integer getTime() {
        return time;
    }

    public void setTime(Integer time) {
        this.time = time;
    }
}
