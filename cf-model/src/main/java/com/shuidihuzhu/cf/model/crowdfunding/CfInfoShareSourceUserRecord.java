package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class CfInfoShareSourceUserRecord {

	private long id;

	private long userId;

	private int infoId;

	private long userSourceId;

	private Date createTime;

	private Date updateTime;

	private int isDelete;

	public CfInfoShareSourceUserRecord(CfInfoShareRecord cfInfoShareRecord) {

		this.id = cfInfoShareRecord.getId();

		this.userId = cfInfoShareRecord.getUserId();

		this.infoId = cfInfoShareRecord.getInfoId();

		this.userSourceId = cfInfoShareRecord.getUserSourceId();

		this.createTime = cfInfoShareRecord.getDateCreated();
	}

}
