package com.shuidihuzhu.cf.model.crowdfunding;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Author: wangpeng
 * @Date: 2022/6/7 15:32
 * @Description:
 */
@Data
public class InitialAuditOperationMqVO {

    private long workOrderId;
    private int caseId;
    private int handleType;
    private String handleComment;

    private int orderType;

    private int callStatus;

    private String callUnicode;

    private int userCallStatus;

    private String callComment;

    private List<Integer> passIds;
    private List<Integer> rejectIds;

    private int userId;

    @JsonIgnore
    private Set<Integer> allHandleTypes;

    private String pinKunComment;

    private String diBaoComment;

    @ApiModelProperty("是否为系统自动提交初审审核 1:是；0:不是, 默认不是")
    private int systemAutoAudit;

    @ApiModelProperty("没有打过电话强制提交")
    private boolean aiErForceOp;

    @ApiModelProperty("是否特殊报备发起")
    private boolean specialReport;

    @ApiModelProperty("自定义驳回项内容")
    private Map<Integer, String> customRefuseReason;

    @ApiModelProperty("审核结果")
    private InitialAuditOperationItem.CaseInitialAuditResult caseInitialAuditResult;

    /**
     * 是否能创建医疗审核工单
     */
    private boolean canCreateMedicalWorkOrder;

    /**
     * 操作工单是否成功
     */
    private boolean contactWithWorkOrderOk;
}
