package com.shuidihuzhu.cf.model.crowdfunding;

import java.sql.Timestamp;

/**
 *
 * 微信关注自动回复
 *
 * Created by wangsf on 17/3/10.
 *
 */
public class CfWxSubscribeReply {

	private int id;
	private String eventKey;
	private int thirdType;
	private int method; //处理方式 1-完全匹配;2-前缀匹配;3-正则
	private int type;
	private int keepDefaultReply;
	private String content;
	private Timestamp startTime;
	private Timestamp endTime;
	private String imageUrl;
	private int subBizType;
	private String modelNum;

	private boolean abtest = false;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public int getMethod() {
		return method;
	}

	public void setMethod(int method) {
		this.method = method;
	}

	public String getEventKey() {
		return eventKey;
	}

	public void setEventKey(String eventKey) {
		this.eventKey = eventKey;
	}

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

	public int getKeepDefaultReply() {
		return keepDefaultReply;
	}

	public void setKeepDefaultReply(int keepDefaultReply) {
		this.keepDefaultReply = keepDefaultReply;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public Timestamp getStartTime() {
		return startTime;
	}

	public void setStartTime(Timestamp startTime) {
		this.startTime = startTime;
	}

	public Timestamp getEndTime() {
		return endTime;
	}

	public void setEndTime(Timestamp endTime) {
		this.endTime = endTime;
	}

	public String getImageUrl() {
		return imageUrl;
	}

	public void setImageUrl(String imageUrl) {
		this.imageUrl = imageUrl;
	}

	public boolean isAbtest() {
		return abtest;
	}

	public void setAbtest(boolean abtest) {
		this.abtest = abtest;
	}

	public int getSubBizType() {
		return subBizType;
	}

	public void setSubBizType(int subBizType) {
		this.subBizType = subBizType;
	}

	public String getModelNum() {
		return modelNum;
	}

	public void setModelNum(String modelNum) {
		this.modelNum = modelNum;
	}
}

