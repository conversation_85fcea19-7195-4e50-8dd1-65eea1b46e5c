package com.shuidihuzhu.cf.model.clewtrack;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ClewReceiveModel implements Serializable {
    /**
     * 登记手机号
     */
    private String phone;
    /**
     * 微信号
     */
    private String wxNo;
    /**
     * 登记渠道，根据这个可以解析出一级，二级渠道
     */
    private String channel;
    /**
     * 登记时间
     */
    private Date registerTime;
    /**
     * 登记疾病
     */
    private String disease;
    /**
     * 为谁筹款
     */
    private String relationType;
    /**
     * 登记线索发起人ip
     */
    private Object clientIp;
    /**
     * 一级渠道
     */
    private String primaryKeyChannel;
    /**
     * a/b 测试分组id
     */
    private String outerCheckTestValue;

    // 是否已发起案例  1已发起 其他的未发起
    private String note;
    /**
     * 登记媒体来源
     */
    private String source;

    private String uniqueCode;

    private Long userId;

    /**
     * 用户self_tag
     */
    private String selfTag;
    /**
     * 用户open_id
     */
    private String openId;
    /**
     * 登记页面url
     */
    private String registerUrl;
    /**
     * 关键词
     */
    private String keyword;
    /**
     * 关键词匹配模式/ 计划
     */
    private String match;
    /**
     * 账户
     */
    private String account;
    /**
     * 单元
     */
    private String semwp;
    /**
     * 创意
     */
    private String creative;
    /**
     * 扩展字段
     */
    private String extendField;
    /**
     * 登记人姓名
     */
    private String registerName;
    /**
     * 投放请求的referer
     */
    private String referer;
    /**
     * userAgent
     */
    private String userAgent;
    /**
     * 58Town镇长
     */
    private String townLeaderId;
    /**
     * 设备id
     */
    private String deviceId;
    /**
     * IOS广告标识符
     */
    private String idfa;
    /**
     * 年龄
     */
    private String age;
    /**
     * 性别
     */
    private String male;
    /**
     * 线索唯一编码
     */
    private String displayId;

    /**
     * 渠道标识
     */
    private String mediaChannelId;

    /**
     * 投放点击id
     */
    private String mediaClickId;

    /**
     * 来源案例id
     */
    private String sourceInfoUuid;

    /**
     * 患者就诊医院
     */
    private String hospital;

    /**
     * app投放埋点字段
     */
    private Map<String, String> appTouFangExtMap;

}
