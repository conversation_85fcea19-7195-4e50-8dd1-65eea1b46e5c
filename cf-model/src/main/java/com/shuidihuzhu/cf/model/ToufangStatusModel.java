package com.shuidihuzhu.cf.model;

import com.shuidihuzhu.common.web.enums.Platform;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @package: com.shuidihuzhu.cf.model
 * @Author: l<PERSON>jiaw<PERSON>
 * @Date: 2018/9/25  16:23
 */
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Data
public class ToufangStatusModel {

    //是否绑定手机（是否登录）
    private boolean login = false;
    //是否关注
    private boolean follow = false;
    //是否授权（是否有userId）
    private boolean authorizationV2 = false;
    //是否风险地区
    private boolean riskArea = false;
    //是否捐款
    private boolean donate = false;
    //是否发起过筹款
    private boolean launch = false;
    //是否登记过
    private boolean regist = false;
    //是否最近24小时登记过
    private boolean latestRegist = false;
    //UA
    private String userAgent = "";
    //ip
    private String clientIp = "";
    /**
     * 用户类型
     *
     * 0代表游客，1代表筹款人，2代表捐款人
     *
     */
    private Integer userType=0;
    /**
     * 用户图片
     */
    private String photoUrl="";
    /**
     * 用户姓名
     */
    private String userName="";

    /**
     * 是否是BD,false非BD
     */
    private boolean volunteerFlag = false;

    //是否最近24小时登记过
    private boolean registInThirtyDays = false;

    //最近30天是否有草稿,ture为有,fasle为没有
    private boolean hasDraftInThirtyDays = false;

}
