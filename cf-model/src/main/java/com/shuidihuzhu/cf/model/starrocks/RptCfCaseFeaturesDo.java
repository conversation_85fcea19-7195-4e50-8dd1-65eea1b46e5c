package com.shuidihuzhu.cf.model.starrocks;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/12/18  14:40
 */
@Data
public class RptCfCaseFeaturesDo {
    @ApiModelProperty("案例id")
    private Long infoId;
    @ApiModelProperty("顾问code")
    private String volunteerCode;
    @ApiModelProperty("患者年龄区间")
    private String ageRange;
    @ApiModelProperty("患者治疗方式")
    private String patientTreatmentMethods;
    @ApiModelProperty("疾病名称")
    private String normalizedDisease;
    @ApiModelProperty("是否大空间捐单,是：1，否：0")
    private Integer isBigSpaceDonate;
    @ApiModelProperty("更新时间")
    private String updateTime;
    @ApiModelProperty("好友捐单次数")
    private Long friendDonateCnt;
    @ApiModelProperty("总筹款金额,单位：元")
    private Double donateAmt;
    @ApiModelProperty("总捐款次数")
    private Long donateAnt;
    @ApiModelProperty("好友贡献占比")
    private Integer friendContributions;
    @ApiModelProperty("总转发次数")
    private Long shareCnt;
    @ApiModelProperty("筹款人总转发次数")
    private Long ckrShareCnt;
    @ApiModelProperty("日期")
    private Date dt;
}
