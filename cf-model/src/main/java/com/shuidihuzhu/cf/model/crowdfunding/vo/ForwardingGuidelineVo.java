package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.shuidihuzhu.cf.model.crowdfunding.ForwardingGuidelineOneModel;
import com.shuidihuzhu.cf.model.crowdfunding.ForwardingGuidelineTwoModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/10  14:51
 */
@Data
public class ForwardingGuidelineVo {
    @ApiModelProperty("是否灰度")
    private boolean grey;
    @ApiModelProperty("转发攻略顺序")
    private List<Integer> order;
    @ApiModelProperty("转发攻略一")
    private ForwardingGuidelineOneModel forwardingGuidelineOneModel;
    @ApiModelProperty("转发攻略二")
    private ForwardingGuidelineTwoModel forwardingGuidelineTwoModel;
}
