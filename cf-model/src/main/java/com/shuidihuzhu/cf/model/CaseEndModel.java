package com.shuidihuzhu.cf.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.annotation.Nullable;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CaseEndModel {

    private int caseId;

    /**
     * 案例结束类型
     */
    private int finishStatus;

    private int operatorId;

    /**
     * 案例结束具体描述
     */
    @Nullable
    private String desc;

    /**
     * 结束原因类型 CaseEndReasonEnum
     */
    private int reasonType;
}
