package com.shuidihuzhu.cf.model.contribute;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;

import java.util.Date;

@Data
public class CfContributeOrder {


    private long id;

    private int caseId;
    private long userId;
    private int anonymous;

    private long prePayAmount;
    private long realPayAmount;
    private long feeAmount;

    private int orderStatus;

    private String payUid;
    private String thirdPayUid;

    private Date callBackTime;
    private String comment;

    private String contributeChannel;
    private String contributeSourceId;

    private String payPlatform;

    private  long ip;
    private int osType;


    private String selfTag;

    private int userThirdType;

    private String productName;

    private Date createTime;
    private Date updateTime;

    @ApiModelProperty("支付中心映射的clientId")
    private int clientId;

    @Getter
    public enum OrderStatus {

        NO_PAY(0, "未支付"),
        PAY_SUCCESS(1, "已支付"),
        REFUNDING(2, "退款中"),
        REFUND_FAILED(3, "退款失败"),
        REFUND_SUCCESS(4, "退款成功"),
        ;

        private int code;
        private String msg;

        OrderStatus(int code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        public static OrderStatus valueOfCode(int code) {
            for (OrderStatus status : OrderStatus.values()) {
                if (status.getCode() == code) {
                    return status;
                }
            }
            return null;
        }
    }
}
