package com.shuidihuzhu.cf.model.crowdfunding;

import com.google.common.collect.Maps;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class CfTemplateBaseInfo {
    private int amount;
    private String name;
    private int relationship;
    private String authorName;
    private String diseaseName;
    private String hospitalName;
    //非必要词
    private Integer age;
    private String hometown;
    private String disasterDay;
    private Long cost;
    private int fp;

    private String symptomsBeforeDiagnosis;
    private String firstDisease;
    private String firstHospital;
    private int followCost;
    private int homeDebt;
    private String nowTreatment;
    private String laterTreatment;




    private Map<String, String> familyMemberMap = Maps.newConcurrentMap();
    private Map<String, String> authorOccupationMap = Maps.newConcurrentMap();
    private String medicalArrears;

    public CfTemplateBaseInfo() {
    }

}
