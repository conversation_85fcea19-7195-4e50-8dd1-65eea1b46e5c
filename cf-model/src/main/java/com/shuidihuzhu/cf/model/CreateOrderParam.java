package com.shuidihuzhu.cf.model;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 捐款订单创建param
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class CreateOrderParam {

    private long userId;

    private String infoUuid;

    private double amt;

    private String comment;

    private String platform;

    private String source;

    private String selfTag;

    private String showUrl;

    private String returnUrl;

    private Long userAddressId;

    private Long gearId;

    private Integer goodsCount;

    private Boolean anonymous;

    private Integer userThirdType;

    private String splitFlowUuid;

    private int activityId;

    private String shareSourceId;

    private Integer payType;

    private boolean useSubsidy;

    private int shareDv;

    public CreateOrderParam(long userId, String infoUuid, double amt, String comment, String platform, String source, String selfTag, String showUrl, String returnUrl, Long userAddressId, Long gearId, Integer goodsCount, Boolean anonymous, Integer userThirdType, String splitFlowUuid, String shareSourceId, Integer payType, int activityId, boolean useSubsidy, int shareDv) {
        this.userId = userId;
        this.infoUuid = infoUuid;
        this.amt = amt;
        this.comment = comment;
        this.platform = platform;
        this.source = source;
        this.selfTag = selfTag;
        this.showUrl = showUrl;
        this.returnUrl = returnUrl;
        this.userAddressId = userAddressId;
        this.gearId = gearId;
        this.goodsCount = goodsCount;
        this.anonymous = anonymous;
        this.userThirdType = userThirdType;
        this.splitFlowUuid = splitFlowUuid;
        this.shareSourceId = shareSourceId;
        this.payType = payType;
        this.activityId = activityId;
        this.useSubsidy = useSubsidy;
        this.shareDv = shareDv;
    }

}
