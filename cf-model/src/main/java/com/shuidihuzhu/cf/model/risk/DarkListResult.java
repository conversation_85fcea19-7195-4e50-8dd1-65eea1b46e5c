package com.shuidihuzhu.cf.model.risk;

import com.shuidihuzhu.cf.risk.model.risk.BlacklistVerifyDto;
import com.shuidihuzhu.common.web.model.Response;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class DarkListResult {

    @ApiModelProperty("原始结果数据")
    private Response<List<BlacklistVerifyDto>> resourceResponse;

    @ApiModelProperty("命中的条目数据")
    private List<BlacklistVerifyDto> hitList;

    @ApiModelProperty("是否通过")
    private boolean passed;
}
