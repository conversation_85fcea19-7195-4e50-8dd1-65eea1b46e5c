package com.shuidihuzhu.cf.model.awardactivity;

import com.shuidihuzhu.common.web.model.AbstractModel;
import com.shuidihuzhu.common.web.model.AbstractModel;

import java.util.Date;

/**
 * Created by <PERSON><PERSON> on 16/11/28.
 */
public class ActivityInfo extends AbstractModel {
	int id;
	Date startTime;
	Date endTime;
	String description;
	Date dateCreated;
	Date lastModified;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public Date getStartTime() {
		return startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public Date getDateCreated() {
		return dateCreated;
	}

	public void setDateCreated(Date dateCreated) {
		this.dateCreated = dateCreated;
	}

	public Date getLastModified() {
		return lastModified;
	}

	public void setLastModified(Date lastModified) {
		this.lastModified = lastModified;
	}
}
