package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.sql.Timestamp;
import java.util.Date;

/**
 *
 * 区别于CfAdRegisterDay的新版本投放，兼容各个类型的投放
 *
 * Created by wangsf on 17/4/27.
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
public class CfAdRegister {
	private int id;
	private Date dayKey;
	private Timestamp createTime;
	private Timestamp lastModified;
	private String type;
	private String mobile;
	private String cryptoMobile;
	private String channel;
	private long userId;
	private String openId;
	private String selfTag;
	private String userName;
	private String disease;
	private String relation;
	private String help;
	private String note;
	private String infoUuid;
	private boolean isInviter;
	private String inviterMobile;
	private String cryptoInviterMobile;
	private long clientIp;
	private  String source;
	private String primaryChannel;
	private String semwp;
	private String keyword;
}
