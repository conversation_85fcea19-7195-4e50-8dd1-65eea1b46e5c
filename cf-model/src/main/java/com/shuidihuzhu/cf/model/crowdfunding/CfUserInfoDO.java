package com.shuidihuzhu.cf.model.crowdfunding;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.shuidihuzhu.cf.mq.model.UserInfoMessage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.joda.time.DateTime;

import java.util.Date;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @author: fengxuan
 * @create 2019-09-30 10:56
 **/
@Data
@NoArgsConstructor
public class CfUserInfoDO {

    /**
     * firstShareTime:首转时间
     * shardCount:转发案例数
     * shareDonateCount:捐或转总数
     * remotestCaseId:捐款的距离最远案例
     * remotestCaseDistance:距最远案例的距离
     * remotestCaseDonateTime:捐款的距离最远案例的时间
     * donateChildCaseCount:帮助儿童患者案例数
     * affectedByFriends:捐款或转发来自哪些一度好友的转发
     * affectFriends:转发带来的捐款或转发一度用户
     * sameActionFriends:水滴捐或转用户中你的一度好友
     * delete:0未删除
     */
    @JsonIgnore
    private int id;

    @JsonIgnore
    @ApiModelProperty("用户id")
    private long userId;

    @ApiModelProperty("首转时间")
    private Date firstShareTime;

    @ApiModelProperty("转发案例数")
    private int shareCount;

    @ApiModelProperty("捐或转总数")
    private int shareDonateCount;

    @JsonIgnore
    @ApiModelProperty("捐款的距离最远案例")
    private int remotestCaseId;

    @ApiModelProperty("距最远案例的距离")
    private String remotestCaseDistance = "";

    @ApiModelProperty("捐款的距离最远案例的时间")
    private Date remotestCaseDonateTime;

    @ApiModelProperty("帮助儿童患者案例数")
    private int donateChildCaseCount;

    @JsonIgnore
    @ApiModelProperty("捐款或转发来自哪些一度好友的转发")
    private String affectedByFriends;

    @JsonIgnore
    @ApiModelProperty("转发带来的捐款或转发一度用户")
    private String affectFriends;

    @JsonIgnore
    @ApiModelProperty("水滴捐或转用户中你的一度好友")
    private String sameActionFriends;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新时间")
    private Date updateTime;

    private int delete;

    public CfUserInfoDO create(UserInfoMessage message, long userId) {
        this.userId = userId;
        if (message.getFirstShareTime() != null) {
            this.firstShareTime = new Date(message.getFirstShareTime());
        }
        if (message.getShareCount() != null) {
            this.shareCount = message.getShareCount();
        }
        if (message.getShareAndDonateCount() != null) {
            this.shareDonateCount = message.getShareAndDonateCount();
        }
        if (message.getRemotestCaseId() != null) {
            this.remotestCaseId = message.getRemotestCaseId();
        }
        if (message.getRemotestCaseDistance() != null) {
            this.remotestCaseDistance = String.valueOf(message.getRemotestCaseDistance());
        }
        if (message.getRemotestCaseDonateTime() != null) {
            this.remotestCaseDonateTime = new Date(message.getRemotestCaseDonateTime());
        }
        if (message.getDonateChildCaseCount() != null) {
            this.donateChildCaseCount = message.getDonateChildCaseCount();
        }
        if (CollectionUtils.isNotEmpty(message.getAffectedByFriends())) {
            this.affectedByFriends = Joiner.on(',')
                    .join(message.getAffectedByFriends().stream().limit(100).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(message.getAffectFriends())) {
            this.affectFriends = Joiner.on(',')
                    .join(message.getAffectFriends().stream().limit(100).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(message.getSameActionFriends())) {
            this.sameActionFriends = Joiner.on(',')
                    .join(message.getSameActionFriends().stream().limit(100).collect(Collectors.toList()));
        }
        return this;
    }
}
