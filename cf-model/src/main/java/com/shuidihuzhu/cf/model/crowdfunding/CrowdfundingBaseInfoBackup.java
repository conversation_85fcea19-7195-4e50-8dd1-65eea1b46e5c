package com.shuidihuzhu.cf.model.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.shuidihuzhu.cf.enums.crowdfunding.CfVersion;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveIdcardVerifyStatusEnum;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoBaseVo;
import com.shuidihuzhu.cf.param.raise.RaiseCaseParam;
import com.shuidihuzhu.cf.vo.CfFirsApproveMaterialVO;
import com.shuidihuzhu.cf.vo.questionnaire.TemplateTitleAndContentVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

@Data
@ApiModel
public class CrowdfundingBaseInfoBackup implements Serializable {

    public enum DraftTypeEnum {

        //前置审核用的草稿类型
        ORIGIN(0, true),

        //投放用的草稿类型
        TOUFANG(1, false),

        //智能发起
        ZHINENG(10, true),

        //问卷发起
        WENJUAN(11, true),

        //助手一对一问答
        ZHUSHOU(12, true),

        //分布式
        FENBU(13, true),

        //左滑智能发起
        ZUOHUA_ZHINENG(14, true),

        //优化文案
        YOUHUA_WENAN(15,true),

        //发起页智能客服
        FAQI_ZHINENG_KEFU(16,true),

        //多次智能发起
        DUOCI_ZHINENG(17, true),

        //发起页不会写标题/求助说明
        FAQI_ZHINENG(18, true),
        ;

        DraftTypeEnum(int code, boolean sendFirstRecallMsg) {
            this.code = code;
            this.sendFirstRecallMsg = sendFirstRecallMsg;
        }

        public static DraftTypeEnum parse(int code){
            for(DraftTypeEnum draftTypeEnum : DraftTypeEnum.values()){
                if(draftTypeEnum.getCode() == code){
                    return draftTypeEnum;
                }
            }
            throw new IllegalArgumentException(String.valueOf(code));
        }

        @Getter
        private int code;

        /**
         * 是否发送前置审核召回消息，如果这一项不清楚，增加草稿类型的时候问一下PM
         * 早期的PM是品墨、祥瑞
         */
        @Getter
        private boolean sendFirstRecallMsg;
    }

    private int id;

    @JsonIgnore
    private long userId;

    @ApiModelProperty("标题")
    private String title = "";

    @ApiModelProperty("文章")
    private String content = "";

    @ApiModelProperty("目标金额")
    private int targetAmount;

    @ApiModelProperty("照片")
    private String pictureUrl = "";

    @ApiModelProperty("渠道")
    private String channel = "";

    private Date createTime;

    @JsonIgnore
    private Date updateTime;

    @JsonIgnore
    private String preAuditImageUrl = "";  //初次审核的照片

    @JsonIgnore
    @ApiModelProperty("建档立卡脱贫户  补充材料 eg: url1,url2,url3")
    private String povertyImageUrl = "";


    @ApiModelProperty("v3草稿数据 初审加增信")
    private CrowdfundingInfoBaseVo crowdfundingInfoBaseVo;

    @JsonIgnore
    @ApiModelProperty("增信草稿内容id")
    private long materialBundleId;

    @ApiModelProperty("页面标识  - pageFlag传值 baseInfo(图文) firstApprove(前置) family(家庭经济状况) protection(患者保障状况)")
    private String pageFlag = "";

    @ApiModelProperty("页面填写详情")
    private PageDetail pageFillInfoDetail;

    @ApiModelProperty("版本")
    private int cfVersion;
    /**
     * 类型 0 为普通，其他的参见上面的enum
     */
    @JsonIgnore
    private int type;

    // 20200215新增 以前的案例默认值都是0
    @ApiModelProperty("记录是否是智能发起 0不是 1是 ")
    private int useTemplateRaise;

    @ApiModelProperty("用户的智能模版的参数")
    private TemplateTitleAndContentVo templateParam;

    @JsonIgnore
    private String templateParamDetail;

    @ApiModelProperty("用户发起手机号")
    private String mobile;

    @ApiModelProperty("v4草稿内容")
    private RaiseCaseParam raiseCaseParam;

    public void setTemplateFromDetail() {
        if (StringUtils.isNotBlank(templateParamDetail)) {
            templateParam = JSON.parseObject(templateParamDetail, TemplateTitleAndContentVo.class);
        }
    }

    // db存字段使用 勿动
    public String getTemplateParamDetail() {
        if (templateParam == null) {
            return "";
        }

        return JSON.toJSONString(templateParam);

    }


    /**
     *   - pageFlag传值 baseInfo(图文) firstApprove(前置) family(家庭经济状况) protection(患者保障状况)
     */
    @Data
    public static class PageDetail{
        private boolean baseInfo;
        private boolean firstApprove;
        private boolean family;
        private boolean protection;
    }
}
