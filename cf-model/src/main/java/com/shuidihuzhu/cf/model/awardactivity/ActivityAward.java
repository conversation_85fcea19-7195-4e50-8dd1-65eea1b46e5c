package com.shuidihuzhu.cf.model.awardactivity;


import com.shuidihuzhu.common.web.model.AbstractModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * Created by ch<PERSON> on 16/9/12.
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@ToString
public class ActivityAward extends AbstractModel {
	private Integer id;

	private Integer type;   // type = 0 表示未中奖

	private Integer totalCount;  // 总数为0表示没有中奖上限

	private Integer sendOutCount;

	private float odds; // 中奖概率

	private Integer activityId;

	private Date addTime;

	private String extraId;
	
	private String description;

	private Integer awardType;

	private String thumbUrl;

	private String name;

	private Integer sort;
}
