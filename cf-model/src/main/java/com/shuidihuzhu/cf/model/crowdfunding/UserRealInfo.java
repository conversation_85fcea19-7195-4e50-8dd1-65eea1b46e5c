package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.common.web.model.AbstractModel;

import java.sql.Timestamp;

public class UserRealInfo extends AbstractModel {

	private static final long serialVersionUID = 1233418411520489019L;

	private int id;
	private long userId;
	private String orderId;
	private String name;
	private String idCard;
	private String cryptoIdCard;
	private int idcardVerifyStatus;
	private String resultCode;
	private String resultMsg;
	private Timestamp dateCreated;
	private Timestamp lastModified;
	private int medicalStatus;
	private String medicalImageUrl;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getIdCard() {
		return idCard;
	}

	public void setIdCard(String idCard) {
		this.idCard = idCard;
	}

	public String getCryptoIdCard() {
		return cryptoIdCard;
	}

	public void setCryptoIdCard(String cryptoIdCard) {
		this.cryptoIdCard = cryptoIdCard;
	}

	public int getIdcardVerifyStatus() {
		return idcardVerifyStatus;
	}

	public void setIdcardVerifyStatus(int idcardVerifyStatus) {
		this.idcardVerifyStatus = idcardVerifyStatus;
	}

	public Timestamp getDateCreated() {
		return dateCreated;
	}

	public void setDateCreated(Timestamp dateCreated) {
		this.dateCreated = dateCreated;
	}

	public Timestamp getLastModified() {
		return lastModified;
	}

	public void setLastModified(Timestamp lastModified) {
		this.lastModified = lastModified;
	}

	public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}

	public String getOrderId() {
		return orderId;
	}

	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}

	public String getResultCode() {
		return resultCode;
	}

	public void setResultCode(String resultCode) {
		this.resultCode = resultCode;
	}

	public String getResultMsg() {
		return resultMsg;
	}

	public void setResultMsg(String resultMsg) {
		this.resultMsg = resultMsg;
	}


	public int getMedicalStatus() {
		return medicalStatus;
	}

	public void setMedicalStatus(int medicalStatus) {
		this.medicalStatus = medicalStatus;
	}

	public String getMedicalImageUrl() {
		return medicalImageUrl;
	}

	public void setMedicalImageUrl(String medicalImageUrl) {
		this.medicalImageUrl = medicalImageUrl;
	}
}
