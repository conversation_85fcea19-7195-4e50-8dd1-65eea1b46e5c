package com.shuidihuzhu.cf.model.crowdfunding;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * cf_patient_evaluation_record
 * <AUTHOR>
@Data
public class CfPatientEvaluationRecord implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 患者userid
     */
    private Long userId;

    /**
     * 案例id
     */
    private Long caseId;

    private String volunteerName;

    /**
     * 顾问unique
     */
    private String volunteersUniqueCode;

    /**
     * 顾问类型
     */
    private Integer volunteerType;

    /**
     * 评价等级
     */
    private Integer evaluationRank;

    /**
     * 评价标签,多个用逗号隔开
     */
    private String evaluationLabel;

    /**
     * 评价内容
     */
    private String evaluationContent;


    private static final long serialVersionUID = 1L;
}