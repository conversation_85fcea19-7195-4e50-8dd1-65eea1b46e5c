package com.shuidihuzhu.cf.model.crowdfunding;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Author：liuchangjun
 * @Date：2021/9/28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserSatisfactionDO {

    private long id;

    @ApiModelProperty("案例id")
    private int caseId;

    @ApiModelProperty("用户id")
    private long userId;

    @ApiModelProperty("测试组类型")
    private int testType;

    @ApiModelProperty("被调查信息名称")
    private String informationName;

    @ApiModelProperty("用户选择结果")
    private int result;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新时间")
    private Date updateTime;
}
