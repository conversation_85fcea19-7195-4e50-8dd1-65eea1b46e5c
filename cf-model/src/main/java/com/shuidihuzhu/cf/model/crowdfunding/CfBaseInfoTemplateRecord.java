package com.shuidihuzhu.cf.model.crowdfunding;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR> Ahrievil
 */
@Data
@ApiModel("智能发起记录")
public class CfBaseInfoTemplateRecord extends CfTemplateBaseInfo {

    @JsonIgnore
    private int id;

    @ApiModelProperty("案例Id")
    private String infoUuid;
    @JsonIgnore
    private int titleId;
    @JsonIgnore
    private int contentId;
    @ApiModelProperty("关系")
    private String relationStr;

    public CfBaseInfoTemplateRecord() {
    }

    public CfBaseInfoTemplateRecord(CfTemplateBaseInfo cfTemplateBaseInfo) {
        BeanUtils.copyProperties(cfTemplateBaseInfo, this);
    }
}
