package com.shuidihuzhu.cf.model.crowdfunding;

import java.sql.Timestamp;

/**
 *
 * 填写图文页打点
 *
 * Created by wangsf on 17/5/4.
 */
public class CfStatBaseInfoPoint {

	private int id;
	private long userId;
	private String selfTag;
	private int amount;
	private String title;
	private String fromPath;
	private String toPath;
	private int type;
	private String infoUuid;
	private String content;
	private int urlNo;
	private String channel;
	private String openId;
	private String grayMode;
	private String mobile;
	private String appUniqueId;
	private String appVersion;
	private String appSubVersion;
	private Timestamp opTime;

	public CfStatBaseInfoPoint() {
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}

	public String getSelfTag() {
		return selfTag;
	}

	public void setSelfTag(String selfTag) {
		this.selfTag = selfTag;
	}

	public int getAmount() {
		return amount;
	}

	public void setAmount(int amount) {
		this.amount = amount;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getFromPath() {
		return fromPath;
	}

	public void setFromPath(String fromPath) {
		this.fromPath = fromPath;
	}

	public String getToPath() {
		return toPath;
	}

	public void setToPath(String toPath) {
		this.toPath = toPath;
	}

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

	public String getInfoUuid() {
		return infoUuid;
	}

	public void setInfoUuid(String infoUuid) {
		this.infoUuid = infoUuid;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public int getUrlNo() {
		return urlNo;
	}

	public void setUrlNo(int urlNo) {
		this.urlNo = urlNo;
	}

	public Timestamp getOpTime() {
		return opTime;
	}

	public void setOpTime(Timestamp opTime) {
		this.opTime = opTime;
	}

	public String getChannel() {
		return channel;
	}

	public void setChannel(String channel) {
		this.channel = channel;
	}

	public String getOpenId() {
		return openId;
	}

	public void setOpenId(String openId) {
		this.openId = openId;
	}

	public String getGrayMode() {
		return grayMode;
	}

	public void setGrayMode(String grayMode) {
		this.grayMode = grayMode;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        CfStatBaseInfoPoint that = (CfStatBaseInfoPoint) o;

        return userId == that.userId;
    }

    @Override
    public int hashCode() {
        return Long.valueOf(userId).hashCode();
    }

	public String getAppUniqueId() {
		return appUniqueId;
	}

	public void setAppUniqueId(String appUniqueId) {
		this.appUniqueId = appUniqueId;
	}

	public String getAppVersion() {
		return appVersion;
	}

	public void setAppVersion(String appVersion) {
		this.appVersion = appVersion;
	}

	public String getAppSubVersion() {
		return appSubVersion;
	}

	public void setAppSubVersion(String appSubVersion) {
		this.appSubVersion = appSubVersion;
	}

	@Override
	public String toString() {
		return "CfStatBaseInfoPoint{" +
				"id=" + id +
				", userId=" + userId +
				", selfTag='" + selfTag + '\'' +
				", amount=" + amount +
				", title='" + title + '\'' +
				", fromPath='" + fromPath + '\'' +
				", toPath='" + toPath + '\'' +
				", type=" + type +
				", infoUuid='" + infoUuid + '\'' +
				", content='" + content + '\'' +
				", urlNo=" + urlNo +
				", channel='" + channel + '\'' +
				", openId='" + openId + '\'' +
				", mobile='" + mobile + '\'' +
				", grayMode='" + grayMode + '\'' +
				", opTime=" + opTime +
				'}';
	}
}
