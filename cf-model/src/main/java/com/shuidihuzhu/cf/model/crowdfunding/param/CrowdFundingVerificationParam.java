package com.shuidihuzhu.cf.model.crowdfunding.param;

import com.shuidihuzhu.common.web.model.AbstractModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/10/18.
 */
@Data
public class CrowdFundingVerificationParam extends AbstractModel {

    /***
     * 筹款案例ID
     */
    private String crowdFundingInfoId;

    private int caseId;

    /***
     * 受助人
     */
    private long patientUserId;

    /***
     * 证实人
     */
    private long verifyUserId;

    /***
     * 受助人, 证实人的关系
     */
    @NotNull
    private Integer relationShip;

    @NotNull
    private String userName;

    @NotNull
    private String description;

    @NotNull
    private Integer valid;

    private String openId;

    private int thirdType;
    
    private String selfTag;
    
    private String infoUuid;

    private Integer userThirdType;
	
    private String mobile;

    private String shareSourceId;

    private String channel;

    @ApiModelProperty("用户当前访问的url")
    private String currentPath = "";

    @ApiModelProperty("用户分享时对分享用户的标识")
    private String userSourceId = "";

    @ApiModelProperty("来自微信群或者个人消息")
    private String from = "";

    @ApiModelProperty("省份code")
    private Integer provinceCode;

    @ApiModelProperty("省份名称")
    private String provinceName;

    @ApiModelProperty("医疗机构code")
    private Integer hospitalCode;

    @ApiModelProperty("医院名称")
    private String hospitalName;

    @ApiModelProperty("医护证明图片url List")
    private List<String> medicalImageList;

    private String label;
}
