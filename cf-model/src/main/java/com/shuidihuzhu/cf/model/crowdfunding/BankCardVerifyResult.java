package com.shuidihuzhu.cf.model.crowdfunding;

import org.apache.commons.lang3.StringUtils;

/**
 * Author: <PERSON> Date: 16/10/18 17:34
 */
public class BankCardVerifyResult {
	private String nowpayTransId;
	private String responseCode;
	private String responseMsg;
	private String status;
	private String finalMessage;

	public BankCardVerifyResult() {
	}

	public static BankCardVerifyResult build(String nowpayTransId, String responseCode, String responseMsg,
			String finalMessage) {
		BankCardVerifyResult bankCardVerifyResult = new BankCardVerifyResult();
		bankCardVerifyResult.setNowpayTransId(StringUtils.trimToEmpty(nowpayTransId));
		bankCardVerifyResult.setResponseCode(StringUtils.trimToEmpty(responseCode));
		bankCardVerifyResult.setResponseMsg(StringUtils.trimToEmpty(responseMsg));
		bankCardVerifyResult.setFinalMessage(StringUtils.trimToEmpty(finalMessage));
		return bankCardVerifyResult;
	}

	public BankCardVerifyResult(String nowpayTransId, String responseCode, String responseMsg, String status) {
		this.nowpayTransId = StringUtils.trimToEmpty(nowpayTransId);
		this.responseCode = StringUtils.trimToEmpty(responseCode);
		this.responseMsg = StringUtils.trimToEmpty(responseMsg);
		this.status = StringUtils.trimToEmpty(status);

		switch (responseCode) {
			case "0000":
				finalMessage = "认证信息匹配";
				break;
			case "0001":
				finalMessage = "认证信息不匹配";
				break;
			case "0002":
				finalMessage = "参数为空或不合法";
				break;
			case "0003":
				finalMessage = "获取商户信息失败";
				break;
			case "0004":
				finalMessage = "获取商户费率失败";
				break;
			case "0005":
				finalMessage = "获取商户可用条数失败";
				break;
			case "0006":
				finalMessage = "商户资金不足";
				break;
			case "0007":
				finalMessage = "商户订单号重复";
				break;
			case "0008":
				finalMessage = "验证异常";
				break;
			case "0009":
				finalMessage = "身份证名字转码错误";
				break;
			case "0010":
				finalMessage = "银行卡格式错误或银行规则未配置";
				break;
			case "0011":
				finalMessage = "银行卡不支持";
				break;
			case "0012":
				finalMessage = "未获取到可用渠道";
				break;
			default:
				if (responseMsg.length() > 0) {
					finalMessage = responseMsg;
				} else {
					finalMessage = "未知错误";
				}
				break;
		}
	}

	public boolean isOk() {
		return responseCode.equals("0000");
	}

	public String getResponseCode() {
		return responseCode;
	}

	public void setResponseCode(String responseCode) {
		this.responseCode = responseCode;
	}

	public String getResponseMsg() {
		return responseMsg;
	}

	public void setResponseMsg(String responseMsg) {
		this.responseMsg = responseMsg;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getFinalMessage() {
		return finalMessage;
	}

	public void setFinalMessage(String finalMessage) {
		this.finalMessage = finalMessage;
	}

	public String getNowpayTransId() {
		return nowpayTransId;
	}

	public void setNowpayTransId(String nowpayTransId) {
		this.nowpayTransId = nowpayTransId;
	}

}
