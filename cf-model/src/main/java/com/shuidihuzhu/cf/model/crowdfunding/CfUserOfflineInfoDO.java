package com.shuidihuzhu.cf.model.crowdfunding;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @author: fengxuan
 * @create 2019-10-10 21:02
 **/
@Data
@NoArgsConstructor
public class CfUserOfflineInfoDO {
    /**
     * userLocal: 用户地域（市级）最常出现地域
     * firstViewTime: 首次浏览案例时间
     * viewCount: 浏览案例总数
     * mostViewCity: 浏览案例最多的地区（市级）
     * donateRank: 捐款金额分位，1-按捐款金额降序排列序号/总捐款人数,实际展示是19.01%,存的数据为1901
     * shareOrDonateFirstFinishTime: 捐款或转发的首个筹满案例的时间
     * firstFinishCaseId: 捐款或转发的首个筹满案例标识
     * firstFinishDuration: 捐款或转发的首个筹满案例的筹款周期,天数
     * firstFinishAmount: 捐款或转发的首个筹满案例的获捐金额,分为单位
     * firstFinishDonationCount: 捐款或转发的首个筹满案例的获捐次数
     * contributeViewCount: 转发带来的下一步访问量
     * contributeViewer: 转发带来的下一步访问人数
     * contributeAmount: 转发带来的下一步捐款金额,分为单位
     * contributeDonator: 转发带来的下一步捐款人数
     * contributeShareCount: 转发带来的下一步转发量
     * contributeSharer: 转发带来的下一步转发人数
     * contributeShareRank: 转发带来的下一步访问量分位数,1 - ‘转发带来的下一步访问量’降序排列排名/总的转发人数,实际展示是19.01%,存的数据为1901
     */
    @JsonIgnore
    @ApiModelProperty("id")
    private int id;

    @JsonIgnore
    @ApiModelProperty("用户id")
    private long userId;

    @ApiModelProperty("用户地域（市级）最常出现地域")
    private String userLocal;

    @ApiModelProperty("首次浏览案例时间")
    private Date firstViewTime;

    @ApiModelProperty("浏览案例总数")
    private int viewCount;

    @ApiModelProperty("浏览案例最多的地区（市级）")
    private String mostViewCity;

    @ApiModelProperty("捐款金额分位，1-按捐款金额降序排列序号/总捐款人数,实际展示是19.01%,存的数据为1901")
    private int donateRank;

    @ApiModelProperty("捐款或转发的首个筹满案例的时间")
    private Date shareOrDonateFirstFinishTime;

    @ApiModelProperty("捐款或转发的首个筹满案例标识")
    private int firstFinishCaseId;

    @ApiModelProperty("捐款或转发的首个筹满案例的筹款周期,天数")
    private int firstFinishDuration;

    @ApiModelProperty("捐款或转发的首个筹满案例的获捐金额,分为单位")
    private int firstFinishAmount;

    @ApiModelProperty("捐款或转发的首个筹满案例的获捐次数")
    private int firstFinishDonationCount;

    @ApiModelProperty("转发带来的下一步访问量")
    private int contributeViewCount;

    @ApiModelProperty("转发带来的下一步访问人数")
    private int contributeViewer;

    @ApiModelProperty("转发带来的下一步捐款金额,分为单位")
    private int contributeAmount;

    @ApiModelProperty("转发带来的下一步捐款人数")
    private int contributeDonator;

    @ApiModelProperty("转发带来的下一步转发量")
    private int contributeShareCount;

    @ApiModelProperty("转发带来的下一步转发人数")
    private int contributeSharer;

    @ApiModelProperty("转发带来的下一步访问量分位数,1-‘转发带来的下一步访问量’降序排列排名/总的转发人数,实际展示是19.01%,存的数据为1901")
    private int contributeShareRank;

    @JsonIgnore
    @ApiModelProperty("浏览过的案例id集合")
    private String viewCaseIdList;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新时间")
    private Date updateTime;
}
