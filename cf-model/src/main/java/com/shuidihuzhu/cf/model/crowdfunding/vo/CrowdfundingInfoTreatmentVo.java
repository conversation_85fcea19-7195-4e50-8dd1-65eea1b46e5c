package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.util.List;
import java.util.Map;

public class CrowdfundingInfoTreatmentVo extends CrowdfundingInfoParentVo {

	private String diseaseName;
	//治疗医院
	private String hospitalName;
	//诊断医院
	private String diagnoseHospitalName;
	private int subAttachmentType;
	private int hospitalId;
	private int diagnoseHospitalId;
	private int hospitalCityId;
	private int diagnoseHospitalCityId;
	private String verifyPhoto;
	//旧案例
	private List<String> attachments;
	//新案例
	private List<ImageTypeUrl> newAttachments;
	private int status;
	private Map<AttachmentTypeEnum, List<String>> attachUrlMap;

	@ApiModelProperty("医院编码")
	private String hospitalCode;


	@Override
	public int getStatus() {
		return status;
	}

	@Override
	public void setStatus(int status) {
		this.status = status;
	}

	public Map<Integer, List<String>> getRejectDetails() {
		return rejectDetails;
	}

	private Map<Integer, List<String>> rejectDetails;

	public String getDiseaseName() {
		return diseaseName;
	}

	public void setDiseaseName(String diseaseName) {
		this.diseaseName = diseaseName;
	}

	public String getHospitalName() {
		return hospitalName;
	}

	public void setHospitalName(String hospitalName) {
		this.hospitalName = hospitalName;
	}

	public String getDiagnoseHospitalName() {
		return diagnoseHospitalName;
	}

	public void setDiagnoseHospitalName(String diagnoseHospitalName) {
		this.diagnoseHospitalName = diagnoseHospitalName;
	}

	public int getSubAttachmentType() {
		return subAttachmentType;
	}

	public void setSubAttachmentType(int subAttachmentType) {
		this.subAttachmentType = subAttachmentType;
	}

    public int getHospitalId() {
        return hospitalId;
    }

    public void setHospitalId(int hospitalId) {
        this.hospitalId = hospitalId;
    }

    public int getDiagnoseHospitalId() {
        return diagnoseHospitalId;
    }

    public void setDiagnoseHospitalId(int diagnoseHospitalId) {
        this.diagnoseHospitalId = diagnoseHospitalId;
    }

	public int getHospitalCityId() {
		return hospitalCityId;
	}

	public void setHospitalCityId(int hospitalCityId) {
		this.hospitalCityId = hospitalCityId;
	}

	public int getDiagnoseHospitalCityId() {
		return diagnoseHospitalCityId;
	}

	public void setDiagnoseHospitalCityId(int diagnoseHospitalCityId) {
		this.diagnoseHospitalCityId = diagnoseHospitalCityId;
	}

	public String getVerifyPhoto() {
		return verifyPhoto;
	}

	public void setVerifyPhoto(String verifyPhoto) {
		this.verifyPhoto = verifyPhoto;
	}

	public List<String> getAttachments() {
		return attachments;
	}

	public void setAttachments(List<String> attachments) {
		this.attachments = attachments;
	}

	public List<ImageTypeUrl> getNewAttachments() {
		return newAttachments;
	}

	public void setNewAttachments(List<ImageTypeUrl> newAttachments) {
		this.newAttachments = newAttachments;
	}

	public void setAttachUrlMap(Map<AttachmentTypeEnum, List<String>> attachUrlMap){
		this.attachUrlMap= attachUrlMap;
	}

	public Map<AttachmentTypeEnum, List<String>> getAttachUrlMap(){
		return attachUrlMap;
	}

	public String getHospitalCode() {
		return hospitalCode;
	}

	public void setHospitalCode(String hospitalCode) {
		this.hospitalCode = hospitalCode;
	}

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}

	public void setRejectDetails(Map<Integer,List<String>> rejectDetails) {
		this.rejectDetails = rejectDetails;
	}


	public class ImageTypeUrl {
		private int imageType;
		private List<String> imageUrl;

		public int getImageType() {
			return imageType;
		}

		public void setImageType(int imageType) {
			this.imageType = imageType;
		}

		public List<String> getImageUrl() {
			return imageUrl;
		}

		public void setImageUrl(List<String> imageUrl) {
			this.imageUrl = imageUrl;
		}

		@Override
		public String toString() {
			return "ImageTypeUrl{" +
					"imageType=" + imageType +
					", imageUrl='" + imageUrl + '\'' +
					'}';
		}
	}
}
