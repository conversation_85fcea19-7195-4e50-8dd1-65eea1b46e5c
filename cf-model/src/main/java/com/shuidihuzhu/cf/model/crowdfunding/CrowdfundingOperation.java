package com.shuidihuzhu.cf.model.crowdfunding;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingOperationEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;

@Data
@ApiModel("案例操作相关的统计字段")
public class CrowdfundingOperation {

    @JsonIgnore
	private Integer id;
	private String infoId;
	private Integer operation = CrowdfundingOperationEnum.NONE_OPERATION.value();
	private Integer operatorId;
	private String reason;
	private Timestamp auditCommitTime;
	private Timestamp operateTime;

	@ApiModelProperty("材料审核的驳回次数")
	private Integer refuseCount;

	@ApiModelProperty("用户要求的驳回次数")
	private int userRefuseCount;

	private Integer callCount;
	private Integer callStatus;
    private Integer reportStatus;
    private Integer followType;
	private Timestamp dateCreated;
	private Timestamp lastModified;

	private int caseId;

	//主动服务标签   1  代表主动服务
	private int fuwuType;
}
