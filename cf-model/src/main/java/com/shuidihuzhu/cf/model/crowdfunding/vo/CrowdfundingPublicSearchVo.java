package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-09-07 12:05 PM
 **/
@Data
public class CrowdfundingPublicSearchVo {

    /**
     * 标题
     */
    private String title;
    /**
     * 头图
     */
    private String titleImg;
    /**
     * 案例id
     */
    private String infoId;

    public static CrowdfundingPublicSearchVo of(CrowdfundingInfo crowdfundingInfo) {
        CrowdfundingPublicSearchVo vo = new CrowdfundingPublicSearchVo();
        vo.setInfoId(crowdfundingInfo.getInfoId());
        vo.setTitleImg(crowdfundingInfo.getTitleImg());
        vo.setTitle(crowdfundingInfo.getTitle());
        return vo;
    }
}
