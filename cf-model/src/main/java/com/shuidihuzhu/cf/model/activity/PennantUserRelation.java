package com.shuidihuzhu.cf.model.activity;

import lombok.Data;

import java.util.Date;

/**
 * Created by dongcf on 2020/3/16
 */
@Data
public class PennantUserRelation {

    private Long id;

    private Long userId;

    /**
     * 锦旗id
     */
    private Long pennantId;

    /**
     * 授权类型
     *{@link com.shuidihuzhu.cf.enums.activity.PennantAwardTypeEnum}
     */
    private Integer awardType;

    /**
     * 案例id
     */
    private String infoId;

    /**
     * 支付单号
     */
    private String payUid;

    /**
     * 领取状态
     *{@link com.shuidihuzhu.cf.enums.activity.PennantReceiveStatusEnum}
     */
    private Integer receiveStatus;

    private Date createTime;
}
