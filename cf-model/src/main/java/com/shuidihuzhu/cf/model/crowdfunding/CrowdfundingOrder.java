package com.shuidihuzhu.cf.model.crowdfunding;

import com.google.common.base.Preconditions;
import com.shuidihuzhu.account.compatible.model.HasUserId;
import com.shuidihuzhu.common.web.model.AbstractModel;
import com.shuidihuzhu.common.web.model.base.HasCode;
import com.shuidihuzhu.common.web.util.MoneyUtil;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by lgj on 16/6/21.
 */
public class CrowdfundingOrder extends AbstractModel implements HasCode, HasUserId {

	private static final long serialVersionUID = -1L;

//	private Integer id;

    private Long id;

    private String code;

    private long userId;

    private Integer crowdfundingId;

    private Integer amount;

    private String comment;

    private Integer payStatus;

    private Date ctime;

    private Date payTime;

    private Integer valid;

    private Long ip;

    private Integer osType;

    private String channel;

    private String from;

    private String selfTag;

    private Integer userThirdId;

    private Integer userThirdType;

    private boolean anonymous;

    private int score;

    private String payUid;

    private String infoId;

    private Integer cfAmount;

    @Getter @Setter
    private String shareSourceId;

    @Getter @Setter
    private int activityId;

    @Getter @Setter
    private int shareDv;

    /**
     * 单笔退款标记
     */
    @Getter @Setter
    private int singleRefundFlag;


    public Long getIp() {
        return ip;
    }

    public void setIp(Long ip) {
        this.ip = ip;
    }

    public Integer getOsType() {
        return osType;
    }

    public void setOsType(Integer osType) {
        this.osType = osType;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }
//
//    public Long getId() {
//        return id;
//    }
//
//    public void setId(Long id) {
//        this.id = id;
//    }


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public Integer getCrowdfundingId() {
        return crowdfundingId;
    }

    public void setCrowdfundingId(Integer crowdfundingId) {
        this.crowdfundingId = crowdfundingId;
    }

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public Integer getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(Integer payStatus) {
        this.payStatus = payStatus;
    }

    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }

    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public Integer getValid() {
        return valid;
    }

    public void setValid(Integer valid) {
        this.valid = valid;
    }

    public String getFrom() {
        return from;
    }

    public void setFrom(String from) {
        this.from = from;
    }

    public String getSelfTag() {
        return selfTag;
    }

    public void setSelfTag(String selfTag) {
        this.selfTag = selfTag;
    }

    public Integer getUserThirdId() {
        return userThirdId;
    }

    public void setUserThirdId(Integer userThirdId) {
        this.userThirdId = userThirdId;
    }

    public Integer getUserThirdType() {
        return userThirdType;
    }

    public void setUserThirdType(Integer userThirdType) {
        this.userThirdType = userThirdType;
    }

    public boolean isAnonymous() {
        return anonymous;
    }

    public void setAnonymous(boolean anonymous) {
        this.anonymous = anonymous;
    }

    public int getScore() {
        return score;
    }

    public void setScore(int score) {
        this.score = score;
    }

    private int consumeStatus = 0;

    public int getConsumeStatus() {
        return consumeStatus;
    }

    public void setConsumeStatus(int consumeStatus) {
        this.consumeStatus = consumeStatus;
    }

    public String getPayUid() {
        return payUid;
    }

    public void setPayUid(String payUid) {
        this.payUid = payUid;
    }


    public String getInfoId() {
        return infoId;
    }

    public void setInfoId(String infoId) {
        this.infoId = infoId;
    }

    public Integer getCfAmount() {
        return cfAmount;
    }

    public void setCfAmount(Integer cfAmount) {
        this.cfAmount = cfAmount;
    }

    /**
     * 转化为元级别的数量
     * @return
     */
    public BigDecimal getAmountInYuan(){
        Preconditions.checkNotNull(this.getAmount());
        Preconditions.checkArgument(this.getAmount() >= 0);
        if(this.getAmount().intValue() == 0){
            return new BigDecimal(0);
        }
        return MoneyUtil.divide(String.valueOf(this.getAmount()), "100",2, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 转化为元级别的数量,保留小数点后两位，不包括元字样
     *
     * @return @example "11.11"
     */
    public String getAmountInYuanStr(){
        BigDecimal big = this.getAmountInYuan();
        return big.toPlainString();
    }

    /**
     * 转化为元级别的double
     * @return
     */
    public double getAmountInYuanDouble(){
        BigDecimal big = this.getAmountInYuan();
        return big.doubleValue();
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }

    public String getShardingUserIdTableNameSuffix(){
        return String.format("%03d", Math.abs(this.userId) % 100);
    }
    public String getShardingCrowdfundingIdTableNameSuffix(){
        return String.format("%03d", Math.abs(this.crowdfundingId) % 100);
    }
    public String getShardingIdTableNameSuffix(){
        return String.format("%03d", Math.abs(this.id) % 100);
    }


    @Data
    public static class CrowdfundingOrderIdHolder {
        private long id;
    }
}
