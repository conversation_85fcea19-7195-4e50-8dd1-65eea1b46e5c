package com.shuidihuzhu.cf.model.crowdfunding.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "好友捐款数据V1")
public class CfFriendLoopInfoMsgVo {

    @ApiModelProperty(value = "有无一度好友")
    private boolean onceFriendFlay;

    @ApiModelProperty(value = "一度好友的人数")
    private int onceFriend;

    @ApiModelProperty(value = "所有一度好友累计分享次数")
    private long sharedCount;

    @ApiModelProperty(value = "所有一度好友累计捐款次数")
    private int amountCount;

    private List<CfFriendLoopInfoVo> cfFriendLoopInfoVos;
}
