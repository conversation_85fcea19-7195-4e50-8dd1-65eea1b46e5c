package com.shuidihuzhu.cf.model.bigdata;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * create table cf_big_data.rpt_cf_case_features_full_h_dupl
 * (
 *     id                        bigint(20) unsigned not null auto_increment comment '主键',
 *     info_id                   bigint(20)          not null default '0' COMMENT '案例id',
 *     volunteer_code            varchar(100)        not null default '' comment '顾问编码',
 *     age_range                 varchar(100)        not null default '' COMMENT '患者年龄区间',
 *     patient_treatment_methods varchar(200)        not null default '' COMMENT '患者治疗方式',
 *     normalized_disease        varchar(200)        not null default '' COMMENT '疾病名称',
 *     is_big_space_donate       int(11)             not null default '0' COMMENT '是否大空间捐单,是：1，否：0',
 *     friend_donate_cnt         bigint(20)          not null default '0' COMMENT '好友捐单次数',
 *     donate_amt                decimal             not null default '0' COMMENT '总筹款金额,单位：元',
 *     donate_ant                bigint(20)          not null default '0' COMMENT '总捐款次数',
 *     friend_contributions      int(11)             not null default '0' COMMENT '好友贡献占比',
 *     share_cnt                 bigint(20)          not null default '0' COMMENT '总转发次数',
 *     ckr_share_cnt             bigint(20)          not null default '0' COMMENT '筹款人总转发次数',
 *     dt                        varchar(100)        not null default '' COMMENT '分区',
 *     create_time               timestamp(3)                 default CURRENT_TIMESTAMP(3) not null comment '创建时间',
 *     update_time               timestamp(3)                 default CURRENT_TIMESTAMP(3) not null on update CURRENT_TIMESTAMP(3) comment '修改时间',
 *     is_delete                 tinyint(4)                   default 0 not null comment '是否删除',
 *     PRIMARY KEY (`id`),
 *     KEY `idx_create_time` (`create_time`),
 *     KEY `idx_update_time` (`update_time`),
 *     KEY `idx_info_id_dt` (`info_id`, `dt`),
 *     KEY `idx_volunteer_code_is_big_space_donate_dt` (`volunteer_code`, `is_big_space_donate`, `dt`)
 * ) ENGINE = InnoDB
 *   DEFAULT CHARSET = UTF8MB4 COMMENT ='案例特征';
 * <AUTHOR>
 * @date 2024/12/26  21:40
 */
@Data
public class CfCaseFeaturesDo {
    @ApiModelProperty("案例id")
    private Long infoId;
    @ApiModelProperty("顾问code")
    private String volunteerCode;
    @ApiModelProperty("患者年龄区间")
    private String ageRange;
    @ApiModelProperty("患者治疗方式")
    private String patientTreatmentMethods;
    @ApiModelProperty("疾病名称")
    private String normalizedDisease;
    @ApiModelProperty("是否大空间捐单,是：1，否：0")
    private Integer isBigSpaceDonate;
    @ApiModelProperty("好友捐单次数")
    private Long friendDonateCnt;
    @ApiModelProperty("总筹款金额,单位：元")
    private BigDecimal donateAmt;
    @ApiModelProperty("总捐款次数")
    private Long donateAnt;
    @ApiModelProperty("好友贡献占比")
    private Integer friendContributions;
    @ApiModelProperty("总转发次数")
    private Long shareCnt;
    @ApiModelProperty("筹款人总转发次数")
    private Long ckrShareCnt;
    @ApiModelProperty("日期")
    private String dt;
}
