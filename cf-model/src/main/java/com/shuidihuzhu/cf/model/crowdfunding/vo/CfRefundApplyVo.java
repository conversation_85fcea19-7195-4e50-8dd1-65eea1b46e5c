package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.Splitter;
import com.shuidihuzhu.cf.finance.enums.CfRefundEnums;
import com.shuidihuzhu.cf.finance.model.vo.refund.CfRefundApplyV2Vo;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 退款申请Vo
 *
 * <AUTHOR>
 * @since 2022-08-19 3:13 下午
 **/
@Data
public class CfRefundApplyVo {

    private String infoUuid;

    /**
     * 原因选项code
     */
    private List<Integer> refundMsgCodes;

    /**
     * 原因说明
     */
    private String reason;

    /**
     * 申请时间
     */
    private Date applyTime;

    /**
     * 审核状态
     *
     * @see com.shuidihuzhu.cf.finance.enums.CfRefundEnums.ApplyStatus
     */
    private int applyStatus;

    /**
     * 申请人类型 0用户，1运营
     */
    private int applyUserType;

    /**
     * C端展示审核状态文案
     */
    private String applyStatusDesc;

    /**
     * 取消时间
     */
    private Date cancelTime;

    /**
     * 审核时间
     */
    private Date auditTime;
    /**
     * 是否可取消
     */
    @JsonProperty(value = "cancel")
    private boolean cancel;
    /**
     * 是否可重新申请
     */
    @JsonProperty(value = "reApply")
    private boolean reApply;
    /**
     * 审核备注
     */
    private String remark;

    public static CfRefundApplyVo build(CfRefundApplyV2Vo cfRefundApplyV2Vo) {
        CfRefundApplyVo vo = new CfRefundApplyVo();
        if (Objects.isNull(cfRefundApplyV2Vo)) {
            return vo;
        }
        vo.setInfoUuid(cfRefundApplyV2Vo.getInfoUuid());
        String refundMsgCodes = cfRefundApplyV2Vo.getRefundMsgCodes();
        List<Integer> refundCodes = Splitter.on(",").splitToList(refundMsgCodes).stream().map(Integer::valueOf).distinct().collect(Collectors.toList());
        vo.setRefundMsgCodes(refundCodes);
        vo.setReason(cfRefundApplyV2Vo.getApplyReason());
        vo.setApplyTime(cfRefundApplyV2Vo.getApplyTime());
        vo.setApplyStatus(cfRefundApplyV2Vo.getApplyStatus());
        vo.setRemark(cfRefundApplyV2Vo.getRemark());
        CfRefundEnums.ApplyStatus applyStatus = CfRefundEnums.ApplyStatus.getEnumByCode(cfRefundApplyV2Vo.getApplyStatus());
        String applyStatusDesc =
                applyStatus == CfRefundEnums.ApplyStatus.SUBMIT_APPROVE_PENDING || applyStatus == CfRefundEnums.ApplyStatus.WAIT_FOLLOW ? "审核中" :
                        applyStatus == CfRefundEnums.ApplyStatus.APPROVE_SUCCESS ? "已通过" :
                                applyStatus == CfRefundEnums.ApplyStatus.APPROVE_REJECT || applyStatus == CfRefundEnums.ApplyStatus.CANCEL ? "已取消" : StringUtils.EMPTY;
        vo.setApplyStatusDesc(applyStatusDesc);
        vo.setApplyUserType(cfRefundApplyV2Vo.getApplyUserType());
        vo.setCancelTime(cfRefundApplyV2Vo.getCancelTime());
        vo.setAuditTime(cfRefundApplyV2Vo.getApplyAuditTime());
        boolean isSeaUserApply = vo.getApplyUserType() == 1;
        // sea后台运营人员申请不能做任何操作
        if (!isSeaUserApply) {
            // 可取消申请
            vo.setCancel(applyStatus == CfRefundEnums.ApplyStatus.SUBMIT_APPROVE_PENDING || applyStatus == CfRefundEnums.ApplyStatus.WAIT_FOLLOW);
            // 可重新申请
            vo.setReApply(applyStatus == CfRefundEnums.ApplyStatus.CANCEL || applyStatus == CfRefundEnums.ApplyStatus.APPROVE_REJECT);
        }
        return vo;
    }
}
