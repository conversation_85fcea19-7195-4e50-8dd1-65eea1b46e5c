package com.shuidihuzhu.cf.model.crowdfunding;

import java.util.Date;

public class CfDailyCharityCfInfo {
    private Integer id;

    private Integer caseId;

    private Date dt;

    private Integer orderNum;

    private Date createTime;

    private Date lastModified;

    private Integer caseType;

    private Boolean valid;
    private String tag;

    public CfDailyCharityCfInfo(Integer id, Integer caseId, Date dt, Integer orderNum, Date createTime, Date lastModified, Integer caseType, Boolean valid,String tag) {
        this.id = id;
        this.caseId = caseId;
        this.dt = dt;
        this.orderNum = orderNum;
        this.createTime = createTime;
        this.lastModified = lastModified;
        this.caseType = caseType;
        this.valid = valid;
        this.tag = tag;
    }

    public CfDailyCharityCfInfo() {
        super();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getCaseId() {
        return caseId;
    }

    public void setCaseId(Integer caseId) {
        this.caseId = caseId;
    }

    public Date getDt() {
        return dt;
    }

    public void setDt(Date dt) {
        this.dt = dt;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getLastModified() {
        return lastModified;
    }

    public void setLastModified(Date lastModified) {
        this.lastModified = lastModified;
    }

    public Integer getCaseType() {
        return caseType;
    }

    public void setCaseType(Integer caseType) {
        this.caseType = caseType;
    }

    public Boolean getValid() {
        return valid;
    }

    public void setValid(Boolean valid) {
        this.valid = valid;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }
}
