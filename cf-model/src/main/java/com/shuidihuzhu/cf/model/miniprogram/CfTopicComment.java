package com.shuidihuzhu.cf.model.miniprogram;

import java.sql.Timestamp;

public class CfTopicComment {
    private long id;
    private int topicId;
    private long userId;
    private String nickname;
    private String headUrl;
    private String content;
    private int isSensitive;
    private Timestamp createTime;
    private Timestamp updateTime;
    //被回复的评论id
    private long parentId;
    //按照评论的分组id
    private long commentGroupId;
    private int isDelete;

    public CfTopicComment() {
    }

    public CfTopicComment(int topicId,long userId, String nickname, String headUrl,
                          String content, int isSensitive, long parentId, long commentGroupId) {
        this.topicId = topicId;
        this.userId = userId;
        this.nickname = nickname;
        this.headUrl = headUrl;
        this.content = content;
        this.isSensitive = isSensitive;
        this.parentId = parentId;
        this.commentGroupId = commentGroupId;
    }

    public int getIsSensitive() {
        return isSensitive;
    }

    public void setIsSensitive(int isSensitive) {
        this.isSensitive = isSensitive;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public int getTopicId() {
        return topicId;
    }

    public void setTopicId(int topicId) {
        this.topicId = topicId;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getHeadUrl() {
        return headUrl;
    }

    public void setHeadUrl(String headUrl) {
        this.headUrl = headUrl;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public Timestamp getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }

    public int getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(int isDelete) {
        this.isDelete = isDelete;
    }

    public long getParentId() {
        return parentId;
    }

    public void setParentId(long parentId) {
        this.parentId = parentId;
    }

    public long getCommentGroupId() {
        return commentGroupId;
    }

    public void setCommentGroupId(long commentGroupId) {
        this.commentGroupId = commentGroupId;
    }

    @Override
    public String toString() {
        return "CfTopicComment{" +
                "id=" + id +
                ", topicId=" + topicId +
                ", userId=" + userId +
                ", nickname='" + nickname + '\'' +
                ", headUrl='" + headUrl + '\'' +
                ", content='" + content + '\'' +
                ", isSensitive=" + isSensitive +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", parentId=" + parentId +
                ", isDelete=" + isDelete +
                '}';
    }
}
