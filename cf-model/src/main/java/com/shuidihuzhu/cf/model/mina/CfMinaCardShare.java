package com.shuidihuzhu.cf.model.mina;

/**
 * Created by dongcf on 2017/12/18
 */
public class CfMinaCardShare {

    private Integer id;

    private String shareKey;

    private long userId;

    private Integer minaCardId;

    private Integer minaGreetingId;

    private String customGreeting;

    private Integer targetType;

    private Integer shareNumber;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getShareKey() {
        return shareKey;
    }

    public void setShareKey(String shareKey) {
        this.shareKey = shareKey;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public Integer getMinaCardId() {
        return minaCardId;
    }

    public void setMinaCardId(Integer minaCardId) {
        this.minaCardId = minaCardId;
    }

    public Integer getMinaGreetingId() {
        return minaGreetingId;
    }

    public void setMinaGreetingId(Integer minaGreetingId) {
        this.minaGreetingId = minaGreetingId;
    }

    public String getCustomGreeting() {
        return customGreeting;
    }

    public void setCustomGreeting(String customGreeting) {
        this.customGreeting = customGreeting;
    }

    public Integer getTargetType() {
        return targetType;
    }

    public void setTargetType(Integer targetType) {
        this.targetType = targetType;
    }

    public Integer getShareNumber() {
        return shareNumber;
    }

    public void setShareNumber(Integer shareNumber) {
        this.shareNumber = shareNumber;
    }
}
