package com.shuidihuzhu.cf.model.miniprogram;

public class CfTopic {
    private int id;
    private String title;
    private String description;
    private String icon;
    private int isDelete;
    private int phaseId;
    private String imgUrls;

    public CfTopic() {
    }

    public CfTopic(String title, String description, String icon, int phaseId) {
        this.title = title;
        this.description = description;
        this.icon = icon;
        this.phaseId = phaseId;
    }

    public int getPhaseId() {
        return phaseId;
    }

    public void setPhaseId(int phaseId) {
        this.phaseId = phaseId;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public int getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(int isDelete) {
        this.isDelete = isDelete;
    }

    public String getImgUrls() {
        return imgUrls;
    }

    public void setImgUrls(String imgUrls) {
        this.imgUrls = imgUrls;
    }

    @Override
    public String toString() {
        return "CfTopic{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", description='" + description + '\'' +
                ", icon='" + icon + '\'' +
                ", isDelete=" + isDelete +
                ", phaseId=" + phaseId +
                ", imgUrls='" + imgUrls + '\'' +
                '}';
    }
}
