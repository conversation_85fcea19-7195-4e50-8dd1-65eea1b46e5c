package com.shuidihuzhu.cf.model.crowdfunding;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.shuidihuzhu.cf.model.UserAccountView;
import com.shuidihuzhu.common.web.model.AbstractModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * Created by roy on 16/7/18.
 */
@Data
@ApiModel("评论模型")
public class CrowdfundingCommentView {

    @Deprecated
    @ApiModelProperty("回复Id，后续不要再使用")
    private Long id;

    @JsonIgnore
    @ApiModelProperty("不要用")
    private Integer crowdfundingId;

    @ApiModelProperty("评论id")
    private String content;

    private Date createTime;
    /**
     * @see {@link com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingCommentType}
     */
    @ApiModelProperty("评论类型,参见CrowdfundingCommentType")
    private Integer type;

    @ApiModelProperty("该评论的父节点id")
    private Long parentId;

    /**
     * @see {@link com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingType}
     */
    @ApiModelProperty("筹款类型")
    private Integer infoType;

    @ApiModelProperty("案例id")
    private String infoUuid;

    @ApiModelProperty("回复")
    private CrowdfundingCommentView comment;

    private UserAccountView user;
    private UserAccountView commentedUser;

    private String targetId;
    private String targetCommentId;

    public void resetReplyId(CrowdfundingCommentView reply){
        if (reply == null){
            return;
        }
        reply.setId(-1L);
        if (reply.getComment() == null){
            return;
        }
        resetReplyId(reply.getComment());
    }
}
