package com.shuidihuzhu.cf.model.miniprogram;

public class CfTopicShare {
    private long id;
    private int topicId;
    private long userId;
    private String uuid;
    private int userSourceId;
    private String source;
    private int wxSource;
    private int toWxSource;
    private int isDelete;

    public CfTopicShare() {
    }

    public CfTopicShare(int topicId,long userId, String uuid, int userSourceId, String source, int wxSource, int toWxSource) {
        this.topicId = topicId;
        this.userId = userId;
        this.uuid = uuid;
        this.userSourceId = userSourceId;
        this.source = source;
        this.wxSource = wxSource;
        this.toWxSource = toWxSource;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public int getTopicId() {
        return topicId;
    }

    public void setTopicId(int topicId) {
        this.topicId = topicId;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public int getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(int isDelete) {
        this.isDelete = isDelete;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public int getUserSourceId() {
        return userSourceId;
    }

    public void setUserSourceId(int userSourceId) {
        this.userSourceId = userSourceId;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public int getWxSource() {
        return wxSource;
    }

    public void setWxSource(int wxSource) {
        this.wxSource = wxSource;
    }

    public int getToWxSource() {
        return toWxSource;
    }

    public void setToWxSource(int toWxSource) {
        this.toWxSource = toWxSource;
    }

    @Override
    public String toString() {
        return "CfTopicShare{" +
                "id=" + id +
                ", topicId=" + topicId +
                ", userId=" + userId +
                ", isDelete=" + isDelete +
                ", uuid='" + uuid + '\'' +
                ", userSourceId=" + userSourceId +
                ", source='" + source + '\'' +
                ", wxSource=" + wxSource +
                ", toWxSource=" + toWxSource +
                '}';
    }
}
