package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.shuidihuzhu.cf.finance.model.vo.drawcash.CfPublicMessageVo;
import com.shuidihuzhu.cf.finance.model.vo.drawcash.CfServiceChargeInfoVo;
import com.shuidihuzhu.cf.model.crowdfunding.CfCaseVerifyInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 结清案例新案例详情页
 *
 * <AUTHOR>
 * @since 2022-11-29 11:18 上午
 **/
@Data
@ApiModel("结清案例新案例详情页")
public class CfSettleFundingInfoVo {

    private SettleUser user;

    private SettleFundingBaseInfo baseInfo;

    private CfPublicMessageVo publicMessage;

    private CfCaseVerifyInfo verifyInfo;

    private CfServiceChargeInfoVo serviceInfo;



    @Data
    @ApiModel("结清案例筹款人信息")
    public static class SettleUser {

        @ApiModelProperty("头像")
        private String headImgUrl;

        @ApiModelProperty("昵称")
        private String nickname;
    }


    @Data
    @ApiModel("结清案例筹款人信息")
    public static class SettleFundingBaseInfo {

        @ApiModelProperty("筹款标签")
        private List<String> label;

        @ApiModelProperty("案例标题")
        private String title;

        @ApiModelProperty("已筹金额")
        private int amount;

        @ApiModelProperty("目标金额")
        private int targetAmount;

        @ApiModelProperty("已提现金额(包含手续费)")
        private int alreadyDrawCashAmount;

        @ApiModelProperty("实际到账金额")
        private int drawAmountWithoutFee;

        @ApiModelProperty("提现中金额(包含手续费)")
        private int drawCashingAmountWithFee;

        @ApiModelProperty("提现手续费（扣除返还）")
        private int feeAmount;

        @ApiModelProperty("实收提现服务费（扣除返还）")
        private int serviceAmount;

        @ApiModelProperty("应收服务费（扣除返还）")
        private int originalServiceAmount;

        @ApiModelProperty("免除服务费")
        private int removeServiceAmount;

        @ApiModelProperty("未提现金额")
        private int surplusAmount;

        @ApiModelProperty("退款金额")
        private int refundAmount;

        @ApiModelProperty("提现完成")
        private int drawReceivedAmountWithFee;

    }
}
