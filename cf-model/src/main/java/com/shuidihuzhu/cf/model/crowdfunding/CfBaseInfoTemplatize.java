package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.sql.Timestamp;
import java.util.List;

/**
 * Created by Ahrievil on 2017/12/8
 */
@NoArgsConstructor
@Data
@ToString
public class CfBaseInfoTemplatize {

    private int id;

    /**
     * 筹款关系
     */
    private int relationType;

    /**
     * 文案类型
     * @see com.shuidihuzhu.cf.enums.crowdfunding.BaseInfoTemplateConst.ContentType
     */
    private int contentType;

    /**
     * 文案内容
     */
    private String content;

    /**
     * short For firstPerson (=1表示为第一人称，否则暂时为第三人称)
     */
    private int fp;

    /**
     * 文案使用场景
     * 1：发起案例文案
     * 2：转发文案
     */
    private int channelType;

    /**
     * 文案生效的最小岁数
     */
    private int minAge = 0;

    /**
     * 文案生效的最大岁数
     */
    private int maxAge = 10000;

    private Timestamp createTime;

    private Timestamp updateTime;

    private int isDelete;

    /**
     * 使用位置
     * 0: C端,
     * 1: 1V1服务
     */
    private List<Integer> useScenes;

    private long operatorId;

    private String operatorName;

    /**
     * 适用疾病
     */
    private String applicableDiseases = "";


    @Data
    @NoArgsConstructor
    public static class TemplateUseSceneMapping{
        int templateId;
        int useScene;

        public TemplateUseSceneMapping(int templateId, int useScene) {
            this.templateId = templateId;
            this.useScene = useScene;
        }
    }

    @Getter
    public enum UseScene {
        FOR_C(0, "C端用户的智能发起"),
        FOR_1V1(1, "1v1服务"),
        ;

        UseScene(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        private int code;
        private String desc;
    }

}
