package com.shuidihuzhu.cf.model.dedicated;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: wanghui
 * @create: 2019/1/3 8:20 PM
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CustomerModel {
    private String headUrl; // 头像url
    private String customerName;  // 客服名称
    private String qrCode;  // 二维码
    private List<String> labelList;  //  标签
    private Integer helpPatients;  // 服务患者数
    private Double raiseAmount;  // 筹到的金额  单位万元
    private String raiseAmountText;//筹到的金额，文本展示
    private String userId;  // 客服userId
    private Long CurrentDayAddfriendAmount;  //  当天加好友数量
    private String favorableRate;//好评率
}
