package com.shuidihuzhu.cf.model.crowdfunding.material;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.vo.v5.MaterialModifySuggestType;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
public class CfRefuseSuggestModify {

    private int uniqueId;
    private int dataType;
    private String content;

    public static CfRefuseSuggestModify buildFromSuggestType(MaterialModifySuggestType suggestType) {
        if (suggestType == null) {
            return null;
        }

        CfRefuseSuggestModify modify = new CfRefuseSuggestModify();
        modify.setUniqueId(suggestType.getCode());
        modify.setContent(suggestType.getText());
        modify.setDataType(suggestType.getDataType());
        return modify;
    }

    public static void main(String[] args) {
        List<CfRefuseSuggestModify> modifyList = Lists.newArrayList();
        CfRefuseSuggestModify m1 = new CfRefuseSuggestModify();
        m1.setContent("修改建议1");
        m1.setDataType(3);

        CfRefuseSuggestModify m2 = new CfRefuseSuggestModify();
        m2.setContent("修改建议2");
        m2.setDataType(3);

        modifyList.add(m1);
        modifyList.add(m2);

//        System.out.println(JSON.toJSONString(NewResponseUtil.makeSuccess(modifyList)));
//        System.out.println(generateInsertSql());
//        Set<Integer> entityIds = Sets.newHashSet(134, 158, 179);
//        Set<Integer> uniqueIds = Sets.newHashSet(1, 2, 3, 4);
//        AuditSuggestModifyParam param = new AuditSuggestModifyParam();
//        param.setEntityIds(entityIds);
//        param.setUniqueIds(uniqueIds);
////        System.out.println(JSON.toJSONString(param));
//        AuditSuggestModifyDetail detail = new AuditSuggestModifyDetail();
//        detail.setSuggestUserModify("建议用户修改：住院证明、出院证明、诊断证明");
//        Map<Integer, Set<Integer>> rejectTModify = Maps.newHashMap();
//        rejectTModify.put(134, Sets.newHashSet(1, 2, 3));
//        rejectTModify.put(158, Sets.newHashSet(4, 2, 3));
//        detail.setRejectTModify(rejectTModify);
//
//        Map<Integer, CfRefuseSuggestModify> modifyIdTContent = Maps.newHashMap();
//        modifyIdTContent.put(1, m1);
//        modifyIdTContent.put(2, m2);
//        detail.setModifyIdTContent(modifyIdTContent);

//        System.out.println(JSON.toJSONString(NewResponseUtil.makeSuccess(detail)));
//        System.out.println(JSON.toJSONString(NewResponseUtil.makeSuccess(0)));
        System.out.println(generateInsertSql());
    }

    // CrowdfundingInfoDataStatusTypeEnum
    private static  String generateInsertSql() {
        String insertSql = "INSERT INTO shuidi_crowdfunding.cf_refuse_suggest_modify (`data_type`,  `content`) values \n";

        String sqlFormat = "(%d,  \"%s\"), \n";
        List<String> payeeModifys = Lists.newArrayList(
                "患者本人收款", "配偶与近亲属收款", "医院账户收款",
                "授权委托书", "村委会/居委会证明", "出生证", "结婚证", "上传户口本视频", "户口本照片",
                "身份验证"
                );

        List<String> treatmentModifys = Lists.newArrayList("住院证明", "出院证明", "诊断证明", "收费票据");

        for (String str : payeeModifys) {
            insertSql += String.format(sqlFormat, 3,    str);
        }

        for (String str : treatmentModifys) {
            insertSql += String.format(sqlFormat, 4,   str);
        }

        return insertSql.substring(0, insertSql.length() - 3) + "; \n";
    }


    public static String generateRejectPostionSql() {
        String addRejectSql = "INSERT INTO shuidi_crowdfunding.cf_refuse_reason_item (`content`, `content_for_user`, `type`, `group_rank`, `pro_type`) values \n";


        List<String> payeeReject = Lists.newArrayList(
                "收款人是患者的",
                "身份证正面照片");
        List<String> fundUse = Lists.newArrayList("未来花费金额");

        String sqlFormat = "(\"%s\", \"%s\", %d, %d, 0),\n";
        int payeeRank = 14;

        for (String reject : payeeReject) {
            addRejectSql += String.format(sqlFormat, reject, reject, 3, ++payeeRank);
        }

        int useRank = 3;
        for (String reject : fundUse) {
            addRejectSql += String.format(sqlFormat, reject, reject, 7, ++useRank);
        }

        return addRejectSql.substring(0, addRejectSql.length()-2) +  "; \n";
    }
}
