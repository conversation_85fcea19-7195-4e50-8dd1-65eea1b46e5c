package com.shuidihuzhu.cf.model.crowdfunding;

import java.sql.Timestamp;

/**
 *
 *  筹款汇总信息（暂时包含，总金额、总次数)
 *
 *  Created by wangsf on 17/1/16.
 */
public class CrowdfundingSummary {
    /**
     * 自增ID
     */
    private int id;

    /**
     * 筹款类型，参见 @link{CrowdfundingType}
     */
    private int type;
    /**
     * 筹款总金额
     */
    private long totalAmount;
    /**
     * 捐款款总次数
     */
    private int totalCount;
    /**
     * 汇总时间
     */
    private Timestamp summaryTime;
    /**
     * 创建时间
     */
    private Timestamp createTime;
    /**
     * 更新时间
     */
    private Timestamp updateTime;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public long getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(long totalAmount) {
        this.totalAmount = totalAmount;
    }

    public int getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(int totalCount) {
        this.totalCount = totalCount;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public Timestamp getUpdateTime() {
        return updateTime;
    }

    public Timestamp getSummaryTime() {
        return summaryTime;
    }

    public void setSummaryTime(Timestamp summaryTime) {
        this.summaryTime = summaryTime;
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "CrowdfundingSummary{" +
                "id=" + id +
                ", type=" + type +
                ", totalAmount=" + totalAmount +
                ", totalCount=" + totalCount +
                ", summaryTime=" + summaryTime +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
