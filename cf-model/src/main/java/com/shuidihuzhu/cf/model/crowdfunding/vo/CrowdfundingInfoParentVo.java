package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;

import java.util.Date;

public class CrowdfundingInfoParentVo {

	private String infoUuid;
	private int status;
	private int dataStatus;
	private boolean hasFinished;
	private int displayStatus;
	private boolean isSelf;
	
	public void buildParentParam(CrowdfundingInfo crowdfundingInfo) {
		this.setInfoUuid(crowdfundingInfo.getInfoId());
		this.setStatus(crowdfundingInfo.getStatus().value());
		Date endTime = crowdfundingInfo.getEndTime();
		if(endTime != null && endTime.after(new Date())){
			this.setHasFinished(false);
		} else {
			this.setHasFinished(true);
		}
	}

	public String getInfoUuid() {
		return infoUuid;
	}

	public void setInfoUuid(String infoUuid) {
		this.infoUuid = infoUuid;
	}

	public int getStatus() {
		return status;
	}

	public void setStatus(int status) {
		this.status = status;
	}

	public boolean isHasFinished() {
		return hasFinished;
	}

	public void setHasFinished(boolean hasFinished) {
		this.hasFinished = hasFinished;
	}

	public int getDataStatus() {
		return dataStatus;
	}

	public void setDataStatus(int dataStatus) {
		this.dataStatus = dataStatus;
	}

	public int getDisplayStatus() {
		return displayStatus;
	}

	public void setDisplayStatus(int displayStatus) {
		this.displayStatus = displayStatus;
	}

	public boolean isSelf() {
		return isSelf;
	}

	public void setSelf(boolean self) {
		isSelf = self;
	}
}
