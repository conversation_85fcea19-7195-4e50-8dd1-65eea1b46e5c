package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.ReportSourceEnum;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.sql.Timestamp;
import java.util.Date;

/**
 * Created by roy on 16/7/29.
 */
@Data
public class CrowdfundingReport {
    /**
     * CREATE TABLE `shuidi_dev`.`crowdfunding_report`(
     * `id` INT(11) NOT NULL AUTO_INCREMENT comment 'id',
     * `user_id` INT(11) DEFAULT null comment '用户id',
     * `activity_id` int(11) NOT NULL COMMENT '众筹id',
     * `image_urls` TEXT DEFAULT null COMMENT '图片地址',
     * `content` TEXT comment '举报内容',
     * `contact` TEXT NOT NULL COMMENT '联系方式',
     * `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ,
     * `is_newreport` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为新增举报',
     * `case_follow_status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '举报所属案例的跟进状态 0 未跟进  1 跟进中  2  案例跟进完成',
     * <p>
     * PRIMARY KEY (`id`),
     * KEY `idx_user_id`(`user_id`),
     * KEY `idx_activity_id`(`activity_id`)
     * ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 comment='众筹举报表';
     */
    private Integer id;
    private Integer activityId;
    private String content;
    // 加密手机号（新）
    private String encryptContact;
    private String imageUrls;
    private long userId;
    private Date createTime;
    private int operatorId;
    private int dealStatus;
    private int isNewreport;
    private int caseFollowStatus;
    private Timestamp lastModified;
    //0:默认值(老数据都是0) 1:实名举报 2:非实名举报
    private int realNameReport;
    private String name;
    private String identity;
    //处理状态 0:默认(历史数据) 1:未处理 2:已处理(举报重构增加的新字段)
    private int handleStatus;
    //接通状态 0:默认(历史数据) 1:未接通 2:已接通(举报重构增加的新字段)
    private int connectStatus;
    //举报新逻辑的操作人
    private int newOperatorId;
    /**
     * 是否命中黑名单
     */
    private boolean hitBlackList;
    /**
     * 举报渠道
     */
    private int reportChannel;
    /**
     * 其他举报渠道
     */
    private String reportChannelOther;
    /**
     * 风险标签
     */
    private String riskLabel;

    /**
     * 参加ReportSourceEnum, 默认值为通过老环境发起
     *
     * @see {@link com.shuidihuzhu.cf.enums.crowdfunding.ReportSourceEnum}
     */
    private int reportSource = ReportSourceEnum.ORIGIN.getSource();

    public CrowdfundingReport() {
    }

	public CrowdfundingReport(Integer crowdfundingId, String content, String encryptContact, String imageUrls,
	                         long userId) {
		this.activityId = crowdfundingId;
		this.content = content;
        this.encryptContact = encryptContact;
        this.imageUrls = imageUrls;
        this.userId = userId;
    }

//    public CrowdfundingReport(Integer crowdfundingId, String content, String contact, String imageUrls,
//                              long userId, boolean realNameReport,
//                              String name, String identity, int handleStatus, int connectStatus) {
//        this.activityId = crowdfundingId;
//        this.content = content;
//        this.contact = contact;
//        this.imageUrls = imageUrls;
//        this.userId = userId;
//        this.realNameReport = realNameReport ? BooleanEnum.TRUE_FLAG.getValue() : BooleanEnum.FALSE_FLAG.getValue();
//        this.name = realNameReport ? name : "";
//        this.identity = realNameReport ? oldShuidiCipher.aesEncrypt(identity) : "";
//        this.handleStatus = handleStatus;
//        this.connectStatus = connectStatus;
//    }

    public CrowdfundingReport(Integer crowdfundingId, String content, String encryptContact, String imageUrls,
                              long userId, ReportSourceEnum reportSourceEnum) {
        this.activityId = crowdfundingId;
        this.content = content;
        this.encryptContact = encryptContact;
        this.imageUrls = imageUrls;
        this.userId = userId;
        this.reportSource = reportSourceEnum.getSource();
    }

    public int getIsNewreport() {
        return isNewreport;
    }

    public void setIsNewreport(int isNewreport) {
        this.isNewreport = isNewreport;
    }

    public int getCaseFollowStatus() {
        return caseFollowStatus;
    }

    public void setCaseFollowStatus(int caseFollowStatus) {
        this.caseFollowStatus = caseFollowStatus;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getActivityId() {
        return activityId;
    }

    public void setActivityId(Integer activityId) {
        this.activityId = activityId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getEncryptContact() {
        return encryptContact;
    }

    public void setEncryptContact(String encryptContact) {
        this.encryptContact = encryptContact;
    }

    public String getImageUrls() {
        return imageUrls;
    }

    public void setImageUrls(String imageUrls) {
        this.imageUrls = imageUrls;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public int getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(int operatorId) {
        this.operatorId = operatorId;
    }

    public int getDealStatus() {
        return dealStatus;
    }

    public void setDealStatus(int dealStatus) {
        this.dealStatus = dealStatus;
    }

    public Timestamp getLastModified() {
        return lastModified;
    }

    public void setLastModified(Timestamp lastModified) {
        this.lastModified = lastModified;
    }

    public int getRealNameReport() {
        return realNameReport;
    }

    public void setRealNameReport(int realNameReport) {
        this.realNameReport = realNameReport;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIdentity() {
        return identity;
    }

    public void setIdentity(String identity) {
        this.identity = identity;
    }

    public int getHandleStatus() {
        return handleStatus;
    }

    public void setHandleStatus(int handleStatus) {
        this.handleStatus = handleStatus;
    }

    public int getConnectStatus() {
        return connectStatus;
    }

    public void setConnectStatus(int connectStatus) {
        this.connectStatus = connectStatus;
    }

    public int getNewOperatorId() {
        return newOperatorId;
    }

    public void setNewOperatorId(int newOperatorId) {
        this.newOperatorId = newOperatorId;
    }

    public boolean isHitBlackList() {
        return hitBlackList;
    }

    public void setHitBlackList(boolean hitBlackList) {
        this.hitBlackList = hitBlackList;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }

    public int getReportChannel() {
        return reportChannel;
    }

    public void setReportChannel(int reportChannel) {
        this.reportChannel = reportChannel;
    }

    public String getReportChannelOther() {
        return reportChannelOther;
    }

    public void setReportChannelOther(String reportChannelOther) {
        this.reportChannelOther = reportChannelOther;
    }

    public String getRiskLabel() {
        return riskLabel;
    }

    public void setRiskLabel(String riskLabel) {
        this.riskLabel = riskLabel;
    }
}
