package com.shuidihuzhu.cf.model.deposit;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;

/**
 * @Author: lianghongchao
 * @Date: 2019/12/28 21:58
 * <p>
 * C端 收款人填写日志记录信息
 */
@Data
public class CfPayeeParamRecord {
    private long id;
    private int caseId;
    private String infoUuid = StringUtils.EMPTY;
    private String name = StringUtils.EMPTY;
    private String encryptIdCard = StringUtils.EMPTY;
    private String encryptBankCardNo = StringUtils.EMPTY;
    private String encryptReservedMobile = StringUtils.EMPTY;
    private String bankName = StringUtils.EMPTY;
    private String bankBranchName = StringUtils.EMPTY;
    private String cnapsBranchId = StringUtils.EMPTY;
    private String sourceChannel = StringUtils.EMPTY;
    private String ip = StringUtils.EMPTY;
    private int isDelete;
    private Timestamp createTime;
    private Timestamp updateTime;

    public CfPayeeParamRecord() {

    }
}
