package com.shuidihuzhu.cf.model.dedicated;

import lombok.Data;

import java.util.List;

@Data
public class CfVolunteerCheckModel {
    /**
     * 基本材料填写状态
     * (0:表示未提交 1:已提交)
     */
    private Integer baseInfoStatus;
    /**
     * 个人认证材料填写状态
     * (0:表示未提交 1:已提交)
     */
    private Integer personalVerifyStatus;
    /**
     * 0:表示未提交
     * 1:已提交待审核
     * 2:审核通过
     * 3:审核拒绝
     */
    private Integer applyStatus;
    /**
     * 在职状态
     */
    private Integer workStatus;
    /**
     * true:表示已绑定，false:表示未绑定
     */
    private Boolean phoneBinded;
    /**
     * 驳回原因
     */
    private List<String> refuseReasonList;
    /**
     * 志愿者类型
     */
    private Integer volunteerType;
    /**
     * 志愿者唯一码
     */
    private String uniqueCode;
    /**
     * 材料
     */
    private CfVolunteerMaterialModel volunteerMaterials;
}
