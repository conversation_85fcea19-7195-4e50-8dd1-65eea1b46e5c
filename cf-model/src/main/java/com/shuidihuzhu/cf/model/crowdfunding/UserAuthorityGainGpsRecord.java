package com.shuidihuzhu.cf.model.crowdfunding;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: wangpeng
 * @Date: 2020/12/31 19:12
 * @Description:
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserAuthorityGainGpsRecord implements Serializable {

    private static final long serialVersionUID = 1300217624508111859L;
    /**
     * 主键Id
     */
    private long id;
    /**
     * 用户Id
     */
    private long userId;
    /**
     * 是否授权
     */
    private boolean authorityGain;
    /**
     * 确定授权时间
     */
    private Date authorityTime;
}
