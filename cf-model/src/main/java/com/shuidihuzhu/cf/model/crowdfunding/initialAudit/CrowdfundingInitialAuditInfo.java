package com.shuidihuzhu.cf.model.crowdfunding.initialAudit;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;

import java.util.Date;

@Data
@NoArgsConstructor
public class CrowdfundingInitialAuditInfo {
    private long id;
    private int caseId;
    private int baseInfo;
    private int firstApproveInfo;
    private int creditInfo;
    private String rejectDetail;
    private long workOrderId;
    private Date createTime;
    private Date updateTime;



    public CrowdfundingInitialAuditInfo buildCaseId(int caseId) {
        this.caseId = caseId;
        return this;
    }

    public CrowdfundingInitialAuditInfo buildBaseInfo(int baseInfo) {
        this.baseInfo = baseInfo;
        return this;
    }

    public CrowdfundingInitialAuditInfo buildFirstApproveInfo(int firstApproveInfo) {
        this.firstApproveInfo = firstApproveInfo;
        return this;
    }

    public CrowdfundingInitialAuditInfo buildCreditInfo(int creditInfo) {
        this.creditInfo = creditInfo;
        return this;
    }

    public CrowdfundingInitialAuditInfo buildRejectDetail(String rejectDetail) {
        this.rejectDetail = StringUtils.isBlank(rejectDetail) ? JSON.toJSONString(Maps.newHashMap())
                : rejectDetail;
        return this;
    }

    public CrowdfundingInitialAuditInfo buildWorkOrderId(long workOrderId) {
        this.workOrderId = workOrderId;
        return this;
    }

}
