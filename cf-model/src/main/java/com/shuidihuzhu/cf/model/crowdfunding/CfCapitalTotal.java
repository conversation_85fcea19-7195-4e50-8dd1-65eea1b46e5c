package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.Data;

import java.sql.Timestamp;

/**
 * @author: lixuan
 * @date: 2018/3/21 12:00
 */
@Data
public class CfCapitalTotal {

    private long id;
    private String date;
    private String infoUuid;
    private int payNum;
    private long payAmount;
    private int singleRefundNum;
    private long singleRefundAmount;
    private int allRefundNum;
    private long allRefundAmount;
    private long balance;
    private int isDelete;
    private Timestamp createTime;
    private Timestamp updateTime;

    public long getDonationAmountInFen() {
        return this.payAmount - this.singleRefundAmount;
    }

    public CfCapitalTotal buildByInfo(String date, String infoUuid) {
        this.infoUuid = infoUuid;
        this.date = date;
        return this;
    }

    public CfCapitalTotal buildByPay(int num, long amount) {
        this.payNum = num;
        this.payAmount = amount;
        return this;
    }

    public CfCapitalTotal buildBySingleRefund(int num, long amount) {
        this.singleRefundNum = num;
        this.singleRefundAmount = amount;
        return this;
    }

    public CfCapitalTotal buildByAllRefund(int num, long amount) {
        this.allRefundNum = num;
        this.allRefundAmount = amount;
        return this;
    }

}
