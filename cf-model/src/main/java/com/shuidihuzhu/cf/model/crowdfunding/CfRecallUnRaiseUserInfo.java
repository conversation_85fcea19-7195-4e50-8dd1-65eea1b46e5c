package com.shuidihuzhu.cf.model.crowdfunding;

import java.sql.Timestamp;

/**
 * Created by Ahrievil on 2017/10/13
 */
public class CfRecallUnRaiseUserInfo {

    private long id;
    private long userId;
    private String openId;
    private Timestamp subscribeEventTime;
    private int isSend;
    private Timestamp createTime;
    private Timestamp updateTime;
    private int isDelete;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public Timestamp getSubscribeEventTime() {
        return subscribeEventTime;
    }

    public void setSubscribeEventTime(Timestamp subscribeEventTime) {
        this.subscribeEventTime = subscribeEventTime;
    }

    public int getIsSend() {
        return isSend;
    }

    public void setIsSend(int isSend) {
        this.isSend = isSend;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public Timestamp getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }

    public int getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(int isDelete) {
        this.isDelete = isDelete;
    }
}
