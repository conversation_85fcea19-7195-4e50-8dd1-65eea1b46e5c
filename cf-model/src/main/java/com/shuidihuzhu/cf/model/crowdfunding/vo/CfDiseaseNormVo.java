package com.shuidihuzhu.cf.model.crowdfunding.vo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * @Author：liuchangjun
 * @Date：2021/8/25
 */
@ApiModel("归一化疾病信息")
@Data
public class CfDiseaseNormVo {

    @ApiModelProperty("有科普知识的归一化标签")
    private Map<String,Integer> userfulDiseaseNorm;

    @ApiModelProperty("用户填写疾病和归一化疾病对应关系")
    private Map<String,String> diseaseNormShip;
}
