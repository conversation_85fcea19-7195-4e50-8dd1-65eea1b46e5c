package com.shuidihuzhu.cf.model.risk;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.Date;


/**
 * 异常身份风险识别策略-提交收款人信息策略
 * <AUTHOR>
 * @date 2020-11-09 16:30:13
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RiskPayeeTimesModel {

    @ApiModelProperty(value = "调用时机，见BlacklistCallPhaseEnum", required = true)
    private Integer callPhase;

    @ApiModelProperty(value = "案例发起时间", required = true)
    private Date launchTime;

    @ApiModelProperty(value = "命中后限制动作", required = true)
    private Long limitAction = 4L;

    @ApiModelProperty(value = "患者证件类型", required = true)
    private Integer patientIdCardType;

    @ApiModelProperty(value = "患者加密证件号", required = true)
    private String cipherIdCardNumber;

    @ApiModelProperty(value = "收款人证件号", required = true)
    private String cipherPayeeIdNumber;

    @ApiModelProperty(value = "caseId", required = true)
    private Integer caseId;

    @ApiModelProperty(value = "打款类型(个人0 对公1)", required = true)
    private Integer payeeType;

    @ApiModelProperty(value = "风控策略，见RiskStrategyEnum", required = true)
    private Integer riskStrategy = 2;

    @ApiModelProperty(value = "当前调用时间", required = true)
    private Date callTime;


}