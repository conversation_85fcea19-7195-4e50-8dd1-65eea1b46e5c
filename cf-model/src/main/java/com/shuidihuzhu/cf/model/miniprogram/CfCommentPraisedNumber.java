package com.shuidihuzhu.cf.model.miniprogram;

import java.util.Date;

public class CfCommentPraisedNumber {
    private int id;
    private long userId;
    private int praisedNumber;
    private Date date;
    private int isDelete;

    public CfCommentPraisedNumber() {
    }

    public CfCommentPraisedNumber(long userId, int praisedNumber, Date date) {
        this.userId = userId;
        this.praisedNumber = praisedNumber;
        this.date = date;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getPraisedNumber() {
        return praisedNumber;
    }

    public void setPraisedNumber(int praisedNumber) {
        this.praisedNumber = praisedNumber;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public int getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(int isDelete) {
        this.isDelete = isDelete;
    }

    @Override
    public String
    toString() {
        return "CfCommentPraisedNumber{" +
                "id=" + id +
                ", userId=" + userId +
                ", praisedNumber=" + praisedNumber +
                ", date=" + date +
                ", isDelete=" + isDelete +
                '}';
    }
}
