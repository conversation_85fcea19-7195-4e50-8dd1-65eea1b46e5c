package com.shuidihuzhu.cf.model.statemachine;

import com.shuidihuzhu.common.web.model.AbstractModel;

/**
 * Created by <PERSON><PERSON> on 2017/9/12.
 */
public class StateChange extends AbstractModel {
	private int fromState;
	private int toState;
	private int independentOfPause;
	private int id;

	public StateChange() {

	}

	public StateChange(int fromState, int toState, int id) {
		this.fromState = fromState;
		this.toState = toState;
		this.id = id;
	}

	public int getFromState() {
		return fromState;
	}

	public void setFromState(int fromState) {
		this.fromState = fromState;
	}

	public int getToState() {
		return toState;
	}

	public void setToState(int toState) {
		this.toState = toState;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public int getIndependentOfPause() {
		return independentOfPause;
	}

	public void setIndependentOfPause(int independentOfPause) {
		this.independentOfPause = independentOfPause;
	}
}
