package com.shuidihuzhu.cf.model.crowdfunding;

/**
 * Created by wangsf on 18/3/7.
 */
public class CfVlinkWebCallLog {

	private int result = 0;
	private String uniqueId = "";
	private String clid = "";
	private String description = "";
	private int recordId = 0;
	private int id;
	private int type = 0;
	private String mobile = "";
	private String originChannel;

	public CfVlinkWebCallLog() {

	}

	public CfVlinkWebCallLog(int result, String uniqueId, String clid, String description) {
		this.result = result;
		this.uniqueId = uniqueId;
		this.clid = clid;
		this.description = description;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public int getRecordId() {
		return recordId;
	}

	public void setRecordId(int recordId) {
		this.recordId = recordId;
	}

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public int getResult() {
		return result;
	}

	public void setResult(int result) {
		this.result = result;
	}

	public String getUniqueId() {
		return uniqueId;
	}

	public void setUniqueId(String uniqueId) {
		this.uniqueId = uniqueId;
	}

	public String getClid() {
		return clid;
	}

	public void setClid(String clid) {
		this.clid = clid;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getOriginChannel() {
		return originChannel;
	}

	public void setOriginChannel(String originChannel) {
		this.originChannel = originChannel;
	}
}
