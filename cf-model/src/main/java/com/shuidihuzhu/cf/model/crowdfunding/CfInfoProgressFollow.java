package com.shuidihuzhu.cf.model.crowdfunding;

import java.util.Date;

public class CfInfoProgressFollow {
    private Integer id;

    private long userId;

    private Boolean isFollow;

    private String infoUuid;

    private Date createTime;

    private Date lastModified;

    private Boolean valid;

    public CfInfoProgressFollow(Integer id,Long userId, Boolean isFollow, String infoUuid, Date createTime, Date lastModified, Boolean valid) {
        this.id = id;
        this.userId = userId;
        this.isFollow = isFollow;
        this.infoUuid = infoUuid;
        this.createTime = createTime;
        this.lastModified = lastModified;
        this.valid = valid;
    }

    public CfInfoProgressFollow() {
        super();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public Boolean getIsFollow() {
        return isFollow;
    }

    public void setIsFollow(Boolean isFollow) {
        this.isFollow = isFollow;
    }

    public String getInfoUuid() {
        return infoUuid;
    }

    public void setInfoUuid(String infoUuid) {
        this.infoUuid = infoUuid == null ? null : infoUuid.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getLastModified() {
        return lastModified;
    }

    public void setLastModified(Date lastModified) {
        this.lastModified = lastModified;
    }

    public Boolean getValid() {
        return valid;
    }

    public void setValid(Boolean valid) {
        this.valid = valid;
    }
}