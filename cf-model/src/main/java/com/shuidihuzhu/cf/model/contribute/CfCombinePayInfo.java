package com.shuidihuzhu.cf.model.contribute;

import com.shuidihuzhu.cf.enums.crowdfunding.CfPayOrigin;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingType;
import com.shuidihuzhu.client.baseservice.pay.model.v3.combine.CombinePayWxParam;
import com.shuidihuzhu.common.web.enums.Platform;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CfCombinePayInfo {

    private String wxAppId;
    private String openId;
    private long userId;
    private Platform platform;
    private CrowdfundingType crowdfundingType;
    private CfPayOrigin cfPayOriginEnum;
    private String productName;

    private CfCombinePayUidInfo combinePayInfo;

    private int clientId;
}
