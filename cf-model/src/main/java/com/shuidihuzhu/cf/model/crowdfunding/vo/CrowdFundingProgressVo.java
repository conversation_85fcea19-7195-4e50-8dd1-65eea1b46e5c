package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingProgress;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCommentView;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

@Getter
@Setter
public class CrowdFundingProgressVo extends CrowdFundingProgress {

	private static final long serialVersionUID = -7235006305380143456L;

	private String targetId;
    private String userName;
    private String headImageUrl;
    private List<CrowdfundingCommentView> comments;


    public void resetId(){
        Integer id = getId();
        if (id != null && id >= 0){
            setId(-1);
        }
        if (CollectionUtils.isNotEmpty(comments)){
            for (CrowdfundingCommentView comment : comments) {
                comment.resetReplyId(comment);
            }
        }
    }

}
