package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.shuidihuzhu.cf.model.crowdfunding.ThanksgivingPosterMsg;

/**
 * Created by wangsf on 17/11/20.
 */
public class ThanksgivingPosterMsgVo {
	private String backgroundImgUrl;    //背景图url
	private String title;       //感谢标题
	private String thankfulText;        //想说的话
	private String headportraitUrl;     //头像url
	private String posterUrl; //海报地址
	private String posterUuid;

	public ThanksgivingPosterMsgVo() {
	}

	public ThanksgivingPosterMsgVo(ThanksgivingPosterMsg msg) {
		if(msg != null) {
			this.backgroundImgUrl = msg.getBackgroundImgUrl();
			this.title = msg.getTitle();
			this.thankfulText = msg.getThankfulText();
			this.headportraitUrl = msg.getHeadportraitUrl();
			this.posterUrl = msg.getPosterUrl();
			this.posterUuid = msg.getPosterUuid();
		}
	}

	public String getBackgroundImgUrl() {
		return backgroundImgUrl;
	}

	public void setBackgroundImgUrl(String backgroundImgUrl) {
		this.backgroundImgUrl = backgroundImgUrl;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getThankfulText() {
		return thankfulText;
	}

	public void setThankfulText(String thankfulText) {
		this.thankfulText = thankfulText;
	}

	public String getHeadportraitUrl() {
		return headportraitUrl;
	}

	public void setHeadportraitUrl(String headportraitUrl) {
		this.headportraitUrl = headportraitUrl;
	}

	public String getPosterUrl() {
		return posterUrl;
	}

	public void setPosterUrl(String posterUrl) {
		this.posterUrl = posterUrl;
	}

	public String getPosterUuid() {
		return posterUuid;
	}

	public void setPosterUuid(String posterUuid) {
		this.posterUuid = posterUuid;
	}
}
