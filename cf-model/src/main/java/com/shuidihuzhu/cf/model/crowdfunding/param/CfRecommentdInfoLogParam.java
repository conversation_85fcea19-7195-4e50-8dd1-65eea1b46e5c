package com.shuidihuzhu.cf.model.crowdfunding.param;

import javax.validation.constraints.Size;

/**
 * Author: <PERSON>
 * Date: 2017/7/21 11:40
 */
public class CfRecommentdInfoLogParam {
	@Size(min = 36, max = 36)
	private String fromInfoId;
	@Size(min = 36, max = 36)
	private String infoId;
	@Size(min = 32, max = 32)
	private String selfTag;
	private boolean isPay = false;

	public String getFromInfoId() {
		return fromInfoId;
	}

	public void setFromInfoId(String fromInfoId) {
		this.fromInfoId = fromInfoId;
	}

	public String getInfoId() {
		return infoId;
	}

	public void setInfoId(String infoId) {
		this.infoId = infoId;
	}

	public String getSelfTag() {
		return selfTag;
	}

	public void setSelfTag(String selfTag) {
		this.selfTag = selfTag;
	}

	public boolean isPay() {
		return isPay;
	}

	public void setPay(boolean pay) {
		isPay = pay;
	}
}
