package com.shuidihuzhu.cf.model.crowdfunding;

import java.io.Serializable;
import java.sql.Date;

/**
 * Created by wangsf on 17/5/22.
 */
public class CfAppVisitRecord implements Serializable{

	public static final long serialVersionUID = 1L;

	private long userId;
	private String appUniqueId;
	private String appVersion;
	private String appSubVersion;
	private String channel;
	private String page;
	private Date createDate;
	private String path;
	private String grayMode;
	private String openId;

	public String getGrayMode() {
		return grayMode;
	}

	public void setGrayMode(String grayMode) {
		this.grayMode = grayMode;
	}

	public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}

	public String getChannel() {
		return channel;
	}

	public void setChannel(String channel) {
		this.channel = channel;
	}

	public String getPage() {
		return page;
	}

	public void setPage(String page) {
		this.page = page;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public String getPath() {
		return path;
	}

	public void setPath(String path) {
		this.path = path;
	}

	public String getAppUniqueId() {
		return appUniqueId;
	}

	public void setAppUniqueId(String appUniqueId) {
		this.appUniqueId = appUniqueId;
	}

	public String getAppVersion() {
		return appVersion;
	}

	public void setAppVersion(String appVersion) {
		this.appVersion = appVersion;
	}

	public String getOpenId() {
		return openId;
	}

	public void setOpenId(String openId) {
		this.openId = openId;
	}

	public String getAppSubVersion() {
		return appSubVersion;
	}

	public void setAppSubVersion(String appSubVersion) {
		this.appSubVersion = appSubVersion;
	}
}
