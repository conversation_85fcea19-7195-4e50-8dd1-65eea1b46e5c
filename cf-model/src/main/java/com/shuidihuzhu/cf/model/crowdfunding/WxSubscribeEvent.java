package com.shuidihuzhu.cf.model.crowdfunding;

import java.util.Date;

public class WxSubscribeEvent {
    private Long id;

    private long userId;

    private String fromUserName;

    private String toUserName;

    private Byte mpType;

    private Byte msgType;

    private Byte eventType;

    private String eventKey;

    private Date eventTime;

    private Date createTime;

    private Date lastModified;

    public WxSubscribeEvent(Long id, Long userId, String fromUserName, String toUserName, Byte mpType, Byte msgType, Byte eventType, String eventKey, Date eventTime, Date createTime, Date lastModified) {
        this.id = id;
        this.userId = userId;
        this.fromUserName = fromUserName;
        this.toUserName = toUserName;
        this.mpType = mpType;
        this.msgType = msgType;
        this.eventType = eventType;
        this.eventKey = eventKey;
        this.eventTime = eventTime;
        this.createTime = createTime;
        this.lastModified = lastModified;
    }

    public WxSubscribeEvent() {
        super();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public String getFromUserName() {
        return fromUserName;
    }

    public void setFromUserName(String fromUserName) {
        this.fromUserName = fromUserName == null ? null : fromUserName.trim();
    }

    public String getToUserName() {
        return toUserName;
    }

    public void setToUserName(String toUserName) {
        this.toUserName = toUserName == null ? null : toUserName.trim();
    }

    public Byte getMpType() {
        return mpType;
    }

    public void setMpType(Byte mpType) {
        this.mpType = mpType;
    }

    public Byte getMsgType() {
        return msgType;
    }

    public void setMsgType(Byte msgType) {
        this.msgType = msgType;
    }

    public Byte getEventType() {
        return eventType;
    }

    public void setEventType(Byte eventType) {
        this.eventType = eventType;
    }

    public String getEventKey() {
        return eventKey;
    }

    public void setEventKey(String eventKey) {
        this.eventKey = eventKey == null ? null : eventKey.trim();
    }

    public Date getEventTime() {
        return eventTime;
    }

    public void setEventTime(Date eventTime) {
        this.eventTime = eventTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getLastModified() {
        return lastModified;
    }

    public void setLastModified(Date lastModified) {
        this.lastModified = lastModified;
    }

    @Override
    public String toString() {
        return "WxSubscribeEvent{" +
                "id=" + id +
                ", userId=" + userId +
                ", fromUserName='" + fromUserName + '\'' +
                ", toUserName='" + toUserName + '\'' +
                ", mpType=" + mpType +
                ", msgType=" + msgType +
                ", eventType=" + eventType +
                ", eventKey='" + eventKey + '\'' +
                ", eventTime=" + eventTime +
                ", createTime=" + createTime +
                ", lastModified=" + lastModified +
                '}';
    }
}
