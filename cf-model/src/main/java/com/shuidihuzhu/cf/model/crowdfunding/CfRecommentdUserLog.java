package com.shuidihuzhu.cf.model.crowdfunding;

import java.util.Date;

public class CfRecommentdUserLog {
    private Long id;

    private String fromInfoId;

    private String infoId;

    private Boolean isPay;

    private long userId;

    private String selfTag;

    private Date createTime;

    private Date updateTime;

    private Boolean valid;

    public CfRecommentdUserLog(Long id, String fromInfoId, String infoId, Boolean isPay,Long userId, String selfTag, Date createTime, Date updateTime, Boolean valid) {
        this.id = id;
        this.fromInfoId = fromInfoId;
        this.infoId = infoId;
        this.isPay = isPay;
        this.userId = userId;
        this.selfTag = selfTag;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.valid = valid;
    }

    public CfRecommentdUserLog() {
        super();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getFromInfoId() {
        return fromInfoId;
    }

    public void setFromInfoId(String fromInfoId) {
        this.fromInfoId = fromInfoId == null ? null : fromInfoId.trim();
    }

    public String getInfoId() {
        return infoId;
    }

    public void setInfoId(String infoId) {
        this.infoId = infoId == null ? null : infoId.trim();
    }

    public Boolean getIsPay() {
        return isPay;
    }

    public void setIsPay(Boolean isPay) {
        this.isPay = isPay;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public String getSelfTag() {
        return selfTag;
    }

    public void setSelfTag(String selfTag) {
        this.selfTag = selfTag == null ? null : selfTag.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Boolean getValid() {
        return valid;
    }

    public void setValid(Boolean valid) {
        this.valid = valid;
    }
}