package com.shuidihuzhu.cf.model.crowdfunding;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2020-07-29
 **/
@ApiModel
@NoArgsConstructor
@Data
public class CfHospitalAuditInfoTel {

    private long id;
    private long cfHospitalAuditInfoId;
    @ApiModelProperty("区号")
    private String areaCode;
    @ApiModelProperty("座机号")
    private String telNum;
    @ApiModelProperty("分机号")
    private String extNum;
    @ApiModelProperty("风险等级")
    private int riskLevel;
    @ApiModelProperty("风险描述")
    private String riskMsg;
    private int isDelete;
    private Timestamp createTime;
    private Timestamp updateTime;

    public CfHospitalAuditInfoTel(long cfHospitalAuditInfoId, String areaCode, String telNum, String extNum) {
        this.cfHospitalAuditInfoId = cfHospitalAuditInfoId;
        this.areaCode = areaCode;
        this.telNum = telNum;
        this.extNum = extNum;
    }

    public String getFullNum() {
        return areaCode + "-" + telNum + "-" + extNum;
    }

}
