package com.shuidihuzhu.cf.model.calltree;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2021/2/2
 */
@Data
@Accessors(chain = true)
public class Endpoint {

    private String id;

    /**
     * controller method 上的 url
     */
    private String url;

    /**
     * 项目名
     */
    private String projectName;

    /**
     * 当前方法签名
     */
    private String methodSignature;

    /**
     * 简单方法签名(参数不带全路径)
     */
    private String simpleMethodSignature;

    // TODO @RocketMQListener @ElasticTask

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否删除 0未删除 1已删除
     */
    private Integer isDelete;
}
