package com.shuidihuzhu.cf.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/22  19:10
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CfDonateFellowRecordVo {

    @ApiModelProperty("患者姓名")
    private String patientName;

    @ApiModelProperty("省份")
    private String province;

    @ApiModelProperty("省份数量")
    private int provinceCount;

    @ApiModelProperty("城市")
    private String city;

    @ApiModelProperty("城市数量")
    private int cityCount;

    @ApiModelProperty("入口数量")
    private int entranceCount;

    @ApiModelProperty("捐款人头像集合")
    private List<String> headImgUrlList;

}
