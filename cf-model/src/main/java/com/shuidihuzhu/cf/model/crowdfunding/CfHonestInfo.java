package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.HonestPersonChangeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.HonestStatusEnum;
import lombok.Data;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @time 2019/6/26 下午5:39
 * @desc
 */
@Data
public class CfHonestInfo {
    private Integer id;
    private Integer caseId;
    private Boolean hospital;

    private String raiserName;
    private String raiserIdentity;
    private Integer raiserDishonest;

    private String payeeName;
    private String payeeIdentity;
    private Integer payeeDishonest;

    private String authorName;
    private String authorIdentity;
    private Integer authorDishonest;

    private Boolean delete;
    private Date createTime;
    private Date updateTime;


    public static CfHonestInfo build(CfHonestInfo cfHonestInfo, boolean hospital, int caseId, int changeType, String raiserName, String raiserIdentity, String payeeName, String payeeIdentity, String authorName, String authorIdentity){

        if(Objects.isNull(cfHonestInfo)){
            cfHonestInfo = new CfHonestInfo();
            cfHonestInfo.setCaseId(caseId);
            cfHonestInfo.setHospital(hospital);
            cfHonestInfo.setRaiserName(raiserName);
            cfHonestInfo.setRaiserIdentity(raiserIdentity);
            cfHonestInfo.setRaiserDishonest(HonestStatusEnum.DEFAULT.getKey());
            cfHonestInfo.setPayeeName(payeeName);
            cfHonestInfo.setPayeeIdentity(payeeIdentity);
            cfHonestInfo.setPayeeDishonest(HonestStatusEnum.DEFAULT.getKey());
            cfHonestInfo.setAuthorName(authorName);
            cfHonestInfo.setAuthorIdentity(authorIdentity);
            cfHonestInfo.setAuthorDishonest(HonestStatusEnum.DEFAULT.getKey());
            return cfHonestInfo;
        }

        cfHonestInfo.setId(null);
        cfHonestInfo.setHospital(hospital);
        if(changeType == HonestPersonChangeEnum.RAISER.getKey()){
            cfHonestInfo.setRaiserName(raiserName);
            cfHonestInfo.setRaiserIdentity(raiserIdentity);
            cfHonestInfo.setRaiserDishonest(HonestStatusEnum.DEFAULT.getKey());
            return cfHonestInfo;
        }

        if(changeType == HonestPersonChangeEnum.PAYEE.getKey()){
            cfHonestInfo.setPayeeName(payeeName);
            cfHonestInfo.setPayeeIdentity(payeeIdentity);
            cfHonestInfo.setPayeeDishonest(HonestStatusEnum.DEFAULT.getKey());
            return cfHonestInfo;
        }

        if(changeType == HonestPersonChangeEnum.AUTHOR.getKey()){
            cfHonestInfo.setAuthorName(authorName);
            cfHonestInfo.setAuthorIdentity(authorIdentity);
            cfHonestInfo.setAuthorDishonest(HonestStatusEnum.DEFAULT.getKey());

            return cfHonestInfo;
        }

        return cfHonestInfo;
    }
}
