package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecord;
import com.shuidihuzhu.cf.risk.model.DiscussionVO;
import com.shuidihuzhu.cf.vo.GoodsOrderExtVo;
import com.shuidihuzhu.common.web.enums.ValidEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * Created by wangsf on 17/2/9.
 */
@ApiModel("订单对象")
@Data
public class CrowdfundingOrderVo {

    @ApiModelProperty("orderId,请使用newOrderId字段")
//	private Integer orderId;
	private Long orderId;

    @ApiModelProperty("筹款基本信息")
	private CrowdfundingBaseInfoVo crowdfundingInfo;

    @ApiModelProperty("筹款金额，单位为元")
	private double donationAmount;

    @ApiModelProperty("当前用户是否证实过这个案例")
	private boolean hasVerified;

    @ApiModelProperty("当前用户对这个案例的转发次数")
	private int shareCount;

    @Deprecated
    @ApiModelProperty("梦想筹相关数据，已废弃")
	private GoodsOrderExtVo goodsOrderExtVo;

    @ApiModelProperty("此订单的相关退款信息")
	private RefundVo refundVo;
	private int score;

	@ApiModelProperty("新订单id")
	private Long newOrderId;

	@ApiModelProperty("提现公示")
	private PublicContent publicContent;
	/**
	 * 个人退款状态
	 */
	@ApiModelProperty("退款状态，具体请找后端同学询问CfDonorRefundApply.StatusEnum")
	private int recentStatus;

	@ApiModelProperty("是否匿名 true：是 false：否")
	private boolean anonymous;

	@ApiModelProperty("订单code")
	private String orderCode;

	@ApiModelProperty("是否设置匿名状态 true：是 false：否")
	private boolean anonymousValid;

	public CrowdfundingOrderVo(Long orderId, CrowdfundingInfo crowdfundingInfo, double donationAmount,
							   DiscussionVO discussionVO, boolean hasVerified, int shareCount) {
		this.orderId = orderId;
		this.crowdfundingInfo = new CrowdfundingBaseInfoVo();
		if (crowdfundingInfo != null) {
			this.crowdfundingInfo.infoUuid = crowdfundingInfo.getInfoId();
			this.crowdfundingInfo.title = crowdfundingInfo.getTitle();
			this.crowdfundingInfo.coverImg = crowdfundingInfo.getTitleImg();
			this.crowdfundingInfo.status = crowdfundingInfo.getStatus().value();
			this.crowdfundingInfo.type = crowdfundingInfo.getType();
			Date endTime = crowdfundingInfo.getEndTime();
			if (endTime != null && endTime.after(new Date())) {
				this.crowdfundingInfo.hasFinished = false;
			} else {
				this.crowdfundingInfo.hasFinished = true;
			}
			
			if(discussionVO != null){
				this.crowdfundingInfo.setDiscussionStatus(discussionVO.getDiscussionStatus());
				this.crowdfundingInfo.setDiscussionEndTime(discussionVO.getDiscussionEndTime());
			}
		}

		this.donationAmount = donationAmount;
		this.hasVerified = hasVerified;
		this.shareCount = shareCount;
	}

	public void buildRefundVo(CfInfoExt cfInfoExt, CrowdfundingPayRecord crowdfundingPayRecord) {
		refundVo = new RefundVo();
		if (cfInfoExt != null && cfInfoExt.getRefundEndTime() != null && cfInfoExt.getRefundEndTime().after(new Date())) {
			refundVo.setCanRefund(ValidEnum.VALID.getValue());
		}
		if (crowdfundingPayRecord != null) {
			refundVo.setRefundStatus(crowdfundingPayRecord.getRefundStatus());
		}
	}


	@Data
    @ApiModel("筹款基本信息")
	public static class CrowdfundingBaseInfoVo {

	    @ApiModelProperty("案例id")
		private String infoUuid;
	    @ApiModelProperty("案例标题")
		private String title;
	    @ApiModelProperty("案例头图")
		private String coverImg;

	    @ApiModelProperty("案例状态")
		private int status;

	    @Deprecated
	    @ApiModelProperty("案例类型，此字段基本废弃，基本都是大病筹款")
		private int type;

	    @ApiModelProperty("案例是否结束")
		private boolean hasFinished;

	    @ApiModelProperty("案例的评议状态")
		private int discussionStatus;

	    @ApiModelProperty("评议结束时间")
		private long discussionEndTime;
	}

    /**
     * 下发的自主退款在使用这个这个对象
     */
    @ApiModel("退款信息")
    @Data
	public static class RefundVo {

    	@ApiModelProperty("是否能退款")
		private int canRefund;

    	@ApiModelProperty("退款状态")
		private int refundStatus;
	}

	@ApiModel("提现公示")
	@Data
	public static class PublicContent{

    	@ApiModelProperty("收款人姓名")
    	private String payeeName;

		@ApiModelProperty("资金用途")
    	private String useOfFunds;

		@ApiModelProperty("患者关系")
		private String relation;

		@ApiModelProperty("银行卡号")
		private String bankCard;

		@ApiModelProperty("打款时间")
		private Date drawCashTime;

		@ApiModelProperty("银行名称")
		private String bankName;





	}
}
