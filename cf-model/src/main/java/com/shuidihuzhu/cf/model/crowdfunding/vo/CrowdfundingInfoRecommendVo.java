package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * Author: <PERSON>
 * Date: 2017/6/4 15:14
 */
public class CrowdfundingInfoRecommendVo {

	private List<String> tags;
	private int donationCount;
	private double targetAmount;
	private double amount;
	private String title;
	private String content;
	@JsonIgnore
	private long userId;
	private Integer type;
	private String infoUuid;
	private long createTime;


	public List<String> getTags() {
		return tags;
	}

	public CrowdfundingInfoRecommendVo setTags(List<String> tags) {
		this.tags = tags;
		return this;
	}

	public String getInfoUuid() {
		return infoUuid;
	}

	public void setInfoUuid(String infoUuid) {
		this.infoUuid = infoUuid;
	}


	public int getDonationCount() {
		return donationCount;
	}

	public void setDonationCount(int donationCount) {
		this.donationCount = donationCount;
	}

	public double getTargetAmount() {
		return targetAmount;
	}

	public void setTargetAmount(double targetAmount) {
		this.targetAmount = targetAmount;
	}

	public double getAmount() {
		return amount;
	}

	public void setAmount(double amount) {
		this.amount = amount;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}


	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public List<CrowdfundingAttachmentVo> getCrowdfundingAttachmentList() {
		return crowdfundingAttachmentList;
	}

	public void setCrowdfundingAttachmentList(
			List<CrowdfundingAttachmentVo> crowdfundingAttachmentList) {
		this.crowdfundingAttachmentList = crowdfundingAttachmentList;
	}

	private List<CrowdfundingAttachmentVo> crowdfundingAttachmentList = Lists.newArrayList();

	public void setCreateTime(long createTime) {
		this.createTime = createTime;
	}

	public long getCreateTime() {
		return createTime;
	}
}
