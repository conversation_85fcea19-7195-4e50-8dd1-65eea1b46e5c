package com.shuidihuzhu.cf.model.crowdfunding.message;

import com.shuidihuzhu.msg.vo.rpc.MsgRecordBatch;
import lombok.Data;

@Data
public class RetrySendTemplateMsgPayload {
    private MsgRecordBatch customMsgRecord;
    private MsgRecordBatch templateMsgRecord;

    public RetrySendTemplateMsgPayload(MsgRecordBatch customMsgRecord, MsgRecordBatch templateMsgRecord) {
        this.customMsgRecord = customMsgRecord;
        this.templateMsgRecord = templateMsgRecord;
    }
}
