package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.Data;

/**
 * <AUTHOR>
 * @DATE 2018/12/4
 *
 * logType   1  草稿   2  手动文案
 */
@Data
public class RecordLog {

    private long id;

    private long userId;

    private long caseId;

    private String logJson;

    private int logType;

    private int logVersion;


    public RecordLog() {
    }
    //草稿箱初始化
    public RecordLog(long userId, String logJson) {
        this.userId = userId;
        this.logJson = logJson;
        this.logType = 1;
        this.logVersion =1;
    }

    //草稿箱初始化
    public RecordLog(long userId, String logJson,int logType) {
        this.userId = userId;
        this.logJson = logJson;
        this.logType = logType;
        this.logVersion =1;
    }
}
