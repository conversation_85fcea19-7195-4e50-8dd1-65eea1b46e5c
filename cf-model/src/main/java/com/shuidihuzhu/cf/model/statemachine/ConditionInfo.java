package com.shuidihuzhu.cf.model.statemachine;


import com.shuidihuzhu.common.web.model.AbstractModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * Created by ch<PERSON> on 2017/9/12.
 */
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ConditionInfo extends AbstractModel {
	private static final long serialVersionUID = 1L;
	
	private int id;
	private int value;
	private String comment;
	private String className;

	public ConditionInfo(int value, String desc) {
		this.value = value;
		this.comment = desc;
	}
}
