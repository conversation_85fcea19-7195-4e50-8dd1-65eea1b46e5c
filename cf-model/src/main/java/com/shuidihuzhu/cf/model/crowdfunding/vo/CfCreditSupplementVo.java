package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.enums.crowdfunding.material.credit.PropertySaleStatusEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfCreditSupplement;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by ahrievil on 2017/4/7.
 */
@ApiModel("车房财产信息字段")
@Data
@NoArgsConstructor
public class CfCreditSupplementVo {

    /**
     * 公约版
     */
    public final static int CONVENTION = 1;

    @ApiModelProperty("财产类型,1房产2车产")
    private Integer propertyType;
    @ApiModelProperty("价值，单位为元")
    private Integer totalValue;
    @ApiModelProperty("财产数量")
    private Integer count;

    @ApiModelProperty("兼容历史版本的老字段，0为未出售，1为已出售，新代码请勿使用，请用下面的propertySaleStatus")
    //老字段只有售卖中和未售卖
    private Integer status;
    //新字段，兼容售卖中
    @ApiModelProperty("财产出售状态，0为未出售，1为已出售，2为出售中")
    private Integer propertySaleStatus;

    @ApiModelProperty("已出售数量")
    private Integer sellCount;

    @ApiModelProperty("已售价格，为0表示未填写，不展示")
    private int saleValue;


    public CfCreditSupplementVo(CfCreditSupplement cfCreditSupplement) {
        this.propertyType = cfCreditSupplement.getPropertyType();
        this.totalValue = cfCreditSupplement.getTotalValue();
        this.count = cfCreditSupplement.getCount();
        this.propertySaleStatus = cfCreditSupplement.getStatus();
        this.status = cfCreditSupplement.getStatus();

        PropertySaleStatusEnum propertySaleStatusEnum = PropertySaleStatusEnum.parse(cfCreditSupplement.getStatus());
        if(propertySaleStatusEnum == PropertySaleStatusEnum.SELLING){
            //根据PM需求，为了兼容老版本，售卖中的也归为未售卖
            setStatus(PropertySaleStatusEnum.NOT_SALE.getCode());
        }

        this.sellCount = cfCreditSupplement.getSellCount();
        this.saleValue = cfCreditSupplement.getSaleValue();
    }

    public static CfCreditSupplement convertOne(CfCreditSupplementVo cfCreditSupplementVo,String infoUuid){
        Preconditions.checkNotNull(cfCreditSupplementVo);
        CfCreditSupplement cfCreditSupplement = new CfCreditSupplement();
        cfCreditSupplement.setCount(cfCreditSupplementVo.getCount());
        cfCreditSupplement.setPropertyType(cfCreditSupplementVo.getPropertyType());
        cfCreditSupplement.setInfoUuid(infoUuid);
        cfCreditSupplement.setSellCount(cfCreditSupplementVo.getSellCount());
        cfCreditSupplement.setTotalValue(cfCreditSupplementVo.getTotalValue());
        if(cfCreditSupplementVo.getPropertySaleStatus()  != null){
            //如果是没填写，改为出售
            if(cfCreditSupplementVo.getPropertySaleStatus() == -1){
                cfCreditSupplementVo.setPropertySaleStatus(0);
            }
            cfCreditSupplement.setStatus(cfCreditSupplementVo.getPropertySaleStatus());
        } else {
            cfCreditSupplement.setStatus(cfCreditSupplementVo.getStatus());
        }
        cfCreditSupplement.setSaleValue(cfCreditSupplementVo.getSaleValue());

        return cfCreditSupplement;
    }

    public static List<CfCreditSupplement> convertList(List<CfCreditSupplementVo> list, String infoUuid){
        if(CollectionUtils.isEmpty(list)){
            return Lists.newArrayList();
        }
        return list.stream().map(r-> CfCreditSupplementVo.convertOne(r,infoUuid)).collect(Collectors.toList());
    }
}
