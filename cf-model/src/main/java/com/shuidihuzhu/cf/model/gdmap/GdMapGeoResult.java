package com.shuidihuzhu.cf.model.gdmap;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GdMapGeoResult extends GdMapBaseResult{

    private List<GeoCodeModel> geocodes;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @ToString
    public static class GeoCodeModel{
        private String location;
        private Object district;
        private String province;
    }

}
