package com.shuidihuzhu.cf.model.calltree;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2021/2/2
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MethodCallChain {
    private Set<String> dataSources;

    private String markdown;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private MethodCallTree methodCallTree;


}
