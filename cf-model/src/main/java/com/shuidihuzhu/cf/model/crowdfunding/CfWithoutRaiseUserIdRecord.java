package com.shuidihuzhu.cf.model.crowdfunding;

import java.sql.Timestamp;

/**
 * Created by ahrievil on 2017/6/22.
 */
public class CfWithoutRaiseUserIdRecord {
    private int id;
    private long userId;
    private int subBizType;
    private Timestamp dateCreated;
    private Timestamp lastModified;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public int getSubBizType() {
        return subBizType;
    }

    public void setSubBizType(int subBizType) {
        this.subBizType = subBizType;
    }

    public Timestamp getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(Timestamp dateCreated) {
        this.dateCreated = dateCreated;
    }

    public Timestamp getLastModified() {
        return lastModified;
    }

    public void setLastModified(Timestamp lastModified) {
        this.lastModified = lastModified;
    }
}
