package com.shuidihuzhu.cf.model.task;

import java.sql.Timestamp;

import lombok.Data;

/**
 * @author: lixuan
 * @date: 2018/3/16 17:34
 */
@Data
public class CfInfoTask {
    private long id;
    private String infoUuid;
    private String date;
    private long taskRuleId;
    private int times;
    private int finish;
    private int hide;
    private int isDelete;
    private Timestamp createTime;
    private Timestamp updateTime;
    /**
     * 给23任务专用
     */
    private String name;

    public static CfInfoTask build(String infoUuid, long taskRuleId, String date) {
        CfInfoTask cfInfoTask = new CfInfoTask();
        cfInfoTask.setInfoUuid(infoUuid);
        cfInfoTask.setDate(date);
        cfInfoTask.setTaskRuleId(taskRuleId);
        return cfInfoTask;
    }
}
