package com.shuidihuzhu.cf.model.crowdfunding;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.shuidihuzhu.cf.enums.crowdfunding.UserCommentSourceEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
public class InitialAuditOperationItem {

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class HandleCaseInfoParam {
        private long workOrderId;
        private int caseId;
        private int handleType;
        private String handleComment;

        private int orderType;

        private int callStatus;

        private String callUnicode;

        private int userCallStatus;

        private String callComment;

        private List<Integer> passIds;
        private List<Integer> rejectIds;

        private int userId;

        @JsonIgnore
        private Set<Integer> allHandleTypes;

        private String diBaoComment;

        @ApiModelProperty("是否为系统自动提交初审审核 1:是；0:不是, 默认不是")
        private int systemAutoAudit;

        @ApiModelProperty("没有打过电话强制提交")
        private boolean aiErForceOp;

        @ApiModelProperty("是否特殊报备发起")
        private boolean specialReport;

        @ApiModelProperty("自定义驳回项内容")
        private Map<Integer, String> customRefuseReason;

    }



    @Getter
    public enum HandleTypeEnum {
        SUBMIT(1, "提交"),
        END_CASE(2, "停止筹款"),
        RETURN_VISIT(3, "回访处理"),
        DELAY_HANDLE(4, "稍后处理"),
        ;

        HandleTypeEnum(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        private int code;
        private String desc;

        public static HandleTypeEnum parseCode(int code) {

            for (HandleTypeEnum type : HandleTypeEnum.values()) {
                if (type.getCode() == code) {
                    return type;
                }
            }

            return null;
        }
    }

    @Getter
    public enum RejectOperation {
        ENABLE(0, "启用"),
        DELETE(1, "删除"),
        DISABLE(2, "弃用"),
        ;

       RejectOperation(int code, String desc) {
           this.code = code;
           this.desc = desc;
       }

       private int code;
       private String desc;

       static public RejectOperation codeOf(int code) {
           RejectOperation[] rejects = values();
           for (RejectOperation reject : rejects) {
               if (reject.getCode() == code) {
                   return reject;
               }
           }

           return null;
       }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class EditBaseInfo {
        private int caseId;
        private String title;
        private String content;
        private String imgUrls;
        private String reason;
        private int userId;
        private long workOrderId;
        private String delImgUrls;
        private String headPictureUrl;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CaseInitialAuditResult {
        private int handleResult;
        UserCommentSourceEnum.CommentType operateType;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class InitialAuditSmsMsg {
        String mobile;
        String content;
        int caseId;
        String infoId;
        int userId;
        long workOrderId;
        String modelNum;
    }


    @Data
    public static class CanSubmitResult {
        boolean canSubmit;
    }

}
