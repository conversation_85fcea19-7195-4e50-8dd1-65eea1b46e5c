package com.shuidihuzhu.cf.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/11/20 5:01 PM
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserFriendTagModel {

    /**
     * 用户是否有过广告位曝光 1 是 0 否
     */
    private int showCfPosition;
    /**
     * 用户否点击过筹广告位 1 是 0 否
     */
    private int clickCfPosition;
    /**
     * 用户是否存在三要素 1 是 0 否
     */
    private int basicIsNameAgeSex;

    /**
     * 用户是否买过保险 1 是 0 否
     */
    private int sdbIsHaveInsured;
    private Long userId;

}
