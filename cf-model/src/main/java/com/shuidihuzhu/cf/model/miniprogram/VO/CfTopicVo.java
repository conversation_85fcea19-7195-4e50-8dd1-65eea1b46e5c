package com.shuidihuzhu.cf.model.miniprogram.VO;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class CfTopicVo {
    private int id;
    private String title;
    private String description;
    private String icon;
    private int shareNum;
    private int commentNum;
    private List<String> contentImageUrls = Lists.newArrayList();

    public CfTopicVo() {
    }

    public CfTopicVo(int id, String title, String description,
                     String icon, int shareNum, int commentNum, String imgUrls) {
        this.id = id;
        this.title = title;
        this.description = description;
        this.icon = icon;
        this.shareNum = shareNum;
        this.commentNum = commentNum;
        if (StringUtils.isNotBlank(imgUrls)) {
            this.contentImageUrls = Splitter.on(",").splitToList(imgUrls);
        }
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public int getShareNum() {
        return shareNum;
    }

    public void setShareNum(int shareNum) {
        this.shareNum = shareNum;
    }

    public int getCommentNum() {
        return commentNum;
    }

    public void setCommentNum(int commentNum) {
        this.commentNum = commentNum;
    }


    public List<String> getContentImageUrls() {
        return contentImageUrls;
    }

    public void setContentImageUrls(List<String> contentImageUrls) {
        this.contentImageUrls = contentImageUrls;
    }

    @Override
    public String toString() {
        return "CfTopicVo{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", description='" + description + '\'' +
                ", icon='" + icon + '\'' +
                ", shareNum=" + shareNum +
                ", commentNum=" + commentNum +
                ", contentImageUrls=" + contentImageUrls +
                '}';
    }
}
