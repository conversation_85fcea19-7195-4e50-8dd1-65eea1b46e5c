package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Author:梁洪超
 * Date:2017/11/29
 */
@Data
public class CfUserInfoStatMobel implements Serializable {
	private static final long serialVersionUID = -4691885395719911479L;
	//info的自增id
	private int infoId;
	private long userId;
	private String nickname;
	private String headImgUrl;
	private String infoUuId;
	private int score;
	private int shareCount;
	private int shareAmount;
	private double donationCount;
	private double donationAmount;
	private int commentCount;
	private boolean infoBlessing;
	private boolean infoVerifivation;
	private Date beginTime;
	private Date endTime;
}
