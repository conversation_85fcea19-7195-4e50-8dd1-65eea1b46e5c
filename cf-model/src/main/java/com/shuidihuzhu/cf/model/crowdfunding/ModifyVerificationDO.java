package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 修改证实
 * @Author: pan<PERSON><PERSON><PERSON>
 * @Date: 2023/6/28 2:51 PM
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModifyVerificationDO {

    private Integer caseId;

    private Long verifyId;

    private Long verifyUserId;

    /**
     * 更改次数
     */
    private Integer modifyNum;

}
