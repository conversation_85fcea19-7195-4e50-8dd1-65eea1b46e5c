package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.CfPropertyTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.material.credit.PropertySaleStatusEnum;
import lombok.Data;

import java.sql.Timestamp;

/**
 * Created by ahrievil on 2017/4/6.
 * 患者基本信息增信补充
 */
@Data
public class CfCreditSupplement {
    private int id;
    private int caseId;
    private String infoUuid;
    private int count;
    /**
     * 财产类型
     * {@link CfPropertyTypeEnum}
     */
    private int propertyType;
    private int totalValue;

    /**
     * 财产状态
     * {@link PropertySaleStatusEnum}
     */
    private int status;
    private int sellCount;
    /** 已售价格 */
    private int saleValue = -1;
    private Timestamp dateCreated;
    private Timestamp lastModified;
    /** 是否公约版 0否，1是 */
    private int convention = 0;

    public void dataDesensitization() {
        this.caseId = -1;
    }
}
