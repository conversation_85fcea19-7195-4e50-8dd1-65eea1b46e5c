package com.shuidihuzhu.cf.model.crowdfunding.message;

import com.shuidihuzhu.msg.model.WxRecordMetaTemplate;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@NoArgsConstructor
@AllArgsConstructor
@ToString
@Data
public class CfWxModelContent {
    private Integer subBusinessType;
    private String templateId;
    private String templateUrl;
    private WxRecordMetaTemplate templateFirst;
    private WxRecordMetaTemplate templateKeyword1;
    private WxRecordMetaTemplate templateKeyword2;
    private WxRecordMetaTemplate templateKeyword3;
    private WxRecordMetaTemplate templateKeyword4;
    private WxRecordMetaTemplate templateKeyword5;
    private WxRecordMetaTemplate templateKeyword6;
    private WxRecordMetaTemplate templateRemark;
}
