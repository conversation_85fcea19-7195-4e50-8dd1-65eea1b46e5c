package com.shuidihuzhu.cf.model.crowdfunding;

import java.util.Date;

public class CfRecommendTag {
    private Integer id;

    private String tag;

    private Short type;

    private Date createTime;

    private Date lastModified;

    private Boolean valid;

    public CfRecommendTag(Integer id, String tag, Short type, Date createTime, Date lastModified, Boolean valid) {
        this.id = id;
        this.tag = tag;
        this.type = type;
        this.createTime = createTime;
        this.lastModified = lastModified;
        this.valid = valid;
    }

    public CfRecommendTag() {
        super();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag == null ? null : tag.trim();
    }

    public Short getType() {
        return type;
    }

    public void setType(Short type) {
        this.type = type;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getLastModified() {
        return lastModified;
    }

    public void setLastModified(Date lastModified) {
        this.lastModified = lastModified;
    }

    public Boolean getValid() {
        return valid;
    }

    public void setValid(<PERSON>olean valid) {
        this.valid = valid;
    }
}