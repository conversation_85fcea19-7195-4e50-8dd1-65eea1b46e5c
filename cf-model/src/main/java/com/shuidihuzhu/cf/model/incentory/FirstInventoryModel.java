package com.shuidihuzhu.cf.model.incentory;

import com.shuidihuzhu.cf.enums.FirstInventoryEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description:
 * @Author: pan<PERSON><PERSON><PERSON>
 * @Date: 2023/1/11 3:37 下午
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FirstInventoryModel {

    private String tips;

    private List<FirstInventoryDetail> list;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FirstInventoryDetail {

        private String firstType;

        private String firstTypeDes;

    }

}
