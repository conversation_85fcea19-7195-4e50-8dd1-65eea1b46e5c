package com.shuidihuzhu.cf.model.crowdfunding;

import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.util.Date;

public class CfDailyCharityMessage {
	private Integer id;
	@NotNull(message = "不能为空")
	private Date startDate;

	@NotBlank(message = "不能为空")
	private String title;
	@NotBlank(message = "不能为空")
	private String content;
	@NotBlank(message = "不能为空")
	private String url;

	private Date createTime;

	private Date lastModified;

	private Boolean valid;
	@NotNull(message = "不能为空")
	private String picUrl;

	public String getPicUrl() {
		return picUrl;
	}

	public void setPicUrl(String picUrl) {
		this.picUrl = picUrl;
	}

	public CfDailyCharityMessage(Integer id, Date startDate, String title, String content, String url, Date createTime, Date lastModified,
	                             Boolean valid, String picUrl) {
		this.id = id;
		this.startDate = startDate;
		this.title = title;
		this.content = content;
		this.url = url;
		this.createTime = createTime;
		this.lastModified = lastModified;
		this.valid = valid;
		this.picUrl = picUrl;
	}

	public CfDailyCharityMessage() {
		super();
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title == null ? null : title.trim();
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content == null ? null : content.trim();
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url == null ? null : url.trim();
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getLastModified() {
		return lastModified;
	}

	public void setLastModified(Date lastModified) {
		this.lastModified = lastModified;
	}

	public Boolean getValid() {
		return valid;
	}

	public void setValid(Boolean valid) {
		this.valid = valid;
	}
}
