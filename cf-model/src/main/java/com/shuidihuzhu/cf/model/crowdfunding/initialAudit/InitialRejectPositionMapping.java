package com.shuidihuzhu.cf.model.crowdfunding.initialAudit;

import com.google.common.collect.Maps;

import java.util.Map;

public class InitialRejectPositionMapping {


    private final static Map<String, InitialAuditItem.EditMaterialType> INITIAL_REJECT_POSITION_MAP = Maps.newHashMap();

    static {
        INITIAL_REJECT_POSITION_MAP.put("标题", InitialAuditItem.EditMaterialType.BASE_INFO_TITLE);
        INITIAL_REJECT_POSITION_MAP.put("求助说明", InitialAuditItem.EditMaterialType.BASE_INFO_CONTENT);
        INITIAL_REJECT_POSITION_MAP.put("展示图片", InitialAuditItem.EditMaterialType.BASE_INFO_IMAGE);

        INITIAL_REJECT_POSITION_MAP.put("医疗材料", InitialAuditItem.EditMaterialType.IMAGE_URL);
        INITIAL_REJECT_POSITION_MAP.put("贫困证明", InitialAuditItem.EditMaterialType.POVERTY);
        INITIAL_REJECT_POSITION_MAP.put("患者信息", InitialAuditItem.EditMaterialType.PATIENT_NAME_IDCARD);
        INITIAL_REJECT_POSITION_MAP.put("所需金额说明", InitialAuditItem.EditMaterialType.TARGET_AMOUNT_DESC);
        INITIAL_REJECT_POSITION_MAP.put("停止筹款", InitialAuditItem.EditMaterialType.SUGGEST_END_CASE);

        // 包括房产信息、车产信息、家庭年收入、家庭金融资产、负债情况、医保情况、人身险、财产险、政府医疗救助情况
        INITIAL_REJECT_POSITION_MAP.put("房产信息", InitialAuditItem.EditMaterialType.HOUSE);
        INITIAL_REJECT_POSITION_MAP.put("车产信息", InitialAuditItem.EditMaterialType.CAR);
        INITIAL_REJECT_POSITION_MAP.put("家庭年收入", InitialAuditItem.EditMaterialType.HOME_INCOME);
        INITIAL_REJECT_POSITION_MAP.put("家庭金融资产", InitialAuditItem.EditMaterialType.HOME_STOCK);
        INITIAL_REJECT_POSITION_MAP.put("负债情况", InitialAuditItem.EditMaterialType.HOME_DEBT);
        INITIAL_REJECT_POSITION_MAP.put("医保情况", InitialAuditItem.EditMaterialType.MEDICAL_INSURANCE);
        INITIAL_REJECT_POSITION_MAP.put("人身险", InitialAuditItem.EditMaterialType.LIFE_INSURANCE);
        INITIAL_REJECT_POSITION_MAP.put("财产险", InitialAuditItem.EditMaterialType.PROPERTY_INSURANCE);
        INITIAL_REJECT_POSITION_MAP.put("政府医疗救助情况", InitialAuditItem.EditMaterialType.GOV_RELIEF);
        INITIAL_REJECT_POSITION_MAP.put("其他平台筹款", InitialAuditItem.EditMaterialType.HAS_OTHER_PLATFORM);
    }

    public InitialAuditItem.EditMaterialType getInitialRejectPositionByContent(String content) {
        return INITIAL_REJECT_POSITION_MAP.get(content);
    }

}
