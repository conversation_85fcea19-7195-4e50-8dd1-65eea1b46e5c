package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.ForwardingGuidelineEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/10  15:17
 * @see ForwardingGuidelineEnum
 */
@Data
public class ForwardingGuidelineOneModel {

    @ApiModelProperty("转发攻略")
    private int forwardingGuideline;
    @ApiModelProperty("自身信息")
    private ForwardingGuidelineOneInfo selfInfo;

    private List<ForwardingGuidelineOneInfo> forwardingGuidelineOneInfos;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ForwardingGuidelineOneInfo {
        @ApiModelProperty("案例uuid")
        private String infoUuid;

        @ApiModelProperty("案例图片")
        private String caseImg;

        @ApiModelProperty("案例标题")
        private String caseTitle;

        @ApiModelProperty("筹款人总转发次数")
        private Long ckrShareCnt;

        @ApiModelProperty("筹款人总筹款金额 单位：元")
        private Double donateAmt;

        @ApiModelProperty("标签")
        private String label;
    }

}
