package com.shuidihuzhu.cf.model.crowdfunding;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @time 2019/2/19 下午8:30
 * @desc
 */
@Data
@ApiModel("案例自身的基本信息")
public class CfCaseBaseInfo {
    @ApiModelProperty("案例标题")
    private String title;
    @ApiModelProperty("案例总捐款次数")
    private int donateCount;
    @ApiModelProperty("案例总转发次数")
    private int shareCount;
    @ApiModelProperty("案例已筹金额 单位：分")
    private int amount;
    @ApiModelProperty("案例结束时间（还有X天结束）")
    private int remainderDay;
    @ApiModelProperty("案例待筹金额（目标减已筹） 单位：分")
    private int remainderAmount;
    @ApiModelProperty("案例已发起天数")
    private int lastDays;
    @ApiModelProperty("筹款人真实姓名 先取患者后发起人")
    private String realName;
}
