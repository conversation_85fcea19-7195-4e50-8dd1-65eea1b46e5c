package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.constants.crowdfunding.CrowdfundingCons;
import com.shuidihuzhu.cf.domain.caseinfo.CaseInfoApproveStageDO;
import com.shuidihuzhu.cf.enums.crowdfunding.*;
import com.shuidihuzhu.cf.finance.model.vo.CfFinanceCapitalStatusVo;
import com.shuidihuzhu.cf.model.CfContentStyle;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.card.CardMsg;
import com.shuidihuzhu.cf.model.getfundinginfo.CrowdfundingBaseInfoParam;
import com.shuidihuzhu.common.web.util.MoneyUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 * Author: Wesley Wu
 * Date: 16/9/12 20:25
 */
@ApiModel
@Data
public class CrowdfundingBaseInfo implements Serializable {


	@ApiModelProperty("案例id")
	private String infoId;

	@JsonIgnore
	private long userId;

	@ApiModelProperty("收款委托渠道: organization 基金会渠道, individual  个人求助")
	private CrowdfundingChannelTypeEnum channelType;

	@Deprecated
	@ApiModelProperty("此字段已废弃，不要使用")
	private String name;

	@Deprecated
	@ApiModelProperty("此字段已废弃，不要使用")
	private UserIdentityType idType;

	@JsonIgnore
	@Deprecated
	@ApiModelProperty("此字段已废弃，不要使用")
	private String idCardPhoto;

	@ApiModelProperty("目标筹款金额，单位为分")
	private int targetAmount;

	@Deprecated
	@ApiModelProperty("当前筹款金额，单位为分，现在已废弃，使用资金系统提供的字段")
	private int amount;

	@ApiModelProperty("当前筹款金额，单位为元")
	private double amountInDouble;

	@ApiModelProperty("捐助次数,没有去重")
	private int donationCount;

	@ApiModelProperty("状态: 0-审批中, 1-被驳回，2-筹款中，3-已结束', 4-已提交,")
	private int status;

	@Deprecated
	@ApiModelProperty("此字段已废弃，不要使用")
	private Date beginTime;

	@ApiModelProperty("案例发起时间")
	private Date createTime;

	@ApiModelProperty("案例结束时间")
	private Date endTime;

	@ApiModelProperty("案例头图")
	private String titleImg;

	@ApiModelProperty("求助标题")
	private String title;

	@ApiModelProperty("求助文章")
	private String content;

	@ApiModelProperty("求助图片")
	private List<String> attachments;

	@Getter @Setter
	@ApiModelProperty("生活照首位图片列表")
	private List<String> lifeAttachments;

	@Getter @Setter
	@ApiModelProperty("病床照首位图片列表")
	private List<String> diseaseAttachments;

	@ApiModelProperty("发起渠道")
	private String channel;

	@ApiModelProperty("实际只有一个含义了，大病筹款")
	private int type;

	@Deprecated
	@ApiModelProperty("此字段已废弃，不要使用")
	private Integer healthInsurance;

	@Deprecated
	@ApiModelProperty("此字段已废弃，不要使用")
	private Integer commercialInsurance;
	private CfContentTypeEnum contentType;

	@Deprecated
	@ApiModelProperty("此字段已废弃，不要使用")
	private CrowdfundingSeekHelpInfoFragment contentV2;

	@Deprecated
	@ApiModelProperty("此字段已废弃，不要使用")
	private List<VideoAttachmentVo> videoAttachments;

	@ApiModelProperty("案例通过初次审核的时间")
	private Date startTime = new Date(0);

	@ApiModelProperty("捐助人数,对同一个用户多次捐款同一案例去重,只对筹款人展示")
	private int donatorCount;

	@Getter @Setter
	@ApiModelProperty("案例对应的省名称")
	private String provinceName;

	@Getter @Setter
	@ApiModelProperty("患者的年龄")
	private int age;

	@Getter
	@Setter
	@ApiModelProperty("患者的性别")
	private int sex;

	@Getter
	@Setter
	@ApiModelProperty("患者的是否使用身份证")
	private boolean idCardFlag;

	@Getter @Setter
	@ApiModelProperty("案例的图文混排信息")
	private String contentImage;

	/**
	 * 已申请提现金额（包含手续费, 扣除返还款 单位 元）
	 */
	private double alreadyApplyDrawAmountWithFeeInFen;

	/**
	 * 实际到账金额（不包含手续费，扣除返还款）
	 */
	@Getter @Setter
	private double drawAmountWithoutFee;

	/**
	 * 提现中的金额（包含手续费）
	 */
	@Getter @Setter
	private double drawCashingAmountWithFee;

	/**
	 * 提现手续费（扣除返还）
	 */
	@Getter @Setter
	private double feeAmount;

	/**
	 * 实收提现服务费（扣除返还）
	 */
	@Getter @Setter
	private double serviceAmount;

	/**
	 * 应收服务费（扣除返还）
	 */
	@Getter @Setter
	private double originalServiceAmount;

	/**
	 * 免除服务费
	 */
	@Getter @Setter
	private double removeServiceAmount;

	/**
	 * 案例剩余金额 (单位 元)
	 */
	private double surplusAmountInFen;

	/**
	 * 筹款人退款（包含手续费，未必是退款成功的金额）
	 */
	@Getter @Setter
	private double refundAmount;

	/**
	 * 提现已到账金额 = 实际到账金额 + 扣除平台服务费 + 扣除第三方支付通道费 - 平台补贴
	 */
	@Getter @Setter
	private double drawReceivedAmount;

	/**
	 * 案例样式关键字
	 */
	private List<String> styleKeyWords;

	/**
	 * 是否重构案例
	 */
	private boolean restructure;

	/**
	 * 案例版本
	 */
	private Integer planId;

	public CrowdfundingBaseInfo() {
	}


	public CrowdfundingBaseInfo(CrowdfundingBaseInfoParam param) {

		CrowdfundingInfo crowdfundingInfo = param.getCrowdfundingInfo();
		this.infoId = crowdfundingInfo.getInfoId();
		this.userId = crowdfundingInfo.getUserId();
		this.channelType = crowdfundingInfo.getChannelType();
		this.targetAmount = crowdfundingInfo.getTargetAmount() / 100;
		this.donationCount = crowdfundingInfo.getDonationCount();
		this.status = crowdfundingInfo.getStatus().value();
		this.beginTime = crowdfundingInfo.getBeginTime();
		this.endTime = crowdfundingInfo.getEndTime();
		this.titleImg = crowdfundingInfo.getTitleImg();
		this.title = crowdfundingInfo.getTitle();
		this.content = crowdfundingInfo.getContent();
		this.planId = crowdfundingInfo.getMaterialPlanId();
		if (crowdfundingInfo.getContentImageStatus() == CfContentImageStatusEnum.ING.getStatus()) {
			this.contentImage = crowdfundingInfo.getContentImage();
		}


		CaseInfoApproveStageDO stageInfo = param.getStageInfo();
		if (stageInfo != null) {
			this.title = stageInfo.getTitle();
			this.content = stageInfo.getContent();
		}


		CrowdfundingAuthor author = param.getAuthor();
		if (author != null) {
			this.name = author.getName();
			this.idType = author.getIdType();
			this.healthInsurance = author.getHealthInsurance();
			this.commercialInsurance = author.getCommercialInsurance();
		}

		this.type = crowdfundingInfo.getType();
		if (type == CrowdfundingType.DREAM.value()) {
			this.name = crowdfundingInfo.getPayeeName();
		}
		this.channel = crowdfundingInfo.getChannel();
		this.createTime = crowdfundingInfo.getCreateTime();
		this.contentType = CfContentTypeEnum.valueOf(crowdfundingInfo.getContentType());


		CfInfoExt cfInfoExt = param.getCfInfoExt();
		if (cfInfoExt != null) {
			if (cfInfoExt.getFirstApproveStatus() == FirstApproveStatusEnum.APPLY_SUCCESS.getCode()) {
				this.startTime = cfInfoExt.getFirstApproveTime();
			} else if (cfInfoExt.getFirstApproveStatus() == FirstApproveStatusEnum.DEFAULT.getCode()) {
				this.startTime = crowdfundingInfo.getCreateTime();
			}
		}


		CfFinanceCapitalStatusVo cfFinanceCapitalStatusVo = param.getCfFinanceCapitalStatusVo();
		if (cfFinanceCapitalStatusVo != null) {
			this.restructure = cfFinanceCapitalStatusVo.isRestructure();
			this.amount = cfFinanceCapitalStatusVo.getDonationAmountInFen() / 100;
			this.amountInDouble = amountTypeConvert(cfFinanceCapitalStatusVo.getDonationAmountInFen());
			this.alreadyApplyDrawAmountWithFeeInFen = amountTypeConvert(cfFinanceCapitalStatusVo.getAlreadyApplyDrawAmountWithFeeInFen());
			this.surplusAmountInFen = amountTypeConvert(cfFinanceCapitalStatusVo.getSurplusAmountInFen());
			this.drawAmountWithoutFee = amountTypeConvert(cfFinanceCapitalStatusVo.getDrawAmountWithoutFeeInFen());
			this.drawCashingAmountWithFee = amountTypeConvert(cfFinanceCapitalStatusVo.getDrawCashingAmountWithFeeInFen());
			this.feeAmount = amountTypeConvert(cfFinanceCapitalStatusVo.getFeeAmountInFen());
			this.serviceAmount = amountTypeConvert(cfFinanceCapitalStatusVo.getServiceAmountInFen());
			this.originalServiceAmount = amountTypeConvert(cfFinanceCapitalStatusVo.getOriginalServiceAmountInFen());
			this.removeServiceAmount = amountTypeConvert(cfFinanceCapitalStatusVo.getRemoveServiceAmountInFen());
			this.refundAmount = amountTypeConvert(cfFinanceCapitalStatusVo.getRefundAmountInFen());
			this.drawReceivedAmount = amountTypeConvert(cfFinanceCapitalStatusVo.getDrawReceivedAmountWithFeeInFen());
		}

		CfContentStyle cfContentStyle = param.getCfContentStyle();
		if (Objects.nonNull(cfContentStyle)) {
			this.styleKeyWords = cfContentStyle.getKeyWordList();
		} else {
			this.styleKeyWords = Lists.newArrayList();
		}

		CardMsg cardMsg = param.getCardMsg();
		this.provinceName = cardMsg.getProvinceName();
		this.age = cardMsg.getAge();
		this.sex = cardMsg.getSex();
		this.idCardFlag = cardMsg.isIdCardFlag();


		this.contentV2 = param.getCrowdfundingSeekHelpInfoFragment();
		this.donatorCount = param.getDonatorCount();


		Map<AttachmentTypeEnum, List<CrowdfundingAttachmentVo>> attachmentMap = param.getAttachmentMap();

		List<CrowdfundingAttachmentVo> idPhotos = attachmentMap.get(AttachmentTypeEnum.ATTACH_ID_CARD);
		if (CollectionUtils.isNotEmpty(idPhotos)) {
			CrowdfundingAttachmentVo idPhoto = idPhotos.iterator().next();
			if (idPhoto != null) {
				this.idCardPhoto = idPhoto.getUrl();
			}
		}

		this.attachments = Lists.newArrayList();
		List<CrowdfundingAttachmentVo> attachmentVos = attachmentMap.get(AttachmentTypeEnum.ATTACH_CF);
		if (CollectionUtils.isNotEmpty(attachmentVos)) {
			for (CrowdfundingAttachmentVo attachmentVo : attachmentVos) {
				attachments.add(attachmentVo.getUrl());
			}
		}

		List<String> attachmentList = param.getAttachments();
		if (CollectionUtils.isNotEmpty(attachmentList)) {
			this.attachments = attachmentList;
		}

		this.videoAttachments = Lists.newArrayList();
		List<CrowdfundingAttachmentVo> videoAttachmentVos = attachmentMap.get(AttachmentTypeEnum.ATTACH_VIDEO);
		if (CollectionUtils.isNotEmpty(videoAttachmentVos)) {
			Map<Integer, List<CrowdfundingAttachmentVo>> crowdfundingAttachmentVoMap = Maps.newHashMap();

			List<CrowdfundingAttachmentVo> videoCoverPlanAttachmentVos = attachmentMap.get(AttachmentTypeEnum.ATTACH_VIDEO_COVER_PLAN);
			if (CollectionUtils.isNotEmpty(videoCoverPlanAttachmentVos)) {
				crowdfundingAttachmentVoMap = videoCoverPlanAttachmentVos.stream().collect(Collectors.groupingBy(CrowdfundingAttachmentVo::getSequence));
			}
			Set<Integer> filter = Sets.newHashSet();
			for (CrowdfundingAttachmentVo attachmentVo : videoAttachmentVos) {
				if (filter.contains(attachmentVo.getSequence())) {
					continue;
				}
				filter.add(attachmentVo.getSequence());
				List<CrowdfundingAttachmentVo> crowdfundingAttachmentVos = crowdfundingAttachmentVoMap.get(attachmentVo.getSequence());
				CrowdfundingAttachmentVo crowdfundingAttachmentVo = crowdfundingAttachmentVos != null ? crowdfundingAttachmentVos.get(0) : null;
				videoAttachments.add(new VideoAttachmentVo(attachmentVo.getUrl(), crowdfundingAttachmentVo == null ? "" : crowdfundingAttachmentVo.getUrl()));
			}
		}

	}

	private double amountTypeConvert(int amount) {
		return MoneyUtil.divide(String.valueOf(amount), "100", 2, BigDecimal.ROUND_HALF_UP).doubleValue();
	}

}
