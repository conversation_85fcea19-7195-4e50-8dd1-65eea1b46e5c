package com.shuidihuzhu.cf.model.crowdfunding;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class CrowdfundingRefuseMsg {

	private Long id;
	private Integer infoId;
	private String commentText;
	private String reason; //原因列表
	private Timestamp dateCreated;
	private Timestamp lastModified;
	private List<Integer> reasonIds;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}

	public Integer getInfoId() {
		return infoId;
	}

	public void setInfoId(Integer infoId) {
		this.infoId = infoId;
	}

	public String getCommentText() {
		return commentText;
	}

	public void setCommentText(String commentText) {
		this.commentText = commentText;
	}

	public Timestamp getDateCreated() {
		return dateCreated;
	}

	public void setDateCreated(Timestamp dateCreated) {
		this.dateCreated = dateCreated;
	}

	public Timestamp getLastModified() {
		return lastModified;
	}

	public void setLastModified(Timestamp lastModified) {
		this.lastModified = lastModified;
	}

	public List<Integer> getReasonIds() {
		if (reasonIds == null) {
			if (StringUtils.isNoneEmpty(reason)) {
				reasonIds = Stream.of(StringUtils.split(reason, ",")).map(NumberUtils::toInt).collect(Collectors.toList());
			} else {
				reasonIds = Collections.EMPTY_LIST;
			}
		}
		return reasonIds;
	}


}
