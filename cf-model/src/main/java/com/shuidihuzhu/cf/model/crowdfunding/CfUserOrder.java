package com.shuidihuzhu.cf.model.crowdfunding;

import java.io.Serializable;

public class CfUserOrder implements Serializable {
    private Long userId=0L;
    private Integer orderNum=0;

    public CfUserOrder() {
    }

    public CfUserOrder(Long userId, Integer orderNum) {
        this.userId = userId;
        this.orderNum = orderNum;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public int getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(int orderNum) {
        this.orderNum = orderNum;
    }

    @Override
    public String toString() {
        return "CfUserOrder{" +
                "userId=" + userId +
                ", orderNum=" + orderNum +
                '}';
    }
}
