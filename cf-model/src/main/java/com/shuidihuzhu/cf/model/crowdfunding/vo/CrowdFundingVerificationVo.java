package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.constants.crowdfunding.status.RelationShip;
import com.shuidihuzhu.cf.enums.crowdfunding.EmergencyConcatEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.IdcardVerifyStatus;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerificationType;
import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/10/19.
 */
@Data
public class CrowdFundingVerificationVo {

    private static final long serialVersionUID = 6535487654232475236L;

    private int id;

    /***
     * 筹款案例ID
     */
    private String crowdFundingInfoId;

    /***
     * 受助人
     */
    @JsonIgnore
    private long patientUserId;

    /***
     * 证实人
     */
    @JsonIgnore
    private long verifyUserId;

    private String openId;

    private String relationShipName;

    private String userName;

    private String description;

    /***
     * 微信昵称
     */
    private String nickName;

    /***
     * 头像
     */
    private String headImgUrl = "";

    /**
     * 是否转发过
     */
    private boolean hasShared = false;

    private int idcardVerifyStatus = IdcardVerifyStatus.UNHANDLE.getCode();

    private Timestamp createTime;

    private int score;

    private CrowdFundingVerificationType type;

    /**
     * 扩展展示标签
     */
    private List<String> extendRelationShipList= Lists.newArrayList();

    //是否关注   true 关注点赞
    private boolean follow;

    private String infoId;

    /**
     * 志愿者标签
     */
    private boolean showLabel;

    /**
     * 编辑入口是否展示 true展示 false不展示
     */
    private boolean showEdit;

}
