package com.shuidihuzhu.cf.model.crowdfunding.message;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2019/3/28 上午10:17
 * @desc
 */
@Data
public class MsgProxyRecordBatch {

    private String modelNum;

    private String businessInfo;

    private List<MsgProxyRecord> msgProxyRecords;

    public static MsgProxyRecordBatch build(String modelNum, String businessInfo, List<MsgProxyRecord> msgProxyRecords){
        MsgProxyRecordBatch msgProxyRecordBatch = new MsgProxyRecordBatch();
        msgProxyRecordBatch.setModelNum(modelNum);
        msgProxyRecordBatch.setBusinessInfo(businessInfo);
        msgProxyRecordBatch.setMsgProxyRecords(msgProxyRecords);
        return msgProxyRecordBatch;
    }
}
