package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.model.UserAccountView;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@ApiModel(description = "资金提现申请vo")
@Data
public class CrowdfundingInfoPublicityDrawVo {
	private String infoUuid;

	@ApiModelProperty(value = "提现申请终止原因")
	private String stopCfReason;

	@ApiModelProperty(value = "资金用途")
	private String useOfFunds;

	@ApiModelProperty(value = "患者病情现状")
	private String patientConditionNow;

	@ApiModelProperty(value = "提现申请图片")
	private List<String> attachments = Lists.newArrayList();

	@ApiModelProperty(value = "提现申请公示结束时间")
	private Date publicityEndTime;

	@ApiModelProperty(value = "提现申请审核通过时间")
	private Date paybankPassTime;

	@ApiModelProperty(value = "提现申请申请者信息")
	private UserAccountView user;

	@ApiModelProperty(value = "收款人与患者关系类型")
	private Integer infoType;

	public CrowdfundingInfoPublicityDrawVo() {
	}
}
