package com.shuidihuzhu.cf.model.patientservicesplatform;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PatientServicesPlatformVo {

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("最新案例id")
    private String infoUuid;

    @ApiModelProperty("当前筹款金额，单位为元")
    private double amountInDouble;

    @ApiModelProperty("可提现金额，单位为元")
    private double surplusAmountInDouble;

    @ApiModelProperty("筹款状态 false:其他状态（案例未初审通过、案例结束） true:案例筹款中")
    private boolean status;

    @ApiModelProperty("案例是否超过自发起10天")
    private boolean initiateLimit;

}
