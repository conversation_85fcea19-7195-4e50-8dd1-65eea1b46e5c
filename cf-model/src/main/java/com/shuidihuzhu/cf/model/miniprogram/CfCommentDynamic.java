package com.shuidihuzhu.cf.model.miniprogram;

public class CfCommentDynamic {
    private long id;
    private int topicId;
    private long commentId;
    private int praiseNumber;
    private int topStatus;
    private int isReply;
    private int isDelete;

    public CfCommentDynamic() {
    }

    public CfCommentDynamic(int topicId, long commentId, int praiseNumber, int topStatus, int isReply) {
        this.topicId = topicId;
        this.commentId = commentId;
        this.praiseNumber = praiseNumber;
        this.topStatus = topStatus;
        this.isReply = isReply;
    }

    public int getTopicId() {
        return topicId;
    }

    public void setTopicId(int topicId) {
        this.topicId = topicId;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getCommentId() {
        return commentId;
    }

    public void setCommentId(long commentId) {
        this.commentId = commentId;
    }

    public int getPraiseNumber() {
        return praiseNumber;
    }

    public void setPraiseNumber(int praiseNumber) {
        this.praiseNumber = praiseNumber;
    }

    public int getTopStatus() {
        return topStatus;
    }

    public void setTopStatus(int topStatus) {
        this.topStatus = topStatus;
    }

    public int getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(int isDelete) {
        this.isDelete = isDelete;
    }

    public int getIsReply() {
        return isReply;
    }

    public void setIsReply(int isReply) {
        this.isReply = isReply;
    }

    @Override
    public String toString() {
        return "CfCommentDynamic{" +
                "id=" + id +
                ", topicId=" + topicId +
                ", commentId=" + commentId +
                ", praiseNumber=" + praiseNumber +
                ", topStatus=" + topStatus +
                ", isDelete=" + isDelete +
                '}';
    }
}
