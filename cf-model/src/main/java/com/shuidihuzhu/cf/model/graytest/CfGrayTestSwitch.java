package com.shuidihuzhu.cf.model.graytest;

/**
 *
 * 灰度测试开关
 *
 * Created by wangsf on 17/5/12.
 */
public class CfGrayTestSwitch {
	private int id;
	private String code;
	private int bizType;
	private int caseCount;
	private boolean ifRecordResult;
	private String casePercentage;
	private String description;

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public int getBizType() {
		return bizType;
	}

	public void setBizType(int bizType) {
		this.bizType = bizType;
	}

	public int getCaseCount() {
		return caseCount;
	}

	public void setCaseCount(int caseCount) {
		this.caseCount = caseCount;
	}

	public String getCasePercentage() {
		return casePercentage;
	}

	public void setCasePercentage(String casePercentage) {
		this.casePercentage = casePercentage;
	}

	public boolean isIfRecordResult() {
		return ifRecordResult;
	}

	public void setIfRecordResult(boolean ifRecordResult) {
		this.ifRecordResult = ifRecordResult;
	}

	@Override
	public String toString() {
		return "CfGrayTestSwitch{" +
				"id=" + id +
				", code='" + code + '\'' +
				", bizType=" + bizType +
				", caseCount=" + caseCount +
				", casePercentage='" + casePercentage + '\'' +
				'}';
	}
}
