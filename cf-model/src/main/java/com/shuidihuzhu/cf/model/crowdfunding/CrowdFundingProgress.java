package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.account.compatible.model.HasUserId;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdFundingProgressType;
import com.shuidihuzhu.common.web.model.AbstractModel;

import java.sql.Timestamp;
import java.util.Date;

/**
 * Created by chao on 16/7/12.
 */
public class CrowdFundingProgress extends AbstractModel implements HasUserId {

	private static final long serialVersionUID = -4591636485961311511L;

	private Integer id;
    private long userId;
    private Integer activityId;
    private Integer type = CrowdFundingProgressType.PROGRESS.value();
    private String title;
    private String content;
    private String imageUrls;
    private Date postDate;
    private Date updateDate;
    private Integer infoType;
    private Integer isDelete = 0;
    private Timestamp drawFinishTime;
    private String drawFinishTimeStr;

    private Date createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public Integer getActivityId() {
        return activityId;
    }

    public void setActivityId(Integer activityId) {
        this.activityId = activityId;
    }

    public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getImageUrls() {
        return imageUrls;
    }

    public void setImageUrls(String imageUrls) {
        this.imageUrls = imageUrls;
    }

    public Date getPostDate() {
        return postDate;
    }

    public void setPostDate(Date postDate) {
        this.postDate = postDate;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public void setInfoType(Integer infoType) {
        this.infoType = infoType;
    }

    public Integer getInfoType() {
        return infoType;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Timestamp getDrawFinishTime() {
        return drawFinishTime;
    }

    public void setDrawFinishTime(Timestamp drawFinishTime) {
        this.drawFinishTime = drawFinishTime;
    }


    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public void setDrawFinishTimeStr(String drawFinishTimeStr) {
        this.drawFinishTimeStr = drawFinishTimeStr;
    }

    public String getDrawFinishTimeStr() {
        return drawFinishTimeStr;
    }
}
