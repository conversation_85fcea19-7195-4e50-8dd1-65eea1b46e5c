package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.common.web.util.MoneyUtil;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/24  11:59
 */
@Data
public class FriendDonationVo {

    //好友昵称
    private String friendNickname;

    //好友数量
    private long friendCount;

    //好友数量
    private long friendCountDistinct;

    //好友信息
    private List<FriendDonationMsg> friendDonationMsgList;


    @Data
    public static class FriendDonationMsg {
        //好友昵称
        private String nickname;

        //好友头像
        private String headImgUrl;

        //捐款金额
        private double amount;

        public FriendDonationMsg() {
        }

        public FriendDonationMsg(String headImgUrl) {
            this.headImgUrl = headImgUrl;
        }

        public FriendDonationMsg(String nickname, String headImgUrl, int amount) {
            this.nickname = nickname;
            this.headImgUrl = headImgUrl;
            this.amount = changeTypeByAmount(amount);
        }

        public double changeTypeByAmount(int amount) {
            return MoneyUtil.divide(String.valueOf(amount), "100", 2, BigDecimal.ROUND_HALF_UP).doubleValue();
        }
    }
}
