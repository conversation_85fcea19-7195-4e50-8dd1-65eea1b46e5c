package com.shuidihuzhu.cf.model.crowdfunding;

import java.sql.Date;
import java.sql.Timestamp;

/**
 *
 * 用户款表里面的信息
 *
 * Created by wangsf on 17/4/28.
 */
public class CfStatInfoWide {

	private int id;
	private long userId;
	private String infoId;
	private Date createDate;
	private Timestamp createTime;
	private String channel;
	private String mobile;
	private String openId;

	public CfStatInfoWide() {

	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}

	public String getInfoId() {
		return infoId;
	}

	public void setInfoId(String infoId) {
		this.infoId = infoId;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Timestamp getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Timestamp createTime) {
		this.createTime = createTime;
	}

	public String getChannel() {
		return channel;
	}

	public void setChannel(String channel) {
		this.channel = channel;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getOpenId() {
		return openId;
	}

	public void setOpenId(String openId) {
		this.openId = openId;
	}

	@Override
	public String toString() {
		return "CfStatInfoWide{" +
				"id=" + id +
				", userId='" + userId + '\'' +
				", infoId='" + infoId + '\'' +
				", createDate=" + createDate +
				", createTime=" + createTime +
				", channel='" + channel + '\'' +
				", mobile='" + mobile + '\'' +
				", openId='" + openId + '\'' +
				'}';
	}
}
