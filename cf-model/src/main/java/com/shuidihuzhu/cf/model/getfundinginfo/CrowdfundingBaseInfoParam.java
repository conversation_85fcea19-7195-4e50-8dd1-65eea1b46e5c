package com.shuidihuzhu.cf.model.getfundinginfo;

import com.shuidihuzhu.cf.domain.caseinfo.CaseInfoApproveStageDO;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.finance.model.vo.CfFinanceCapitalStatusVo;
import com.shuidihuzhu.cf.model.CfContentStyle;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.card.CardMsg;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingAttachmentVo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/3/1  16:28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CrowdfundingBaseInfoParam {
    private CrowdfundingInfo crowdfundingInfo;
    private CrowdfundingAuthor author;
    private Map<AttachmentTypeEnum, List<CrowdfundingAttachmentVo>> attachmentMap;
    private CfInfoExt cfInfoExt;
    private CardMsg cardMsg;
    private CfFirsApproveMaterial material;
    private CfFinanceCapitalStatusVo cfFinanceCapitalStatusVo;
    private CfContentStyle cfContentStyle;
    private CrowdfundingSeekHelpInfoFragment crowdfundingSeekHelpInfoFragment;
    private int donatorCount;
    private boolean isSelf;
    private CaseInfoApproveStageDO stageInfo;
    private List<String> attachments;

}
