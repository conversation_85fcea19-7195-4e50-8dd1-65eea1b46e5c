package com.shuidihuzhu.cf.model.contribute;


import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.enums.combine.CombineCommonEnum;
import com.shuidihuzhu.cf.param.order.LoveHelpCombineOrderParam;
import lombok.*;

import java.util.List;
import java.util.stream.Collectors;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CfCombinePayUidInfo {

    /**
     * 合并支付主支付单号
     */
    private String parentPayUid;

    private CombineCommonEnum.CombineOrderBizType combineBizType;
    /**
     * 案例合并订单
     */
    private List<CaseOrderPayUidInfo> caseOrderPayUidInfoList;
    /**
     * 打赏订单
     */
    private ContributeOrderPayUidInfo contributeOrderPayUidInfo;

    public String getCombineProductName() {
        return this.combineBizType.getProductName();
    }

    /**
     * 构造打赏场景合并支付
     *
     * @param contributeAmount 打赏金额
     * @param amountInFen 捐款金额
     * @return -
     */
    public static CfCombinePayUidInfo buildCombinePayInfo(Integer contributeAmount, int caseId, Integer amountInFen, String productName) {
        if (contributeAmount == null || contributeAmount <= 0 ) {
            return null;
        }

        return CfCombinePayUidInfo.builder()
                .parentPayUid(CfCombineOrderManage.buildParentUid())
                .combineBizType(CombineCommonEnum.CombineOrderBizType.CONTRIBUTE)
                .caseOrderPayUidInfoList(Lists.newArrayList(
                        CaseOrderPayUidInfo.builder()
                                .caseSubPayUid(CfCombineOrderManage.buildSubPayUid())
                                .caseId(caseId)
                                .amountInFen(amountInFen)
                                .productName(productName)
                                .main(true)
                                .build()
                ))
                .contributeOrderPayUidInfo(
                        ContributeOrderPayUidInfo.builder()
                                .contributeAmountInFen(contributeAmount)
                                .contributeSubPayUid(CfCombineOrderManage.buildSubPayUid())
                                .build()
                )
                .build();
    }

    public static CfCombinePayUidInfo buildLoveHelpPayInfo(int mainCaseId, int mainAmountInFen, String mainProductName
                                                           , List<LoveHelpCombineOrderParam> subCombineOrders) {
        CaseOrderPayUidInfo mainCasePayUidInfo = CaseOrderPayUidInfo.builder()
                .caseSubPayUid(CfCombineOrderManage.buildSubPayUid())
                .caseId(mainCaseId)
                .amountInFen(mainAmountInFen)
                .productName(mainProductName)
                .main(true)
                .build();
        List<CaseOrderPayUidInfo> subCasePayUidInfoList = subCombineOrders.stream()
                .map(item ->
                        CaseOrderPayUidInfo.builder()
                                .caseSubPayUid(CfCombineOrderManage.buildSubPayUid())
                                .caseId(item.getCaseId())
                                .productName(item.getProductName())
                                .amountInFen(item.getAmount())
                                .main(false)
                                .build()
                ).collect(Collectors.toList());
        List<CaseOrderPayUidInfo> payInfoList = Lists.newArrayList(mainCasePayUidInfo);
        payInfoList.addAll(subCasePayUidInfoList);
        return CfCombinePayUidInfo.builder()
                .parentPayUid(CfCombineOrderManage.buildParentUid())
                .combineBizType(CombineCommonEnum.CombineOrderBizType.LOVE_MORE_DONATE)
                .caseOrderPayUidInfoList(payInfoList)
                .build();
    }



    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class CaseOrderPayUidInfo {
        /**
         * 案例订单payUid
         */
        private String caseSubPayUid;
        /**
         * 案例订单thirdPayUid
         */
        private String caseThirdSubPayUid;

        private int amountInFen;
        /**
         * 案例productName
         */
        private String productName;
        /**
         * 是否主订单
         */
        private boolean main;
        /**
         * 案例id
         */
        private int caseId;
        /**
         * 订单id
         */
        private long orderId;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class ContributeOrderPayUidInfo {
        /**
         * 打赏payUid
         */
        private String contributeSubPayUid;
        /**
         * 打赏thirdPayUid
         */
        private String contributeThirdSubPayUid;
        /**
         * 打赏金额
         */
        private Integer contributeAmountInFen;
        /**
         * 打赏id
         */
        private long contributeId;
    }

}
