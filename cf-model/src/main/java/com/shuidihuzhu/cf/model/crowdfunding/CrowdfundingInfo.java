package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.*;
import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;
import java.util.Date;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 6/21/16.
 * Modified by <PERSON> on 2016-09-12 for V3
 */
public class CrowdfundingInfo {
	/**
	 * 筹款ID,自增长
	 */
	private int id;
	/**
	 * UUID,前端引用时使用(避免暴露顺序ID,防止被猜中)
	 */
	private String infoId;
	/**
	 * 发起筹款人用户ID,发起筹款人必须通过水滴账号验证
	 */
	private long userId = -1;
	/**
	 * 收款委托渠道:
	 * organization 基金会渠道
	 * individual  个人求助
	 * 使用 name 保存
	 */
	private CrowdfundingChannelTypeEnum channelType = CrowdfundingChannelTypeEnum.organization;
	/**
	 * 发起筹款人姓名,V3版本没用了
	 * todo 因为V3之前发起人就是收款人, 应该将老数据复制到 {@link #payeeName}
	 * {@link #}
	 */
	private String applicantName;
	/**
	 * 发起人QQ,V3不需要这个字段了
	 */
	private String applicantQq;
	/**
	 * 发起人邮箱,V3不需要这个字段了
	 */
	private String applicantMail;
	/**
	 * 发起人和受助人的关系
	 * todo V3不再直接记录,而应该使用 relationType 的 description 来记录
	 * |        1 | 儿子         |
	 * |      128 | 兄弟姐妹     |
	 * |       98 | 其他         |
	 * |        9 | 同事         |
	 * |        3 | 同学         |
	 * |        1 | 女儿         |
	 * |       57 | 子女         |
	 * |       98 | 本人         |
	 * |       68 | 父母         |
	 */
	private String relation;
	/**
	 * 发起人和受助人的关系枚举,老版本没有使用,为String类型
	 * 参见 {@link CrowdfundingRelationType}
	 * 使用 ordinal 保存
	 * todo V3需要使用枚举类型,并且需要根据老版本的 relation 字段数据,进行相应填充,不能有 null
	 */
	private CrowdfundingRelationType relationType = CrowdfundingRelationType.other;
	/**
	 * 收款人真实姓名,V3新版才有
	 * 参与银行卡信息验证
	 */
	private String payeeName;
	/**
	 * 收款人身份证号码,V3新版才有
	 * 参与银行卡信息验证
	 */
	private String payeeIdCard;
	/**
	 * 收款人联系电话,V3新版才有
	 * 本电话不需要和银行卡绑定的电话一致,不参与银行卡信息验证
	 */
	private String payeeMobile;
	/**
	 * 收款人银行名称,V3新版才有
	 * 银行名称不参与银行卡信息验证,只做记录
	 */
	private String payeeBankName;
	/**
	 * 收款人开户支行,V3新版才有
	 * 开户支行不参与银行卡信息验证,只做记录
	 */
	private String payeeBankBranchName;
	/**
	 * 收款人银行卡号,V3新版才有
	 * 参与银行卡信息验证
	 */
	private String payeeBankCard;
	/**
	 * 银行卡验证结果
	 */
	private BankCardVerifyStatus bankCardVerifyStatus = BankCardVerifyStatus.pending;
	/**
	 * 验证的返回信息,缺省为 null
	 */
	private String bankCardVerifyMessage;
	/**
	 * 验证的返回信息2,缺省为 null
	 */
	private String bankCardVerifyMessage2;
	/**
	 * 筹款标题
	 */
	private String title;
	/**
	 * 筹款用途,新版本没用了
	 * 例如:手术费治疗费, 治疗颅内感染的费用
	 */
	private String use;
	/**
	 * 首图地址,取类型为
	 * {@link AttachmentTypeEnum#ATTACH_CF}
	 * 图片的第一张
	 */
	private String titleImg;
	/**
	 * 筹款情况详细说明
	 */
	private String content;

	/**
	 * 加密的筹款情况详细说明
	 */
	private String encryptContent;
	/**
	 * 推广渠道来源,例如 抗癌卫士
	 * 很少使用,正式表中有非null值的只有一条
	 */
	private String from;
	/**
	 * 筹款金额,单位:分
	 */
	private int targetAmount;
	/**
	 * 已筹金额,单位:分
	 */
	private int amount;
	/**
	 * 捐助次数
	 */
	private int donationCount;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 筹款开始时间
	 */
	private Date beginTime;
	/**
	 * 筹款结束时间
	 */
	private Date endTime;

	private String channel;

	/**
	 * 求助说明的内容类型：
	 * 0：整个文章
	 * 1：分部分填写
	 */
	private int contentType;

	/**
	 * 审核状态
	 * 参见 {@link CrowdfundingStatus}
	 * 使用 ordinal 保存
	 */
	private CrowdfundingStatus status = CrowdfundingStatus.APPROVE_PENDING;
	/**
	 * 最后修改时间,由数据库自动维护本字段
	 */
	private Timestamp lastModified;

	/**
	 * 案例提交材料的方案 默认0
	 * @see MaterialPlanVersion
	 */
	private int materialPlanId;

	/**
	 * 图文混排
	 */
	private String contentImage;
	private int contentImageStatus;

	public CrowdfundingInfo() {

	}

	public CrowdfundingInfo(String infoUuid,
	                       long userId,
	                        CfContentTypeEnum contentTypeEnum,
	                        CrowdfundingChannelTypeEnum channelType,
	                        String title,
	                        String content,
	                        int targetAmountInFen,
	                        String channel,
	                        String titleImg,
	                        Date beginTime,
	                        Date endTime,
	                        CrowdfundingRelationType relationType,
	                        String relation,
	                        CrowdfundingType crowdfundingType,
	                        String payeeMobile,
	                        Date createTime) {
		this.infoId = infoUuid;
		this.userId = userId;
		this.contentType = contentTypeEnum.getValue();
		this.channelType = channelType;
		this.title = title;

		if (StringUtils.isNotEmpty(content)) {
			this.setContent(content.replaceAll("\r", "<br />"));
		}
		this.targetAmount = targetAmountInFen;
		this.channel = channel;
		this.titleImg = titleImg;
		this.beginTime = beginTime;
		this.endTime = endTime;
		this.relationType = relationType;
		this.relation = relation;
		this.type = crowdfundingType.value();
		this.createTime = createTime;
		this.payeeMobile = payeeMobile;
	}

	private Integer dataStatus = CrowdfundingInfoDataStatusEnum.BASEDATA.getCode();

	private Integer type = CrowdfundingType.SERIOUS_ILLNESS.value();

	private Integer operation = CrowdfundingOperationEnum.NONE_OPERATION.value();
	
	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getInfoId() {
		return infoId;
	}

	public void setInfoId(String infoId) {
		this.infoId = infoId;
	}

	public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}

	public CrowdfundingChannelTypeEnum getChannelType() {
		return channelType;
	}

	public void setChannelType(CrowdfundingChannelTypeEnum channelType) {
		this.channelType = channelType;
	}

	public String getApplicantName() {
		return applicantName;
	}

	public void setApplicantName(String applicantName) {
		this.applicantName = applicantName;
	}

	public String getApplicantQq() {
		return applicantQq;
	}

	public void setApplicantQq(String applicantQq) {
		this.applicantQq = applicantQq;
	}

	public String getApplicantMail() {
		return applicantMail;
	}

	public void setApplicantMail(String applicantMail) {
		this.applicantMail = applicantMail;
	}

	public String getRelation() {
		return relation;
	}

	public void setRelation(String relation) {
		this.relation = relation;
	}

	public CrowdfundingRelationType getRelationType() {
		return relationType;
	}

	public void setRelationType(CrowdfundingRelationType relationType) {
		this.relationType = relationType;
	}

	public String getPayeeName() {
		return payeeName;
	}

	public void setPayeeName(String payeeName) {
		this.payeeName = payeeName;
	}

	public String getPayeeIdCard() {
		return payeeIdCard;
	}

	public void setPayeeIdCard(String payeeIdCard) {
		this.payeeIdCard = payeeIdCard;
	}

	public String getPayeeMobile() {
		return payeeMobile;
	}

	public void setPayeeMobile(String payeeMobile) {
		this.payeeMobile = payeeMobile;
	}

	public String getPayeeBankName() {
		return payeeBankName;
	}

	public void setPayeeBankName(String payeeBankName) {
		this.payeeBankName = payeeBankName;
	}

	public String getPayeeBankBranchName() {
		return payeeBankBranchName;
	}

	public void setPayeeBankBranchName(String payeeBankBranchName) {
		this.payeeBankBranchName = payeeBankBranchName;
	}

	public String getPayeeBankCard() {
		return payeeBankCard;
	}

	public void setPayeeBankCard(String payeeBankCard) {
		this.payeeBankCard = payeeBankCard;
	}

	public BankCardVerifyStatus getBankCardVerifyStatus() {
		return bankCardVerifyStatus;
	}

	public void setBankCardVerifyStatus(BankCardVerifyStatus bankCardVerifyStatus) {
		this.bankCardVerifyStatus = bankCardVerifyStatus;
	}

	public String getBankCardVerifyMessage() {
		return bankCardVerifyMessage;
	}

	public void setBankCardVerifyMessage(String bankCardVerifyMessage) {
		this.bankCardVerifyMessage = bankCardVerifyMessage;
	}

	public String getBankCardVerifyMessage2() {
		return bankCardVerifyMessage2;
	}

	public void setBankCardVerifyMessage2(String bankCardVerifyMessage2) {
		this.bankCardVerifyMessage2 = bankCardVerifyMessage2;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getUse() {
		return use;
	}

	public void setUse(String use) {
		this.use = use;
	}

	public String getTitleImg() {
		return titleImg;
	}

	public void setTitleImg(String titleImg) {
		this.titleImg = titleImg;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public String getEncryptContent() {
		return encryptContent;
	}

	public void setEncryptContent(String encryptContent) {
		this.encryptContent = encryptContent;
	}

	public String getFrom() {
		return from;
	}

	public void setFrom(String from) {
		this.from = from;
	}

	public int getTargetAmount() {
		return targetAmount;
	}

	public void setTargetAmount(int targetAmount) {
		this.targetAmount = targetAmount;
	}

	public int getAmount() {
		return amount;
	}

	public void setAmount(int amount) {
		this.amount = amount;
	}

	public int getDonationCount() {
		return donationCount;
	}

	public void setDonationCount(int donationCount) {
		this.donationCount = donationCount;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getBeginTime() {
		return beginTime;
	}

	public void setBeginTime(Date beginTime) {
		this.beginTime = beginTime;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public CrowdfundingStatus getStatus() {
		return status;
	}

	public void setStatus(CrowdfundingStatus status) {
		this.status = status;
	}

	public Timestamp getLastModified() {
		return lastModified;
	}

	public void setLastModified(Timestamp lastModified) {
		this.lastModified = lastModified;
	}

	public String getChannel() {
		return channel;
	}

	public void setChannel(String channel) {
		this.channel = channel;
	}

	public Integer getDataStatus() {
		return dataStatus;
	}

	public void setDataStatus(Integer dataStatus) {
		this.dataStatus = dataStatus;
	}

	public Integer getType() {
		if(type == null){
			return CrowdfundingType.SERIOUS_ILLNESS.value();
		} else {
			return type;
		}
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public int getContentType() {
		return contentType;
	}

	public void setContentType(int contentType) {
		this.contentType = contentType;
	}

	public Integer getOperation() {
		return operation;
	}

	public void setOperation(Integer operation) {
		this.operation = operation;
	}

	public int getMaterialPlanId() {
		return materialPlanId;
	}

	public void setMaterialPlanId(int materialPlanId) {
		this.materialPlanId = materialPlanId;
	}

	public String getContentImage() {
		return contentImage;
	}

	public void setContentImage(String contentImage) {
		this.contentImage = contentImage;
	}

	public int getContentImageStatus() {
		return contentImageStatus;
	}

	public void setContentImageStatus(int contentImageStatus) {
		this.contentImageStatus = contentImageStatus;
	}

	@Override
	public String toString() {
		return "CrowdfundingInfo{" +
				"id=" + id +
				", infoId='" + infoId + '\'' +
				", userId=" + userId +
				", channelType=" + channelType +
				", applicantName='" + applicantName + '\'' +
				", applicantQq='" + applicantQq + '\'' +
				", applicantMail='" + applicantMail + '\'' +
				", relation='" + relation + '\'' +
				", relationType=" + relationType +
				", payeeName='" + payeeName + '\'' +
				", payeeIdCard='" + payeeIdCard + '\'' +
				", payeeMobile='" + payeeMobile + '\'' +
				", payeeBankName='" + payeeBankName + '\'' +
				", payeeBankBranchName='" + payeeBankBranchName + '\'' +
				", payeeBankCard='" + payeeBankCard + '\'' +
				", bankCardVerifyStatus=" + bankCardVerifyStatus +
				", bankCardVerifyMessage='" + bankCardVerifyMessage + '\'' +
				", bankCardVerifyMessage2='" + bankCardVerifyMessage2 + '\'' +
				", title='" + title + '\'' +
				", use='" + use + '\'' +
				", titleImg='" + titleImg + '\'' +
				", content='" + content + '\'' +
				", encryptContent='" + encryptContent + '\'' +
				", from='" + from + '\'' +
				", targetAmount=" + targetAmount +
				", amount=" + amount +
				", donationCount=" + donationCount +
				", createTime=" + createTime +
				", beginTime=" + beginTime +
				", endTime=" + endTime +
				", channel='" + channel + '\'' +
				", contentType=" + contentType +
				", status=" + status +
				", lastModified=" + lastModified +
				", materialPlanId=" + materialPlanId +
				", contentImage='" + contentImage + '\'' +
				", contentImageStatus=" + contentImageStatus +
				", dataStatus=" + dataStatus +
				", type=" + type +
				", operation=" + operation +
				'}';
	}

	//是否是发起人
	public boolean isApplicant(long userId) {
		return this.userId == userId;
	}

}
