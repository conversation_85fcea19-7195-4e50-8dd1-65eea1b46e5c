package com.shuidihuzhu.cf.model.river;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;

/**
 * <AUTHOR>
 */
public enum RiverUsageTypeEnum {

    /**
     * 默认值
     */
    DEFAULT(0, "审核"),

    @ApiModelProperty("增信")
    CREDIT_INFO(1, "增信审核"),

    @ApiModelProperty("贫困")
    PIN_KUN(2, "贫困审核"),

    @ApiModelProperty("低保")
    DI_BAO(3, "低保审核"),

    ;

    @Getter
    private final int value;

    /**
     * 用途名称
     * 1. 用于展示备注
     */
    @Getter
    private final String usageName;

    RiverUsageTypeEnum(int v, String usageName) {
        this.value = v;
        this.usageName = usageName;
    }

    public static RiverUsageTypeEnum parse(int v){
        for (RiverUsageTypeEnum e : RiverUsageTypeEnum.values()) {
            if (e.getValue() == v) {
                return e;
            }
        }
        throw new IllegalArgumentException("can not find RiverUsageTypeEnum code: " + v);
    }

}
