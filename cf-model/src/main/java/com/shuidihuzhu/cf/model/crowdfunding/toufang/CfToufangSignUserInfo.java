package com.shuidihuzhu.cf.model.crowdfunding.toufang;

/**
 * Created by wangsf on 18/3/12.
 */
public class CfToufangSignUserInfo {
	private int toufangId;
	private String type;
	private String relation;
	private String help;
	private String disease;
	private String infoUuid;
	private String note;
	private String userName;
	private boolean isInviter;
	private String inviterMobile;
	private String croptyInviterMobile;

	public int getToufangId() {
		return toufangId;
	}

	public void setToufangId(int toufangId) {
		this.toufangId = toufangId;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getRelation() {
		return relation;
	}

	public void setRelation(String relation) {
		this.relation = relation;
	}

	public String getHelp() {
		return help;
	}

	public void setHelp(String help) {
		this.help = help;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getDisease() {
		return disease;
	}

	public void setDisease(String disease) {
		this.disease = disease;
	}

	public String getInfoUuid() {
		return infoUuid;
	}

	public void setInfoUuid(String infoUuid) {
		this.infoUuid = infoUuid;
	}

	public String getNote() {
		return note;
	}

	public void setNote(String note) {
		this.note = note;
	}

	public boolean isInviter() {
		return isInviter;
	}

	public void setInviter(boolean inviter) {
		isInviter = inviter;
	}

	public String getInviterMobile() {
		return inviterMobile;
	}

	public void setInviterMobile(String inviterMobile) {
		this.inviterMobile = inviterMobile;
	}

	public String getCroptyInviterMobile() {
		return croptyInviterMobile;
	}

	public void setCroptyInviterMobile(String croptyInviterMobile) {
		this.croptyInviterMobile = croptyInviterMobile;
	}
}
