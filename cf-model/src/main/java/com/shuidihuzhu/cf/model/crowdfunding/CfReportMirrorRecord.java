package com.shuidihuzhu.cf.model.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.List;
import java.util.Objects;

/**
 * Created by niejiangnan on 2017/10/17.
 */
public class CfReportMirrorRecord {

    private static final Logger LOGGER = LoggerFactory.getLogger(CfInfoMirrorRecord.class);

    private long id;
    private String infoUuid;
    private int type;
    private long operatingRecordId;
    private String reportInfo;
    private int caseReportStatus;
    private int reportFollowType;
    private int operatingType;
    private String content;
    private int addTrustStatus;
    private String addTrustInfo;
    private Timestamp createTime;
    private Timestamp updateTime;
    private AddTrustInfo addTrustInfoObject;
    private List<CfReportBranch> reportInfoObject;

    public void buildReportInfo(List<CrowdfundingReport> crowdfundingReports){
        this.reportInfoObject = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(crowdfundingReports)) {
            for (CrowdfundingReport crowdfundingReport : crowdfundingReports) {
                CfReportBranch cfReportBranch = new CfReportBranch();
                BeanUtils.copyProperties(crowdfundingReport, cfReportBranch);
                this.reportInfoObject.add(cfReportBranch);
            }
        }
        this.reportInfo = JSON.toJSONString(reportInfoObject);
    }

    public void buildAddTrustInfo(CfReportAddTrust cfReportAddTrust, CfReportCommitmentInfo cfReportCommitmentInfo){
        if (Objects.isNull(cfReportCommitmentInfo)) {
            cfReportCommitmentInfo = new CfReportCommitmentInfo();
        }
        this.addTrustInfoObject = new AddTrustInfo();
        if (cfReportAddTrust!=null){
            this.addTrustInfoObject.setContent(cfReportAddTrust.getContent());
            this.addTrustInfoObject.setImageUrls(cfReportAddTrust.getImageUrls());
            this.addTrustInfoObject.setOperatorContent(cfReportAddTrust.getOperatorContent());
            this.addTrustInfoObject.setIssuedCommitment(cfReportAddTrust.isIssuedCommitment());
            this.addTrustInfoObject.setCfReportCommitmentInfo(cfReportCommitmentInfo);
        }
        this.addTrustInfo = JSON.toJSONString(addTrustInfoObject);
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public long getOperatingRecordId() {
        return operatingRecordId;
    }

    public void setOperatingRecordId(long operatingRecordId) {
        this.operatingRecordId = operatingRecordId;
    }

    public String getInfoUuid() {
        return infoUuid;
    }

    public void setInfoUuid(String infoUuid) {
        this.infoUuid = infoUuid;
    }

    public int getCaseReportStatus() {
        return caseReportStatus;
    }

    public void setCaseReportStatus(int caseReportStatus) {
        this.caseReportStatus = caseReportStatus;
    }

    public int getOperatingType() {
        return operatingType;
    }

    public void setOperatingType(int operatingType) {
        this.operatingType = operatingType;
    }

    public int getReportFollowType() {
        return reportFollowType;
    }

    public void setReportFollowType(int reportFollowType) {
        this.reportFollowType = reportFollowType;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getAddTrustStatus() {
        return addTrustStatus;
    }

    public void setAddTrustStatus(int addTrustStatus) {
        this.addTrustStatus = addTrustStatus;
    }

    public String getAddTrustInfo() {
        return addTrustInfo;
    }

    public void setAddTrustInfo(String addTrustInfo) {
        this.addTrustInfo = addTrustInfo;
        if (StringUtils.isNotBlank(addTrustInfo)){
            try {
                this.addTrustInfoObject = new Gson().fromJson(addTrustInfo, AddTrustInfo.class);
            } catch (Exception e) {
                LOGGER.error("error :", e);
            }
        }
    }

    public String getReportInfo() {
        return reportInfo;
    }

    public void setReportInfo(String reportInfo) {
        this.reportInfo = reportInfo;
        if (StringUtils.isNotBlank(reportInfo)){
            try {
                this.reportInfoObject = new Gson().fromJson(reportInfo, List.class);
            } catch (Exception e) {
                LOGGER.error("error :", e);
            }
        }
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public Timestamp getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }

    public AddTrustInfo getAddTrustInfoObject() {
        return addTrustInfoObject;
    }

    public List<CfReportBranch> getReportInfoObject() {
        return reportInfoObject;
    }

    public class AddTrustInfo{
        private String operatorContent;
        private String content;
        private String imageUrls;
        private boolean issuedCommitment;
        private CfReportCommitmentInfo cfReportCommitmentInfo;

        public String getOperatorContent() {
            return operatorContent;
        }

        public void setOperatorContent(String operatorContent) {
            this.operatorContent = operatorContent;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public String getImageUrls() {
            return imageUrls;
        }

        public void setImageUrls(String imageUrls) {
            this.imageUrls = imageUrls;
        }

        public boolean isIssuedCommitment() {
            return issuedCommitment;
        }

        public void setIssuedCommitment(boolean issuedCommitment) {
            this.issuedCommitment = issuedCommitment;
        }

        public CfReportCommitmentInfo getCfReportCommitmentInfo() {
            return cfReportCommitmentInfo;
        }

        public void setCfReportCommitmentInfo(CfReportCommitmentInfo cfReportCommitmentInfo) {
            this.cfReportCommitmentInfo = cfReportCommitmentInfo;
        }
    }

    public class CfReportBranch{
        private int id;
        private int dealStatus;

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public int getDealStatus() {
            return dealStatus;
        }

        public void setDealStatus(int dealStatus) {
            this.dealStatus = dealStatus;
        }
    }
}
