package com.shuidihuzhu.cf.model.crowdfunding;


import com.shuidihuzhu.cf.model.crowdfunding.message.CfPayRecord;
import com.shuidihuzhu.cf.model.crowdfunding.message.CfRefundRecord;
import com.shuidihuzhu.cf.model.crowdfunding.message.CfShareRecord;
import lombok.Data;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.sql.Timestamp;

@Data
public class CfStatData {

    private long id;

    private long caseId;
    //案例结束时间
    private Timestamp finishTime;
    //案例开始时间
    private Timestamp caseCreateTime;
    private int amount;
    private int donationCount;
    private int shareCount;
    private int refundCount;
    private int refundAmount;
    //批次id
    private int batchId;

    //命中次数
    private int hitCount;

    public CfStatData() {
    }


    public CfStatData(CfShareRecord shareRecord) {
        this.caseId = shareRecord.getCaseId();
        this.finishTime = shareRecord.getFinishTime();
        this.caseCreateTime = shareRecord.getCaseCreateTime();
        this.shareCount = shareRecord.getShareCount();
        this.batchId = Integer.valueOf(DateFormatUtils.format(System.currentTimeMillis(), "yyyyMMddHH"));
    }

    public CfStatData(CfPayRecord payRecord) {
        this.caseId = payRecord.getCaseId();
        this.finishTime = payRecord.getFinishTime();
        this.caseCreateTime = payRecord.getCaseCreateTime();
        this.amount = payRecord.getPayMoney();
        this.donationCount = payRecord.getPayCount();
        this.batchId =  Integer.valueOf(DateFormatUtils.format(System.currentTimeMillis(), "yyyyMMddHH"));
    }


    public CfStatData(CfRefundRecord refundRecord) {
        this.caseId = refundRecord.caseId;
        this.finishTime = refundRecord.getCaseCreateTime();
        this.caseCreateTime = refundRecord.getFinishTime();
        this.refundCount = refundRecord.getRefundCount();
        this.refundAmount = refundRecord.getRefundAmount();
        this.batchId =  Integer.valueOf((DateFormatUtils.format(System.currentTimeMillis(), "yyyyMMddHH")));
    }
}
