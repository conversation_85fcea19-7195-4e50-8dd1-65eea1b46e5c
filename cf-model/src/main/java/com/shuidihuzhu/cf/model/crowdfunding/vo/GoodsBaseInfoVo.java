package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.google.common.collect.Lists;
import com.shuidihuzhu.msg.util.DateUtil;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingChannelTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.cf.enums.crowdfunding.UserIdentityType;
import com.shuidihuzhu.cf.model.crowdfunding.CfCapitalAccount;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAuthor;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.goods.GoodsDetail;
import com.shuidihuzhu.common.web.model.AbstractModel;
import org.apache.commons.collections4.CollectionUtils;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

public class GoodsBaseInfoVo extends AbstractModel {

	private static final long serialVersionUID = 1781421701203325939L;

	private String infoId;
	private long userId;
	private CrowdfundingChannelTypeEnum channelType;
	private String name;
	private String headImgUrl;
	private UserIdentityType idType;
	private String idCard;
	private String idCardPhoto;
	private int targetAmount;
	private int amount;
	private int donationCount;
	private CrowdfundingStatus status;
	private Date beginTime;
	private Date endTime;
	private Date createTime;
	private String titleImg;
	private List<String> attachments;
	private String channel;
	private int type;
	private Integer healthInsurance;
	private int remainingDays;
	private int shareCount;
	private String strAmount;
	private String strTargetAmount;

	@SuppressWarnings("deprecation")
	public GoodsBaseInfoVo(CrowdfundingInfo crowdfundingInfo, CrowdfundingAuthor author,
	                       CrowdfundingAttachmentVo idCardPhotoAttachmentVo, List<CrowdfundingAttachmentVo> attachmentVos,
	                       GoodsDetail goodsDetail, String headImgUrl, boolean isNew, int shareCount, int donationAmountInFen) {
		this.headImgUrl = headImgUrl;
		infoId = crowdfundingInfo.getInfoId();
		userId = crowdfundingInfo.getUserId();
		channelType = crowdfundingInfo.getChannelType();
		targetAmount = crowdfundingInfo.getTargetAmount() / 100;
		amount = donationAmountInFen / 100;
		strAmount = this.fen2yuan(crowdfundingInfo.getAmount());
		strTargetAmount = this.fen2yuan(crowdfundingInfo.getTargetAmount());
		donationCount = crowdfundingInfo.getDonationCount();
		status = crowdfundingInfo.getStatus();
		beginTime = crowdfundingInfo.getBeginTime();
		endTime = crowdfundingInfo.getEndTime();
		if(endTime!=null) {
			remainingDays = DateUtil.getDaysBetween(new Timestamp(System.currentTimeMillis()),
					new Timestamp(endTime.getTime()));
		}
		remainingDays = remainingDays >= 0 ? remainingDays : 0;
		createTime = crowdfundingInfo.getCreateTime();
		titleImg = crowdfundingInfo.getTitleImg();
		type = crowdfundingInfo.getType();
		if (!isNew && endTime != null && endTime.before(new Date())) {
			status = CrowdfundingStatus.FINISHED;
		}
		this.name = goodsDetail.getName();
		channel = crowdfundingInfo.getChannel();
		if (idCardPhotoAttachmentVo != null) {
			idCardPhoto = idCardPhotoAttachmentVo.getUrl();
		}
		attachments = Lists.newArrayList();
		if (!CollectionUtils.isEmpty(attachmentVos)) {
			for (CrowdfundingAttachmentVo attachmentVo : attachmentVos) {
				attachments.add(attachmentVo.getUrl());
			}
		}
		this.shareCount = shareCount;
	}

	public String getInfoId() {
		return infoId;
	}

	public void setInfoId(String infoId) {
		this.infoId = infoId;
	}

	public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}

	public CrowdfundingChannelTypeEnum getChannelType() {
		return channelType;
	}

	public void setChannelType(CrowdfundingChannelTypeEnum channelType) {
		this.channelType = channelType;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public UserIdentityType getIdType() {
		return idType;
	}

	public void setIdType(UserIdentityType idType) {
		this.idType = idType;
	}

	public String getIdCard() {
		return idCard;
	}

	public void setIdCard(String idCard) {
		this.idCard = idCard;
	}

	public String getIdCardPhoto() {
		return idCardPhoto;
	}

	public void setIdCardPhoto(String idCardPhoto) {
		this.idCardPhoto = idCardPhoto;
	}

	public int getTargetAmount() {
		return targetAmount;
	}

	public void setTargetAmount(int targetAmount) {
		this.targetAmount = targetAmount;
	}

	public String getStrTargetAmount() {
		return strTargetAmount;
	}

	public void setStrTargetAmount(String strTargetAmount) {
		this.strTargetAmount = strTargetAmount;
	}

	public int getAmount() {
		return amount;
	}

	public String getStrAmount() {
		return strAmount;
	}

	public void setStrAmount(String strAmount) {
		this.strAmount = strAmount;
	}

	public void setAmount(int amount) {
		this.amount = amount;
	}

	public int getDonationCount() {
		return donationCount;
	}

	public void setDonationCount(int donationCount) {
		this.donationCount = donationCount;
	}

	public CrowdfundingStatus getStatus() {
		return status;
	}

	public void setStatus(CrowdfundingStatus status) {
		this.status = status;
	}

	public Date getBeginTime() {
		return beginTime;
	}

	public void setBeginTime(Date beginTime) {
		this.beginTime = beginTime;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getTitleImg() {
		return titleImg;
	}

	public void setTitleImg(String titleImg) {
		this.titleImg = titleImg;
	}

	public List<String> getAttachments() {
		return attachments;
	}

	public void setAttachments(List<String> attachments) {
		this.attachments = attachments;
	}

	public String getChannel() {
		return channel;
	}

	public void setChannel(String channel) {
		this.channel = channel;
	}

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

	public Integer getHealthInsurance() {
		return healthInsurance;
	}

	public void setHealthInsurance(Integer healthInsurance) {
		this.healthInsurance = healthInsurance;
	}

	public int getRemainingDays() {
		return remainingDays;
	}

	public void setRemainingDays(int remainingDays) {
		this.remainingDays = remainingDays;
	}

	public String getHeadImgUrl() {
		return headImgUrl;
	}

	public void setHeadImgUrl(String headImgUrl) {
		this.headImgUrl = headImgUrl;
	}

	public int getShareCount() {
		return shareCount;
	}

	public void setShareCount(int shareCount) {
		this.shareCount = shareCount;
	}

	private String fen2yuan(int amoutInFen) {
		int yuan = amoutInFen / 100;
		int fen = amoutInFen % 100;
		StringBuilder sb = new StringBuilder();
		sb.append(yuan).append(".");
		if(fen < 10) {
			sb.append("0").append(fen);
		} else {
			sb.append(fen);
		}

		return sb.toString();
	}
}
