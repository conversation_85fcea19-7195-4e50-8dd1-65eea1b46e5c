package com.shuidihuzhu.cf.model.crowdfunding;

import java.sql.Date;
import java.sql.Timestamp;

/**
 * Created by wangsf on 18/1/27.
 */
public class CfMeetPoint {

	private int id;
	private int infoId;
	private Date createDt;

	private boolean isLastMeetPoint;
	private Timestamp lastMeetPointTime;
	private String meetPoint = "";

	private boolean isFirstMeetExceptFollow;
	private Timestamp firstMeetTimeExceptFollow;
	private String firstMeetPointExceptFollow = "";

	private boolean isLastMeetExceptFollow;
	private Timestamp lastMeetTimeExceptFollow;
	private String lastMeetPointExceptFollow = "";

	private String lastScene = "";
	private String firstSceneExceptFollow = "";
	private String lastSceneExceptFollow = "";

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public int getInfoId() {
		return infoId;
	}

	public void setInfoId(int infoId) {
		this.infoId = infoId;
	}

	public Date getCreateDt() {
		return createDt;
	}

	public void setCreateDt(Date createDt) {
		this.createDt = createDt;
	}

	public boolean isLastMeetPoint() {
		return isLastMeetPoint;
	}

	public void setLastMeetPoint(boolean lastMeetPoint) {
		isLastMeetPoint = lastMeetPoint;
	}

	public Timestamp getLastMeetPointTime() {
		return lastMeetPointTime;
	}

	public void setLastMeetPointTime(Timestamp lastMeetPointTime) {
		this.lastMeetPointTime = lastMeetPointTime;
	}

	public String getMeetPoint() {
		return meetPoint;
	}

	public void setMeetPoint(String meetPoint) {
		this.meetPoint = meetPoint;
	}

	public boolean isLastMeetExceptFollow() {
		return isLastMeetExceptFollow;
	}

	public boolean isFirstMeetExceptFollow() {
		return isFirstMeetExceptFollow;
	}

	public void setFirstMeetExceptFollow(boolean firstMeetExceptFollow) {
		isFirstMeetExceptFollow = firstMeetExceptFollow;
	}

	public Timestamp getFirstMeetTimeExceptFollow() {
		return firstMeetTimeExceptFollow;
	}

	public void setFirstMeetTimeExceptFollow(Timestamp firstMeetExceptFollow) {
		this.firstMeetTimeExceptFollow = firstMeetExceptFollow;
	}

	public String getFirstMeetPointExceptFollow() {
		return firstMeetPointExceptFollow;
	}

	public void setFirstMeetPointExceptFollow(String firstMeetPointExceptFollow) {
		this.firstMeetPointExceptFollow = firstMeetPointExceptFollow;
	}

	public boolean getLastMeetExceptFollow() {
		return isLastMeetExceptFollow;
	}

	public void setLastMeetExceptFollow(boolean lastMeetExceptFollow) {
		isLastMeetExceptFollow = lastMeetExceptFollow;
	}

	public Timestamp getLastMeetTimeExceptFollow() {
		return lastMeetTimeExceptFollow;
	}

	public void setLastMeetTimeExceptFollow(Timestamp lastMeetExceptFollow) {
		this.lastMeetTimeExceptFollow = lastMeetExceptFollow;
	}

	public String getLastMeetPointExceptFollow() {
		return lastMeetPointExceptFollow;
	}

	public void setLastMeetPointExceptFollow(String lastMeetPointExceptFollow) {
		this.lastMeetPointExceptFollow = lastMeetPointExceptFollow;
	}

	public String getLastScene() {
		return lastScene;
	}

	public void setLastScene(String lastScene) {
		this.lastScene = lastScene;
	}

	public String getFirstSceneExceptFollow() {
		return firstSceneExceptFollow;
	}

	public void setFirstSceneExceptFollow(String firstSceneExceptFollow) {
		this.firstSceneExceptFollow = firstSceneExceptFollow;
	}

	public String getLastSceneExceptFollow() {
		return lastSceneExceptFollow;
	}

	public void setLastSceneExceptFollow(String lastSceneExceptFollow) {
		this.lastSceneExceptFollow = lastSceneExceptFollow;
	}
}



//	CREATE TABLE `cf_meet_point` (
//		`id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
//		`is_last_meet_point` tinyint(1) unsigned not null default '0' comment '是否找到了最晚接触点',
//		`last_meet_point_time` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '最后接触点',
//		`last_meet_point` varchar(30) not null default '' comment '最晚接触点',
//		`is_first_meet_except_follow` tinyint(1) unsigned not null default '0' comment '去除默认和支付关注后有没有其他首次接触点',
//		`first_meet_time_except_follow`  timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '去除默认和支付关注后的首次接触时间',
//		`first_meet_point_except_follow`  timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '去除默认和支付关注后的首次接触点',
//		`is_last_meet_except_follow` tinyint(1) unsigned not null default '0' comment '去除默认和支付关注后有没有最后接触点',
//		`last_meet_time_except_follow`  timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '去除默认和支付关注后的最后接触时间',
//		`last_meet_point_except_follow`  timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '去除默认和支付关注后的最后接触点',
//		`create_time` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
//		`update_time` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
//		`is_delete` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否逻辑删除',
//		PRIMARY KEY (`id`),
//		KEY `idx_create_time` (`create_time`),
//		KEY `idx_update_time` (`update_time`)
//		) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='筹款案例接触点';
//