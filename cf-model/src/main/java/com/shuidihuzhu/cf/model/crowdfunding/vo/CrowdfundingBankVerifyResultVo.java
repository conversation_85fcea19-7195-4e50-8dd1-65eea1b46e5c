package com.shuidihuzhu.cf.model.crowdfunding.vo;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 *
 * 银行卡校验结果
 *
 * Created by wangsf on 17/2/15.
 */
@Data
public class CrowdfundingBankVerifyResultVo {
    private String infoUuid;
	private String message = "";

	private String verifyMessage1 = "";

	private String verifyMessage2 = "";

    private String paSubAcctNo = StringUtils.EMPTY;

	public CrowdfundingBankVerifyResultVo() {

	}

	public CrowdfundingBankVerifyResultVo(String message, String verifyMessage1, String verifyMessage2) {
		this.message = message;
		this.verifyMessage1 = verifyMessage1;
		this.verifyMessage2 = verifyMessage2;
	}
}
