package com.shuidihuzhu.cf.model.gdmap;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class GdMapRegeoResult extends GdMapBaseResult{

    /**
     * 逆地理编码列表
     */
    private Regeocode regeocode;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @ToString
    public static class AddressComponent {
        private Object city;
        private String citycode;
        private String province;
        private String country;
    }
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @ToString
    public static class Regeocode {
        private AddressComponent addressComponent;
    }
}
