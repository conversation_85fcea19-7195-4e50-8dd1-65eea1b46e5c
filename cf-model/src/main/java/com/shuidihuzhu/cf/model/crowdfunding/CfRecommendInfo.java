package com.shuidihuzhu.cf.model.crowdfunding;

import java.util.Date;

public class CfRecommendInfo {
    private Integer id;

    private Integer infoId;

    private Short type;

    private Date createTime;

    private Date lastModified;

    private Boolean valid;

    private String infoUuid;

    public CfRecommendInfo(Integer id, Integer infoId, Short type, Date createTime, Date lastModified, Boolean valid, String infoUuid) {
        this.id = id;
        this.infoId = infoId;
        this.type = type;
        this.createTime = createTime;
        this.lastModified = lastModified;
        this.valid = valid;
        this.infoUuid = infoUuid;
    }

    public CfRecommendInfo() {
        super();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getInfoId() {
        return infoId;
    }

    public void setInfoId(Integer infoId) {
        this.infoId = infoId;
    }

    public Short getType() {
        return type;
    }

    public void setType(Short type) {
        this.type = type;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getLastModified() {
        return lastModified;
    }

    public void setLastModified(Date lastModified) {
        this.lastModified = lastModified;
    }

    public Boolean getValid() {
        return valid;
    }

    public void setValid(Boolean valid) {
        this.valid = valid;
    }

    public String getInfoUuid() {
        return infoUuid;
    }

    public void setInfoUuid(String infoUuid) {
        this.infoUuid = infoUuid == null ? null : infoUuid.trim();
    }
}