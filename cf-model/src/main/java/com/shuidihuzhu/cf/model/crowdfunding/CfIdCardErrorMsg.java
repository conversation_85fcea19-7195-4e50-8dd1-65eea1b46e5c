package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @DATE 2020/3/26
 */
@Data
public class CfIdCardErrorMsg {

    private long id;


    private long backupId;

    /**
     * @see com.shuidihuzhu.cf.enums.CfErrorCode
     */
    private int errorCode;

    private int cfVersion;

    public CfIdCardErrorMsg() {
    }

    public CfIdCardErrorMsg(long backupId, int errorCode, int cfVersion) {
        this.backupId = backupId;
        this.errorCode = errorCode;
        this.cfVersion = cfVersion;
    }
}
