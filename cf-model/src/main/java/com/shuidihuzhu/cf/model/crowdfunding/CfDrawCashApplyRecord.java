package com.shuidihuzhu.cf.model.crowdfunding;

import java.sql.Timestamp;

public class CfDrawCashApplyRecord {

	private Long id;
	private Long drawCashId;
	private String stopCfReason;
	private String useOfFunds;
	private String patientConditionNow;
	private Timestamp dateCreated;
	private Timestamp lastModified;
	private String patientRegion;
	private String patientAddress;
	private String emergencyContactName;
	private String emergencyContactPhone;
	private int emergencyContactRelation;


	public String getPatientRegion() {
		return patientRegion;
	}

	public void setPatientRegion(String patientRegion) {
		this.patientRegion = patientRegion;
	}

	public String getPatientAddress() {
		return patientAddress;
	}

	public void setPatientAddress(String patientAddress) {
		this.patientAddress = patientAddress;
	}

	public String getEmergencyContactName() {
		return emergencyContactName;
	}

	public void setEmergencyContactName(String emergencyContactName) {
		this.emergencyContactName = emergencyContactName;
	}

	public String getEmergencyContactPhone() {
		return emergencyContactPhone;
	}

	public void setEmergencyContactPhone(String emergencyContactPhone) {
		this.emergencyContactPhone = emergencyContactPhone;
	}

	public int getEmergencyContactRelation() {
		return emergencyContactRelation;
	}

	public void setEmergencyContactRelation(int emergencyContactRelation) {
		this.emergencyContactRelation = emergencyContactRelation;
	}


	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getDrawCashId() {
		return drawCashId;
	}

	public void setDrawCashId(Long drawCashId) {
		this.drawCashId = drawCashId;
	}

	public String getStopCfReason() {
		return stopCfReason;
	}

	public void setStopCfReason(String stopCfReason) {
		this.stopCfReason = stopCfReason;
	}

	public String getUseOfFunds() {
		return useOfFunds;
	}

	public void setUseOfFunds(String useOfFunds) {
		this.useOfFunds = useOfFunds;
	}

	public String getPatientConditionNow() {
		return patientConditionNow;
	}

	public void setPatientConditionNow(String patientConditionNow) {
		this.patientConditionNow = patientConditionNow;
	}

	public Timestamp getDateCreated() {
		return dateCreated;
	}

	public void setDateCreated(Timestamp dateCreated) {
		this.dateCreated = dateCreated;
	}

	public Timestamp getLastModified() {
		return lastModified;
	}

	public void setLastModified(Timestamp lastModified) {
		this.lastModified = lastModified;
	}

}
