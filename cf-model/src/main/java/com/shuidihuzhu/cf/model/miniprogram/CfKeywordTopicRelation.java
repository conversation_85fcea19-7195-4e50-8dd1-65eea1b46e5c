package com.shuidihuzhu.cf.model.miniprogram;

import java.util.Date;

public class CfKeywordTopicRelation {
    private int id;
    private int keywordId;
    private Date showDate;
    private int expectNumber;
    private int notExpectNumber;
    private int isDelete;

    public CfKeywordTopicRelation() {
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getKeywordId() {
        return keywordId;
    }

    public void setKeywordId(int keywordId) {
        this.keywordId = keywordId;
    }

    public Date getShowDate() {
        return showDate;
    }

    public void setShowDate(Date showDate) {
        this.showDate = showDate;
    }

    public int getExpectNumber() {
        return expectNumber;
    }

    public void setExpectNumber(int expectNumber) {
        this.expectNumber = expectNumber;
    }

    public int getNotExpectNumber() {
        return notExpectNumber;
    }

    public void setNotExpectNumber(int notExpectNumber) {
        this.notExpectNumber = notExpectNumber;
    }

    public int getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(int isDelete) {
        this.isDelete = isDelete;
    }

    @Override
    public String toString() {
        return "CfKeywordTopicRelation{" +
                "id=" + id +
                ", keywordId=" + keywordId +
                ", showDate=" + showDate +
                ", expectNumber=" + expectNumber +
                ", notExpectNumber=" + notExpectNumber +
                ", isDelete=" + isDelete +
                '}';
    }
}
