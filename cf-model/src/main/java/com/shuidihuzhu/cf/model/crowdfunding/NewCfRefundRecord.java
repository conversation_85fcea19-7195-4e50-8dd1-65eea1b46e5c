package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.NewCfRefundConstant;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.sql.Timestamp;

@Data
public class NewCfRefundRecord {

	private Long id;
	private String infoUuid;
	private String appId;
	private String mhtOrderNo;
	private String mhtRefundNo;
	private Integer payType;
	private Integer refundAmount;
	private Integer status;
	private String refundReason;
	private String responseCode;
	private String tradeStatus;
	private String nowPayOrderNo;
	private Timestamp dateCreated;
	private Timestamp lastModified;
	private int refundType;
	private Timestamp finishTime;

	public NewCfRefundRecord() {
	}

	public NewCfRefundRecord(String infoUuid, String appId, String payUid, int payType, String refundReason,
			int refundAmount, String mhtRefundNo, int refundType) {
		this.infoUuid = infoUuid;
		this.appId = appId;
		this.mhtOrderNo = payUid;
		this.payType = payType;
		this.mhtRefundNo = mhtRefundNo;
		this.refundAmount = refundAmount;
		this.refundReason = refundReason;
		this.status = NewCfRefundConstant.RefundStatus.UNHANDLE.getCode();
		this.responseCode = "";
		this.tradeStatus = "";
		this.nowPayOrderNo = "";
		this.refundType = refundType;
	}

}
