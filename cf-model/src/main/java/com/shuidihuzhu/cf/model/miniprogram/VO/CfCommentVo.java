package com.shuidihuzhu.cf.model.miniprogram.VO;

public class CfCommentVo {
    private int topicId;
    private long commentId;
//    private long userId;

    private int praiseNumber;
    private String nickname;
    private String headUrl;
    private String content;
    private String time;
    //回复评论id
    private long parentId;
    //回复昵称
    private String replyNickname;

    private int praiseStatus;

    public CfCommentVo() {
    }

    public CfCommentVo(int topicId, long commentId, int praiseNumber, int praiseStatus, String nickname,
                       String headUrl, String content, String time, long parentId, String replyNickname) {
        this.topicId = topicId;
        this.commentId = commentId;
        this.praiseNumber = praiseNumber;
        this.nickname = nickname;
        this.headUrl = headUrl;
        this.content = content;
        this.time = time;
        this.praiseStatus = praiseStatus;
        this.parentId = parentId;
        this.replyNickname = replyNickname;
    }

    public int getTopicId() {
        return topicId;
    }

    public void setTopicId(int topicId) {
        this.topicId = topicId;
    }

    public long getCommentId() {
        return commentId;
    }

    public void setCommentId(long commentId) {
        this.commentId = commentId;
    }

    public int getPraiseNumber() {
        return praiseNumber;
    }

    public void setPraiseNumber(int praiseNumber) {
        this.praiseNumber = praiseNumber;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getHeadUrl() {
        return headUrl;
    }

    public void setHeadUrl(String headUrl) {
        this.headUrl = headUrl;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public int getPraiseStatus() {
        return praiseStatus;
    }

    public void setPraiseStatus(int praiseStatus) {
        this.praiseStatus = praiseStatus;
    }

    public long getParentId() {
        return parentId;
    }

    public void setParentId(long parentId) {
        this.parentId = parentId;
    }

    public String getReplyNickname() {
        return replyNickname;
    }

    public void setReplyNickname(String replyNickname) {
        this.replyNickname = replyNickname;
    }

    @Override
    public String toString() {
        return "CfCommentVo{" +
                "topicId=" + topicId +
                ", commentId=" + commentId +
                ", praiseNumber=" + praiseNumber +
                ", nickname='" + nickname + '\'' +
                ", headUrl='" + headUrl + '\'' +
                ", content='" + content + '\'' +
                ", time='" + time + '\'' +
                ", parentId=" + parentId +
                ", praiseStatus=" + praiseStatus +
                '}';
    }
}
