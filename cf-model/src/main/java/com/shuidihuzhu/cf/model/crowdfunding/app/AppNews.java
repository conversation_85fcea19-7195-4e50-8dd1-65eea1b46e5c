package com.shuidihuzhu.cf.model.crowdfunding.app;

import java.sql.Timestamp;
import java.util.Objects;

/**
 * Created by wangsf on 18/2/2.
 */
public class AppNews {

    private int id;
    private String appName;
    private String title;
    private String description;
    private String picUrl;
    private String url;
    private Timestamp createTime;
    private Integer sort;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getPicUrl() {
        return picUrl;
    }

    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AppNews appNews = (AppNews) o;
        return id == appNews.id &&
                Objects.equals(appName, appNews.appName) &&
                Objects.equals(title, appNews.title) &&
                Objects.equals(description, appNews.description) &&
                Objects.equals(picUrl, appNews.picUrl) &&
                Objects.equals(url, appNews.url) &&
                Objects.equals(createTime, appNews.createTime) &&
                Objects.equals(sort, appNews.sort);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, appName, title, description, picUrl, url, createTime, sort);
    }
}
