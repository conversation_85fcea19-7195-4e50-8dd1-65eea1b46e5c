package com.shuidihuzhu.cf.model.crowdfunding;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Author:梁洪超
 * Date:2017/11/29
 */
public class CfInfoStatMobel extends CfInfoStat implements Serializable {
	
	private String title;
	private List<String> titleImgList;
	private String infoId;
	private String authorName;
	private Date beginTime;
	private Date endTime;
	private int donationPeoples;
	
	public String getTitle() {
		return title;
	}
	
	public void setTitle(String title) {
		this.title = title;
	}
	
	public List<String> getTitleImgList() {
		return titleImgList;
	}
	
	public void setTitleImgList(List<String> titleImgList) {
		this.titleImgList = titleImgList;
	}
	
	public String getInfoId() {
		return infoId;
	}
	
	public void setInfoId(String infoId) {
		this.infoId = infoId;
	}
	
	public Date getBeginTime() {
		return beginTime;
	}
	
	public void setBeginTime(Date beginTime) {
		this.beginTime = beginTime;
	}
	
	public Date getEndTime() {
		return endTime;
	}
	
	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}
	
	public String getAuthorName() {
		return authorName;
	}
	
	public void setAuthorName(String authorName) {
		this.authorName = authorName;
	}
	
	public int getDonationPeoples() {
		return donationPeoples;
	}
	
	public void setDonationPeoples(int donationPeoples) {
		this.donationPeoples = donationPeoples;
	}
	
	public CfInfoStatMobel() {
		super();
	}
	
	public CfInfoStatMobel(CfInfoStat cfInfoStat) {
		if (cfInfoStat == null) {
			return;
		}
		this.setAmount(cfInfoStat.getAmount());
		this.setCommentCount(cfInfoStat.getCommentCount());
		this.setDonationCount(cfInfoStat.getDonationCount());
		this.setShareCount(cfInfoStat.getShareCount());
		this.setVerifyUserCount(cfInfoStat.getVerifyUserCount());
		this.setId(cfInfoStat.getId());
		this.setVerifyFriendCount(cfInfoStat.getVerifyFriendCount());
		this.setVerifyHospitalCount(cfInfoStat.getVerifyHospitalCount());
		this.setBlessingCount(cfInfoStat.getBlessingCount());
	}
}
