package com.shuidihuzhu.cf.model.mina;

import java.sql.Timestamp;

/**
 * Created by Ahrievil on 2017/8/31
 */
public class CfMinaLaunchInfo {

    private int id;
    private String infoUuid;
    private String appId;
    private String fromAppId;
    private String channel;
    private String source;
    private String keyWord;
    private String semwp;
    private Timestamp createTime;
    private Timestamp updateTime;

    public CfMinaLaunchInfo() {
    }

    public CfMinaLaunchInfo(String infoUuid, String appId, String fromAppId, String channel, String source, String keyWord, String semwp) {
        this.infoUuid = infoUuid;
        this.appId = appId;
        this.fromAppId = fromAppId;
        this.channel = channel;
        this.source = source;
        this.keyWord = keyWord;
        this.semwp = semwp;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getInfoUuid() {
        return infoUuid;
    }

    public void setInfoUuid(String infoUuid) {
        this.infoUuid = infoUuid;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getFromAppId() {
        return fromAppId;
    }

    public void setFromAppId(String fromAppId) {
        this.fromAppId = fromAppId;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getKeyWord() {
        return keyWord;
    }

    public void setKeyWord(String keyWord) {
        this.keyWord = keyWord;
    }

    public String getSemwp() {
        return semwp;
    }

    public void setSemwp(String semwp) {
        this.semwp = semwp;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public Timestamp getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }
}
