package com.shuidihuzhu.cf.model.river;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class RiverReviewDO {

    private long id;

    private int caseId;

    /**
     * {@link RiverUsageTypeEnum}
     */
    @ApiModelProperty("材料类型")
    private int usageType;

    /**
     * {@link RiverStatusEnum}
     */
    @ApiModelProperty("材料状态")
    private int infoStatus;

    /**
     * 结构Map<Integer, List<RiverRejectReasonVO>> rejectDetail = Maps.newHashMap();
     */
    @ApiModelProperty("驳回详情")
    private String rejectDetail;

    private Date updateTime;



    public static  Map<Integer, List<String>> parseRejectDetail(RiverReviewDO dbReviewDo) {

        Map<Integer, List<String>> result = Maps.newHashMap();
        if (dbReviewDo == null || StringUtils.isBlank(dbReviewDo.getRejectDetail())
                || dbReviewDo.getInfoStatus() == RiverStatusEnum.PASS.getValue()) {
            return result;
        }

        Map<Integer,  List<RiverRejectReasonVO>> rejectSummary = JSON.parseObject(dbReviewDo.getRejectDetail(),
                new TypeReference<Map<Integer, List<RiverRejectReasonVO>>>(){});

        for (Map.Entry<Integer, List<RiverRejectReasonVO>> entry : rejectSummary.entrySet()) {

            List<String> rejectMsgs = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(entry.getValue())) {
                for (RiverRejectReasonVO reasonVo : entry.getValue()) {
                    rejectMsgs.add(reasonVo.getMsg());
                }
            }
            result.put(entry.getKey(), rejectMsgs);
        }

        return result;
    }
}
