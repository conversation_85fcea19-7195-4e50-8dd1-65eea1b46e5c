package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingChannelTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingRelationType;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by <PERSON> on 2016-09-12 for V3 之前的接口兼容性
 */
@Deprecated
public class CrowdfundingInfoVOOld implements Serializable {
	/**
	 * 筹款ID,自增长
	 */
	private int id;
	/**
	 * UUID,前端引用时使用(避免暴露顺序ID,防止被猜中)
	 */
	private String infoId;
	/**
	 * 发起筹款人用户ID,发起筹款人必须通过水滴账号验证
	 */
	private long userId;
	/**
	 * 收款委托渠道:
	 * organization 基金会渠道
	 * individual  个人求助
	 */
	private String channelType;
	/**
	 * 发起筹款人姓名,V3版本没用了
	 * todo 因为V3之前发起人就是收款人, 应该将老数据复制到 {@link #payeeName}
	 * {@link #}
	 */
	private String applicantName;
	/**
	 * 发起人QQ,V3不需要这个字段了
	 */
	private String applicantQq;
	/**
	 * 发起人邮箱,V3不需要这个字段了
	 */
	private String applicantMail;
	/**
	 * 发起人和受助人的关系
	 * todo V3不再直接记录,而应该使用 relationType 的 description 来记录
	 */
	private String relation;
	/**
	 * 发起人和受助人的关系枚举,老版本没有使用,为String类型
	 * 参见 {@link CrowdfundingRelationType}
	 * todo V3需要使用枚举类型,并且需要根据老版本的 relation 字段数据,进行相应填充,不能有 null
	 */
	private String relationType;
	/**
	 * 筹款标题
	 */
	private String title;
	/**
	 * 筹款用途,新版本没用了
	 * 例如:手术费治疗费, 治疗颅内感染的费用
	 */
	private String use;
	/**
	 * 首图地址,取类型为
	 * {@link AttachmentTypeEnum#ATTACH_CF}
	 * 图片的第一张
	 */
	private String titleImg;
	/**
	 * 筹款情况详细说明
	 */
	private String content;
	/**
	 * 推广渠道来源,例如 抗癌卫士
	 * 很少使用,正式表中有非null值的只有一条
	 */
	private String from;
	/**
	 * 筹款金额,单位:分
	 */
	private int targetAmount;
	/**
	 * 已筹金额,单位:分
	 */
	private int amount;
	/**
	 * 捐助次数
	 */
	private int donationCount;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 筹款开始时间
	 */
	private Date beginTime;
	/**
	 * 筹款结束时间
	 */
	private Date endTime;
	/**
	 * 审核状态
	 * 参见 {@link CrowdfundingStatus}
	 */
	private int status;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getInfoId() {
		return infoId;
	}

	public void setInfoId(String infoId) {
		this.infoId = infoId;
	}

	public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}

	public String getChannelType() {
		return channelType;
	}

	public void setChannelType(String channelType) {
		this.channelType = channelType;
	}

	public String getApplicantName() {
		return applicantName;
	}

	public void setApplicantName(String applicantName) {
		this.applicantName = applicantName;
	}

	public String getApplicantQq() {
		return applicantQq;
	}

	public void setApplicantQq(String applicantQq) {
		this.applicantQq = applicantQq;
	}

	public String getApplicantMail() {
		return applicantMail;
	}

	public void setApplicantMail(String applicantMail) {
		this.applicantMail = applicantMail;
	}

	public String getRelation() {
		return relation;
	}

	public void setRelation(String relation) {
		this.relation = relation;
	}

	public String getRelationType() {
		return relationType;
	}

	public void setRelationType(String relationType) {
		this.relationType = relationType;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getUse() {
		return use;
	}

	public void setUse(String use) {
		this.use = use;
	}

	public String getTitleImg() {
		return titleImg;
	}

	public void setTitleImg(String titleImg) {
		this.titleImg = titleImg;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public String getFrom() {
		return from;
	}

	public void setFrom(String from) {
		this.from = from;
	}

	public int getTargetAmount() {
		return targetAmount;
	}

	public void setTargetAmount(int targetAmount) {
		this.targetAmount = targetAmount;
	}

	public int getAmount() {
		return amount;
	}

	public void setAmount(int amount) {
		this.amount = amount;
	}

	public int getDonationCount() {
		return donationCount;
	}

	public void setDonationCount(int donationCount) {
		this.donationCount = donationCount;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getBeginTime() {
		return beginTime;
	}

	public void setBeginTime(Date beginTime) {
		this.beginTime = beginTime;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public int getStatus() {
		return status;
	}

	public void setStatus(int status) {
		this.status = status;
	}

	public CrowdfundingInfo transferToBO() {
		CrowdfundingInfo crowdfundingInfo = new CrowdfundingInfo();
		BeanUtils.copyProperties(this, crowdfundingInfo, "channelType", "relationType", "relation", "status");
		crowdfundingInfo.setChannelType(CrowdfundingChannelTypeEnum.fromTypeName(channelType));
		/*
		 * |        1 | 儿子         |
		 * |      128 | 兄弟姐妹     |
		 * |       98 | 其他         |
		 * |        9 | 同事         |
		 * |        3 | 同学         |
		 * |        1 | 女儿         |
		 * |       57 | 子女         |
		 * |       98 | 本人         |
		 * |       68 | 父母         |
		 */
		CrowdfundingRelationType relationType = CrowdfundingRelationType.other;
		String relation = StringUtils.trimToEmpty(this.relation);
		if (relation.equals("儿子")) {
			relationType = CrowdfundingRelationType.children;
		} else if (relation.equals("兄弟姐妹")) {
			relationType = CrowdfundingRelationType.brothers_or_sisters;
		} else if (relation.equals("兄弟")) {
			relationType = CrowdfundingRelationType.brothers_or_sisters;
		} else if (relation.equals("姐妹")) {
			relationType = CrowdfundingRelationType.brothers_or_sisters;
		} else if (relation.equals("哥哥")) {
			relationType = CrowdfundingRelationType.brothers_or_sisters;
		} else if (relation.equals("弟弟")) {
			relationType = CrowdfundingRelationType.brothers_or_sisters;
		} else if (relation.equals("姐姐")) {
			relationType = CrowdfundingRelationType.brothers_or_sisters;
		} else if (relation.equals("妹妹")) {
			relationType = CrowdfundingRelationType.brothers_or_sisters;
		} else if (relation.equals("同事")) {
			relationType = CrowdfundingRelationType.workmate;
		} else if (relation.equals("同学")) {
			relationType = CrowdfundingRelationType.classmate;
		} else if (relation.equals("女儿")) {
			relationType = CrowdfundingRelationType.children;
		} else if (relation.equals("子女")) {
			relationType = CrowdfundingRelationType.children;
		} else if (relation.equals("本人")) {
			relationType = CrowdfundingRelationType.self;
		} else if (relation.equals("父母")) {
			relationType = CrowdfundingRelationType.parents;
		} else if (relation.equals("父亲")) {
			relationType = CrowdfundingRelationType.parents;
		} else if (relation.equals("母亲")) {
			relationType = CrowdfundingRelationType.parents;
		} else {
			relationType = CrowdfundingRelationType.other;
		}
		crowdfundingInfo.setRelationType(relationType);
		crowdfundingInfo.setRelation(relationType.getDescription());
		return crowdfundingInfo;
	}
}
