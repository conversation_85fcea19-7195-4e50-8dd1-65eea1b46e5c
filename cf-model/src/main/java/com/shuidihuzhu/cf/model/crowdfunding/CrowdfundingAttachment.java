package com.shuidihuzhu.cf.model.crowdfunding;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * Created by wuxin<PERSON> on 6/21/16.
 * Modified by <PERSON> on 2016-09-12 for V3
 * Modified by lixuan on 2017-01-15
 */
public class CrowdfundingAttachment implements Serializable {
	private static final long serialVersionUID = 1810986865940328435L;

	private int id;
	private int parentId;
	private AttachmentTypeEnum type;
    private String url;
    private int sequence;	
    private Timestamp createTime;
	private Timestamp lastModified;
	private int isDelete;

	public CrowdfundingAttachment() {
	}

	public CrowdfundingAttachment(int parentId, AttachmentTypeEnum type, String url) {
		this.parentId = parentId;
		this.type = type;
		this.url = url;
	}

	public CrowdfundingAttachment(int parentId, AttachmentTypeEnum type, String url, int sequence) {
		this.parentId = parentId;
		this.type = type;
		this.url = url;
		this.sequence = sequence;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public int getParentId() {
		return parentId;
	}

	public void setParentId(int parentId) {
		this.parentId = parentId;
	}

	@JSONField(serialize = false)
	public AttachmentTypeEnum getType() {
		return type;
	}

	@JSONField(name = "type")
	public int getTypeValue() {
		return type.value();
	}

	@JSONField(deserialize = false)
	public void setType(AttachmentTypeEnum type) {
		this.type = type;
	}

	@JSONField(name = "type")
	public void setType(int type) {
		this.type = AttachmentTypeEnum.getAttachmentTypeEnum(type);
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public int getSequence() {
		return sequence;
	}

	public void setSequence(int sequence) {
		this.sequence = sequence;
	}

	public Timestamp getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Timestamp createTime) {
		this.createTime = createTime;
	}

	public Timestamp getLastModified() {
		return lastModified;
	}

	public void setLastModified(Timestamp lastModified) {
		this.lastModified = lastModified;
	}

	public int getIsDelete() {
		return isDelete;
	}

	public void setIsDelete(int isDelete) {
		this.isDelete = isDelete;
	}


	public static List<CrowdfundingAttachment> buildAttachmentByImages(int caseId, String images, AttachmentTypeEnum type) {
		if (StringUtils.isBlank(images)) {
			return Lists.newArrayList();
		}

		return buildAttachmentByImageList(caseId, Splitter.on(",").splitToList(images), type);
	}

	public static List<CrowdfundingAttachment> buildAttachmentByImageList(int caseId, List<String> imageList, AttachmentTypeEnum type) {

		List<CrowdfundingAttachment> attachmentList = Lists.newArrayList();

		if (caseId <= 0 || CollectionUtils.isEmpty(imageList) || type == null) {
			return attachmentList;
		}

		for (int i = 0; i < imageList.size(); i++) {
			String url = imageList.get(i);
			if (StringUtils.isBlank(url)){
				continue;
			}
			attachmentList.add(new CrowdfundingAttachment(caseId, type, url, i));
		}

		return attachmentList;
	}

}
