package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.shuidihuzhu.cf.enums.crowdfunding.CfContentTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseStatus;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoStatus;

import java.util.List;

/**
 * Created by ahrievil on 2017/2/27.
 */
public class CrowdfundingDreamBaseVo extends CrowdfundingInfoBaseVo {

    private String purpose;
    private Integer originatorType;

    public CrowdfundingDreamBaseVo() {

    }
    
    public CrowdfundingDreamBaseVo(CrowdfundingInfo crowdfundingInfo,
                                   List<String> attachments,
                                   CfContentTypeEnum contentType,
                                   CrowdfundingSeekHelpInfoFragmentVo fragmentVo,
                                   CrowdfundingInfoStatus infoStatus,
                                   CfRefuseStatus cfRefuseStatus,
                                   String purpose, Integer originatorType) {
        super(crowdfundingInfo, attachments, contentType, fragmentVo, infoStatus, cfRefuseStatus);
        this.purpose = purpose;
        this.originatorType = originatorType;
    }

    public String getPurpose() {
        return purpose;
    }

    public void setPurpose(String purpose) {
        this.purpose = purpose;
    }

    public Integer getOriginatorType() {
        return originatorType;
    }

    public void setOriginatorType(Integer originatorType) {
        this.originatorType = originatorType;
    }
}