package com.shuidihuzhu.cf.model;

import com.shuidihuzhu.cf.enums.crowdfunding.ConsumePointsResultCode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: <PERSON>
 * @date: 2018/3/29 16:39
 */
@NoArgsConstructor
@Data
@AllArgsConstructor
public class ConsumePointsResult {
	private ConsumePointsResultCode code;
	private String tradeNo;

	public ConsumePointsResult(ConsumePointsResultCode code) {
		this.code = code;
	}
}
