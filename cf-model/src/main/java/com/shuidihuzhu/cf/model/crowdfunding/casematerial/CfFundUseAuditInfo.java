package com.shuidihuzhu.cf.model.crowdfunding.casematerial;

import com.shuidihuzhu.cf.client.material.model.CfRaiseFundUseModel;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoStatusEnum;
import com.shuidihuzhu.cf.vo.v5.CfMaterialAuditListView;
import lombok.Data;
import lombok.Getter;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Data
public class CfFundUseAuditInfo {

    private CfRaiseFundUseModel fundUseModel;

    private CrowdfundingInfoStatusEnum auditStatus;

    private Map<Integer, Set<String>> rejectDetail;

    //
    private Map<Integer, Set<String>> rejects;
    //
    private List<CfMaterialAuditListView.ModifySuggest> suggests;


    @Getter
    public enum MaterialRejectPosition {
        ZHI_LIAO_HUA_FEI(33, "治疗花费情况"),
        CHOU_KUAN_WEI_LAI_YONG_TU(34, "款项预期用途"),
        ZHENG_FU_YI_LIAO_JIU_ZHU(35, "政府医疗救助"),
        ;


        private int code;
        private String desc;

        MaterialRejectPosition(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }
}
