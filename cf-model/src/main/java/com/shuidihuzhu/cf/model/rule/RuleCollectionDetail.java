package com.shuidihuzhu.cf.model.rule;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by sven on 18/8/7.
 *
 * 用于存储一个规则集合的详情，设计请参见：
 * https://wiki.shuiditech.com/pages/viewpage.action?pageId=19958239
 *
 * <AUTHOR>
 */
@Data
public class RuleCollectionDetail implements Serializable {

    /**
     * 一个活动的唯一主键
     */
    private long id;

    /**
     * 创建者id
     */
    private int adminUserId;

    /**
     * 拥有者名称
     */
    private String ownerName;

    /**
     * 如果为空表示从现在开始就有效
     */
    private Date startTime;

    /**
     * 如果为空表示没有结束时间
     */
    private Date endTime;

    /**
     * 0:  不启用  1: 启用
     */
    private int status;

    /**
     * 退出模式
     * @see {@link com.shuidihuzhu.cf.enums.rule.RuleFailModeEnum}
     */
    private int failMode;

    private long rootNodeId;

    private Date createTime;
    private Date updateTime;
}