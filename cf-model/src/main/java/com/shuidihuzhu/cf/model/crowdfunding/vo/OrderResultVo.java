package com.shuidihuzhu.cf.model.crowdfunding.vo;

import com.shuidihuzhu.client.baseservice.pay.model.PayResultV2;
import com.shuidihuzhu.common.web.enums.Platform;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderResultVo {
    private String tradeNo;
    private double amt;
    private String body;
    private String openId;
    private PayResultV2 payInfo;
    private Platform payType;

    @ApiModelProperty("订单唯一id(非自增)")
    private String orderCode;
}
