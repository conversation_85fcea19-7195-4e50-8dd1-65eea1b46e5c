package com.shuidihuzhu.cf.model.crowdfunding;

import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2016/8/31
 */
@ToString
public class CrowdfundingOrderCount {

	private int sequence;
	private int amount;
	private String coverUrl;
	private String authorName;
	private String crowdfundingTitle;
	private List<String> userHeadImages;
	private String diseaseName;
	private int countNum;
	private String headImage;

	public String getHeadImage() {
		return headImage;
	}

	public void setHeadImage(String headImage) {
		this.headImage = headImage;
	}

	public int getCountNum() {
		return countNum;
	}

	public void setCountNum(int countNum) {
		this.countNum = countNum;
	}

	public String getDiseaseName() {
		return diseaseName;
	}

	public void setDiseaseName(String diseaseName) {
		this.diseaseName = diseaseName;
	}

	public int getAmount() {
		return amount;
	}

	public void setAmount(int amount) {
		this.amount = amount;
	}

	public String getAuthorName() {
		return authorName;
	}

	public void setAuthorName(String authorName) {
		this.authorName = authorName;
	}

	public String getCoverUrl() {
		return coverUrl;
	}

	public void setCoverUrl(String coverUrl) {
		this.coverUrl = coverUrl;
	}

	public String getCrowdfundingTitle() {
		return crowdfundingTitle;
	}

	public void setCrowdfundingTitle(String crowdfundingTitle) {
		this.crowdfundingTitle = crowdfundingTitle;
	}

	public int getSequence() {
		return sequence;
	}

	public void setSequence(int sequence) {
		this.sequence = sequence;
	}

	public List<String> getUserHeadImages() {
		return userHeadImages;
	}

	public void setUserHeadImages(List<String> userHeadImages) {
		this.userHeadImages = userHeadImages;
	}
}
