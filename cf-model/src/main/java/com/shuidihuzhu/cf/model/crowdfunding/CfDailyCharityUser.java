package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.CfDailyCharityConstants;

import java.util.Date;

public class CfDailyCharityUser {
	private Integer id;

	private long userId;

	private String openId;

	private Boolean valid;

	private Boolean isSubscribe;

	private Date createTime;

	private Date lastModified;
	private Boolean isPush;
	private int dayOfWeek;
	private int pushPeriodType = CfDailyCharityConstants.PushPeriodTypeEnums.ONCE_A_WEEK.getCode();

	public CfDailyCharityUser(Integer id, Long userId, String openId, Boolean valid, Boolean isSubscribe, Date createTime,
	                          Date lastModified, Boolean isPush, Integer dayOfWeek, Integer pushPeriodType) {
		this.id = id;
		this.userId = userId;
		this.openId = openId;
		this.valid = valid;
		this.isSubscribe = isSubscribe;
		this.createTime = createTime;
		this.lastModified = lastModified;
		this.isPush = isPush;
		this.dayOfWeek = dayOfWeek;
		this.pushPeriodType = pushPeriodType;
	}

	public CfDailyCharityUser() {
		super();
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public long getUserId() {
		return userId;
	}

	public void setUserId(long userId) {
		this.userId = userId;
	}

	public String getOpenId() {
		return openId;
	}

	public void setOpenId(String openId) {
		this.openId = openId == null ? null : openId.trim();
	}

	public Boolean getValid() {
		return valid;
	}

	public void setValid(Boolean valid) {
		this.valid = valid;
	}

	public Boolean getIsSubscribe() {
		return isSubscribe;
	}

	public void setIsSubscribe(Boolean isSubscribe) {
		this.isSubscribe = isSubscribe;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getLastModified() {
		return lastModified;
	}

	public void setLastModified(Date lastModified) {
		this.lastModified = lastModified;
	}

	public Boolean getIsPush() {
		return isPush;
	}

	public void setIsPush(Boolean isPush) {
		this.isPush = isPush;
	}

	public void setDayOfWeek(int dayOfWeek) {
		this.dayOfWeek = dayOfWeek;
	}

	public int getDayOfWeek() {
		return dayOfWeek;
	}

	public int getPushPeriodType() {
		return pushPeriodType;
	}

	public CfDailyCharityConstants.PushPeriodTypeEnums getPushPeriod() {
		return CfDailyCharityConstants.PushPeriodTypeEnums.valueOfCode(pushPeriodType);
	}

	public CfDailyCharityUser setPushPeriodType(int pushPeriodType) {
		this.pushPeriodType = pushPeriodType;
		return this;
	}
}
