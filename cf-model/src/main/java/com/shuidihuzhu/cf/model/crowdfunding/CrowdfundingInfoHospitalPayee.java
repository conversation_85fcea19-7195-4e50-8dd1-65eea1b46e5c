package com.shuidihuzhu.cf.model.crowdfunding;


import com.shuidihuzhu.cf.client.base.mask.NumberMaskVo;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;

/**
 * Created by ni<PERSON>iangnan on 2017/9/26.
 */

public class CrowdfundingInfoHospitalPayee {
    private int id;
    private String infoUuid;
    private int relationType;
    private String department;
    private String bedNum;
    private String hospitalizationNum;
    private String hospitalAccountName;
    private String hospitalBankCard;
    private NumberMaskVo hospitalBankCardMask;
    private String hospitalBankBranchName;
    /**
     * 对公紧急联系人姓名
     */
    private String hospitalEmergencyName = "";
    /**
     * 对公紧急联系人电话号
     */
    private String hospitalEmergencyPhone = "";
    private NumberMaskVo hospitalEmergencyPhoneMask;
    private int caseId;
    private Timestamp createTime;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getInfoUuid() {
        return infoUuid;
    }

    public void setInfoUuid(String infoUuid) {
        this.infoUuid = infoUuid;
    }

    public int getRelationType() {
        return relationType;
    }

    public void setRelationType(int relationType) {
        this.relationType = relationType;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getBedNum() {
        return bedNum;
    }

    public void setBedNum(String bedNum) {
        this.bedNum = bedNum;
    }

    public String getHospitalizationNum() {
        return hospitalizationNum;
    }

    public void setHospitalizationNum(String hospitalizationNum) {
        this.hospitalizationNum = hospitalizationNum;
    }

    public String getHospitalAccountName() {
        return hospitalAccountName;
    }

    public void setHospitalAccountName(String hospitalAccountName) {
        this.hospitalAccountName = hospitalAccountName;
    }

    public String getHospitalBankCard() {
        return hospitalBankCard;
    }

    public NumberMaskVo getHospitalBankCardMask() {
        return hospitalBankCardMask;
    }

    public void setHospitalBankCard(String hospitalBankCard) {
        this.hospitalBankCard = hospitalBankCard;
    }

    public void setHospitalBankCardMask(NumberMaskVo hospitalBankCardMask) {
        this.hospitalBankCardMask = hospitalBankCardMask;
    }

    public String getHospitalBankBranchName() {
        return hospitalBankBranchName;
    }

    public void setHospitalBankBranchName(String hospitalBankBranchName) {
        this.hospitalBankBranchName = hospitalBankBranchName;
    }

    public NumberMaskVo getHospitalEmergencyPhoneMask() {
        return hospitalEmergencyPhoneMask;
    }

    public void setHospitalEmergencyPhoneMask(NumberMaskVo hospitalEmergencyPhoneMask) {
        this.hospitalEmergencyPhoneMask = hospitalEmergencyPhoneMask;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public int getCaseId() {
        return caseId;
    }

    public void setCaseId(int caseId) {
        this.caseId = caseId;
    }

    public String getHospitalEmergencyName() {
        return hospitalEmergencyName;
    }

    public void setHospitalEmergencyName(String hospitalEmergencyName) {
        this.hospitalEmergencyName = hospitalEmergencyName;
    }

    public String getHospitalEmergencyPhone() {
        return hospitalEmergencyPhone;
    }

    public void setHospitalEmergencyPhone(String hospitalEmergencyPhone) {
        this.hospitalEmergencyPhone = hospitalEmergencyPhone;
    }


    public String replaceSpecialString(String string){
        if (StringUtils.isEmpty(string)) {
            return string;
        }
        return string.replaceAll("•"," ").replaceAll("——","-");
    }
}
