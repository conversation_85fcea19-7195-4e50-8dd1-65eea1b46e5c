package com.shuidihuzhu.cf.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * Created by dongcf on 2020/7/21
 */
@ApiModel("案例自定义关系")
@Data
public class CfCustomRelation {


    private long id;

    @ApiModelProperty("案例id")
    private long caseId;

    /**
     * {@link com.shuidihuzhu.cf.enums.crowdfunding.RelationShowTypeEnum}
     */
    @ApiModelProperty("展示类型")
    private int showType;

    /**
     * 与案例版本有关系
     * 3100版本 {@link com.shuidihuzhu.cf.enums.crowdfunding.BaseInfoTemplateConst.CfBaseInfoRelationshipEnum}
     * 其他版本 {@link com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum}
     */
    @ApiModelProperty("关系类型")
    private int relationType;

    @ApiModelProperty("关系描述")
    private String relationDesc;

    @ApiModelProperty("备注")
    private String remark;

    @JsonIgnore
    @ApiModelProperty("操作人用户ID")
    private int operateUserId;

    @ApiModelProperty("创建时间")
    private Date createTime;
}
