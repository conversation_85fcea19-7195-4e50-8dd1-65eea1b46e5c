package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.ForwardingGuidelineEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/10  15:17
 * @see ForwardingGuidelineEnum
 */
@Data
public class ForwardingGuidelineTwoModel {

    private int forwardingGuideline;

    private List<ForwardingGuidelineTwoInfo> forwardingGuidelineTwoInfos;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ForwardingGuidelineTwoInfo {
        @ApiModelProperty("头像")
        private String avatar;
        @ApiModelProperty("最多展示7个字，超过用...代替")
        private String nickname;
        @ApiModelProperty("取该用户对该案例所有转发带来的所有下一步用户捐款金额总和")
        private Double forwardingAmount;
    }

}
