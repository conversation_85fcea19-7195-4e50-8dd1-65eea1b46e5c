package com.shuidihuzhu.cf.domain.caseinfo;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 */
@Data
public class CaseEndRecordDO {

    private long id;

    private int caseId;

    /**
     * {@link com.shuidihuzhu.cf.enums.crowdfunding.CfFinishStatus}
     */
    private int finishStatus;

    @Length(max = 256)
    private String description;

    private int operatorId;

    @Length(max = 64)
    private String operatorName;

    /**
     * 结束原因类型
     */
    private int reasonType;

    /**
     * 创建时间
     */
    private String createTime;
}
