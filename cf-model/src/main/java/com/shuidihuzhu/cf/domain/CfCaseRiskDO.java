package com.shuidihuzhu.cf.domain;

import com.shuidihuzhu.cf.constants.CaseRiskConstants;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2018-08-04  10:26
 * 筹款审核风险DO
 *
 */
@Data
public class CfCaseRiskDO {

    private long id;

    private String infoUuid;

    /**
     * 是否已验证
     * @see com.shuidihuzhu.cf.enums.crowdfunding.risk.CfCaseRiskVerifiedEnum
     */
    private int verified;

    /**
     * 是否通过
     * @see com.shuidihuzhu.cf.enums.crowdfunding.risk.CaseRiskPassedEnum
     */
    private int passed;

    /**
     * 类型
     * @see com.shuidihuzhu.cf.enums.crowdfunding.risk.CfCaseRiskTypeEnum
     */
    private int riskType;

    /**
     * @see com.shuidihuzhu.cf.constants.CaseRiskConstants.Risk
     */
    private int risk;

    private String riskData;

    /**
     * @see CaseRiskConstants.CaseInfo
     */
    private int caseInfoPassed;

    /**
     * @see CaseRiskConstants.Period
     */
    private int delayPeriod;

    /**
     * 处理状态
     *
     * <a href="https://wiki.shuiditech.com/pages/viewpage.action?pageId=171508251">WIKI</a>
     * @see com.shuidihuzhu.cf.enums.crowdfunding.risk.StatRiskHandleStatusEnum
     */
    private int handleStatus;

    private Date createTime;

    private Date updateTime;
}

