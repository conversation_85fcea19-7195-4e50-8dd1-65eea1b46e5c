package com.shuidihuzhu.cf.domain.ugc;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * cf_ugc_operate_record
 * <AUTHOR>
@Data
public class CfUgcOperateRecord implements Serializable {
    /**
     * id
     */
    private Long id;

    /**
     * 案例id
     */
    private Long caseId;

    /**
     * biz_id
     */
    private Long bizId;

    /**
     * biz_type
     */
    private Integer bizType;

    /**
     * 操作类型
     */
    private Integer operateType;

    /**
     * 操作内容
     */
    private String content;

    /**
     * 操作人id
     */
    private Integer operatorId;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 部门
     */
    private String department;

    /**
     * 是否逻辑删除 0有效，1删除
     */
    private Integer isDelete;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}