package com.shuidihuzhu.cf.domain.toG;

import com.shuidihuzhu.cf.enums.toG.CfToGoMarkRecordEnum;
import lombok.Data;

import java.util.Date;

/**
 * @Description: 缙云案例打标记录
 * @Author: pangh<PERSON><PERSON>
 * @Date: 2022/3/18 3:11 下午
 */
@Data
public class CfJinYunMarkRecord {

    private long id;

    /**
     * 案例id
     */
    private int caseId;

    /**
     * 案例uuid
     */
    private String infoId;

    /**
     * 案例发起时间
     */
    private Date cfCreateTime;

    /**
     * 核实状态
     * @see com.shuidihuzhu.cf.enums.toG.CfToGVerifyStatusEnum
     */
    private int verifyStatus;

    /**
     * 打标方式 0系统 1运营
     * @see CfToGoMarkRecordEnum
     */
    private int markType;

    /**
     * 初审是否通过
     * @see com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum
     */
    private int firstApproveStatus;

    /**
     * 案例是否结束
     * @see com.shuidihuzhu.cf.enums.crowdfunding.CfFinishStatus
     */
    private int finishStatus;

    /**
     * 患者姓名
     */
    private String patientName;

    /**
     * 发起人手机号
     */
    private String encryptMobile;

    private Date createTime;

    private Date updateTime;
}
