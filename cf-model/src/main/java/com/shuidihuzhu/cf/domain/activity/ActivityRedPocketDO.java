package com.shuidihuzhu.cf.domain.activity;

import com.shuidihuzhu.cf.enums.activity.ActivityRedPocketStatusEnum;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;

import java.util.Date;

/**
 * Created by sven on 2020/4/25.
 *
 * <AUTHOR>
 */
@Data
public class ActivityRedPocketDO {

    private long id;
    private int caseId;
    private int money;
    private ActivityRedPocketStatusEnum status;
    private boolean needAlert;
    private boolean sendResult;
    private Date startTime;
    private Date endTime;
    private int currentValue;

    public double getMoneyInDouble(){
        return money/100D;
    }

    public double getCurrentValueInDouble(){
        return currentValue/ 100D;
    }
}
