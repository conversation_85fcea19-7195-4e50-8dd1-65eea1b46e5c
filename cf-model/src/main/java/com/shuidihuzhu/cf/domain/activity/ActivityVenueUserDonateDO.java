package com.shuidihuzhu.cf.domain.activity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @author: fengxuan
 * @create 2020-02-20 16:14
 * 活动主会场用户捐款信息
 **/
@Data
@NoArgsConstructor
public class ActivityVenueUserDonateDO {

    private long id;

    private long userId;

    private int caseId;

    private String infoId;
    /**
     * 主会场捐款金额
     */
    private int amount;
    /**
     * 活动id
     */
    private int activityId;
    /**
     * 订单号
     */
    @JsonIgnore
    private long orderId;

    /**
     *@see com.shuidihuzhu.common.web.enums.PayStatus
     * 1:支付成功，0:未支付
     */
    private int payStatus;

    private Date createTime;

    public ActivityVenueUserDonateDO(long userId, int caseId, String infoId, int amount, int activityId, long orderId) {
        this.userId = userId;
        this.caseId = caseId;
        this.infoId = infoId;
        this.amount = amount;
        this.activityId = activityId;
        this.orderId = orderId;
    }
}
