package com.shuidihuzhu.cf.domain.dedicated;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: wanghui
 * @create: 2019/1/3 8:24 PM
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CfServiceStaffDO {
    private Integer id;  //'唯一id',
    private String qyWechatUserId;  // '企业微信userid',
    private String name;  // 服务人员名
    private String headUrl;  // 头像url
    private String labels;  // 标签
    private String qrCode;  //'二维码',
    private Integer type;  // '类型0=1v1服务客服 2=专属客服',
    private Integer helpPatients;  // '帮助患者数',
    private Integer raiseAmount;  // '筹款金额',  单位 元
    private String createTime;  //'创建时间',
    private String updateTime;  // '更新时间',
    private Integer isDelete;  // '是否删除',
    private String restTime; // 休息时间
    private String favorableRate;//好评率
}
