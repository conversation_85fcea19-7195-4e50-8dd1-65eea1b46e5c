package com.shuidihuzhu.cf.domain.risk;

import com.shuidihuzhu.cf.enums.risk.UgcTypeEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019-04-10  19:47
 */
@Data
public class RiskChannelUgcRecordDO {

    private long id;

    /**
     * @see UgcTypeEnum#getValue()
     */
    private int code;

    /**
     * 根据code不同对应不同的ugcId eg: orderId, commentId ...
     */
    private long bizId;

    /**
     * 渠道
     */
    private String channel;

}
