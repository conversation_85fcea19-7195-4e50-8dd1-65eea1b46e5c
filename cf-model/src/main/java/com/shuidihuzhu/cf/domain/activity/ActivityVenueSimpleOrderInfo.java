package com.shuidihuzhu.cf.domain.activity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * Created by sven on 2020/4/4.
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActivityVenueSimpleOrderInfo {

    private long orderId;
    private int caseId;
    private Date cTime;
    private long userId;
}
