package com.shuidihuzhu.cf.domain;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2018-08-28  17:43
 */
@NoArgsConstructor
@Data
public class TimeLineDO {

    private long id;

    private long caseId;

    private int operateType;

    private String description;

    private long userId;

    private String stackTrace;

    private  String ip;

    private String traceId;

    private String content;

    private Date createTime;

    private Date updateTime;

    public TimeLineDO(long caseId, int operateType, String content) {
        this.caseId = caseId;
        this.operateType = operateType;
        this.content = content;
    }
}
