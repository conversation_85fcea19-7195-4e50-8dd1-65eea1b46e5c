package com.shuidihuzhu.cf.domain.yiqing;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created by sven on 2020/1/24.
 *
 * <AUTHOR>
 */
@Data
@ApiModel("历天疫情统计数据")
public class YiqingHistory {

    private long id;

    @ApiModelProperty("日期")
    private int dayValue;

    @ApiModelProperty("对应dayvalue那天的23:59:59的时间戳")
    private long timestamp;

    @ApiModelProperty("确诊数量")
    private int confirmedCount;

    @ApiModelProperty("疑似数量")
    private int suspectedCount;

    @ApiModelProperty("死亡数量")
    private int deathCount;

    @ApiModelProperty("治愈数量")
    private int curedCount;
}
