package com.shuidihuzhu.cf.domain.material;

import com.shuidihuzhu.cf.client.material.model.PropertyInsuranceCaseType;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum;
import com.shuidihuzhu.cf.model.river.RiverStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CaseLifeApproveStageDO {

    @ApiModelProperty("案例id")
    private int caseId;

    private String infoUuid;

    private String title;

    private String titleImg;

    private String content;

    @ApiModelProperty("案例类型")
    private PropertyInsuranceCaseType caseType;

    @ApiModelProperty("初审状态")
    private FirstApproveStatusEnum initialStatus;

    @ApiModelProperty("材料审核状态")
    private CrowdfundingStatus materialStatus;

    @ApiModelProperty("增信审核状态")
    private RiverStatusEnum insuranceStatus;
}
