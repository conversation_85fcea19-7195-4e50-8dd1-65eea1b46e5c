package com.shuidihuzhu.cf.domain.activity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 111活动盘点 反馈数据model
 * <AUTHOR>
 */
@Data
public class SummaryLetterDO {

    private long id;

    @ApiModelProperty("案例id")
    private int caseId;

    @ApiModelProperty("反馈内容")
    private String content;

    @ApiModelProperty("图片url")
    private String imageUrl;

    @ApiModelProperty("地区")
    private String area;

    @ApiModelProperty("筹款人姓名")
    private String name;

    @ApiModelProperty("筹款人头像")
    private String headImageUrl;

    @ApiModelProperty("是否是头部内容")
    private boolean headCase;

    @ApiModelProperty("提现成功时间")
    private Date applyFinishTime;

    @ApiModelProperty("提现金额(分)")
    private long applyAmount;

    @ApiModelProperty("反馈类型(1: 尾部反馈, 2: 头部反馈, 3: 平台反馈)")
    private int letterType;

    private Date createTime;

    @JsonIgnore
    private Date shortTime;

}
