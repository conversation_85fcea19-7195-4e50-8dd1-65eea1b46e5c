package com.shuidihuzhu.cf.domain.activity;

import lombok.Data;

import java.util.Date;

/**
 * Created by sven on 2020/5/6.
 *
 * <AUTHOR>
 */
@Data
public class ActivityDonatorRedPocketDO {

    private long id;
    private int caseId;
    private int totalValue;
    //0==已拿到，1==正在拿
    private int status;
    private int needUserCount;
    private int remainUserCount;
    private Date createTime;
    private Date updateTime;
}
