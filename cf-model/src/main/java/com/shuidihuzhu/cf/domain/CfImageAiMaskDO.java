package com.shuidihuzhu.cf.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.Date;

/**
 * @Description: AI掩码图片信息
 * @Author: pan<PERSON><PERSON><PERSON>
 * @Date: 2022/9/8 4:27 下午
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CfImageAiMaskDO {

    private Integer caseId;

    /**
     * AI掩码后的图片
     */
    private String imageUrlAi;

    /**
     * 图片处理状态 1:未操作;2:已掩码;3:无需掩码;4:不对外公示
     */
    private Integer imageHandleStatus;

    /**
     * 业务类型 ImageMaskBizEnum
     */
    private Integer bizType;

    /**
     * 业务id
     */
    private Long bizId;

    /**
     * AI处理完成时间
     */
    private Timestamp finishAiTime;

    /**
     * 创建时间
     */
    private Date createTime;

    private Integer isDelete;

}
