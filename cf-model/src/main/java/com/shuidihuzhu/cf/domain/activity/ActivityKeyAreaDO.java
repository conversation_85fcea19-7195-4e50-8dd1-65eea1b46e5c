package com.shuidihuzhu.cf.domain.activity;

import com.shuidihuzhu.cf.activity.feign.CfRuleLevelFeignClient;
import com.shuidihuzhu.cf.activity.model.DonateCooperateRuleLevelModel;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.enums.activity.ActivityKeyAreaTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ActivityKeyAreaDO {

    private long id;
    private long waveId;
    private int caseId;
    private long activityId;
    /**
     * {@link com.shuidihuzhu.cf.enums.activity.ActivityKeyAreaTypeEnum}
     */
    private int subsidyType;

    /**
     * 暂时没用到
     */
    private long cityId;

    /**
     * 记录补贴次数 修改type后重置
     */
    private int subsidyCount;

    /**
     * 是否达到提现门槛
     */
    private boolean reachedThreshold;

    @ApiModelProperty("案例软帽")
    private int caseSoftAmountLimit;

    @ApiModelProperty("规则类型")
    private int ruleType;

    public double getCaseSoftAmountLimitInDouble(CfRuleLevelFeignClient cfRuleLevelFeignClient) {
        if (caseSoftAmountLimit < 0) {
            // 使用软帽处暂不支持判断-1为无软帽。所以这里用返回一个大值替代
            return 10_0000_00D;
        }
        if (caseSoftAmountLimit == 0) {
            RpcResult<DonateCooperateRuleLevelModel> ruleLevel = cfRuleLevelFeignClient.getByCode(getSubsidyType());
            if (ruleLevel.isSuccess()){
                DonateCooperateRuleLevelModel data = ruleLevel.getData();
                return data.getCaseSoftAmountLimit();
            }
        }
        return caseSoftAmountLimit / 100D;
    }
}
