package com.shuidihuzhu.cf.domain.toG.hainan;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class CfHainanShowRecord {
    private long id;

    /**
     * 案例id
     */
    private int caseId;

    /**
     * 案例uuid
     */
    private String infoId;

    /**
     * 案例发起时间
     */
    private Date cfCreateTime;

    /**
     * 初审状态
     */
    private int firstApproveStatus;

    /**
     * 案例是否结束
     * @see com.shuidihuzhu.cf.enums.crowdfunding.CfFinishStatus
     */
    private int finishStatus;

    /**
     * 入库类型
     * @see com.shuidihuzhu.cf.enums.toG.hainan.CfHainanShowTypeEnum
     */
    private int showType;

    /**
     * 患者姓名
     */
    private String patientName;

    /**
     * 发起人手机号
     */
    private String encryptMobile;

    private Date createTime;

    private Date updateTime;
}
