package com.shuidihuzhu.cf.domain.activity;

import com.shuidihuzhu.cf.enums.activity.Activity111CaseTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by sven on 2020/4/4.
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActivityVenueSimpleCaseInfo {

    private int caseId;
    private String infoId;
    private int donateCount;
    private int amount;
    private int targetAmount;

    private String title;
    private String content;
    private String titleImg;


    private Activity111CaseTypeEnum typeEnum;
}
