package com.shuidihuzhu.cf.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2018-08-28  17:43
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RemarkDO {

    private long id;

    private long caseId;

    /**
     * @see com.shuidihuzhu.cf.constants.remark.RemarkTypeConsts
     */
    private int remarkType;

    private String description;

    private long operatorId;

    private String stackTrace;

    private  String ip;

    private String traceId;

    private String content;

    private Date createTime;

    private Date updateTime;

}
