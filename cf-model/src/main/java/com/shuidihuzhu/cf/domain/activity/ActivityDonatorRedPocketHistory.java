package com.shuidihuzhu.cf.domain.activity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * Created by sven on 2020/5/10.
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActivityDonatorRedPocketHistory {
    private long id;
    private int caseId;
    private long userId;
    private long pocketId;
    private Date createTime;
    private Date updateTime;
}
