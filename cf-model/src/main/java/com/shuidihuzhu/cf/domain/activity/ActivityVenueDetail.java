package com.shuidihuzhu.cf.domain.activity;

import com.shuidihuzhu.cf.enums.activity.Activity111CaseTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;


/**
 * Created by sven on 2020/3/24.
 *
 * <AUTHOR>
 */
@Data
public class ActivityVenueDetail {

    @ApiModelProperty("主会场的id")
    private long id;
    private Date startTime;
    private Date endTime;
    private boolean status;
    private long version;
    @ApiModelProperty("此次主会场的最高金额")
    private int targetAmount;
    @ApiModelProperty("对应配捐配转活动id")
    private int cooperateActivityId;

    @ApiModelProperty("类别")
    private Activity111CaseTypeEnum typeEnum;

    private Date createTime;
    private Date updateTime;
}
