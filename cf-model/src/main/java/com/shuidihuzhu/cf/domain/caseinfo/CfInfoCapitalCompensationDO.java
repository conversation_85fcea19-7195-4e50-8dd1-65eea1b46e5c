package com.shuidihuzhu.cf.domain.caseinfo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

/**
*  资金补偿信息
* <AUTHOR> 2019-04-18
*/
@Data
public class CfInfoCapitalCompensationDO {

    /**
    * id
    */
    @JsonIgnore
    private long id;

    /**
    * 筹款案例id
    */
    @JsonIgnore
    private int caseId;

    /**
     * @see com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoStatusEnum
    */
    private int status;

    /**
    * 是否有其他组织救助
     * 0 default 1 yes 2 no
    */
    private int otherOrgHelpHave;

    /**
    * 其他组织救助金额
    */
    private String otherOrgHelp;

    /**
    * 是否有其他平台筹款
    */
    private int otherPlatformFundraisingHave;

    /**
    * 其他平台筹款金额
    */
    private String otherPlatformFundraising;

    /**
    * 是否有其他社会赠与
    */
    private int otherSocialGiftHave;

    /**
    * 其他社会赠与金额
    */
    private String otherSocialGift;

    /**
    * 是否有治疗欠款
    */
    private int treatmentArrearsHave;

    /**
    * 治疗欠款金额
    */
    private String treatmentArrears;

    /**
    * 是否有自己负担的治疗费用
    */
    private int ownTreatmentFeeHave;

    /**
    * 自己负担的治疗费用金额
    */
    private String ownTreatmentFee;

    /**
    * 图片
    */
    private String images;

}