package com.shuidihuzhu.cf.domain.dedicated;

import lombok.Data;
import lombok.Getter;

import java.util.Date;


@Data
public class CfToufangInviteCaseRelationDO {
    private Integer id;
    private Date createTime;
    private Date updateTime;
    /**
     * 邀请者唯一标识
     */
    private String invitorUniqueCode;
    /**
     * 案例的infoUuid
     */
    private String infoUuid;
    /**
     * 是否达标标识 0:未达标 1:已达标 2:已过期(超过了激励时间段，案例筹款数据仍未达标)
     */
    private Integer completeFlag;

    /**
     * 是否已推送达标奖励标识 0:未推送 1:已推送
     */
    private Integer taskFlag;
    /**
     * 激励过期时间
     */
    private Date expireTime;

    /**
     * @see InvitorTypeEnum
     * 推荐人类型
     */
    private int invitorType;

    //筹款人作为推荐人时的案例
    private int invitorCaseId;
    @Getter
    public enum InvitorTypeEnum {
        normal(0, "非在筹人员"),
        raiser(1, "在筹人员"),
        ;
        private int code;

        private String desc;

        InvitorTypeEnum(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }
}
