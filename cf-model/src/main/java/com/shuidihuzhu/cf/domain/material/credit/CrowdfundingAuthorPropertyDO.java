package com.shuidihuzhu.cf.domain.material.credit;

import com.google.common.base.Preconditions;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.material.credit.CreditSupplementModel;
import lombok.Data;

/**
 * Created by sven on 18/11/16.
 *
 * <AUTHOR>
 */
@Data
public class CrowdfundingAuthorPropertyDO {

    private long id;

    //案例id
    private int caseId;

    //商业保险 0:没有1:有
    private Integer commercialInsurance;

    //医疗保险 0:没有1:有
    private Integer healthInsurance;

    //低保 0:没有1:有
    private Integer livingSecurity;

    //政府救助 0:没有1:有
    private Integer govRelief;

    //家庭年收入
    private Integer homeIncome;

    //家庭金融信息
    private Integer homeStock;

    //低保情况说明
    private String livingSecurityText;

    //低保情况说明
    private String govReliefText;

    public static CrowdfundingAuthorPropertyDO convert(CreditSupplementModel creditSupplementModel, CrowdfundingInfo crowdfundingInfo){
        Preconditions.checkNotNull(creditSupplementModel);
        Preconditions.checkNotNull(crowdfundingInfo);

        CrowdfundingAuthorPropertyDO crowdfundingAuthorPropertyDO = new CrowdfundingAuthorPropertyDO();
        crowdfundingAuthorPropertyDO.setCaseId(crowdfundingInfo.getId());
        crowdfundingAuthorPropertyDO.setHealthInsurance(creditSupplementModel.getHealthInsurance());
        crowdfundingAuthorPropertyDO.setCommercialInsurance(creditSupplementModel.getCommercialInsurance());
        crowdfundingAuthorPropertyDO.setGovRelief(creditSupplementModel.getGovRelief());
        crowdfundingAuthorPropertyDO.setGovReliefText(creditSupplementModel.getGovReliefText());
        crowdfundingAuthorPropertyDO.setHomeIncome(creditSupplementModel.getHomeIncome());
        crowdfundingAuthorPropertyDO.setHomeStock(creditSupplementModel.getHomeStock());
        crowdfundingAuthorPropertyDO.setLivingSecurity(creditSupplementModel.getLivingSecurity());
        crowdfundingAuthorPropertyDO.setLivingSecurityText(creditSupplementModel.getLivingSecurityText());

        return crowdfundingAuthorPropertyDO;
    }
}
