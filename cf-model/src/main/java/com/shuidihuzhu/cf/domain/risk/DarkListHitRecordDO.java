package com.shuidihuzhu.cf.domain.risk;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class DarkListHitRecordDO {

    private long id;
    private long userId;

    /**
     * {@link com.shuidihuzhu.cf.risk.model.enums.risk.LimitActionEnum}
     */
    private int actionType;

    /**
     * 命中结果元数据
     */
    private String hitResultJson;

    private Date createTime;
}
