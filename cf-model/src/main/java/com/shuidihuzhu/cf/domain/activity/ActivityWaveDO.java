package com.shuidihuzhu.cf.domain.activity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class ActivityWaveDO {

    private long id;
    private long activityId;
    /**
     * {@link com.shuidihuzhu.cf.enums.activity.ActivityWaveTypeEnum}
     */
    private int waveType;
    /**
     * {@link com.shuidihuzhu.cf.enums.activity.wave.WaveJoinTypeEnum}
     */
    @ApiModelProperty("参加方式{1: 初审自动加入,2: 机器人申请}")
    private int joinType;
    /**
     * {@link com.shuidihuzhu.cf.enums.activity.wave.WaveScopeEnum}
     */
    @ApiModelProperty("参加范围{1: all,11: province, 12: city, 13: hospital}")
    private int scope;


    private Date startTime;
    private Date endTime;

    /**
     * 提现门槛
     */
    @ApiModelProperty("补贴金额提现门槛 单位分")
    private int threshold;
    @ApiModelProperty("捐款金额们门槛 单位分")
    private int donateMoneyThreshold;
    @ApiModelProperty("捐单次数门槛")
    private int donateCountThreshold;

    /**
     * key area 硬帽 案例最大补贴数量限制(非金额)
     */
    private int caseHardAmountLimit;

    private int cityId;

    /**
     * 仅admin查询会返回值
     */
    private String cityName;

    @ApiModelProperty("最大参加案例数 -1表示不限制")
    private int maxPlace;

    @ApiModelProperty("当前参加案例数")
    private int currentPlace;

    @ApiModelProperty("每月名额")
    private int monthlyPlace;

    @ApiModelProperty("医院唯一标示")
    private String hospitalCode;

    @ApiModelProperty("医院名")
    private String hospitalName;

    /**
     * @deprecated 业务需求字段改为落到活动补贴策略维度
     * todo delete 老配置过期后删除
     */
    @Deprecated
    @ApiModelProperty("任务补贴 任务组id")
    private long taskGroupId;

    @ApiModelProperty("案例软帽")
    private int caseSoftAmountLimit;

    public double getCaseHardAmountLimitInDouble() {
        return caseHardAmountLimit / 100D;
    }

    @ApiModelProperty("是否在活动时间中")
    public Boolean getStatus() {
        Date date = new Date();
        return date.after(startTime) && date.before(endTime);
    }
}
