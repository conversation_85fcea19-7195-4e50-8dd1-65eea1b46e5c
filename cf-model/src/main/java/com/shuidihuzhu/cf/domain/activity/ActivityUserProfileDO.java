package com.shuidihuzhu.cf.domain.activity;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class ActivityUserProfileDO {

    private long id;

    private long userId;

    private int profileType;

    private int profileValue;

    private Date createTime;

    private Date updateTime;

    public ActivityUserProfileDO(long userId, int profileType, int profileValue) {
        this.userId = userId;
        this.profileType = profileType;
        this.profileValue = profileValue;
    }
}
