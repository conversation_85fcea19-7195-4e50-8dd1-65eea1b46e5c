package com.shuidihuzhu.cf.domain.toG;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author: wangpeng
 * @Date: 2022/3/19 16:14
 * @Description:
 */
@Data
public class CfJinYunMarkRecordModel {
    /**
     * 案例uuid
     */
    private String infoId;

    /**
     * 案例发起时间
     */
    private Date cfCreateTime;

    /**
     * 核实状态
     */
    private int verifyStatus;

    /**
     * 打标方式
     */
    private int markType;

    /**
     * 初审状态
     */
    private int firstApproveStatus;

    /**
     * 案例是否结束
     */
    private int finishStatus;

    /**
     * 标题
     */
    private String title;

    /**
     * 筹款金额,单位:分
     */
    private int targetAmount;

    /**
     * 已筹金额,单位:分
     */
    private int amount;

    /**
     * 标签列表
     */
    private List<String> labelList;

    /**
     * 头图
     */
    private String titleImg;
}
