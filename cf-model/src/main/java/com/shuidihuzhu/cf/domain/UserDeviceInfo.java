package com.shuidihuzhu.cf.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Description: 用户设备信息
 * @Author: pan<PERSON><PERSON><PERSON>
 * @Date: 2023/1/11 4:49 下午
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserDeviceInfo {

    private Long userId;

    /**
     * 二级清单标识
     */
    private String identification;

    /**
     * 设备内容
     */
    private String content;

    private Date createTime;

}
