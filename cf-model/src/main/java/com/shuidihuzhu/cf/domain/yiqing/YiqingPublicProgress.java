package com.shuidihuzhu.cf.domain.yiqing;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * Created by sven on 2020/1/26.
 *
 * <AUTHOR>
 */
@Data
@ApiModel("官方通告")
public class YiqingPublicProgress {

    private long id;

    @ApiModelProperty("通告日期")
    private String progressDate;

    @ApiModelProperty("通告时间")
    private String progressTime;

    @ApiModelProperty("通告来源")
    private String progressSource;

    @ApiModelProperty("通告内容")
    private String content;
    private Date createTime;
    private Date updateTime;

    @ApiModelProperty("是否有视频，0为没有，1为有0")
    private int hasVedio;

    @ApiModelProperty("视频地址")
    private String vedioUrl;

    @ApiModelProperty("操作人姓名")
    private String operatorName;
}
