package com.shuidihuzhu.cf.domain.dedicated;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: wanghui
 * @create: 2019/1/3 8:29 PM
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CfServiceStaffFriendDO {
    /**
     *  唯一id
     */
    private Integer id;
    /**
     * v
     */
    private String qyWechatUserId;
    /**
     * 普通用户的userid
     */
    private Long userId;
    /**
     * 企业微信unionid
     */
    private String unionId;
    /**
     * 是否申请(步骤状态使用字段) 0=未申请 1=申请
     */
    private Integer isApply;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新时间
     */
    private String updateTime;
    /**
     * 是否删除
     */
    private Integer isDelete;
    /**
     * 微信userid
     */
    private String externalUserid;
    /**
     * 用户的手机号，企业微信外部联系人 的 remark中提取的
     */
    private String phone;

    /**
     * 好友添加时间
     */
    private String passTime;
}
