package com.shuidihuzhu.cf.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 新式分页对象
 *
 * <AUTHOR>
 * @since 2021-09-08 8:40 下午
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class NewPageVo<T> {

    private boolean hasNext;

    private long headId;

    private long tailId;

    private List<T> pageList;
}
