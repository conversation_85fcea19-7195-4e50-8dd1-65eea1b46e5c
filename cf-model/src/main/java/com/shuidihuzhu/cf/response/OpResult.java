package com.shuidihuzhu.cf.response;

import com.google.common.base.Preconditions;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.common.web.enums.MyErrorCode;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2018-05-14  14:17
 */
public final class OpResult<T> implements Serializable {

    private static final long serialVersionUID = 3365273740561316434L;

    public static <T> OpResult<T> createResult(boolean isSuccess, CfErrorCode code, T data) {
        return isSuccess ? createSucResult(data) : createFailResult(code, data);
    }

    /**
     * eg:         return OpResult.createStorageResult(dao.insert(v) > 0, v);
     * 创建数据库操作执行结果
     * @param isSuccess
     * @param <T>
     * @return
     */
    public static <T> OpResult<T> createStorageResult(boolean isSuccess, T data){
        OpResult<T> r = createResult(isSuccess, CfErrorCode.STORAGE_ERROR, data);
        r.buildMessage(isSuccess ? "" : "数据库操作异常");
        return r;
    }

    public static <T> OpResult<T> createSucResult() {
        return new OpResult<T>(CfErrorCode.SUCCESS);
    }

    public static <T> OpResult<T> createSucResult(T data) {
        return new OpResult<>(CfErrorCode.SUCCESS, data);
    }

    public static <T> OpResult<T> createFailResult(MyErrorCode errorCode) {
        Preconditions.checkNotNull(errorCode);
        Preconditions.checkArgument(errorCode != CfErrorCode.SUCCESS);
        return new OpResult<>(errorCode);
    }

    public static <T> OpResult<T> createFailResult(MyErrorCode errorCode, String message) {
        Preconditions.checkNotNull(errorCode);
        Preconditions.checkArgument(errorCode != CfErrorCode.SUCCESS);
        OpResult<T> r = new OpResult<>(errorCode);
        r.buildMessage(message);
        return r;
    }

    public static <T> OpResult<T> createFailResult(MyErrorCode errorCode, T data) {
        Preconditions.checkNotNull(errorCode);
        Preconditions.checkArgument(errorCode != CfErrorCode.SUCCESS);
        return new OpResult<>(errorCode, data);
    }

    /**
     * 从其他opResult 获取code message
     * 丢弃 data
     * @param opResult
     * @param <T>
     * @return
     */
    public static <T> OpResult<T> createFromLastResult(OpResult opResult) {
        OpResult<T> v = opResult.isSuccess() ? createSucResult() : createFailResult(opResult.getErrorCode());
        v.buildMessage(opResult.getMessage());
        return v;
    }

    //-----------------------------------

    private OpResult() {
        //禁止实例化
    }

    public boolean isSuccess() {
        return errorCode == CfErrorCode.SUCCESS;
    }

    public boolean isFail() {
        return !isSuccess();
    }

    /**
     * 改名啦
     * @see #isSuccessWithNonNullData()
     * @return
     */
    @Deprecated
    public boolean isSuccessWithNotNullData() {
        return isSuccess()&& data != null;
    }

    public boolean isSuccessWithNonNullData() {
        return isSuccess()&& data != null;
    }

    public boolean isFailOrNullData() {
        return !isSuccess() || data == null;
    }

    public OpResult<T> buildMessage(String message){
        this.message = message;
        return this;
    }

    private OpResult(MyErrorCode errorCode) {
        this.errorCode = errorCode;
    }

    private OpResult(MyErrorCode errorCode, T data) {
        this.errorCode = errorCode;
        this.data = data;
    }

    @Getter
    private MyErrorCode errorCode;

    @Getter
    private T data;

    private String message;

    public String getMessage() {
        if (StringUtils.isEmpty(message)) {
            return errorCode.getMsg();
        }
        return message;
    }

    @Override
    public String toString() {
        return "OpResult{" +
                "errorCode=" + errorCode +
                ", data=" + data +
                ", message='" + message + '\'' +
                '}';
    }
}

