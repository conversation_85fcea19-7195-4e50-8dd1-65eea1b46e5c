package com.shuidihuzhu.cf.response.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import lombok.Data;

@Data
public class CreateInfoResultWrap extends CrowdfundingInfoResponse {
    private CrowdfundingInfo caseInfo;
    private CfInfoExt infoExt;
    private CfFirsApproveMaterial firstApproveMaterial;


    public static CrowdfundingInfoResponse convertToInfoResp(CrowdfundingInfoResponse resultWrap) {
        if (resultWrap == null) {
            return null;
        }
        CrowdfundingInfoResponse resp = new CrowdfundingInfoResponse();
        resp.setData(resultWrap.getData());
        resp.setErrorCode(resultWrap.getErrorCode());
        resp.setInfoUuid(resultWrap.getInfoUuid());
        resp.setMsg(resultWrap.getMsg());
        resp.setExtra(resultWrap.getExtra());
        resp.setShowSubmit(resultWrap.getShowSubmit());
        return resp;
    }
}
