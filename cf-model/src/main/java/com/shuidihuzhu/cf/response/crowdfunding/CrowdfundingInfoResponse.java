package com.shuidihuzhu.cf.response.crowdfunding;

import com.shuidihuzhu.cf.enums.CfErrorCode;
import org.apache.commons.lang.StringUtils;

public class CrowdfundingInfoResponse {

	private CfErrorCode errorCode;
	private Data data;

	private String msg;

	public CrowdfundingInfoResponse() {
		data = new Data();
	}

	public static class Data {

		private String infoUuid;

		private boolean showSubmit = false;

		private Object extra;

		public String getInfoUuid() {
			return infoUuid;
		}

		public void setInfoUuid(String infoUuid) {
			this.infoUuid = infoUuid;
		}

		public Object getExtra() {
			return extra;
		}

		public void setExtra(Object extra) {
			this.extra = extra;
		}

		public boolean isShowSubmit() {
			return showSubmit;
		}

		public void setShowSubmit(boolean showSubmit) {
			this.showSubmit = showSubmit;
		}
	}

	public CfErrorCode getErrorCode() {
		return errorCode;
	}

	public void setErrorCode(CfErrorCode errorCode) {
		this.errorCode = errorCode;
	}

	public Data getData() {
		return data;
	}

	public void setData(Data data) {
		this.data = data;
	}

	public void setInfoUuid(String infoUuid){
		if(this.data != null) {
			this.data.setInfoUuid(infoUuid);
		}
	}

	public String getInfoUuid() {
		return this.data == null ? "" : this.data.getInfoUuid();
	}

	public Object getExtra() {
		return this.data == null ? null : this.data.getExtra();
	}

	public void setExtra(Object extra) {
		if(this.data != null) {
			this.data.setExtra(extra);
		}
	}

	public void setShowSubmit(boolean showSubmit) {
		if(this.data != null) {
			this.data.setShowSubmit(showSubmit);
		}
	}

	public void setMsg(String msg){
		this.msg = msg;
	}

	public String getMsg(){
	    if (StringUtils.isNotEmpty(msg)) {
	    	return msg;
		}
	    if (StringUtils.equals(CfErrorCode.SUCCESS.getMsg(), errorCode.getMsg())) {
	    	return "";
		}
		return errorCode.getMsg();
	}

	public boolean getShowSubmit() {
		return this.data == null ? false : this.data.showSubmit;
	}
}
