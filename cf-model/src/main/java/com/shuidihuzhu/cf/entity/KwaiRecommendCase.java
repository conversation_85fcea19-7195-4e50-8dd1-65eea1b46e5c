package com.shuidihuzhu.cf.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.ValidationException;

/**
 * <AUTHOR>
 * @date 2022/8/4  11:22
 */
@Data
public class KwaiRecommendCase {
    @ApiModelProperty("发送验证码时使用的key")
    private String key;
    @ApiModelProperty("验证码")
    private String verifyCode;
    @ApiModelProperty("未加密手机号")
    private String mobile;
    @ApiModelProperty("访问人")
    private long userId;
    @ApiModelProperty("访问ip")
    private String clientIp;

    public KwaiRecommendCase(String key, String verifyCode, String mobile, long userId, String clientIp) {
        this.key = key;
        this.verifyCode = verifyCode;
        this.mobile = mobile;
        this.userId = userId;
        this.clientIp = clientIp;
    }
}
