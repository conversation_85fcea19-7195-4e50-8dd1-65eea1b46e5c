package com.shuidihuzhu.cf.constants;

/**
 * Created by sven on 2019/3/11.
 *
 * <AUTHOR>
 */
public final class AsyncPoolConstants {

    private AsyncPoolConstants() {
        //禁止实例化
    }

    /**
     * 专门刷库信息到redis的线程池
     */
    public static final String WRITE_SIMPLE_INFO_POOL = "writeSimpleInfoPool";

    /**
     * 专门发送身份关系的MQ
     * created by z<PERSON><PERSON> on 2019/04/08
     */
    public static final String SEND_CRYPTO_RELATION_MQ_POOL = "sendCryptoRealtionMQPool";

    public static final String SYNC_TOP_LIST_CHANGE_POOL = "syncTopListChangePool";

    public static final String SYNC_VERIFICATE_TOP_LIST_CHANGE_POOL = "syncVerificateTopListChangePool";

    /**
     * 托管相关的异步操作
     */
    public static final String ASYNC_CASE_DEPOSIT_POOL = "ASYNC_CASE_DEPOSIT_POOL";

    //从活动主会场捐款的异步操作
    public static final String ASYNC_VENUE_DONATE_POOL = "ASYNC_VENUE_DONATE_POOL";

    /**
     * 下单好友度数缓存
     */
    public static final String SEND_PAY_SUCCESS_OLAP = "SEND_PAY_SUCCESS_OLAP";

    /**
     * 下单时更新配转数据
     */
    public static final String UPDATE_SUBSIDY_SHARE_ON_CREATE_ORDER = "UPDATE_SUBSIDY_SHARE_ON_CREATE_ORDER";

    /**
     * 并发调用olap的线程池
     */
    public static final String OLAP_THREAD_POOL = "OLAP_THREAD_POOL";

    /**
     * 下单成功通知线程池
     */
    public static final String ORDER_ADD = "ORDER_ADD";

    /**
     * 热点案例
     */
    public static final String HOT_CASE = "HOT_CASE";

    public static final String TOG_READ = "TOG_READ";

    public static final String FEI_SHU_TASK= "FEI_SHU_TASK";

    public static final String GET_VISITCONFIG_POOL = "getVisitConfig-pool";

    public static final String GET_SETTLE_CASE_INFO_POOL = "getSettleCaseInfo-pool";
    /**
     * 微信回调V2
     */
    public static final String WX_CALLBACK_POOL = "WX_CALLBACK_POOL";

    public static final String GET_BAN_INFO_VO_POOL = "getBanInfoVo-pool";

}
