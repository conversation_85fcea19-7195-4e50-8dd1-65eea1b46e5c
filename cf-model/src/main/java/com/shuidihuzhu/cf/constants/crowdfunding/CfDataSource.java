package com.shuidihuzhu.cf.constants.crowdfunding;

public class CfDataSource {

    public static final String CF_RW = "crowdfundingDataSource_rw";
    public static final String CF_USER_RW = "cfUserDataSource_rw";
    public static final String CF_FRAME_RW = "shuidiFrameDataSource_rw";
    public static final String SHUIDI_CROWDFUNDING_SHARE_RW = "shuidiCrowdfundingShare_rw";
    /**
     * 捐款相关业务数据库主库(大表相关，crowdfunding_order，crowdfunding_pay_record，cf_refund_record)
     */
    public static final String DONATION_BIGTABLE_MASTER = "crowfundingDonationBigTableMaster";
    /**
     * 捐款相关业务数据库从库一(大表相关，crowdfunding_order，crowdfunding_pay_record，cf_refund_record)
     */
    public static final String DONATION_BIGTABLE_SLAVE_1 = "crowfundingDonationBigTableSlave";
    /**
     * 捐款相关业务数据库从库一(大表相关，crowdfunding_order，crowdfunding_pay_record，cf_refund_record)
     */
    public static final String DONATION_BIGTABLE_SLAVE_2 = "crowfundingDonationBigTableSlave2";
    /**
     * 捐款相关业务数据库   分表数据源  master
     */
    public static final String SHARDING_DONATION_BIG_TABLE_MASTER = "shardingDonationBigTableMaster";
    /**
     * 捐款相关业务数据库   分表数据源  slave
     */
    public static final String SHARDING_DONATION_BIG_TABLE_SLAVE = "shardingDonationBigTableSlave";

    /**
     * 拆库后  shuidi_crowdfunding_share 新实例  主库
     */
    public static final String SHUIDI_CROWDFUNDING_SHARE_MASTER = "shuidiCrowdfundingShareMasterDataSource";
    /**
     * 拆库后  shuidi_crowdfunding_share 新实例  从库
     */
    public static final String SHUIDI_CROWDFUNDING_SHARE_SLAVE = "shuidiCrowdfundingShareSlaveDataSource";

    /**
     * 新捐款相关业务数据库   数据源  shuidi-cf-order-new-master
     */
    public static final String CF_ORDER_NEW_MASTER_DATASOURCE = "cfOrderNewMasterDataSource";
    /**
     * 新捐款相关业务数据库   数据源  shuidi-cf-order-new-slave
     */
    public static final String CF_ORDER_NEW_SLAVE_DATASOURCE = "cfOrderNewSlaveDataSource";
    /**
     * 新捐款相关业务数据库   分表数据源  shuidi-cf-order-new-master
     */
    public static final String SHARDING_DONATION_BIG_TABLE_NEW_MASTER = "shardingDonationBigTableNewMaster";
    /**
     * 新捐款相关业务数据库   分表数据源  shuidi-cf-order-new-slave
     */
    public static final String SHARDING_DONATION_BIG_TABLE_NEW_SLAVE = "shardingDonationBigTableNewSlave";


    /**
     * 新捐款相关业务数据库事务管理器   数据源  shuidi-cf-order-new-master
     */
    public static final String CF_ORDER_NEW_MASTER_DATASOURCE_TRANSACTION_MANAGER = "cfOrderNewMasterDataSourceTransactionManage";


    /**
     * crowdfundingDataSource事务管理器
     */
    public static final String CROWDFUNDIN_DATASOURCE_TRANSACTION_MANAGER = "crowdfundingDataSourceTransactionManage";

    public static final String CF_USER_INFO_SHARDING_CROWDFUNDING = "cfUserInfoShardingCrowdfunding";

    public static final String CF_USER_INFO_SHARDING_CROWDFUNDING_SLAVE = "cfUserInfoShardingCrowdfundingSlave";

    public static final String SHUIDI_CF_CASE_STAT = "shuidiCfCaseStat";

    /**
     * 评论数据源，测试环境：10.2.0.15:3306/shuidi_cf_comment
     */
    public static final String SHUIDI_CF_COMMENT = "datasource-cf-api-cf-comment";

    public static final String SHUIDI_CF_COMMENT_SLAVE = "datasource-cf-api-cf-comment-slave";
    public static final String SHUIDI_CF_COMMENT_SHARE = "cfCommentShardingCrowdfunding";

    public static final String SHUIDI_CF_COMMENT_SLAVE_SHARE = "cfCommentSlaveShardingCrowdfunding";


    //
    public static final String CF_NEW_CASE_DETAIL = "datasource-cf-new-case-detail";

    public static final String CF_NEW_CASE_DETAIL_SHADING = "shardingLoveRankDataSource";

    /**
     * shuidi_crowdfunding_user 迁库 2021年09月07日
     */
    public static final String CROWDFUNDING_USER_MANAGER = "cfCrowdfundingUserMaster";

    public static final String CROWDFUNDING_USER_Slave = "CfCrowdfundingUserSlave";

    public static final String ADMIN_DB_MASTER = "shuidiCfAdminDataSource";

    public static final String ADMIN_DB_SLAVE = "shuidiCfAdminSlaveDataSource";

    /**
     * TDSQL数据源
     */
    public static final String TD_CF_DS = "shuidiCfTdDataSource";
    public static final String TD_CF_ORDER_DS = "shuidiTdCfOrder";
    public static final String TD_CF_ADMIN_DS = "shuidiTdCfAdmin";
    //公用doris
    public static final String STAR_ROCKS_DATASOURCE = "starRocksDirectCfApiDataSource";
    //筹业务线专用doris
    public static final String CF_STAR_ROCKS_DATASOURCE = "cfStarRocksDataSource";

    public static final String TIDB_COMMON_SHUIDI_RPT = "tidbCommonShuidiRpt";
}
