package com.shuidihuzhu.cf.constants.crowdfunding.status;

import com.google.common.base.Optional;
import com.google.common.collect.Maps;
import com.shuidihuzhu.common.web.bean.StateDescription;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Objects;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/8/12.
 */
public enum RelationShip implements StateDescription {

    // 亲属；朋友；同事；同学；邻居；老师；学生；病友；医护；其他
    FAMILY         (0,   PERSIST,    "亲属"),
    FRIEND         (1,   PERSIST,    "朋友"),
    CO_WORKER      (2,   PERSIST,    "同事"),
    CLASS_MATE     (3,   PERSIST,    "同学"),
    NEIGHBOURS     (4,   PERSIST,    "邻居"),
    TEACHER        (5,   PERSIST,    "老师"),
    STUDENT        (6,   PERSIST,    "学生"),
//    TEACHER        (5,   PERSIST,    "师生"),
//    STUDENT        (6,   PERSIST,    "师生"),
    SICK_FRIEND    (7,   PERSIST,    "病友"),
    HEALTH_CARE    (8,   PERSIST,    "医护"),
    OTHER          (9,   PERSIST,    "其他"),
    TEACHER_STUDENT (10, PERSIST,    "师生"),
    VOLUNTEER(11,PERSIST,"志愿者");
    //师生不做“患者+身份”
    //"患者"由前端来拼接，后台修改影响老版本显示
    final int code;
    final int type;
    final String description;

    RelationShip(int code, int type, String description) {
        this.code = code;
        this.type = type;
        this.description = description;
    }

    /**
     * 通过code查找对应的枚举
     *
     * @param code int
     * @return OrderState
     */
    public static RelationShip codeOf(int code) {
        return codeMapping.get(code);
    }


    @Override
    public String getName() {
        return name();
    }

    @Override
    public int getCode() {
        return this.code;
    }

    @Override
    public int getType() {
        return this.type;
    }

    @Override
    public String getDescription() {
        return this.description;
    }

    /**
     * 是否包含code
     *
     * @param code int
     * @return true 包含
     */
    public static boolean contains(int code) {
        return code >=0 && codeMapping.containsKey(code);
    }

    /**
     * 获得optional格式的枚举
     *
     * @param code Integer
     * @return Optional
     */
    public static Optional<RelationShip> optionalOf(Integer code) {
        if (contains(code)) {
            return Optional.fromNullable(codeOf(code));
        }
        return Optional.absent();

    }

    /**
     * 获得optional格式的枚举
     *
     * @param code Integer
     * @return Optional
     */
    public static Optional<RelationShip> optionalOf(int code) {
        if (contains(code)) {
            return Optional.fromNullable(codeOf(code));
        }
        return Optional.absent();

    }

    /**
     * 案例真实性项目，证实排序规则，医护>病友>老师>学生>同学>同事>邻居>朋友>亲属
     * @param relationshipCode
     * @return
     */
    public static int getAuthenticityRelationshipSort(Integer relationshipCode) {
        if (Objects.isNull(relationshipCode)) {
            return 0;
        }
        RelationShip relationShip = codeOf(relationshipCode);
        if (Objects.isNull(relationShip)) {
            return 0;
        }
        switch (relationShip) {
            case HEALTH_CARE:
                return 0;
            case SICK_FRIEND:
                return 1;
            case TEACHER:
                return 2;
            case STUDENT:
                return 3;
            case CLASS_MATE:
                return 4;
            case CO_WORKER:
                return 5;
            case NEIGHBOURS:
                return 6;
            case FRIEND:
                return 7;
            case FAMILY:
                return 8;
            case TEACHER_STUDENT:
                return 9;
            case OTHER:
                return 10;
        }
        return 0;
    }


    /**
     * 是否包含code
     *
     * @param code Integer
     * @return true 包含
     */
    public static boolean contains(Integer code) {
        return code != null && codeMapping.containsKey(code);
    }

    public static final Map<Integer, String> getAllValue () {
        return codeDescMapping;
    }

    private static final Map<Integer, RelationShip> codeMapping = Maps.newHashMap();
    private static final Map<Integer, String> codeDescMapping = Maps.newHashMap();

    static {
        for (RelationShip state : RelationShip.values()) {
            codeMapping.put(state.getCode(), state);
        }

        for (RelationShip state : RelationShip.values()) {
            codeDescMapping.put(state.getCode(), state.getDescription());
        }
    }
}
