package com.shuidihuzhu.cf.constants.crowdfunding;

public class RedisKeyCons {
	
	public static final String CF_INFO_EVENT_KEY_USERID = "cf-event-#infoId#-#userId#-#eventType#";
	public static final long CF_INFO_EVENT_KEY_USERID_TIME = 5 * 24 * 60 * 60 * 1000L;
//	public static final String CF_INFO_EVENT_KEY_USERTHIRDID = "cf-event-#infoId#-#userThridId#-#eventType#";
//	public static final long CF_INFO_EVENT_KEY_USERTHIRDID_TIME = 15 * 24 * 60 * 60 * 1000L;

	public static final String CF_INFO_KEY_USER_BANK_VERIFY = "cf-bank-verify-#userId#";
	public static final long CF_INFO_KEY_USER_BANK_VERIFY_TIME = 24 * 60 * 60 * 1000L;
	public static final String CF_INFO_KEY_USER_BANK_VERIFY_LAST_MODIFED = "cf-bank-verify-#userId#-last-modifed";

	public static final String CF_REDIS_KV = "cf-redis-k-v-#key#";
	public static final long CF_REDIS_KV_TIME = 60 * 1000L;

	public static final String KEY_FILL_IN_FIRST_APPROVE = "fill-in-first-approve-#userId#";

	/**
	 * 爆款案例redis前缀key
	 */
	public static final String CF_API_HOT_CASE = "cf-api-hot-case#";

	/**
	 * 首屏消息广告统计前缀key
	 */
	public static final String CF_API_MESSAGE_ADVERTISEMENT = "cf-api-message-advertisement#";

	/**
	 * 用户当天访问案例次数
	 */
	public static final String CF_API_VISIT_COUNT = "CF_API_VISIT_COUNT";

	/**
	 * 证实投诉,统计投诉人单位时间投诉次数key
	 */
	public static final String CF_VERIFICATION_COMPLAINT_USER_ID = "cf_verification_complaint_user_id#";

	/**
	 * 用户是否同意证实
	 */
	public static final String JUDGEMENT_USER_AGREE_VERIFY = "JUDGEMENT_USER_AGREE_VERIFY";


	/**
	 * cf-share-view redis key
	 */
	public interface ShareView{

		/**
		 * 配转访问key
		 */
		String VIEW = "3#";

		String ORDER = "2#";

		String ORDER_V2 = "activity-subsidy-order#";

		String STAT = "4stat#";
	}

}
