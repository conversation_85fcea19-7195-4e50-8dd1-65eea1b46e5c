package com.shuidihuzhu.cf.constants;

/**
 * Author: <PERSON>
 * Date: 2017/11/9 20:26
 */
public final class MQTagCons {
    public static final String CF_DONATION_SUCCESS_BIZ = "CF_DONATION_SUCCESS_BIZ"; // "捐款成功其他业务处理"
    public static final String CF_DONATION_SUCCESS_MSG = "CF_DONATION_SUCCESS_MSG"; // "捐款成功发送消息"
    public static final String CF_DONATION_ORDER_ADD_MSG = "CF_DONATION_ORDER_ADD_MSG"; // 捐款预下单成功消息
    public static final String CF_DONATION_SUCCESS_COUNT = "CF_API_DONATION_SUCCESS_COUNT"; // "redis统计当天捐款成功次数"
    public static final String CF_LARGE_AMOUNT_DONATION_MSG = "CF_LARGE_AMOUNT_DONATION_MSG"; //大额捐款消息
    public static final String CF_LARGE_AMOUNT_DONATION_MSG_V2 = "CF_LARGE_AMOUNT_DONATION_MSG_V2"; //大额捐款消息V2
    public static final String CF_DUIBA_PAY_SUCCESS_MSG = "CF_DUIBA_PAY_SUCCESS_MSG";//兑吧购物支付成功消息
    public static final String CF_DUIBA_PAY_REFUND_MSG = "CF_DUIBA_PAY_REFUND_MSG";//兑吧购物退款轮询退款状态消息
    public static final String CF_DONATION_XIAOSHUIDI_PUSH = "CF_DONATION_XIAOSHUIDI_PUSH"; // "捐款成功后小水滴延迟推送"
    public static final String CF_DONATION_SUCCESS_SUBSIDY = "CF_DONATION_SUCCESS_SUBSIDY"; // "捐款成功活动相关逻辑

    public static final String CF_DISEASE_NORM_SHIP = "CF_DISEASE_NORM_SHIP";//材审通过后插入归一话疾病与用户填写疾病关系
    public static final String CF_DISEASE_NORM_SHIP_FIRST = "CF_DISEASE_NORM_SHIP_FIRST";//老案例第一次被访问插入数据
    public static final String USER_DONATION_SUCCESS = "USER_DONATION_SUCCESS"; // "公益捐款成功活动相关逻辑

    /**
     * 证实成功后才会发，不过发的是CrowdFundingVerificationParam
     */
    public static final String CF_VERIFICATION_SUCCESS = "CF_VERIFICATION_SUCCESS"; //"证实成功"

    /**
     * 证实成功后才会发，发的是CrowdFundingVerification
     */
    public static final String CF_VERIFICATION_MSG = "CF_VERIFICATION_MSG"; //"证实成功"
    public static final String CF_SUBSCRIBE_CALLBACK_MSG = "CF_SUBSCRIBE_CALLBACK_MSG"; //捐款关注后回调消息
    public static final String IDCARD_WAIT_VERIFY = "WAIT_VERIFY";
    public static final String IDCARD_VERIFIED = "VERIFIED";
    public static final String CF_LAUNCH_WITHOUT_MOBILE = "CF_LAUNCH_WITHOUT_MOBILE";// 发起后验证手机号消息
    public static final String CF_OUTSIDE_GUIDE = "CF_OUTSIDE_GUIDE";//外围公众号用户引导到主号的消息推送
    public static final String CF_WORK_ORDER_APPROVE_LAUNCH = "CF_WORK_ORDER_APPROVE_LAUNCH";// 材料审核工单
    public static final String CF_PUBLISH_PROGRESS = "CF_PUBLISH_PROGRESS"; //发布进展
    public static final String CF_PUBLISH_PROGRESS_NOTICE = "CF_PUBLISH_PROGRESS_NOTICE"; //发布进展通知
    public static final String CF_WORK_ORDER_REPORT = "CF_WORK_ORDER_REPORT"; //举报工单
    public static final String CF_WORK_ORDER_REPORT_V2 = "CF_WORK_ORDER_REPORT_V2"; //举报工单
    public static final String CF_WORK_ORDER_REPORT_NEW_LOGIC = "CF_WORK_ORDER_REPORT_NEW_LOGIC"; //举报工单新逻辑
    public static final String CF_SUBMIT_FOR_REVIEW = "CF_SUBMIT_FOR_REVIEW"; //提交审核
    public static final String CF_ORDER_FROM_SHARE = "CF_ORDER_FROM_SHARE"; //通过分享得到的订单
    public static final String BONUS_POINTS = "BONUS_POINTS"; // 增加积分
    public static final String BONUS_POINTS_CALLBACK = "BONUS_POINTS_CALLBACK"; // 增加积分后的回调
    public static final String CF_ADD_WX_TAG_TO_USER = "CF_ADD_WX_TAG_TO_USER";//给用户加上微信的标签和大数据的标签
    public static final String CF_ADD_OR_UPDATE_PATIENT_MSG = "CF_ADD_OR_UPDATE_PATIENT_MSG";//患者身份证相关照片信息
    public static final String CF_SHARE_SUCCESS_MSG = "CF_SHARE_MSG"; // "分享成功发送消息"
    public static final String CF_SHARE_SUCCESS_REAL_TIME_MSG = "CF_SHARE_SUCCESS_REAL_TIME_MSG"; // "分享成功实时发送消息"
    public static final String CF_SHARE_SUCCESS_DELAY_MSG = "CF_SHARE_SUCCESS_DELAY_MSG"; // "分享成功发送延迟消息"
    public static final String CF_DONATE_SUCCESS_DELAY_MSG = "CF_DONATE_SUCCESS_DELAY_MSG"; // "分享成功发送延迟消息"

    public static final String CF_REFUND_SUCCESS_MSG = "CF_SHARE_SUCCESS_MSG"; // "分享成功发送消息"
    public static final String CF_PAY_SUCCESS_MSG = "CF_PAY_SUCCESS_MSG"; // "捐款成功发送消息"

    public static final String CF_PAY_ONLY_SUCCESS_MSG = "CF_PAY_ONLY_SUCCESS_MSG"; // "捐款成功发送消息"

    /**
     * "捐款成功延迟检验业务数据准确性与数据修复,消费在finance-mq"
     */
    public static final String CF_PAY_SUCCESS_BCP_V1 = "CF_PAY_SUCCESS_BCP_V1";

    /**
     * 对捐款的评论、对动态的评论都发消息
     */
    public static final String CF_COMMENT_MSG = "CF_COMMENT_MSG";

    /**
     * 筹款发起，发出来的消息
     */
    public static final String CF_OPERATION_MSG = "CF_OPERATION_MSG";

    /**
     * 更改图文后，发出来的消息
     */
    public static final String CF_UPDATE_BASE_INFO_MSG = "CF_UPDATE_BASE_INFO_MSG";

    /**
     * 筹款发起，延迟10分钟发出来的消息
     */
    public static final String CF_OPERATION_DELAY_10M_MSG = "CF_OPERATION_DELAY_10M_MSG";

    /**
     * https://wiki.shuiditech.com/pages/viewpage.action?pageId=16451397
     * 捐款成功发送客服消息，10s后检查事发后成功
     */
    public static final String CF_DONATION_CUSTOM_MSG_SENT_MSG = "CF_DONATION_CUSTOM_MSG_SENT_MSG"; //捐款成功客服消息已经发送

    /**
     * https://wiki.shuiditech.com/pages/viewpage.action?pageId=10691233
     * 筹款登记后一分钟延时消息
     * 延时1分钟检查 已登记用户是否已关注公众号。
     * 若没有关注 则发送引导关注短信。
     */
    public static final String CF_DELAY_WITH_REGISTER = "CF_DELAY_WITH_REGISTER";
    public static final String CF_STATUS_CHANGE_MSG = "CF_STATUS_CHANGE_MSG"; // "案例状态变更消息"


    /**
     * 场景消息延时回调
     *
     * @see com.shuidihuzhu.cf.enums.crowdfunding.SceneMessageEnum
     */
    public static final String SCENE_MESSAGE = "scene_message";

    public static final String CF_DAILY_SIGNIN_SUCCESS = "CF_DAILY_SIGNIN_SUCCESS"; //发布进展通知

    //筹款接力消息
    public static final String CF_RELAY_MSG = "CF_RELAY_MSG";

    /**
     * 用户微信关注事件
     */
    public static final String CF_WX_SUBSCRIBE = "CF_WX_SUBSCRIBE";

    /**
     * 关注后3分钟的消息
     */
    public static final String CF_WX_SUBSCRIBE_3MIN = "CF_WX_SUBSCRIBE_3MIN";

    // 微信关注 延迟消息
    public static final String CF_WX_SUBSCRIBE_DELAY = "CF_WX_SUBSCRIBE_DELAY";

    /**
     * 用户关注事件 引流到其他业务
     */
    public static final String CF_WX_SUBSCRIBE_TRANSFORM_TO_OTHER = "CF_WX_SUBSCRIBE_TRANSFORM_TO_OTHER";

    //初审过后发筹款秘籍
    public static final String CF_BIBLE_MSG_AFTER_FIRST_APPROVE = "CF_BIBLE_MSG_AFTER_FIRST_APPROVE";

    public static final String ADMIN_MQ_TAG_FIRST_APPROVE_SUCCESS = "ADMIN_MQ_TAG_FIRST_APPROVE_SUCCESS";

    /**
     * 投放相关
     */
    public static final String CF_TOUFANG_SIGN_MSG = "CF_MQ_TAG_TOUFANG_SIGN_MSG";
    public static final String CF_TOUFANG_TOUTIAO_SIGN_MSG = "CF_MQ_TAG_TOUTIAO_SIGN_MSG";
    public static final String CF_TOUFANG_AD_REGISTER = "CF_MQ_TAG_AD_REGISTER";
    /**
     * 投放一对一服务消息
     */
    public static final String CF_TOUFANG_ONE_TO_ONE_SERVICE = "CF_TOUFANG_ONE_TO_ONE_SERVICE";
    //来自大数据组的广点通投放登记MQ消息
    public static final String SD_DATA_AD_TENGXUN_CF_TAGS = "SD_DATA_AD_TENGXUN_CF_TAGS";
    //来自大数据组的百度登记MQ消息
    public static final String SD_DATA_AD_BAIDU_CF_TAGS = "SD_DATA_AD_BAIDU_CF_TAGS";
    //来自大数据组的头条登记MQ消息
    public static final String SD_DATA_AD_TOUTIAO_CF_TAGS = "SD_DATA_AD_TOUTIAO_CF_TAGS";

    public static final String CF_SNAPSHOT_SAVE_SUCESS = "CF_SNAPSHOT_SAVE_SUCESS";

    public static final String CF_SNAPSHOT_SAVE_SUCESS_NOW = "CF_SNAPSHOT_SAVE_SUCESS_NOW";
    /**
     * 用户发起案例记录发起环境
     */
    public static final String CF_CASE_LABEL = "CF_CASE_LABEL";
    public static final String CF_FIRST_APPROVE_RETRY = "CF_FIRST_APPROVE_RETRY";

    /**
     * * 72小时延时check风险
     */
    public static final String CF_RAISE_CHECK_RISK = "CF_RAISE_CHECK_RISK";

    /**
     * 延时发送消息
     */
    public static final String NOTICE_DELAY_SEND = "NOTICE_DELAY_SEND";

    /**
     * 后台开启变更收款人功能时,下发给用户模板消息
     */
    public static final String CHANGE_PAYEE_INFO_TEMPLATE_MESSAGE = "CHANGE_PAYEE_INFO_TEMPLATE_MESSAGE";

    public static final String CF_SUBSCRIBE_48h_LATER = "CF_SUBSCRIBE_48h_LATER";

    /**
     * 当日19与21点客服与模板(催发起)
     */
    public static final String CF_CASE_BASIC_INTRA_SUPPLEMENT_CUSTOM = "CF_CASE_BASIC_INTRA_SUPPLEMENT_CUSTOM";
    public static final String CF_CASE_BASIC_INTRA_SUPPLEMENT_MODEL = "CF_CASE_BASIC_INTRA_SUPPLEMENT_MODEL";

    /**
     * 次日8点的连续连天催补充发起信息客服与模板(催发起)morrow
     */
    public static final String CF_CASE_BASIC_SUPPLEMENT_MORROW_CUSTOM = "CF_CASE_BASIC_SUPPLEMENT_MORROW_CUSTOM";

    /**
     * 证实实名认证异常延迟一小时认证
     */
    public static final String CF_VERIFY_WAIT_1H_LATER = "CF_VERIFY_WAIT_1H_LATER";

    /**
     * 案例发起
     */
    public static final String DONATE_COOPERATE_CREATE = "DONATE_COOPERATE_CREATE";

    /**
     * 自动参加活动测试
     */
    public static final String ACTIVITY_CASE_AUTO_CREATE_TEST = "ACTIVITY_CASE_AUTO_CREATE_TEST";

    public static final String CASE_JOIN_BONFIRE_ACTIVITY = "CASE_JOIN_BONFIRE_ACTIVITY";

    public static final String CF_BASE_INFO_CHANGE_TAG = "CF_BASE_INFO_CHANGE_TAG";
    public static final String CF_DIAGNOSIS_INFO_CHANGE_TAG = "CF_DIAGNOSIS_INFO_CHANGE_TAG";

    /**
     * 数据审核 案例风险不通过
     */
    public static final String CASE_DATE_RISK_CHECK_NON_PASSED = "CASE_DATE_RISK_CHECK_NON_PASSED";

    /**
     * 用户短信延迟30分钟发送
     */
    public static final String CLICK_ON_DELAY_PAGE_THR_MIN_MSG = "CLICK_ON_DELAY_PAGE_THR_MIN_MSG";

    /**
     * 用户发起线索后发送的消息
     */
    public static final String CF_CLEW_RECEIVE_MSG = "CF_CLEW_RECEIVE_MSG";

    /**
     * 用户与菜单交互，五分钟后发延迟消息
     */
    public static final String MUTUAL_WX_MENU_FIVE_MINUTE_MSG = "MUTUAL_WX_MENU_FIVE_MINUTE_MSG";

    /**
     * 用户与菜单交互，半个小时后发延迟消息
     */
    public static final String MUTUAL_WX_MENU_HELF_HOUR_MSG = "MUTUAL_WX_MENU_HELF_HOUR_MSG";

    /**
     * 用户与菜单交互，次日发延迟消息
     */
    public static final String MUTUAL_WX_MENU_MSG = "MUTUAL_WX_MENU_MSG";

    /**
     * 用户与菜单交互，第三日发延迟消息
     */
    public static final String MUTUAL_WX_MENU_THIRD_DAY_MSG = "MUTUAL_WX_MENU_THIRD_DAY_MSG";

    /**
     * 增加积分发送积分变动通知消息
     */
    public static final String BONUS_POINTS_MSG = "BONUS_POINTS_MSG";

    /**
     * 向捐款人发送[筹款审核通过]消息.
     */
    public static final String CF_AUDIT_PASS_TO_DONOR_MSG = "CF_AUDIT_PASS_TO_DONOR_MSG";

    /**
     * 患者申请提现公示消息.
     */
    public static final String CF_DRAW_CASH_APPLY_NOTIFY = "CF_DRAW_CASH_APPLY_NOTIFY";

    /**
     * 评论回复通知消息.
     */
    public static final String CF_COMMENT_REPLY = "CF_COMMENT_REPLY";

    /**
     * 补发模版消息
     */
    public static final String CF_RETRY_SEND_TEMPLATE_MSG = "CF_RETRY_SEND_TEMPLATE_MSG";

    /**
     * 案例草稿内容保存通知
     */
    public static final String CF_SNAPSHOT_SAVE_SUCESS_RIGHT_NOW = "CF_SNAPSHOT_SAVE_SUCESS_RIGHT_NOW";

    /**
     * 案例草稿内容保存通知，新版
     * 用户发送app push
     */
    public static final String CF_SAVE_DRAFT_DELAY = "cf_save_draft_delay";

    /**
     * 用户资产转移tag
     */
    public static final String ACCOUNT_TRANSFER = "ACCOUNT_TRANSFER";

    /**
     * 111种树消息
     */
    public static final String ACTIVITY_111_TREE_MSG = "ACTIVITY_111_TREE_MSG";

    /**
     * 用户选择标记为二类卡
     */
    public static final String USER_AUTONOMOUS_OPERATION_SECOND_CARD = "USER_AUTONOMOUS_OPERATION_SECOND_CARD";


    /**
     * 用户为案例加油
     */
    public static final String USER_BLESSING = "USER_BLESSING";

    /**
     * 成功获取案例详情
     */
    public static final String CF_DETAIL_GET_SUCCESS = "CF_DETAIL_GET_SUCCESS";

    /**
     * 用户在案例发起页，左滑进入问卷填写页，发送该mq，一个小时后判断用户是否填写了问卷，若未填写发消息
     */
    public static final String USER_ENTRY_QUESTIONNAIRE = "USER_ENTRY_QUESTIONNAIRE";

    /**
     * 用户关注十分钟后推送消息
     */
    public static final String WX_SUBSCRIBE_QR_DELAY_TEN_MINUTE = "WX_SUBSCRIBE_QR_DELAY_TEN_MINUTE";

    public static final String CF_MSG_SEND_RECORD_DELETE_TAG = "CF_MSG_SEND_RECORD_DELETE_TAG";

    public static final String CF_DONOR_APPLY_REFUND_EVENT = "CF_DONOR_APPLY_REFUND_EVENT";

    /**
     * 用户进入填写页
     * 延时半小时消息
     */
    public static final String CF_SAVE_BASE_INFO_DELAY_MSG = "CF_SAVE_BASE_INFO_DELAY_MSG";

    public static final String CF_USER_LABEL_ALTER_MSG = "CF_USER_LABEL_ALTER_MSG";

    public static final String MINA_PLANT_TREES_V2 = "MINA_PLANT_TREES_V2";

    /**
     * 捐款成功一次性订阅延时消息
     */
    public static final String CF_DONATION_SUCCESS_ONE_TIME_MSG_DELAY = "CF_DONATION_SUCCESS_ONE_TIME_MSG_DELAY";
    public static final String ADMIN_MQ_LOVE_HOME = "ADMIN_MQ_LOVE_HOME";

    /**
     * 筹款秘籍延时消息
     */
    public static final String CF_BIBLE_MSG_DELAY = "CF_BIBLE_MSG_DELAY";


    public static final String CF_USER_ADD_SUBSCRIBE_MSG_DELAY = "CF_USER_ADD_SUBSCRIBE_MSG_DELAY";

    /**
     * 重新提交初审
     */
    public static final String CF_SUBMIT_INITIAL_AUDIT = "CF_SUBMIT_INITIAL_AUDIT";

    public static final String CF_INITIAL_AUDIT_OPERATION_MSG = "CF_INITIAL_AUDIT_OPERATION_MSG";

    public static final String CF_INITIAL_AUDIT_OPERATION_RECALL_MSG = "CF_INITIAL_AUDIT_OPERATION_RECALL_MSG";

    /**
     * 筹款人上传图片后，获取图片的一些属性，供风控使用
     */
    public static final String CF_ATTACHMENT_RISK_ATTR_STAT = "CF_ATTACHMENT_RISK_ATTR_STAT";

    /**
     * 案例id生成后发消息
     */
    public static final String CF_CASE_ID_CREATE = "CF_CASE_ID_CREATE";

    /**
     * 案例项目进展更新延迟推送消息
     */
    public static final String CF_PUSH_PROGRESS_DELAY_MSG = "CF_PUSH_PROGRESS_DELAY_MSG";

    /**
     * 案例结束事件
     */
    public static final String CF_CASE_END = "CF_CASE_END";
    /**
     * 资金新项目案例结束消息
     */
    public static final String CF_CASE_END_FROM_FINANCE_TOC_MSG = "CF_CASE_END_FROM_FINANCE_TOC_MSG";

    /**
     * 案例结束事件
     */
    public static final String ACTIVITY_RED_POCKET_END = "ACTIVITY_RED_POCKET_END_V2";

    /**
     * 案例申请退款
     */
    public static final String CF_CASE_APPLY_REFUND = "CF_CASE_APPLY_REFUND";

    /**
     * 自动生成信息传递工单
     */
    public static final String ADMIN_AUTO_GENERATE_FLOW_ORDER = "ADMIN_AUTO_GENERATE_FLOW_ORDER";

    /**
     * 主动关注十分钟发消息
     */
    public static final String SEND_SUBSCRIBE_TEN_TIME_MSG = "SEND_SUBSCRIBE_TEN_TIME_MSG";

    /**
     * 主动关注而未发起用户催发消息
     */
    public static final String SEND_SUBSCRIBE_UN_RAISE_PERSON_MSG = "SEND_SUBSCRIBE_UN_RAISE_PERSON_MSG";
    /**
     * 主动关注而未发起用户催发消息--当天发消息
     */
    public static final String SEND_SUBSCRIBE_UN_RAISE_PERSON_TODAY_MSG = "SEND_SUBSCRIBE_UN_RAISE_PERSON_TODAY_MSG";

    /**
     * 主动关注而未发起用户催发消息--第三天发消息
     */
    public static final String SEND_SUBSCRIBE_UN_RAISE_PERSON_THREEDAY_MSG = "SEND_SUBSCRIBE_UN_RAISE_PERSON_THREEDAY_MSG";

    // 平台恢复筹款消息
    public static final String ADMIN_MQ_RESTORE_FUNDRAISING = "ADMIN_MQ_RESTORE_FUNDRAISING";
    /**
     * 用户扫码志愿者二维码后发起  ||  关注水滴筹等公众号   后通知cf-clewtrack  新建bdcrm线索
     */
    public static final String NOTICE_CLEW_TRACK_ADD_BD_CRM_CLEW = "NOTICE_CLEW_TRACK_ADD_BD_CRM_CLEW";
    //收款人、发起人、患者三者信息齐全或者发生变更时发送MQ校验是否是失信人
    public static final String CF_DISHONEST_VALIDATE_MQ = "CF_DISHONEST_VALIDATE_MQ";

    public static final String CF_DISHONEST_LABLE_REPORT_MQ = "CF_DISHONEST_LABLE_REPORT_MQ";

    /**
     * 发送打开app10分钟消息
     */
    public static final String OPEN_APP_10_MINUTE_MSG = "OPEN_APP_10_MINUTES_MSG";

    /**
     * 发送打开app30分钟消息
     */
    public static final String OPEN_APP_30_MINUTE_MSG = "OPEN_APP_30_MINUTES_MSG";

    /**
     * 发送打开app第二天消息
     */
    public static final String OPEN_APP_2_DAYS_MSG = "OPEN_APP_2_DAYS_MSG";
    /**
     * 发送打开app第4天消息
     */
    public static final String OPEN_APP_4_DAYS_MSG = "OPEN_APP_4_DAYS_MSG";
    /**
     * 发送打开app第7天消息
     */
    public static final String OPEN_APP_7_DAYS_MSG = "OPEN_APP_7_DAYS_MSG";

    /**
     * 保险复购消息
     */
    public static final String CF_INSURANCE_PURCHASE_GUIDE_MSG = "CF_INSURANCE_PURCHASE_GUIDE_MSG";

    /**
     * 水滴保：当有cf渠道的订单承保成功后, 生成一个MQ推送
     */
    public static final String ORDER_2_CF_SALE_NEW = "ORDER_2_CF_SALE_NEW";

    /**
     * 访问案例详情页，发送延迟消息
     */
    public static final String CASE_VISITOR_DELAY_MSG = "case_visitor_delay_msg";
    /**
     * 扫线下BD二维码，关注后60分钟催登记消息ab测试
     */
    public static final String SUBSCRIBE_60_MINUTES_MSG = "SUBSCRIBE_60_MINUTES_MSG";

    /**
     * 扫线下BD二维码，关注后第二天崔登记消息ab测试
     */
    public static final String SUBSCRIBE_2_DAYS_MSG = "SUBSCRIBE_2_DAYS_MSG";

    /**
     * 事件中心固定的tag
     */
    public static final String USER_EVENT_FOR_EVENT_CENTER = "user_event_for_event_center";

    /**
     * 外围号关注10分钟消息
     */
    public static final String OUTER_WX_SUBSCRIBE_TEN_MINUTES_MSG = "OUTER_WX_SUBSCRIBE_TEN_MINUTES_MSG";

    /**
     * 调广告系统失败，三分钟后重试
     */
    public static final String AD_CALL_FAIL_TRYABLE_MQ = "AD_CALL_FAIL_TRYABLE_MQ";

    /**
     * 同步大数据数据
     */
    public static final String CF_SYNC_BIGDATA_MSG = "CF_SYNC_BIGDATA_MSG_V2";

    /**
     * 案例下的用户榜单
     */
    public static final String CASE_USER_RANK_LIST_MSG = "CF_USER_RANK_LIST_MSG";

    /**
     * 如果在19 111活动期间 初审通过4天中午保存 用户前三榜单
     */
    public static final String CASE_111_PRIZE_TOP_LIST_SAVE = "CASE_111_PRIZE_TOP_LIST_SAVE";

    /**
     * 提现成功自动生成反馈信息
     */
    public static final String AUTO_CREATE_FEEDBACK = "AUTO_CREATE_FEEDBACK";

    public static final String ALL_CF_PATIENT_CHANGE_INFO = "ALL_CF_PATIENT_CHANGE_INFO";
    public static final String ALL_CF_PATIENT_CHANGE_INFO_REFRESH_ONCE = "ALL_CF_PATIENT_CHANGE_INFO_REFRESH_ONCE";
    public static final String CF_PATIENT_INFO_NOTICE_RISK_BUSINESS = "CF_PATIENT_INFO_NOTICE_RISK_BUSINESS";

    public static final String CF_PATIENT_CHANGE_INFO_BROADCASTING = "CF_PATIENT_CHANGE_INFO_BROADCASTING";

    public static final String CF_TIANRUN_CALL_UNIQUEID_RECORD_MSG = "CF_TIANRUN_CALL_UNIQUEID_RECORD_MSG";
    public static final String ADD_REPORT_FUNDRAISER_COMMUNICATER = "ADD_REPORT_FUNDRAISER_COMMUNICATER";

    /**
     * 动态下发审核回传消息 cf-admin接收
     */
    public static final String CF_UPLOADING_DYNAMIC_INFO_MSG = "CF_UPLOADING_DYNAMIC_INFO_MSG";


    public static final String CF_UPDATE_CREDIBLE_STATUS = "CF_UPDATE_CREDIBLE_STATUS";

    /**
     * 提交补充证明消息
     */
    public static final String CF_UPDATE_CREDIBLE_STATUS_V2 = "CF_UPDATE_CREDIBLE_STATUS_V2";

    public static final String CF_UPDATE_CREDIBLE_STATUS_HOSPTIAL_CHECK = "CF_UPDATE_CREDIBLE_STATUS_HOSPTIAL_CHECK";

    // 分享成功上报 案例信息记录
    public static final String SHARE_SUCCESS_CASE_DATA = "SHARE_SUCCESS_CASE_DATA";

    public static final String CF_GIFT_INSURANCE_DELAY_MSG = "cf_gift_insurance_delay_msg";

    public static final String CF_ACTIVITY_REFUND_SUCCESS = "CF_ACTIVITY_REFUND_SUCCESS";

    //补贴、配转、生效的mq
    public static final String BILL_USE_TAG = "DONATE_COOPERATE_BILL_USEFUL";

    public static final String CF_RIVER_AUDIT_OPERATION = "CF_RIVER_AUDIT_OPERATION";

    //公益案例结束事件
    public static final String PF_INFO_END = "PF_INFO_END";

    //筹下单支付导购转保险
    public static final String TAG_UNIFIED_ORDER_SUCCESS = "TAG_UNIFIED_ORDER_SUCCESS";

    //关注公众号 发半小时消息
    public static final String CF_SUBSCRIBE_HALF_HOUR_LATER = "CF_SUBSCRIBE_HALF_HOUR_LATER";
    //关注公众号 发一小时消息
    public static final String CF_SUBSCRIBE_1h_LATER = "CF_SUBSCRIBE_1h_LATER";

    //用户捐款后，触发变现消息  发半小时消息
    public static final String DOUBLE_DONATION_CASHING_NEWS_HALF_HOUR_LATER = "DOUBLE_DONATION_CASHING_NEWS_HALF_HOUR_LATER";

    //用户捐款后，触发变现消息  发一小时消息
    public static final String DOUBLE_DONATION_CASHING_NEWS_1h_LATER = "DOUBLE_DONATION_CASHING_NEWS_1h_LATER";
    /**
     * 图片水印判断
     */
    public static final String CF_ATTACHMENT_MARK_ATTR_STAT = "CF_ATTACHMENT_MARK_ATTR_STAT";


    public static final String CF_TITLE_CONTENT_RISK_CHECK = "CF_TITLE_CONTENT_RISK_CHECK";

    //材审重构中2763的app消息
    public static final String MATERIAL_REVIEW_SEND_APP_MSG = "MATERIAL_REVIEW_SEND_APP_MSG";

    //export_clink_call_in_day表写入
    public static final String CF_EXPORT_CLINK_CALL_IN_DAY = "CF_EXPORT_CLINK_CALL_IN_DAY";

    //export_clink_call_out_day表写入
    public static final String CF_EXPORT_CLINK_CALL_OUT_DAY = "CF_EXPORT_CLINK_CALL_OUT_DAY";

    //捐款成功后，自动回复评论消息
    public static final String CF_DONATE_AUTO_REPLY_MSG = "CF_DONATE_AUTO_REPLY_MSG";

    // 大数据插入stat_detail_access_friend表数据mq
    public static final String STAT_DETAIL_ACCESS_FRIEND = "STAT_DETAIL_ACCESS_FRIEND";

    // 大数据延时插入stat_detail_access_friend表数据mq
    public static final String STAT_DELAY_DETAIL_ACCESS_FRIEND = "STAT_DELAY_DETAIL_ACCESS_FRIEND";

    // 证实黑名单用户放出来
    public static final String BLACKLIST_CONFIRMED_USERS_RELEASE = "BLACKLIST_CONFIRMED_USERS_RELEASE";

    // 失信被执行人识别&处理
    public static final String DISHONEST_PEOPLE_HANDLE_MSG = "DISHONEST_PEOPLE_HANDLE_MSG";

    // 资金用途生成工单
    public static final String CF_FUND_USE_CREATE_WORK = "CF_FUND_USE_CREATE_WORK";

    // 删除cos上的对象
    public static final String CF_DELETE_COS_TAG = "CF_DELETE_COS_TAG";

    // 捐赠消息
    public static final String CF_CONTRIBUTE_ORDER_TAG = "CF_CONTRIBUTE_ORDER_TAG";

    // 众人帮逻辑
    public static final String CF_BONFIRE_SUCCESS_SUBSIDY = "CF_BONFIRE_SUCCESS_SUBSIDY";

    // 禁止转发当天结束自动解禁
    public static final String CF_RISK_BAN_SHARE_AUTO_RELEASE = "CF_RISK_BAN_SHARE_AUTO_RELEASE";

    // 图片掩码消息
    public static final String CF_IMAGE_MASK_TO_AI = "CF_IMAGE_MASK_TO_AI";


    // 促转发消息
    public static final String HASTEN_SHARE_MSG = "HASTEN_SHARE_MSG";

    // 捐款成功更新捐款人数
    public static final String PAY_SUCCESS_TO_UPDATE = "PAY_SUCCESS_TO_UPDATE";

    // 退款成功更新捐款人数
    public static final String REFUND_SUCCESS_TO_UPDATE = "REFUND_SUCCESS_TO_UPDATE";

    // 初审处理完成发消息（包含通过&不通过）
    public static final String CF_INITIAL_AUDIT_HANDLE_V2 = "CF_INITIAL_AUDIT_HANDLE_V2";

    public static final String CF_USER_REPORT_CASE = "CF_USER_REPORT_CASE";

    public static final String WX_CALLBACK_ORIGIN_TAG = "WX_CALLBACK_ORIGIN_TAG";
}
