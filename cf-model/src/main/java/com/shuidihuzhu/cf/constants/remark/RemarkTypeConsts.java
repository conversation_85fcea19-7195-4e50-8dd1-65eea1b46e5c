package com.shuidihuzhu.cf.constants.remark;

/**
 * <AUTHOR>
 * @date 2018-11-07  16:05
 */
public interface RemarkTypeConsts {

    /**
     * 数据审核-材料审核跟进记录
     */
    int CASE_DATA_INFO_RISK = 101;

    /**
     * 数据审核-提现审核跟进记录
     */
    int CASE_DATA_DRAW_CASH_RISK = 102;

    /**
     * 数据审核-材料审核跟进记录-处理记录
     */
    int CASE_DATA_INFO_RISK_HANDLE = 111;

    /**
     * 数据审核-提现审核跟进记录-处理记录
     */
    int CASE_DATA_DRAW_CASH_RISK_HANDLE = 112;

    /**
     * 案例结束
     */
    @Deprecated
    int CASE_END = 201;

    /**
     * 案例初始化
     */
    int CASE_RECOVER = 202;

}
