package com.shuidihuzhu.cf.constants.crowdfunding;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * 发起相关常量
 * <AUTHOR>
 */
public interface RaiseCons {

    /**
     *     - pageFlag传值 baseInfo(图文) firstApprove(前置) family(家庭经济状况) protection(患者保障状况)
     */
    interface PageName {
        String BASE_INFO = "baseInfo";
        String FIRST_APPROVE = "firstApprove";
        String FAMILY = "family";
        String PROTECTION = "protection";
    }

    /**
     * - pageFlag传值 basicInfo(基础信息) titleImage(图文) firstApprove(前置) assertInfo(财产信息)
     */
    interface PageNameV2 {
        String BASIC_INFO = "basicInfo";
        String TITLE_IMAGE = "titleImage";
        String FIRST_APPROVE = "firstApprove";
        String ASSERT_INFO = "assertInfo";

        String CONTROL = "control";

        List<String> ALL_PAGE = Lists.newArrayList(
                BASIC_INFO, TITLE_IMAGE,FIRST_APPROVE,ASSERT_INFO
        );
    }
}
