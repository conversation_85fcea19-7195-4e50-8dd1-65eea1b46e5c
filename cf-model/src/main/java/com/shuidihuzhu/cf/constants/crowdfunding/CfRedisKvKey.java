package com.shuidihuzhu.cf.constants.crowdfunding;

/**
 *
 * CfRedisKv中的key
 *
 * Created by wangsf on 17/5/3.
 */
public interface CfRedisKvKey {

	String KEY_ANONYMOUS_DEFAULT_NAME = "ANONYMOUS_DEFAULT_NAME";

	String KEY_ANONYMOUS_DEFAULT_HEAD = "ANONYMOUS_DEFAULT_HEAD";

	String KEY_REGISTER_SMS = "AD_REGISTER_SMS_CONTENT";

	//新版登记外呼测试百分比，v需要取0-100之间的值
	String KEY_AD_TEST_REGISTER_PERCENTAGE = "AD_TEST_REGISTER_PERCENTAGE";

	String PREFIX_WX_CLICK_MSG = "WX_CLICK_";

	//测试"问题咨询，美洽的测试比例"
	String CF_WX_CLICK_MEQIA_PERCENTAGE = "CF_WX_CLICK_MEQIA_PERCENTAGE";

	//测试"问题咨询，七鱼的测试比例"
	String CF_WX_CLICK_QIYUKF_PERCENTAGE = "CF_WX_CLICK_QIYUKF_PERCENTAGE";


	//筹款人订阅消息ABTest
	String CF_SUBSCRIBE_GREETING_ABTEST = "CF_SUBSCRIBE_GREETING_ABTEST";

	/*-
	 Local配置
	 */
	String CF_INFO_LOCAL_CACHE_EXPIRE = "CF_INFO_LOCAL_CACHE_EXPIRE";
	String CF_INFO_LOCAL_CACHE_REFRESH = "CF_INFO_LOCAL_CACHE_REFRESH";
	String CF_INFO_LOCAL_CACHE_CAPACITY = "CF_INFO_LOCAL_CACHE_CAPACITY";
	String CF_INFO_LOCAL_CACHE_MAX_SIZE = "CF_INFO_LOCAL_CACHE_MAX_SIZE";

	String CF_SUBSCRIBE_GREETING_LOCAL_CACHE_EXPIRE = "CF_SUBSCRIBE_GREETING_LOCAL_CACHE_EXPIRE";
	String CF_SUBSCRIBE_GREETING_LOCAL_CACHE_REFRESH = "CF_SUBSCRIBE_GREETING_LOCAL_CACHE_REFRESH";
	String CF_SUBSCRIBE_GREETING_LOCAL_CACHE_CAPACITY = "CF_SUBSCRIBE_GREETING_LOCAL_CACHE_CAPACITY";
	String CF_SUBSCRIBE_GREETING_LOCAL_CACHE_MAX_SIZE = "CF_SUBSCRIBE_GREETING_LOCAL_CACHE_MAX_SIZE";



	/**
	 * 黑名单
	 */
	String TMP_BLACKLIST_FOR_PROGRESS = "TMP_BLACKLIST_FOR_PROGRESS";
	String TMP_BLACKLIST_FOR_PROGRESS_MSG = "TMP_BLACKLIST_FOR_PROGRESS_MSG";


	String TMP_BLACKLIST_FOR_COMMENT = "TMP_BLACKLIST_FOR_COMMENT";

	String PARTNER_MINGYIHUI_SMS = "PARTNER_MINGYIHUI_SMS";


	String TENCENT_VIDEO_START = "VIDEO_START";
	String TENCENT_VIDEO_END = "VIDEO_END";

	/**
	 * 默认回复
	 */
	String WX_DEFAULT_REPLY = "WX_DEFAULT_REPLY";
	String WX_OUTER_WX_DEFAULT_REPLY = "WX_OUTER_WX_DEFAULT_REPLY";

	String ORDER_COUNT_AT_CURRENT_DAY = "ORDER_COUNT_AT_CURRENT_DAY_";

	static String getKeyDayOrderCount(String currentDayStr) {
		return ORDER_COUNT_AT_CURRENT_DAY + currentDayStr;
	}
}
