package com.shuidihuzhu.cf.constants.crowdfunding;

import com.google.common.base.Strings;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;

import java.text.MessageFormat;

/**
 * <AUTHOR>
 * @time 2018/12/19 下午7:53
 * @desc
 */
public class DefaultTextUtil {
    public static final String SEP_COMMA = "，";

    public static String first_value_233 = "患者{0}申请提现，提现申请将公示至{1}。\n\n";
    public static String keyword1_233 = "{0}";
    public static String keyword2_233 = "患者申请提现，善款用途为{0}。\n\n";
    public static String remark_233 = "➜点此查看公示详情";

    public static String title_331 = "{0}感谢你对{1}的善意捐助，再帮{2}转发一下，立即收获爱心值！";
    public static String description_331 = "❤ 爱心值代表你在水滴筹的爱心贡献度，帮{0}转发和证实也可以获得爱心值哟~";

    public static String getConstanceMatchMsg(String hawkeyeMsg, String match, String defaultMsg){
        if(StringUtils.isEmpty(hawkeyeMsg) || !hawkeyeMsg.contains(match)){
            return defaultMsg;
        }
        hawkeyeMsg = hawkeyeMsg.replace(match, defaultMsg);
        return hawkeyeMsg;
    }

    public static String getConstanceMsg(String hawkeyeMsg, String defaultMsg){
        if(StringUtils.isEmpty(hawkeyeMsg)){
            return defaultMsg;
        }
        return hawkeyeMsg;
    }

    public static String getSingleMsg(String hawkeyeMsg, String match, String defaultMsg, String replace){
        if(StringUtils.isEmpty(hawkeyeMsg) || !hawkeyeMsg.contains(match)){
            return MessageFormat.format(defaultMsg, replace);
        }

        hawkeyeMsg = hawkeyeMsg.replace(match, replace);

        return hawkeyeMsg;
    }

    // 匹配并格式化消息。 * 参数必须成对并有序
    public static String formatAndMatchMsg(String hawkeyeMsg, String defaultMsg, String... params) {
        // 纯文本内容
        if (null == params || params.length % 2 != 0) {
            return Strings.isNullOrEmpty(hawkeyeMsg) ? StringUtils.trimToEmpty(defaultMsg) : StringEscapeUtils.unescapeJava(hawkeyeMsg);
        }
        if (!Strings.isNullOrEmpty(hawkeyeMsg)) {
            // 匹配鹰眼变量
            for (int i = 0; i < params.length; i += 2) {
                if (!hawkeyeMsg.contains(params[i])) {
                    continue;
                }
                hawkeyeMsg = hawkeyeMsg.replace(params[i], StringUtils.trimToEmpty(params[i + 1]));
            }
            return StringEscapeUtils.unescapeJava(hawkeyeMsg);
        } else {
            // 匹配默认消息
            String[] values = new String[params.length / 2];
            for (int i = 0; i < params.length; i += 2) {
                values[i/2] = StringUtils.trimToEmpty(params[i + 1]);
            }
            return MessageFormat.format(defaultMsg, values);
        }
    }

}
