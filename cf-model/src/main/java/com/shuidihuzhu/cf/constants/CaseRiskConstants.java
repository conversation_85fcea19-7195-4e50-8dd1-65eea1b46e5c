package com.shuidihuzhu.cf.constants;

/**
 * <AUTHOR>
 * @date 2018-08-04  20:16
 */
public interface CaseRiskConstants {

    interface DataLevel {
        String LEVEL_72 = "72";

        String LEVEL_ALL = "all";
    }

    interface Risk{

        /**
         * 通过
         */
        int PASSED = 100;

    }

    /**
     * 是否待发送
     * 如果是1 数据审查通过的时候发送消息
     */
    interface CaseInfo {

        int PASSED = 1;

    }

    /**
     * 从可以转发开始已经72小时
     * 触发节点
     */
    interface Period {

        int WAITING = 0;

        int delay_72 = 1;

        int CASE_END = 2;
    }
}
