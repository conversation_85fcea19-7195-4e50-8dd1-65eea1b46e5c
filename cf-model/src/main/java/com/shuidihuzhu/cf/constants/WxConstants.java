package com.shuidihuzhu.cf.constants;

import com.google.common.collect.Lists;
import com.shuidihuzhu.wx.enums.AccountThirdTypeEnum;

import java.util.List;

public interface WxConstants {


    //替换水滴筹(3)
    public static int FUNDRAISER_THIRD_TYPE = 899;

    /**
     * 水滴筹健康生活（61）>水滴筹健康（115）>水滴筹健康管家（116）>水滴健康保障（17）>水滴筹（3）> 水滴保典(454) > 一分钟讲健康(453) > 今天你捡钱了吗（54）> 水滴爱心宝(136) > 水滴爱心平台(518)
     */
    List<Integer> MAJOR = Lists.newArrayList(
            AccountThirdTypeEnum.WX_CF_SMALL_MP_61.getCode(),
            AccountThirdTypeEnum.WX_CF_MP_HEALTH.getCode(),
            AccountThirdTypeEnum.WX_CF_MP_INSURANCE.getCode(),
            AccountThirdTypeEnum.WX_CF_SERIOUS_ILLNESS.getCode(),
            FUNDRAISER_THIRD_TYPE,
            AccountThirdTypeEnum.WX_SDBD.getCode(),
            AccountThirdTypeEnum.WX_YFZJJK.getCode(),
            AccountThirdTypeEnum.WX_CF_SMALL_MP_54.getCode(),
            136,
            518
            );


}
