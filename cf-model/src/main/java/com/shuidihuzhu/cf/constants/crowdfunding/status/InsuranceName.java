package com.shuidihuzhu.cf.constants.crowdfunding.status;

import com.google.common.base.Optional;
import com.google.common.collect.Maps;
import com.shuidihuzhu.common.web.bean.StateDescription;

import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/8/19.
 */
public enum InsuranceName implements StateDescription {

    YOUNG_PLAN          (1,   PERSIST,    "中青年抗癌计划"),
    CHILD_PLAN          (2,   PERSIST,    "少儿健康互助计划"),
    OLD_PLAN            (3,   PERSIST,    "中老年抗癌计划"),
    ACCIDENT_PLAN       (4,   PERSIST,    "综合意外互助计划"),
    OTHER_PLAN          (6,   PERSIST,    "水滴健康计划");

    final int code;
    final int type;
    final String description;

    InsuranceName(int code, int type, String description) {
        this.code = code;
        this.type = type;
        this.description = description;
    }

    /**
     * 通过code查找对应的枚举
     *
     * @param code int
     * @return OrderState
     */
    public static InsuranceName codeOf(int code) {
        return codeMapping.get(code);
    }


    @Override
    public String getName() {
        return name();
    }

    @Override
    public int getCode() {
        return this.code;
    }

    @Override
    public int getType() {
        return this.type;
    }

    @Override
    public String getDescription() {
        return this.description;
    }

    /**
     * 是否包含code
     *
     * @param code int
     * @return true 包含
     */
    public static boolean contains(int code) {
        return code >=0 && codeMapping.containsKey(code);
    }

    /**
     * 获得optional格式的枚举
     *
     * @param code Integer
     * @return Optional
     */
    public static Optional<InsuranceName> optionalOf(Integer code) {
        if (contains(code)) {
            return Optional.fromNullable(codeOf(code));
        }
        return Optional.absent();

    }

    /**
     * 获得optional格式的枚举
     *
     * @param code Integer
     * @return Optional
     */
    public static Optional<InsuranceName> optionalOf(int code) {
        if (contains(code)) {
            return Optional.fromNullable(codeOf(code));
        }
        return Optional.absent();

    }

    /**
     * 是否包含code
     *
     * @param code Integer
     * @return true 包含
     */
    public static boolean contains(Integer code) {
        return code != null && codeMapping.containsKey(code);
    }

    private static final Map<Integer, InsuranceName> codeMapping = Maps.newHashMap();

    static {
        for (InsuranceName state : InsuranceName.values()) {
            codeMapping.put(state.getCode(), state);
        }
    }
}
