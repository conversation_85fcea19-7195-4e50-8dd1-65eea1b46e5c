package com.shuidihuzhu.cf.service.notice.workwx;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.model.INameValuePair;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-06-14  18:54
 */
public class WorkWeiXinContentBuilder {

    private String subject;
    private List<INameValuePair> payloadList = Lists.newArrayList();

    private static final String SEPARATOR_PAYLOAD = "：";

    private static final String LINE_BREAK = "\n";

    public static WorkWeiXinContentBuilder create(){
        return new WorkWeiXinContentBuilder();
    }

    private WorkWeiXinContentBuilder() {
    }

    public WorkWeiXinContentBuilder subject(String subject) {
        this.subject = subject;
        return this;
    }

    public WorkWeiXinContentBuilder payload(String key, Object value) {
        String strValue = String.valueOf(value);
        if (StringUtils.isBlank(strValue)) {
            return this;
        }
        payloadList.add(new NameValuePair(key, strValue));
        return this;
    }


    /**
     *
     * eg: "[申请取消订单] | [orderId]: [" + orderId + "]";
     * @return msg content
     */
    public String build() {
        // check 合法
        if (StringUtils.isEmpty(subject)) {
            throw new IllegalArgumentException("必须要有主题 subject");
        }
        // 组装content
        StringBuilder sb = new StringBuilder();

        sb.append(wrapItem(subject));
        sb.append(LINE_BREAK);

        for (INameValuePair pair : payloadList) {
            buildPayload(sb, pair);
        }
        return sb.toString();
    }

    private void buildPayload(StringBuilder sb, INameValuePair pair) {
        sb.append(wrapItem(pair.getName()));
        sb.append(SEPARATOR_PAYLOAD);
        sb.append(wrapItem(pair.getValue()));
        sb.append(LINE_BREAK);
    }

    private static String wrapItem(String item) {
        return item;
    }

    public static void main(String[] args){
        String s = WorkWeiXinContentBuilder.create()
                .subject("主题")
                .payload("举报手机", 123412)
                .payload("d", 123)
                .build();
        System.out.println(s);

    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    static class NameValuePair implements INameValuePair{

        private String name;

        private String value;
    }
}
