package com.shuidihuzhu.cf.service.resulthandler;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2019-09-18 11:08
 *
 * 敏感词汇特殊处理策略，不影响存储，只是展示时特殊处理
 **/
public interface ContentShowHandler {

    String handle(String content);

    /**
     * 添加不需要处理的词，如姓氏，街道
     */
    void resetNoNeedHandleWord(List<WordManager> wordManager);

    /**
     * 敏感词管理
     */
    void addSeed(String seed, boolean ignoreLetter);
}
