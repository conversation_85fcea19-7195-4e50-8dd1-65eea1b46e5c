package com.shuidihuzhu.cf.service.resulthandler;

import com.google.common.collect.Lists;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Comparator;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2019-09-19 20:02
 **/
@Slf4j
public abstract class AbstractContentShowHandler implements ContentShowHandler {

    protected final static String APPEND_WORD = "中国";
    private static final String ALPHABET = "[\\u4E00-\\u9FA5A-Za-z0-9]";

    static List<WordManager> noNeedFilterWordList = Lists.newArrayList();
    static List<SeedManager> seedManagerList = Lists.newArrayList();

    static {
        noNeedFilterWordList.add(new WordManager(APPEND_WORD, true));


        seedManagerList.add(new SeedManager("香港"));
        seedManagerList.add(new SeedManager("澳门"));
        seedManagerList.add(new SeedManager("台湾"));
        seedManagerList.add(new SeedManager("hongkong"));
        seedManagerList.add(new SeedManager("xianggang"));
        seedManagerList.add(new SeedManager("aomen"));
        seedManagerList.add(new SeedManager("macao"));
        seedManagerList.add(new SeedManager("taiwan"));
        seedManagerList.add(new SeedManager("HK", true));
    }


    /**
     * 1、字符串转化为只含有汉字和字母的List<CharPosManager> filterContent
     * 2、获取不需要处理敏感词所在字符串中的原始位置 noNeedHandlePosList
     * 3、获取需要处理敏感词所在字符串中的原始位置 needHandlePosList
     * 4、needHandlePosList.count >= noNeedHandlePosList.couny
     * 从needHandlePosList中删除不需要处理的noNeedHandlePosList
     * 5、加工需要展示的字符串doHandle
     * @return
     */
    @Override
    public String handle(String content) {
        if (StringUtils.isEmpty(content)) {
            return content;
        }
        List<CharPosManager> charPosManagerList = filterContent(content);
        List<SeedAndPos> needHandlePosList = listAllNeedHandlePos(charPosManagerList);
        if (CollectionUtils.isEmpty(needHandlePosList)) {
            return content;
        }
        List<SeedAndPos> noNeedHandlePosList = listAllNoNeedHandlePos(charPosManagerList, content);
        List<SeedAndPos> realNeedHandlePosList =
                beforeHandle(noNeedHandlePosList.stream().distinct().sorted(Comparator.comparingInt(SeedAndPos::getPos)).collect(Collectors.toList()),
                        needHandlePosList.stream().distinct().sorted(Comparator.comparingInt(SeedAndPos::getPos)).collect(Collectors.toList()));
        StringBuilder stringBuilder = new StringBuilder(content);
        for (int handleCount = 0; handleCount < realNeedHandlePosList.size(); handleCount++) {
            doHandle(stringBuilder, handleCount, realNeedHandlePosList.get(handleCount).pos);
        }
        logInfo(realNeedHandlePosList, content, stringBuilder.toString());
        return stringBuilder.toString();
    }

    /**
     * 日志
     */
    private void logInfo(List<SeedAndPos> realNeedHandlePosList, String content, String afterHandlerContent) {
        if (CollectionUtils.isEmpty(realNeedHandlePosList)) {
            return;
        }
        List<String> seedList = Lists.newArrayList();
        for (SeedAndPos seedAndPos : realNeedHandlePosList) {
            seedList.add(seedAndPos.seedManager.word);
        }
        log.info("敏感词:{}##原文本:{}##处理后文本:{}", seedList, content, afterHandlerContent);
    }


    /**
     * 需要处理的位置
     */
    private List<SeedAndPos> listAllNeedHandlePos(List<CharPosManager> charPosManagerList) {
        return listPos(null, charPosManagerList);
    }


    private List<SeedAndPos> listAllNoNeedHandlePos(List<CharPosManager> charPosManagerList, String content) {
        List<SeedAndPos> posList = Lists.newArrayList();
        for (WordManager wordManager : noNeedFilterWordList) {
            posList.addAll(listPos(wordManager, charPosManagerList));
        }
        //需要考虑到前缀 + 敏感词 + 后缀，导致posList重复，去掉后缀
        List<SeedAndPos> sortedPosList = posList.stream().distinct().sorted(Comparator.comparingInt(SeedAndPos::getPos)).collect(Collectors.toList());
        List<String> seedList = noNeedFilterWordList.stream().map(WordManager::getWord).collect(Collectors.toList());
        for (int i = 0; i < sortedPosList.size() - 1; i++) {
            String middleContent = content.substring(sortedPosList.get(i).pos, sortedPosList.get(i + 1).pos);
            if ("HK".equals(middleContent) || seedList.contains(middleContent.toLowerCase())) {
                sortedPosList.remove(i + 1);
            }
        }
        return sortedPosList;
    }

    /**
     * 符合条件的位置
     */
    private List<SeedAndPos> listPos(WordManager wordManager, List<CharPosManager> charPosManagerList) {
        List<SeedAndPos> posList = Lists.newArrayList();
        for (SeedManager seed : seedManagerList) {
            String pattern = seed.word;
            if (wordManager != null && StringUtils.isNotEmpty(wordManager.getWord())) {
                pattern = wordManager.isBeforeSeed() ? wordManager.getWord() + seed.word : seed.word + wordManager.getWord();
            }
            Pattern p =  Pattern.compile(pattern);
            StringBuilder alphabetContentBuilder = new StringBuilder();
            for (CharPosManager charPosManager : charPosManagerList) {
                alphabetContentBuilder.append(charPosManager.charInfo);
            }
            String alphabetContent =  seed.contentCannotIgnoreLetter ? alphabetContentBuilder.toString() : alphabetContentBuilder.toString().toLowerCase();
            Matcher matcher = p.matcher(alphabetContent);
            while (matcher.find()) {
                posList.add(new SeedAndPos(seed, charPosManagerList.get(matcher.start()).originPos));
            }
        }
        return posList;
    }



    private List<SeedAndPos> beforeHandle(List<SeedAndPos> noNeedHandlePosList, List<SeedAndPos> needHandlePosList) {
        log.debug("noNeedHandlePosList:{},needHandlePosList:{}", noNeedHandlePosList, needHandlePosList);
        List<SeedAndPos> realNeedHandlePosList = Lists.newArrayList(needHandlePosList);
        if (!CollectionUtils.isEmpty(noNeedHandlePosList) && !CollectionUtils.isEmpty(needHandlePosList)) {
            //需要确定哪些是需要真正做处理的
            if (noNeedHandlePosList.size() > needHandlePosList.size()) {
                log.error("beforeHandle error noNeedHandlePosList:{}, needHandlePosList:{}",
                        noNeedHandlePosList, needHandlePosList);
            } else {
                for (SeedAndPos noNeedHandlePos : noNeedHandlePosList) {
                    for (int pos = 0; pos < needHandlePosList.size(); pos++) {
                        SeedAndPos needHandlePos = needHandlePosList.get(pos);
                        //获取离‘中国香港’最近的‘香港’位置
                        if (needHandlePos.pos >= noNeedHandlePos.pos) {
                            realNeedHandlePosList.remove(needHandlePos);
                            break;
                        }
                    }
                }
            }
        }
        if (!CollectionUtils.isEmpty(noNeedHandlePosList) && CollectionUtils.isEmpty(needHandlePosList)) {
            log.warn("beforeHandle error this situation should not happen");
        }
        log.debug("beforeHandle posList:{}", realNeedHandlePosList);
        return realNeedHandlePosList;
    }


    private void doHandle(StringBuilder stringBuilder, int handleTimes, int realNeedHandlePos) {
        int pos = realNeedHandlePos + handleTimes * APPEND_WORD.length();
        stringBuilder.insert(pos, APPEND_WORD);
    }

    private static List<CharPosManager> filterContent(String originContent) {
        List<CharPosManager> charPosManagerList = Lists.newArrayList();
        int handlePos = 0;
        Pattern p = Pattern.compile(ALPHABET);
        Matcher matcher = p.matcher(originContent);
        while (matcher.find()) {
            int start = matcher.start();
            charPosManagerList.add(new CharPosManager(originContent.charAt(start), start, handlePos++));
        }
        return charPosManagerList;
    }


    @Data
    static class SeedManager {
        private String word;
        private boolean contentCannotIgnoreLetter;

        public SeedManager(String word) {
            this.word = word;
        }

        public SeedManager(String word, boolean contentCannotIgnoreLetter) {
            this.word = word;
            this.contentCannotIgnoreLetter = contentCannotIgnoreLetter;
        }
    }

    @Data
    @AllArgsConstructor
    private static class CharPosManager {
        private char charInfo;
        private int originPos;
        private int handledPos;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @EqualsAndHashCode(exclude = "seedManager")
    public static class SeedAndPos {
        private SeedManager seedManager;
        private int pos;

        @Override
        public String toString() {
            return "SeedAndPos{" +
                    "pos=" + pos +
                    '}';
        }
    }

}
