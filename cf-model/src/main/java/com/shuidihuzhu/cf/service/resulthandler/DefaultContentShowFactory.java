package com.shuidihuzhu.cf.service.resulthandler;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: fengxuan
 * @create 2019-09-20 00:37
 **/
@Slf4j
public final class DefaultContentShowFactory {

    private static DefaultContentShowHandler showHandler = new DefaultContentShowHandler();

    private DefaultContentShowFactory() {

    }

    public static String handle(String content) {
        return showHandler.handle(content);
    }

    public static void setNoNeedHandleWord(List<WordManager> wordManagerList) {
        if (CollectionUtils.isEmpty(wordManagerList)) {
            return;
        }
        showHandler.resetNoNeedHandleWord(wordManagerList);
    }

    public static void addSeed(String seed, boolean ignoreLetter) {
        showHandler.addSeed(seed, ignoreLetter);
    }

    public static void main(String[] args) {
        //String msg = "香\n港";
        //String msg = "香\n港";
        //String msg = "xianggang,aoMEN,中国馆香。。。。。港";
        //String msg = "中国香。。。。。港";
        String msg = "香港，李李香    港街道,李HK街道，，，，李HK街道， 李HK，香港";
        List<WordManager> wordManagers = Lists.newArrayList(
                new WordManager("李", true),
                new WordManager("张", true),
                new WordManager("街道", false)
        );
        setNoNeedHandleWord(wordManagers);
        log.info("msg:{}", handle(msg));
    }

    static class DefaultContentShowHandler extends AbstractContentShowHandler {

        @Override
        public void addSeed(String seed, boolean ignoreLetter) {
            SeedManager seedManager = new SeedManager(seed, ignoreLetter);
            seedManagerList.add(seedManager);
        }

        @Override
        public void resetNoNeedHandleWord(List<WordManager> wordManager) {
            List<WordManager> list = Lists.newArrayList();
            list.addAll(wordManager);
            list.add(new WordManager(APPEND_WORD, true));
            noNeedFilterWordList = list.stream()
                    .filter((item) -> StringUtils.isNotEmpty(item.getWord()))
                    .distinct().collect(Collectors.toList());
            log.info("reset noNeedFilterWordList:{}", noNeedFilterWordList);
        }
    }
}
