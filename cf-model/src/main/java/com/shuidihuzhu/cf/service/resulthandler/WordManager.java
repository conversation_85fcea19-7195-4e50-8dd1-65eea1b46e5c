package com.shuidihuzhu.cf.service.resulthandler;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: fengxuan
 * @create 2019-09-21 00:00
 **/
@Data
@EqualsAndHashCode
public class WordManager {
    private String word;
    private boolean beforeSeed = true;
    private boolean ignoreLetter = true;

    public WordManager() {
    }

    public WordManager(String word, boolean beforeSeed) {
        this.word = word;
        this.beforeSeed = beforeSeed;
    }
}
