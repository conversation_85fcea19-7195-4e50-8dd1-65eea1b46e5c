package com.shuidihuzhu.cf.bigapp;

/**
 * Author:梁洪超
 * Date:2017/11/20
 */
public enum YouZanData {
	//初始化
	INITIALIZE(1,"https://uic.youzan.com/sso/open/initToken"),
	//登录
	LOGIN(2,"https://uic.youzan.com/sso/open/login"),
	//登出
	LOGOUT(3,"https://uic.youzan.com/sso/open/logout"),
	//CLIENT_ID
	CLIENT_ID(4,"0f43b14ecf800bb1e3"),
	//CLIENT_SECRET
	CLIENT_SECRET(5,"5c27108158d7dc9ac904abe64d8c6910");
	private  int code ;
	private  String value;
	YouZanData(int code , String linkUrl){
		this.code = code ;
		this.value = linkUrl ;
	}
	
	public int getCode() {
		return code;
	}
	public String getValue() {
		return value;
	}
	
	public static YouZanData getYouZanData(int code){
		for (YouZanData youZanData : YouZanData.values()) {
			if(youZanData.getCode() == code) {
				return youZanData ;
			}
		}
		return  null ;
	}
}
