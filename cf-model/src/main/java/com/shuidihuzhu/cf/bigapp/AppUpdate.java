package com.shuidihuzhu.cf.bigapp;

import java.io.Serializable;

/**
 * Author:梁洪超
 * Date:2017/11/19
 */
public class AppUpdate implements Serializable {
	
	private static final long serialVersionUID = 3739240792494050868L;
	int id ;              //自增ID
	String 	bundleId ;    //App标识符
	String systemType;    //系统类型
	int startVersion;     //起始版本
	int endVersion;       //结束版本
	int isUpdate;         //是否更新,1为不更新，0为更新
	int isForceUpdate;    //是否强制更新,1为不更新，0为更新
	String 	descrip ;       //更新文案
	String updateVersion;    //更新版本
	String updateUrl;     //更新地址
	
	public int getId() {
		return id;
	}
	
	public void setId(int id) {
		this.id = id;
	}
	
	public String getBundleId() {
		return bundleId;
	}
	
	public void setBundleId(String bundleId) {
		this.bundleId = bundleId;
	}
	
	public String getSystemType() {
		return systemType;
	}
	
	public void setSystemType(String systemType) {
		this.systemType = systemType;
	}
	
	public int getStartVersion() {
		return startVersion;
	}
	
	public void setStartVersion(int startVersion) {
		this.startVersion = startVersion;
	}
	
	public int getEndVersion() {
		return endVersion;
	}
	
	public void setEndVersion(int endVersion) {
		this.endVersion = endVersion;
	}
	
	public int getIsUpdate() {
		return isUpdate;
	}
	
	public void setIsUpdate(int isUpdate) {
		this.isUpdate = isUpdate;
	}
	
	public int getIsForceUpdate() {
		return isForceUpdate;
	}
	
	public void setIsForceUpdate(int isForceUpdate) {
		this.isForceUpdate = isForceUpdate;
	}
	
	public String getDescrip() {
		return descrip;
	}
	
	public void setDescrip(String descrip) {
		this.descrip = descrip;
	}
	
	public String getUpdateVersion() {
		return updateVersion;
	}
	
	public void setUpdateVersion(String updateVersion) {
		this.updateVersion = updateVersion;
	}
	
	public String getUpdateUrl() {
		return updateUrl;
	}
	
	public void setUpdateUrl(String updateUrl) {
		this.updateUrl = updateUrl;
	}
	
	@Override
	public String toString() {
		return "AppUpdate{" +
		       "id=" + id +
		       ", bundleId='" + bundleId + '\'' +
		       ", systemType='" + systemType + '\'' +
		       ", startVersion=" + startVersion +
		       ", endVersion=" + endVersion +
		       ", isUpdate=" + isUpdate +
		       ", isForceUpdate=" + isForceUpdate +
		       ", title='" + descrip + '\'' +
		       ", updateVersion=" + updateVersion +
		       ", updateUrl='" + updateUrl + '\'' +
		       '}';
	}
}
